"""
LEGACY MRI Property Type Exceptions Script
Converted from R to Python

Version: 20241111
Author: Converted from R script

This script:
1. Connects to Snowflake database
2. Queries for MRI Property Type exceptions
3. Creates Excel report
4. Emails the report to specified recipients
"""

import pandas as pd
import numpy as np

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

import os
import sys
import datetime

from pathlib import Path

import warnings

import libs.snowflake_helper as sf
import libs.email_client as email_client


warnings.filterwarnings('ignore')

OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration
class Config:
    def __init__(self, sf_obj):

        self.sf_obj = sf_obj
        
        self.testing_emails = True  # Set to False for production
        self.version = "20241111"
        self.query_date = datetime.datetime.now().strftime("%d-%b-%y")
        self.script_folder = "LEGACY_MRI_Exceptions-Property_Type"
        self.report_name = "MRI Property Type Exceptions"
        self.okay_to_continue = True
        
        # Environment settings
        self.sf_environ = "PROD"  # or "STAGE"
        # self.setup_snowflake_config()
        self.setup_paths()
        self.setup_email_config()
        
            
    def setup_paths(self):
        """Setup file paths based on computer"""

        self.testing_pc = False
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
            
        self.log_path = self.main_path / self.script_folder
        self.report_path = self.log_path
        
    def setup_email_config(self):
        """Setup email configuration"""
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>", "<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>","<EMAIL>"]
        
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        self.norm_sig = self.get_email_signature()
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        
    def get_email_signature(self):
        """Get email signature"""
        return (
            "<b><span style='font-weight:bold'>Steve Olson</span></b><br/>"
            "Sr. Analytics Mgr.<br/>"
            "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>"
            "2500 Lehigh Ave.<br/>"
            "Glenview, IL 60026<br/>"
            "Ph: 847/904-9043<br/></span></font>"
        )



class ExcelReportGenerator:
    def __init__(self, config):
        self.config = config
        
    def create_excel_report(self, data, filename, sheet_name):
        """Create Excel report with formatting"""
        try:
            # Ensure directory exists
            self.config.report_path.mkdir(parents=True, exist_ok=True)
            
            filepath = self.config.report_path / filename
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data to worksheet
            for r in dataframe_to_rows(data, index=False, header=True):
                ws.append(r)
                
            # Format header row
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_font = Font(name="Arial Narrow", size=12, bold=True)
            header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            
            for cell in ws[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                
            # Set column widths
            column_widths = {
                'ISSUE': 31,
                'BLDGID': 8.5,
                'ADDRESS': 27,
                'CITY': 14,
                'STATE': 7,
                'Property Type ID': 10,
                'Property Type': 10
            }
            
            for col_idx, column in enumerate(data.columns, 1):
                if column in column_widths:
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = column_widths[column]
                    
            # Freeze panes
            ws.freeze_panes = 'A2'
            
            # Add auto filter
            ws.auto_filter.ref = ws.dimensions
            
            # Save workbook
            wb.save(filepath)
            print(f"Excel report saved: {filepath}")
            self.config.sf_obj.log_audit_in_db(log_msg=f"Excel report saved: {filepath}", process_type=self.config.report_name, script_file_name=__file__, log_type='Info')
            return str(filepath)
            
        except Exception as e:
            print(f"Error creating Excel report: {e}")
            self.config.sf_obj.log_audit_in_db(log_msg=f"Error creating Excel report: {e}", process_type=self.config.report_name, script_file_name=__file__, log_type='Error')
            return None


class PropertyTypeExceptionProcessor:
    def __init__(self):

        self.sf_obj = sf.SnowflakeHelper()
        # self.email_obj = email_client.EmailClient()

        self.config = Config(self.sf_obj)

        self.excel_generator = ExcelReportGenerator(self.config)

        
    def check_data_rows(self, data, min_rows=1):
        """Check if data meets minimum row requirements"""
        if data is None or data.empty:
            return False, 0, f"{self.config.report_name}: NO RESULTS"
        elif len(data) >= min_rows:
            return True, len(data), f"{self.config.report_name}: COMPLETE"
        else:
            return False, len(data), f"{self.config.report_name}: INCOMPLETE RESULTS"
            
    def create_html_table(self, data):
        """Create HTML table from DataFrame for email"""
        html_table = data.to_html(
            index=False,
            table_id="exceptions_table",
            classes="table table-striped",
            escape=False,
            border=2
        )
        return html_table
        
    def run_property_type_exceptions(self):
        """Main function to process property type exceptions"""
        print(f"Beginning '{self.config.report_name}' routine")
        self.config.sf_obj.log_audit_in_db(log_msg=f"Beginning '{self.config.report_name}' routine", process_type=self.config.report_name, script_file_name=__file__, log_type='Info')
        
        if not self.config.okay_to_continue:
            print("Script execution halted by configuration")
            self.config.sf_obj.log_audit_in_db(log_msg=f"Script execution halted by configuration", process_type=self.config.report_name, script_file_name=__file__, log_type='Error')
            return
            
        # Connect to Snowflake
        # if not self.sf_connector.connect():
        #     print("Failed to connect to Snowflake")
        #     return
            
        try:
            # Define the exceptions query
            exceptions_query = """
            SELECT 
                'BLDG missing Property Type info (set in ''Entities'')' AS ISSUE,
                BLDG.BLDGID,
                BLDG.ADDRESS1 AS ADDRESS,
                BLDG.CITY,
                BLDG.STATE,
                PTYP.PROPTYPE AS "Property Type ID",
                PTYP.DESCRPN AS "Property Type"
            FROM MRI.BLDG
            INNER JOIN MRI.ENTITY
                ON BLDG.ENTITYID = ENTITY.ENTITYID
            LEFT JOIN MRI.PTYP
                ON ENTITY.PROPTYPE = PTYP.PROPTYPE
            WHERE (BLDG.INACTIVE <> 'Y' OR BLDG.INACTIVE IS NULL)
                AND PTYP.DESCRPN IS NULL
                 AND BLDG.BLDGID NOT IN ('HV3RD','HVCORP')
            ORDER BY BLDG.BLDGID
            """
            # print(f"Query: {exceptions_query}")
            # exit()

            # Execute query
            print("Executing property type exceptions query...")
            self.config.sf_obj.log_audit_in_db(log_msg=f"Executing property type exceptions query...", process_type=self.config.report_name, script_file_name=__file__, log_type='Info')
            # data = self.sf_connector.execute_query(exceptions_query)
            
            data = self.sf_obj.execute_query(query=exceptions_query, return_df=True)
            
            # Check data status
            has_data, row_count, status = self.check_data_rows(data, min_rows=1)
            print(f"Query result: {status} ({row_count} rows)")
            self.config.sf_obj.log_audit_in_db(log_msg=f"Query result: {status} ({row_count} rows)", process_type=self.config.report_name, script_file_name=__file__, log_type='Info')
            
            if has_data:
                # Create Excel file
                filename = "MRI_Property_Type_Exceptions.xlsx"
                sheet_name = self.config.query_date
                
                excel_path = self.excel_generator.create_excel_report(
                    data, filename, sheet_name
                )
                
                if excel_path:
                    # Create email body
                    html_table = self.create_html_table(data)
                    
                    body_text = f"""
                    <p><b>{self.config.report_name}</b></p>
                    <p>The info below (also included in attached Excel file) contains MRI data from yesterday that may need updating.</p>
                    <br/>
                    <table border="2" cellspacing="1">
                        <caption><b>{self.config.report_name} ({self.config.query_date})</b></caption>
                        {html_table}
                    </table>
                    <br/><br/>
                    {self.config.norm_sig}
                    """
                    
                    # Send email
                    email_client.send_email(
                        recipient=self.config.norm_recip,
                        subject=self.config.report_name,
                        body=body_text,
                        attachments=[excel_path],
                        replyto=self.config.gmail_reply_to,
                        override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                    )
                    
                    print(f"Report processing completed successfully")
                    self.config.sf_obj.log_audit_in_db(log_msg=f"Report processing completed successfully", process_type=self.config.report_name, script_file_name=__file__, log_type='Info')
                else:
                    print("Failed to create Excel report")
                    self.config.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel report", process_type=self.config.report_name, script_file_name=__file__, log_type='Error')
            else:
                print("No exceptions found or data retrieval failed")
                self.config.sf_obj.log_audit_in_db(log_msg=f"No exceptions found or data retrieval failed", process_type=self.config.report_name, script_file_name=__file__, log_type='Error')
                
        except Exception as e:
            print(f"Error in main processing: {e}")
        # finally:
        #     # Disconnect from Snowflake
        #     self.sf_connector.disconnect()

def main():
    """Main execution function"""
    try:
        processor = PropertyTypeExceptionProcessor()
        processor.run_property_type_exceptions()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 