"""
LEGACY MRI Exceptions - Suites E with Active Lease
Converted from R to Python

This script finds MRI suite exceptions where:
- Suite Type Area Usage is 'E' (Exclude), but has 'Current' lease (w/rent > $0)

Version: 20241113 (Python conversion)
"""

import pandas as pd

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.worksheet.filters import AutoFilter
from openpyxl.utils.dataframe import dataframe_to_rows

import os
import sys
from datetime import datetime, date
import platform
from pathlib import Path

import libs.snowflake_helper as sf
import libs.email_client as email_client


OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration
TESTING_EMAILS = False  # Set to True for testing
VERSION = "20241113"

class MRISuiteExceptionsProcessor:
    def __init__(self):
        self.sf_obj = sf.SnowflakeHelper()
        self.sf_conn = self.sf_obj.conn
        
        self.script_folder = "LEGACY_MRI_Exceptions-Suites"
        self.report_name = "MRI Suite Exceptions-Excluded suite has active lease"
        self.report_fn = "MRI_Suite_Exceptions-E_suite_active_lease.xlsx"
        self.report_criteria = """<p><b>Criteria for exceptions:</b><ul>
                                <li>Suite Type Area Usage is 'E' (Exclude), but 'Current' lease (w/rent > $0)</li>
                                </ul></p><br/>"""
        
        # Initialize paths and environment
        self.setup_environment()
        
                
        # Email configuration
        self.setup_email_config()
        
        # Query date
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S%Z")
        
        print(f"Beginning '{self.report_name}' routine")
        
    def setup_environment(self):
        """Setup file paths based on environment"""
        self.testing_pc = False
        # self.main_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
            
        self.log_path = self.main_path / self.script_folder
        self.report_path = self.log_path
        
        # Create directories if they don't exist
        self.log_path.mkdir(parents=True, exist_ok=True)
        self.report_path.mkdir(parents=True, exist_ok=True)
        
        
    def setup_email_config(self):
        """Setup email configuration"""
        # Email recipients and signatures
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>","<EMAIL>"]
        self.reply_to = "<EMAIL>"
        
        self.norm_sig = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
                          Sr. Analytics Mgr.<br/>
                          <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
                          2500 Lehigh Ave.<br/>
                          Glenview, IL 60026<br/>
                          Ph: 847/904-9043<br/>"""
        
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        
        # Try to load HTML signatures if available
        self.load_html_signatures()
        
    def load_html_signatures(self):
        """Load HTML email signatures from CSV file"""
        try:
            sig_path = self.main_path / "HTML_signatures.csv"
            if sig_path.exists():
                sig_df = pd.read_csv(sig_path)
                lcp_sig = sig_df[sig_df['Desc'] == 'LCP Reporting']['HTML'].iloc[0]
                if pd.notna(lcp_sig):
                    self.norm_sig = lcp_sig
        except Exception as e:
            self.logger.warning(f"Could not load HTML signatures: {e}")
            
    
            
    def execute_exceptions_query(self):
        """Execute the main exceptions query"""
        query = """
        SELECT
            'Suite Type Usage = ''E (Exclude)'' with active lease' as ISSUE,
            B.BLDGID AS BLDGID,
            SANDL.SUITID,
            SANDL.SUITETYPE_MRI AS "SUITETYPE MRI",
            SANDL.SUITE_TYPE_AREA_USAGE AS "SUITE TYPE AREA USAGE",
            SANDL.COUNT_AS_OCCUPIED AS "COUNT AS OCCUPIED",
            SANDL.LEASID,
            SANDL.OCCUPANT_STATUS AS "OCCUPANT STATUS",
            SANDL.ACTIVE_CONTINGENCY AS "ACTIVE CONTINGENCY",
            SANDL.GENERATION_CODE AS "GEN CODE",
            SANDL.TENANT_NAME AS "TENANT NAME",
            SANDL.RENT_START AS "RENT START",
            SANDL.STOP_BILL_DATE AS "STOP BILL DATE",
            SANDL.VACATE_DATE AS "VACATE DATE",
            SANDL.EXPIRE_DATE AS "EXPIRE DATE",
            SANDL.THIRD_PARTY_RENT AS "3RD PARTY RENT",
            SANDL.INTERNAL_RENT AS "INTERNAL RENT"
        FROM MRI.BLDG B
        LEFT JOIN
        ( /* Suites and Leases */
            select 
                S.BLDGID,
                S.SUITID,
                S.SUITETYPE_MRI,
                S.SUITSQFT AS SUITE_SQ_FEET,
                CONCAT(TB_CM_SUITETYPE.SUITETYPEUSAGE,CASE WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'I' THEN ' (Include)' 
                            WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' THEN ' (Include Not Counted)' 
                            WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'E' THEN ' (Exclude)' END) AS SUITE_TYPE_AREA_USAGE,
                SSQF_TYPE.SQFTTYPE,
                (CASE WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'I' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL THEN 1 END) AS COUNT_AS_SUITE,
                (CASE WHEN LEASED.OCCPSTAT ='C' AND LEASED.GENCODE <> 'SNC' AND (S.SUITETYPE_MRI <> 'KIOSK' OR S.SUITETYPE_MRI IS NULL) THEN 1
                    WHEN LEASED.OCCPSTAT ='C' AND LEASED.GENCODE = 'SNC' AND (S.SUITETYPE_MRI <> 'KIOSK' OR S.SUITETYPE_MRI IS NULL) AND LEASED.RENT_START <= Cast(GetDate() AS date) AND (LEASED.ACTIVE_CONTINGENCY IS NULL OR LEASED.ACTIVE_CONTINGENCY = 'N')  THEN 1 END) AS COUNT_AS_OCCUPIED,
                IFNULL(SSQF_TYPE.SQFT,0) AS LEASABLE_SQFT,
                IFNULL((CASE WHEN LEASED.ACTIVE_CONTINGENCY = 'Y' THEN 0 ELSE SSQF_TYPE.LSDSQFT END),0) AS LEASED_SQFT,
                LEASED.LEASID,
                LEASED.ACTIVE_CONTINGENCY,
                LEASED.GENCODE AS GENERATION_CODE,
                LEASED.OCCPNAME AS TENANT_NAME,
                LEASED.OCCUPANT_STATUS AS OCCUPANT_STATUS,
                LEASED.RENT_START,
                LEASED.STOPBILLDATE AS STOP_BILL_DATE,
                LEASED.VACATEDATE AS VACATE_DATE,
                LEASED.EXPIRDATE AS EXPIRE_DATE,
                LEASED.THIRD_PARTY_RENT,
                LEASED.INTERNAL_RENT
            FROM MRI.SUIT S
            JOIN MRI.BLDG B
            ON B.BLDGID = S.BLDGID
            LEFT JOIN MRI.TB_CM_SUITETYPE
            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            LEFT JOIN 
            (
                SELECT *
                FROM MRI.SSQF
                WHERE SSQF.EFFDATE = (
                    SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
                    )
            ) SSQF_TYPE
            ON S.SUITID = SSQF_TYPE.SUITID
             AND S.BLDGID = SSQF_TYPE.BLDGID
            LEFT JOIN
            (
                SELECT 								
                    L.BLDGID,
                    TRIM(P.PORTID) AS PORTFOLIO,
                    L.SUITID,							
                    (CASE WHEN L.TENTCAT != 'INTER' THEN '3rd Party Base Rent' ELSE 'Internal Base Rent' END) AS RENT_TYPE,
                    L.LEASID,
                    L.OCCPNAME,							
                    TO_DATE(L.RENTSTRT) AS RENT_START,							
                    CAST(S.SUITSQFT AS INT) AS SUITE_SQFT,							
                    CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
                    TRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
                    L.OCCPSTAT,
                    CL.CODEDESC AS OCCUPANT_STATUS,
                    L.TENTCAT AS TENANT_CATEGORY_ID,
                    TCAT.TENTDESC AS TENANT_CATEGORY_DESCRIPTION,	
                    TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
                    TO_CHAR(L.VACATE, 'MM/dd/yyyy') AS VACATEDATE,							
                    TO_CHAR(L.EXPIR,'MM/dd/yyyy') as EXPIRDATE,														
                    (CASE WHEN L.COMPANYGRPID = 2 THEN 0 ELSE IFNULL(RENT.AMOUNT,0) END) AS THIRD_PARTY_RENT,
                    (CASE WHEN L.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE 0 END) AS INTERNAL_RENT,
                    S.SUITETYPE_MRI,							
                    TB_CM_SUITETYPE.SUITETYPEUSAGE,							
                    TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
                    L.GENCODE,
                    L.CONTINGENT,
                    L.CONTINGENTDT,
                    case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= Cast(GetDate() AS date) THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY,
                    case when L.COMPANYGRPID = 2 THEN 'INT' ELSE 'EXT' END AS INTEXT							
                FROM MRI.SUIT S
                LEFT JOIN MRI.LEAS L
                 ON S.SUITID = L.SUITID
                    AND S.BLDGID = L.BLDGID
                left join								
                (								
                    SELECT CMRECC.LEASID,
                        SUM(CMRECC.AMOUNT) AS AMOUNT
                    FROM MRI.CMRECC
                    JOIN MRI.LEAS ON CMRECC.LEASID = LEAS.LEASID
                    LEFT JOIN MRI.INCH_LCP_REPORTS
                    ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
                    WHERE CMRECC.EFFDATE = (
                                SELECT MAX(IC.EFFDATE) AS EFFDATE
                                FROM MRI.CMRECC IC 
                                WHERE TO_DATE(LEAS.RENTSTRT) < CURRENT_DATE
                                AND IC.BLDGID=CMRECC.BLDGID 
                                AND IC.LEASID=CMRECC.LEASID 
                                AND IC.INCCAT=CMRECC.INCCAT 
                                AND IC.INEFFECT = 'Y'
                        )
                        AND (INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT') OR CMRECC.INCCAT IN ('PPR') )
                    GROUP BY
                        CMRECC.LEASID					
                ) RENT	
                ON L.LEASID = RENT.LEASID								
                INNER JOIN MRI.BLDG B								
                ON S.BLDGID = B.BLDGID	
                LEFT JOIN MRI.TCAT ON L.TENTCAT = TCAT.TENTCAT
                LEFT JOIN (								
                    SELECT * 							
                    FROM MRI.SSQF 							
                    WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) 
                            FROM MRI.SSQF I 
                            WHERE I.BLDGID = SSQF.BLDGID 
                            AND I.SUITID = SSQF.SUITID 
                            AND TO_DATE(I.EFFDATE) <= CURRENT_DATE 
                        )							
                ) SQF								
                ON S.BLDGID = SQF.BLDGID								
                    AND S.SUITID = SQF.SUITID							
                LEFT JOIN MRI.TB_CM_SUITETYPE								
                ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
                LEFT JOIN MRI.ENTITY E								
                ON B.ENTITYID = E.ENTITYID								
                LEFT JOIN MRI.PROJ P								
                ON E.PROJID = P.PROJID																
                LEFT JOIN MRI.CODELIST CL								
                ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'								
                WHERE L.OCCPSTAT NOT IN ('P', 'I') 
                    AND (							
                            (							
                                TO_DATE(L.RENTSTRT) <= CURRENT_DATE				
                                AND (				
                                        L.STOPBILLDATE IS NULL 		
                                        OR TO_DATE(L.STOPBILLDATE) >= CURRENT_DATE		
                                        OR (		
                                                TO_DATE(L.STOPBILLDATE) < CURRENT_DATE 
                                                AND COALESCE(L.VACATE,L.EXPIR) >= CURRENT_DATE
                                            )	
                                    )			
                                AND COALESCE(L.VACATE,L.EXPIR) >= CURRENT_DATE 
                            )					
                            OR 						
                            (((TO_DATE(L.EXPIR) <= CURRENT_DATE OR L.EXPIR IS NULL) AND (TO_DATE(L.VACATE) >= CURRENT_DATE OR L.VACATE IS NULL))) 					
                            AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
                        )
            )LEASED
            ON S.BLDGID = LEASED.BLDGID
                AND S.SUITID = LEASED.SUITID
            WHERE (B.INACTIVE <> 'Y' or B.INACTIVE IS NULL)
                AND TB_CM_SUITETYPE.SUITETYPEUSAGE = 'E'
        ) SANDL
        on B.BLDGID = SANDL.BLDGID
        WHERE SANDL.LEASID IS NOT NULL
            AND (SANDL.THIRD_PARTY_RENT > 0 OR SANDL.INTERNAL_RENT > 0)
        ORDER BY B.BLDGID, SANDL.SUITID
        """
        
        try:
            cursor = self.sf_conn.cursor()
            cursor.execute(query)
            
            # Fetch results and column names
            results = cursor.fetchall()
            column_names = [desc[0] for desc in cursor.description]
            
            # Create DataFrame
            df = pd.DataFrame(results, columns=column_names)
            
            # Clean up string columns (trim whitespace)
            for col in df.select_dtypes(include=['object']).columns:
                df[col] = df[col].astype(str).str.rstrip()
                
            cursor.close()
            
            self.sf_obj.log_audit_in_db(log_msg=f"Query executed successfully, returned {len(df)} rows", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            # self.logger.info(f"Query executed successfully, returned {len(df)} rows")
            return df
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to execute query: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Failed to execute query: {e}")
            return None
            
    def create_excel_report(self, df, filename):
        """Create Excel report with formatting"""
        try:
            file_path = self.report_path / filename
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = self.query_date
            
            # Write data to worksheet
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
                
            # Format header row
            header_font = Font(name='Arial Narrow', size=12, bold=True, color='000000')
            header_fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                
            # Set column widths
            column_widths = {
                'ISSUE': 43.5, 'BLDGID': 8, 'SUITID': 8, 'SUITETYPE MRI': 10.25,
                'SUITE TYPE AREA USAGE': 10.25, 'COUNT AS OCCUPIED': 10.25,
                'LEASID': 8, 'OCCUPANT STATUS': 9.5, 'ACTIVE CONTINGENCY': 7.25,
                'GEN CODE': 6, 'TENANT NAME': 20, 'RENT START': 10.5,
                'STOP BILL DATE': 10.5, 'VACATE DATE': 10.5, 'EXPIRE DATE': 10.5,
                '3RD PARTY RENT': 8, 'INTERNAL RENT': 9.5
            }
            
            for col_idx, col_name in enumerate(df.columns, 1):
                if col_name in column_widths:
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = column_widths[col_name]
                    
            # Freeze header row
            ws.freeze_panes = ws['A2']
            
            # Add auto filter
            ws.auto_filter = AutoFilter(ref=f"A1:{openpyxl.utils.get_column_letter(len(df.columns))}{len(df) + 1}")
            
            # Save workbook
            wb.save(file_path)
            self.sf_obj.log_audit_in_db(log_msg=f"Excel report saved: {file_path}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            # self.logger.info(f"Excel report saved: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel report: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Failed to create Excel report: {e}")
            return None
            
    def create_email_body_table(self, df):
        """Create HTML table for email body"""
        if len(df) <= 20:
            # Include table in email body for small datasets
            email_cols = [0, 1, 2, 4, 10, 15, 16]  # Selected columns for email
            email_df = df.iloc[:, email_cols].copy()
            
            # Format dates
            for col in email_df.columns:
                if email_df[col].dtype == 'datetime64[ns]':
                    email_df[col] = email_df[col].dt.strftime('%m/%d/%y')
                    
            html_table = email_df.to_html(index=False, table_id='exceptions_table',
                                        classes='table table-bordered',
                                        escape=False)
            return f"<p>{html_table}</p>"
        else:
            return f"<p>There are {len(df)} results, see attached file for all.</p>"
            
    
    def check_data_quality(self, df, min_rows=1):
        """Check if data meets quality requirements"""
        if df is None:
            return False, 0, f"{self.report_name}: NO RESULTS"
        elif len(df) >= min_rows:
            return True, len(df), f"{self.report_name}: COMPLETE"
        else:
            return False, len(df), f"{self.report_name}: INCOMPLETE RESULTS"
            
    def process_exceptions(self):
        """Main processing method"""
        try:
                
            # Execute query
            df = self.execute_exceptions_query()
            
            # Check data quality
            data_ok, row_count, status = self.check_data_quality(df, min_rows=1)
            self.sf_obj.log_audit_in_db(log_msg=f"Data check result: {status} ({row_count} rows)", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            # self.logger.info(f"Data check result: {status} ({row_count} rows)")
            
            if data_ok and row_count > 0:
                # Create Excel report
                excel_file = self.create_excel_report(df, self.report_fn)
                
                if excel_file:
                    # Create email body with table
                    body_table = self.create_email_body_table(df)
                    
                    body_text = f"""<p><b>REPORT: {self.report_name}</b></p>
                                   {self.report_criteria}
                                   <p>The info below contains MRI data (from yesterday) that appears to be an exception. 
                                   <b>See attached Excel file for more details.</b></p>
                                   {body_table}
                                   <br/>
                                   {self.norm_sig}"""
                    
                    # Send email
                    recipients = self.test_recip if TESTING_EMAILS else self.norm_recip
                    email_client.send_email(
                        recipient=recipients,
                        subject=self.report_name,
                        body=body_text,
                        attachments=[excel_file],
                        override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                    )
                    
                    # if success:
                    self.sf_obj.log_audit_in_db(log_msg="Report completed successfully", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                # self.logger.info("Report completed successfully")
                    return True
                    # else:
                    #     self.sf_obj.log_audit_in_db(log_msg="Failed to send email", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    #     # self.logger.error("Failed to send email")
                    #     return False
                else:
                    self.sf_obj.log_audit_in_db(log_msg="Failed to create Excel file", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    # self.logger.error("Failed to create Excel file")
                    return False
            else:
                self.sf_obj.log_audit_in_db(log_msg="No exceptions found or insufficient data", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                # self.logger.info("No exceptions found or insufficient data")
                return True
                
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error in process_exceptions: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error in process_exceptions: {e}")
            return False
        # finally:
        #     # Close database connection
        #     if self.sf_conn:
        #         self.sf_conn.close()
        #         self.logger.info("Database connection closed")
                
    def run(self):
        """Main entry point"""
        self.sf_obj.log_audit_in_db(log_msg=f"Starting {self.report_name} process", process_type=self.report_name, script_file_name=__file__, log_type='Info')
        # self.logger.info(f"Starting {self.report_name} process")
        success = self.process_exceptions()
        
        if success:
            self.sf_obj.log_audit_in_db(log_msg="Process completed successfully", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            # self.logger.info("Process completed successfully")
        else:
            self.sf_obj.log_audit_in_db(log_msg="Process completed with errors", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            # self.logger.error("Process completed with errors")
            
        return success


def main():
    """Main function"""
    processor = MRISuiteExceptionsProcessor()
    return processor.run()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 