#!/usr/bin/env python3
"""
Portfolio to Google Sheets Report - Python Version
Original R script by <PERSON> August 2022
Converted from R script by <PERSON> 6/7/25
Python conversion: June 2025

This script:
1. Connects to Snowflake database
2. Queries portfolio data 
3. Updates Google Sheets
4. Sends email notifications
"""

import os
import sys
import logging
import pandas as pd
import numpy as np

from datetime import datetime, date

# import smtplib
# from email.mime.text import MIMEText
# from email.mime.multipart import MIMEMultipart
# import socket
import time
import re
from typing import List, Dict, Optional, Tuple, Any

# Configuration
TESTING_EMAILS = False  # Set to True for testing
VERSION = "20241008_Python"

class PortfolioSheetHelper:
    def __init__(self, sf, csm_db):
        self.report_name = "LEGACY Portfolio to Excel"
        # self.gsht_id = "***************************-soaHdfzRxz2mZCQ8"  # PROD version
        # self.gsht_id = "16QUYf48WFORTzDW3ihR8C-UP0-JwAwy39QcKjBngMIY"  # TEST version
        # self.my_sheets = ["PORTFOLIO"]
        self.gsht_auth_email = "<EMAIL>"
        
        
        # Email configuration
        self.gmail_auth_email = "<EMAIL>"
        self.warn_recip = ["<EMAIL>", "<EMAIL>"]
        self.norm_recip = ["<EMAIL>", "<EMAIL>"]
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        

        self.sf = sf
        self.csm_db = csm_db
        # Initialize connections
        self.sf_connection = self.sf.conn

            
    def check_df_rows(self, df: pd.DataFrame, min_rows: int, report_name: str = None) -> Tuple[bool, int, str]:
        """Check if dataframe has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            num_rows = len(df)
            if num_rows >= min_rows:
                return True, num_rows, f"{report_name}: OKAY"
            else:
                return False, num_rows, f"{report_name}: INCOMPLETE"
        else:
            return False, 0, f"{report_name}: ERROR"
            

        
    def get_portfolio_query(self) -> str:
        """Get the main portfolio SQL query"""
        return f"""
            SELECT
            	TRY_CAST(B.BLDGID AS INT) AS "Bldg"
            ,	CASE WHEN B.INACTIVE <> 'Y' or B.INACTIVE IS NULL THEN 'Active' ELSE 'Inactive' END as "Active"
            ,	CONCAT(trim(B.ADDRESS1), (CASE WHEN B.ADDRESS2 IS NOT NULL THEN CONCAT('; ',TRIM(B.ADDRESS2)) ELSE '' END)) as "Address"
            ,	IFNULL(B.CITY,'') AS "City"
            ,	IFNULL(B.STATE,'') AS "State"
            ,	IFNULL(B.ZIPCODE,'') AS "Postal Code"
            ,	CAST(ROUND(SQFT."TOTAL_SQFT",0) AS INT) AS "Total Bldg Size"
            ,	POWNER.OWNERID AS "Owner"
            ,	TO_DATE(E.DISPOSED) AS "Disposition Date"
            ,	CASE WHEN E.DISPOSED IS NOT NULL THEN 'SOLD' ELSE IFNULL(PTYP.DESCRPN,'') END AS "Prop Type"
            -- ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE trim(B.MAP) END,'') AS "Hover for Google Map Link"
            ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE trim(B.MAP) END,'') AS "Google Map URL"
            ,	CAST(LOTSZ.NOTE_VALUE AS INTEGER) AS "Lot Size (sqft)"
            ,	ACREAGE.NOTE_VALUE AS "Acreage"
            ,	CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE trim(MNGR.MNGRNAME) END AS "Corporate PM"
            ,	CASE WHEN B.INACTIVE = 'Y' THEN NULL 
            		ELSE trim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS "PM_Email"
            FROM {self.csm_db}.MRI.BLDG B
            INNER JOIN {self.csm_db}.MRI.ENTITY E
            ON B.ENTITYID = E.ENTITYID
            INNER JOIN {self.csm_db}.MRI.PROJ P
            ON E.PROJID = P.PROJID
            LEFT JOIN {self.csm_db}.MRI.PTYP
            ON E.PROPTYPE = PTYP.PROPTYPE
            LEFT JOIN {self.csm_db}.MRI.LLRD 
            ON B.LLRDID = LLRD.LLRDID
            LEFT JOIN {self.csm_db}.MRI.MNGR
            ON B.MNGRID = MNGR.MNGRID
            LEFT JOIN (
                SELECT SANDL.BLDGID, SUM(CASE WHEN SANDL.SQFTTYPE <> 'BTS' THEN SANDL."LEASABLE SQFT" END) AS TOTAL_SQFT
                FROM (
                    SELECT S.BLDGID, S.SUITID, S.SUITETYPE_MRI, S.SUITSQFT AS "SUITE SQ FEET",
                           SSQF_TYPE.SQFTTYPE, IFNULL(SSQF_TYPE.SQFT,0) AS "LEASABLE SQFT"
                    FROM {self.csm_db}.MRI.SUIT S
                    JOIN {self.csm_db}.MRI.BLDG B ON B.BLDGID = S.BLDGID
                    LEFT JOIN {self.csm_db}.MRI.TB_CM_SUITETYPE ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
                    LEFT JOIN (
                        SELECT * FROM {self.csm_db}.MRI.SSQF
                        WHERE SSQF.EFFDATE = (
                            SELECT MAX(I.EFFDATE) FROM {self.csm_db}.MRI.SSQF I 
                            WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID 
                            AND TO_DATE(I.EFFDATE) <= CURRENT_DATE
                        )
                    ) SSQF_TYPE ON S.SUITID = SSQF_TYPE.SUITID AND S.BLDGID = SSQF_TYPE.BLDGID
                    WHERE (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
                ) SANDL
                WHERE SANDL.SQFTTYPE <> 'BTS'
                GROUP BY SANDL.BLDGID
            ) SQFT ON B.BLDGID = SQFT.BLDGID
            LEFT JOIN (
                SELECT ENTITYID, OWNERID, "Owned / Rented Property"
                FROM (
                    SELECT O.ENTITYID, O.OWNERID,
                           CASE WHEN UPPER(O.OWNERID) = 'RENTED' THEN 'Rented' ELSE 'Owned' END AS "Owned / Rented Property",
                           RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O."PERCENT" DESC, O.BEGPD, O.LASTDATE DESC) AS RANK
                    FROM {self.csm_db}.MRI.GOWN O
                    WHERE (O.BEGPD = (
                        SELECT MAX(I.BEGPD) FROM {self.csm_db}.MRI.GOWN I 
                        WHERE I.ENTITYID = O.ENTITYID AND I.BEGPD <= to_char(GETDATE(), 'yyyyMM')
                    ) OR (SELECT MAX(I.BEGPD) FROM {self.csm_db}.MRI.GOWN I WHERE I.ENTITYID = O.ENTITYID) IS NULL)
                    AND (O.ENDPD IS NULL OR O.ENDPD >= to_char(GETDATE(), 'yyyyMM'))
                ) RANKED WHERE RANK = 1
            ) POWNER ON E.ENTITYID = POWNER.ENTITYID
            LEFT JOIN {self.csm_db}.MRI.GNAM ON POWNER.OWNERID = GNAM.OWNERID
            LEFT JOIN (
                SELECT * FROM (
                    SELECT NOTB.BLDGID, TO_DATE(NOTB.NOTEDATE) AS NOTE_DATE, NOTB.NOTETEXT as FULL_NOTETEXT,
                           TRY_CAST(REPLACE(REGEXP_SUBSTR(NOTB.NOTETEXT, '[0-9,.-]+'),',','') AS NUMBER(38,4)) as NOTE_VALUE,
                           RANK() OVER (PARTITION BY NOTB.BLDGID ORDER BY NOTB.NOTEDATE DESC, NOTB.LASTDATE DESC) AS RANK
                    FROM {self.csm_db}.MRI.NOTB
                    JOIN {self.csm_db}.MRI.BLDG ON NOTB.BLDGID = BLDG.BLDGID
                    WHERE (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
                    AND (NOTB.REF1 = 'ACLOTSZ' OR NOTB.REF2 = 'ACLOTSZ')
                ) ILOTSZ WHERE ILOTSZ.RANK = 1
            ) LOTSZ ON B.BLDGID = LOTSZ.BLDGID
            LEFT JOIN (
                SELECT * FROM (
                    SELECT NOTB.BLDGID, TO_DATE(NOTB.NOTEDATE) AS NOTE_DATE, NOTB.NOTETEXT as FULL_NOTETEXT,
                           TRY_CAST(REPLACE(REGEXP_SUBSTR(NOTB.NOTETEXT, '[0-9,.-]+'),',','') AS NUMBER(38,4)) as "NOTE_VALUE",
                           RANK() OVER (PARTITION BY NOTB.BLDGID ORDER BY NOTB.NOTEDATE DESC, NOTB.LASTDATE DESC) AS RANK
                    FROM {self.csm_db}.MRI.NOTB 
                    JOIN {self.csm_db}.MRI.BLDG ON NOTB.BLDGID = BLDG.BLDGID
                    WHERE (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
                    AND (NOTB.REF1 = 'ACREAGE' OR NOTB.REF2 = 'ACREAGE')
                ) IACREAGE WHERE IACREAGE.RANK = 1
            ) ACREAGE ON B.BLDGID = ACREAGE.BLDGID
            WHERE TRY_CAST(B.BLDGID AS INT) IS NOT NULL
            ORDER BY B.BLDGID
        """
        
    def get_lcp_assignments_query(self) -> str:
        """Get LCP assignments query"""
        return f"""
        select  /* SNOWFLAKE version */
        a.BLDG AS "Bldg"
        , a.RM
        , case when rm.phone is NULL then NULL
            else '('||substr(rm.phone,1,3)||') '||substr(rm.phone,4,3)||'-'||substr(rm.phone,7,4) end as "RM Phone"
        , rm.email as "RM Email"
        , a.SUPPORT_LEASING AS "Support Leasing"
        , case when a.SUPPORT_LEASING = 'BRH' then '(*************' 
            when sl.phone is NULL then NULL
            else '('||substr(sl.phone,1,3)||') '||substr(sl.phone,4,3)||'-'||substr(sl.phone,7,4) end as "SL Phone"
        , case when a.SUPPORT_LEASING = 'BRH' then '<EMAIL>' else sl.email end as "SL Email"
        , a.SUPPORT_PROP_MGMT AS "Support Property Management"
        , case when rm.phone is NULL then NULL
            else '('||substr(spm.phone,1,3)||') '||substr(spm.phone,4,3)||'-'||substr(spm.phone,7,4) end as "SPM Phone"
        , spm.email as "SPM Email"
        from {self.csm_db}.CORPORATE.lcp_assignments_sean a
        left join {self.csm_db}.CORPORATE.ab_employees rm
        on upper(a.rm) = upper(rm.fname)||' '||upper(rm.lname)
        and rm.status not in ('T','R','D')
        left join {self.csm_db}.CORPORATE.ab_employees sl
        on upper(a.SUPPORT_LEASING) = upper(sl.fname)||' '||upper(sl.lname)
        and sl.status not in ('T','R','D')
        left join {self.csm_db}.CORPORATE.ab_employees spm
        on upper(a.SUPPORT_PROP_MGMT) = upper(spm.fname)||' '||upper(spm.lname)
        and spm.status not in ('T','R','D')
        """
        
    def get_pm_phone_query(self) -> str:
        """Get PM phone query"""
        return f"""
        select /* SNOWFLAKE version */
          email as "PM_Email"
        , '(847) 904-'||extension as "PM_Phone"
        from {self.csm_db}.CORPORATE.ab_employees
        where status not in ('R','T')
        and extension is not null
        and email is not null
        """
        
    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute a query and return results as DataFrame"""
        try:
            # print(f"query: {query}")
            # exit(1)
            cursor = self.sf_connection.cursor()
            cursor.execute(query)
            
            # Get column names
            columns = [desc[0] for desc in cursor.description]
            
            # Fetch all results
            results = cursor.fetchall()
            cursor.close()
            
            # Create DataFrame
            df = pd.DataFrame(results, columns=columns)
            
            # Clean string columns (remove trailing spaces)
            string_columns = df.select_dtypes(include=['object']).columns
            for col in string_columns:
                df[col] = df[col].astype(str).str.rstrip()
                # df[col] = df[col].astype(str).str.rstrip() if df[col].astype(str).str.rstrip() is not None else ''
            # df = df.fillna('')
            return df
            
        except Exception as e:
            # self.logger.error(f"Query execution failed: {str(e)}")
            self.sf.log_audit_in_db(log_msg=f"Query execution failed: query: {query}\nError: {str(e)}", process_type=self.report_name, script_file_name=__file__, log_type="Error")
            return pd.DataFrame()
            
    def _create_excel_hyperlink(self, url_value: Any) -> str: # , display_text: str = "View Map"
        """
        Convert a single URL value to Excel HYPERLINK formula.
        
        Args:
            url_value: The URL value (could be string, None, etc.)
            display_text: Text to display for the hyperlink
            
        Returns:
            Excel HYPERLINK formula string or empty string for invalid values
        """
        # Handle None, NaN, or empty values
        if pd.isna(url_value) or url_value is None:
            return ''
        
        # Convert to string and strip whitespace
        url_str = str(url_value).strip()
        
        # Check if empty or 'None' string
        if url_str == '' or url_str.lower() == 'none':
            return ''
        display_text: str = "View Google Map"
        # Return Excel hyperlink formula
        # return f'=HYPERLINK("{url_str}", "{display_text}")'
        # return f'=HYPERLINK("{url_str}", "{url_str}")'
        return f"=HYPERLINK('{url_str}','{display_text}')"
            
    def get_portfolio_data(self) -> Optional[pd.DataFrame]:
        """Process the main portfolio data"""
        try:
            # Get main portfolio data
            portfolio_df = self.execute_query(self.get_portfolio_query())
            status_main = self.check_df_rows(portfolio_df, 1, "Portfolio Main")
            
            # Get PM phone data
            pm_phone_df = self.execute_query(self.get_pm_phone_query())
            status_pm = self.check_df_rows(pm_phone_df, 1, "PM Phone")
            
            # Get LCP assignments data
            lcp_assignments_df = self.execute_query(self.get_lcp_assignments_query())
            status_lcp = self.check_df_rows(lcp_assignments_df, 1, "LCP Assignments")
            
            if not (status_main[0] and status_pm[0] and status_lcp[0]):
                # self.logger.error("One or more data queries failed validation")
                # print("One or more data queries failed validation")
                self.sf.log_audit_in_db(log_msg=f"process_portfolio_data(): One or more data queries failed validation", process_type=self.report_name, script_file_name=__file__, log_type="Error")
                return None
                
            # Merge data
            # Join PM phone data
            merged_df = portfolio_df.merge(pm_phone_df, on='PM_Email', how='left')
            
            # Reorder columns to put PM_Phone before PM_Email
            cols = merged_df.columns.tolist()
            if 'PM_Phone' in cols and 'PM_Email' in cols:
                pm_phone_idx = cols.index('PM_Phone')
                pm_email_idx = cols.index('PM_Email')
                if pm_phone_idx > pm_email_idx:
                    cols[pm_phone_idx], cols[pm_email_idx] = cols[pm_email_idx], cols[pm_phone_idx]
                    merged_df = merged_df[cols]
                
            # Replace underscores with spaces in column names
            merged_df.columns = [col.replace('_', ' ') for col in merged_df.columns]
            
            # Join LCP assignments
            merged_df = merged_df.merge(lcp_assignments_df, on='Bldg', how='left')
            
            # Add updated timestamp column
            date_header = f"Updated {datetime.now().strftime('%m-%d-%Y')}"
            # merged_df[date_header] = None
            merged_df[date_header] = None
            merged_df = merged_df.fillna('')

            # merged_df['Postal Code'] = merged_df['Postal Code'].fillna('')
            merged_df['Postal Code'] = "'" + merged_df['Postal Code'].fillna('').astype(str)            
            merged_df['Owner'] = merged_df['Owner'].fillna('')
            merged_df['Disposition Date'] = merged_df['Disposition Date'].fillna('')

             # Apply hyperlink formula. Generates error in pushing to excel.
            # merged_df['Hover for Google Map Link'] = merged_df['Hover for Google Map Link'].apply(
            #     self._create_excel_hyperlink
            # )            

            return merged_df
            
        except Exception as e:
            # self.logger.error(f"Error processing portfolio data: {str(e)}")
            # print(f"Error processing portfolio data: {str(e)}")
            self.sf.log_audit_in_db(log_msg=f"process_portfolio_data(): Error processing portfolio data: {str(e)}", process_type=self.report_name, script_file_name=__file__, log_type="Error")
            return None
            


def main():
    pass


if __name__ == "__main__":
    main() 