# @todos:
# d- replace URL in gdrv_main_url for email notifications

"""
Legacy Construction SNC Report - Python Version
Converted from R script originally written by <PERSON> August 2022
Python conversion maintains all original functionality
"""

import pandas as pd
import numpy as np
import os
import sys
import datetime
from datetime import <PERSON><PERSON><PERSON>

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.filters import AutoFilter

import time
import re
from pathlib import Path


import libs.email_client as email_client

# Configuration
class Config:
    def __init__(self, sf, excel_helper, excel_session, default_sleep_secs):
        self.sf = sf
        self.excel_helper = excel_helper
        self.excel_session = excel_session
        self.default_sleep_secs = default_sleep_secs
        # Version and settings
        self.version = "20241014"
        self.testing_emails = False  # Set to True for testing

        # self.base_dir_path = os.path.dirname(os.path.realpath(__file__))
        # self.data_dir_path = os.path.join(self.base_dir_path, ".." ,"data")
        self.data_dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]

        
        # print(f"data_dir_path: {self.data_dir_path}")
        # exit(1)

        # Report parameters
        self.report_name = "Legacy Construction SNC Report"
        self.script_folder = "LEGACY_Construction_SNC_Report"
        self.log_path = Path(self.data_dir_path) / self.script_folder
        self.report_folder = "reports"
        
        # Google settings
        self.gsht_auth_email = "<EMAIL>"
        # self.gdrv_main_url = 'https://docs.google.com/spreadsheets/d/1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8/edit#gid=784276350'

        # self.gdrv_main_url = 'https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7BA2DF1A25-0FAF-4C77-9D6F-D9B836FD7D7C%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO.xlsx&action=default&mobileredirect=true&isSPOFile=1&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDUxODAwMjE5IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9'
        self.gdrv_main_url = 'https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7BAD4769CE-F2F9-4E69-B5AD-71894E98F190%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO.xlsx&action=default&mobileredirect=true'
        self.gsht_expected_fns = ["Construction SNC Leases"]
        self.gsht_report_main_sn = "SNC"
        self.gsht_parameters_sn = "Parameters"
        self.gsht_update_rngname = "Last_MRI_Update"
        self.gsht_email_rngname = "Email_Sheets"
        
        # Email settings
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.norm_recip = ["<EMAIL>","<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>","<EMAIL>"]
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Snowflake settings
        self.sf_environ = "PROD"  # or "STAGE"
        # self.setup_snowflake_config()
        
        # Date settings
        self.setup_dates()
        
        # File settings
        self.setup_file_settings()
        
        
        self.report_path = self.log_path / self.report_folder

        self.sf.log_audit_in_db(log_msg=f"data_dir_path: {self.data_dir_path}\nreport_path: {self.report_path}", process_type=self.report_name, script_file_name=__file__, log_type="Info")
        
        
    def setup_dates(self):
        today = datetime.date.today()
        self.query_date = today.strftime("%d-%b-%y")
        self.query_days = 14
        
        # Calculate week start (Sunday)
        days_since_sunday = (today.weekday() + 1) % 7
        week_start = today - timedelta(days=days_since_sunday)
        
        self.query_startdate = (week_start - timedelta(days=self.query_days - 3)).strftime("%d-%b-%y")
        self.query_enddate = (week_start + timedelta(days=2)).strftime("%d-%b-%y")
        
        self.rpt_end = today.strftime("%Y%m%d")
        self.email_end = today.strftime("%m/%d/%Y")
        self.report_time_txt = datetime.datetime.now().strftime("%H%M%S %Z")
        
    def setup_file_settings(self):
        self.rpt_fn_no_ext = self.report_name
        self.rpt_fn = f"{self.rpt_fn_no_ext}.xlsx"
        self.rpt_fn_remove = f"{self.rpt_fn_no_ext} - removals.xlsx"



class ExcelManager:
    def __init__(self, config):
        self.config = config
        
    def create_excel_file(self, df, file_path, sheet_name="Sheet1", col_widths=None):
        """Create Excel file with formatting"""
        try:
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
                
            # Format header row
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_font = Font(bold=True, name="Arial Narrow", size=12)
            header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            
            for cell in ws[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                
            # Set column widths
            if col_widths:
                for col_name, width in col_widths.items():
                    if col_name in df.columns:
                        col_idx = df.columns.get_loc(col_name) + 1
                        ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
                        
            # Add autofilter
            ws.auto_filter = AutoFilter(ref=f"A1:{openpyxl.utils.get_column_letter(len(df.columns))}{len(df) + 1}")
            
            # Freeze first row
            ws.freeze_panes = 'A2'
            
            # Save file
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            wb.save(file_path)
            return True
        except Exception as e:
            # logging.error(f"Failed to create Excel file: {e}")
            self.config.sf.log_audit_in_db(log_msg=f"create_excel_file(): Failed to create Excel file: {e}", process_type=self.config.report_name, script_file_name=__file__, log_type="Error")

            return False

class DataProcessor:
    def __init__(self, config):
        self.config = config
        
    def check_dataframe_rows(self, df, min_rows, report_name=None):
        """Check if DataFrame has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_rows:
                return True, len(df), f"{report_name}: OKAY" if report_name else "OKAY"
            else:
                return False, len(df), f"INCOMPLETE DATA: {report_name}" if report_name else "INCOMPLETE DATA"
        else:
            return False, 0, f"LOAD ERROR: {report_name}" if report_name else "LOAD ERROR"
            
    def compare_dataframes(self, df_full, df_existing):
        """Compare two DataFrames and return additions and removals"""
        # Find missing lease IDs (additions)
        if 'Lease ID' in df_full.columns and 'Lease ID' in df_existing.columns:
            # missing_ids = set(df_full['Lease ID']) - set(df_existing['Lease ID'])
            # remove_ids = set(df_existing['Lease ID']) - set(df_full['Lease ID'])
            # remove empty strings from existing df
            existing_non_empty = df_existing[df_existing['Lease ID'] != '']['Lease ID']
            missing_ids = set(df_full['Lease ID']) - set(existing_non_empty)
            remove_ids = set(existing_non_empty) - set(df_full['Lease ID'])

            # print(f"missing_ids: \n{missing_ids}")
            # print(f"remove_ids: \n{remove_ids}\nremove_ids len: {len(remove_ids)}")
            self.config.sf.log_audit_in_db(log_msg=f"compare_dataframes(): missing_ids: {missing_ids}, remove_ids: {remove_ids}", process_type=self.config.report_name, script_file_name=__file__, log_type="Info")
            # exit(1)
            
            additions = df_full[df_full['Lease ID'].isin(missing_ids)] if missing_ids else pd.DataFrame()
            removals = df_existing[df_existing['Lease ID'].isin(remove_ids)] if remove_ids else pd.DataFrame()

            additions = additions.fillna('')
            removals = removals.fillna('')
            
            return additions, removals
        else:
            return pd.DataFrame(), pd.DataFrame()
            
    def reshape_dataframe(self, df_source, df_target):
        """Reshape source DataFrame to match target DataFrame structure"""
        # Get column names
        source_cols = df_source.columns.tolist()
        target_cols = df_target.columns.tolist()
        
        # Find matching columns
        matching_cols = [col for col in source_cols if col in target_cols]
        
        # Create reshaped DataFrame
        df_reshaped = df_source[matching_cols].copy()
        
        # Add missing columns with NA values
        missing_cols = [col for col in target_cols if col not in matching_cols]
        for col in missing_cols:
            df_reshaped[col] = np.nan
            
        # Reorder columns to match target
        df_reshaped = df_reshaped[target_cols]
        
        return df_reshaped

class SNCSheetHelper:
    def __init__(self, sf, excel_helper, excel_session, default_sleep_secs):
        self.sf = sf
        self.excel_helper = excel_helper
        self.excel_session = excel_session
        self.default_sleep_secs = default_sleep_secs
        self.config = Config(sf=self.sf, excel_helper=self.excel_helper, excel_session=self.excel_session, default_sleep_secs=self.default_sleep_secs)

        self.excel_manager = ExcelManager(self.config)
        self.data_processor = DataProcessor(self.config)
        self.okay_to_continue = True
        

        # Column widths for Excel files
        self.xlsx_col_widths = {
            "Bldg ID": 8.5,
            "Lease ID": 9.5,
            "Occp ID": 9.5,
            "City": 20,
            "State": 8.5,
            "Suite ID": 9.5,
            "Suite SQFT": 9.5,
            "Suite Details SQFT": 9.5,
            "Occupant Name": 45,
            "Execution": 10,
            "Beginning": 10,
            "Rent Start": 10,
            "Expiration": 10,
            "Code": 8.5,
            "Contingent": 10,
            "Contingency Date": 12,
            "Lease Agent": 20,
            "Asset Manager": 20,
            "Contractor": 9.75,
            "Contractor Info": 9.75,
            "Notes": 9,
            "Date Last Actioned": 11
        }
        
        # Email body columns
        self.email_body_cols = ["Bldg ID", "Lease ID", "City", "State", "Occupant Name", "Execution"]
        
    
    def get_snc_leases_from_database(self):
        """Get master list of SNC leases from Snowflake database"""
        query = """
      SELECT /* SNOWFLAKE version, LEGACY_Construction_SNC_Report */
      	LEAS.BLDGID AS "Bldg ID"
      ,	LEAS.LEASID AS "Lease ID"
      ,	LEAS.MOCCPID AS "Occp ID"
      ,	BLDG.CITY AS "City"
      ,	BLDG.STATE AS "State"
      ,	LEAS.SUITID AS "Suite ID"
      ,	SUIT.SUITSQFT AS "Suite SQFT"
      ,	SQF.SQFT AS "Suite Details SQFT"
      ,	LEAS.OCCPNAME AS "Occupant Name"
      ,	CAST(LEAS.EXECDATE AS DATE) AS "Execution"
      ,	CAST(LEAS.BEGINDATE AS DATE) AS "Beginning"
      ,	CAST(LEAS.RENTSTRT AS DATE) AS "Rent Start"
      ,	CAST(LEAS.EXPIR AS DATE) AS "Expiration"
      ,	LEAS.GENCODE AS "Code"
      ,	CASE WHEN UPPER(LEAS.CONTINGENT) != 'Y' OR LEAS.CONTINGENTDT < CURRENT_DATE OR LEAS.CONTINGENTDT IS NULL THEN NULL ELSE 'Y' END AS "Contingent"
      ,	CAST(LEAS.CONTINGENTDT AS DATE) AS "Contingency Date"
      ,	LEASEAG.LEASEAGENT AS "Lease Agent"
      --,	MNGR.MNGRNAME AS "Manager Name"
      ,	ASSETMGR.NAME AS "Asset Manager"
    
      FROM MRI.LEAS
      JOIN MRI.BLDG ON LEAS.BLDGID = BLDG.BLDGID
      JOIN MRI.SUIT ON LEAS.BLDGID = SUIT.BLDGID AND LEAS.SUITID = SUIT.SUITID
      LEFT JOIN 
      (
      		SELECT *
      		FROM MRI.SSQF
      		WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE)
      								FROM MRI.SSQF I
      								WHERE I.BLDGID = SSQF.BLDGID
      								AND I.SUITID = SSQF.SUITID
      								AND I.EFFDATE <= CURRENT_DATE
      								)
      ) SQF
      ON SUIT.BLDGID = SQF.BLDGID
      	AND SUIT.SUITID = SQF.SUITID
      	AND UPPER(SQF.SQFTTYPE) = 'GLA'
      LEFT JOIN
      (
      	SELECT
      		NOTE.BLDGID
      	,	NOTE.LEASID
      	,	LISTAGG(CONCAT(NOTE.NOTETEXT, ' (', TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yyyy'), ')'), '; ') WITHIN GROUP (ORDER BY NOTE.NOTEDATE DESC) AS LEASEAGENT
      	FROM MRI.NOTE
      	WHERE (NOTE.REF1 = 'LEASEAG' OR NOTE.REF2 = 'LEASEAG')
      	GROUP BY NOTE.BLDGID
      	, NOTE.LEASID
      ) LEASEAG
      ON LEAS.BLDGID = LEASEAG.BLDGID
      	AND LEAS.LEASID = LEASEAG.LEASID
      LEFT JOIN MRI.MNGR
      ON BLDG.MNGRID = MNGR.MNGRID
      LEFT JOIN MRI.ENTITY
      ON BLDG.ENTITYID = ENTITY.ENTITYID
      LEFT JOIN MRI.PROJ
      ON ENTITY.PROJID = PROJ.PROJID
      LEFT JOIN MRI.ASSETMGR
      ON PROJ.ASSETMGR = ASSETMGR.ASSETMGR
      WHERE 
      	UPPER(LEAS.GENCODE) = 'SNC'
      	AND UPPER(LEAS.OCCPSTAT) != 'I'
      ORDER BY LEAS.EXECDATE
        """
        
        df = self.sf.execute_query(query, return_df=True)
        
        if df is None or len(df) == 0:
            self.send_error_email("Database query returned no results")
            return None
            
        # Clean up string columns
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].astype(str).str.strip()
            
        return df
        


    def safe_date_conversion(self, df, columns, target_format='%m/%d/%y'):
        """
        Safely convert date columns with error handling
        """
        conversion_log = {}
        
        for col in columns:
            if col not in df.columns:
                conversion_log[col] = "Column not found"
                continue
                
            try:
                # Count successful conversions before
                original_count = len(df[col].dropna())
                
                # Convert to datetime
                df[col] = pd.to_datetime(df[col], errors='coerce')
                
                # Count successful conversions after
                converted_count = df[col].notna().sum()
                
                # Format dates
                df[col] = df[col].dt.strftime(target_format)
                df[col] = df[col].fillna('')
                
                conversion_log[col] = f"Converted {converted_count}/{original_count} values"
                
            except Exception as e:
                conversion_log[col] = f"Error: {str(e)}"
        
        # return df, conversion_log
        return df

    def auto_clean_prefixed_columns(self,df, columns_to_clean, prefix="'"):
        """
        Automatically detect and clean columns with quote prefixes
        """
        cleaned_columns = []
        
        for col in columns_to_clean:
            # Check if column has values that start with single quote
            if df[col].dtype == 'object' or df[col].dtype == 'string':
                str_series = df[col].astype(str)
                if str_series.str.startswith(prefix).any():
                    df[col] = str_series.str.lstrip(prefix)
                    cleaned_columns.append(col)
                    # print(f"Cleaned prefix from column: {col}")
        
        # return df, cleaned_columns
        return df

        
    def process_data_changes(self, df_full, df_existing):
        """Process additions and removals between database and existing data"""
        email_files = []
        email_body_parts = []

        # self.sf.LOG_TO_DB = False # @todo: remove or comment after testing
        cols_to_convert_to_text = ['Bldg ID', 'Lease ID','Execution', 'Beginning', 'Rent Start', 'Expiration', 'Contingency Date', 'Occp ID', 'Suite ID']
        # df_existing = self.auto_clean_prefixed_columns(df_existing, cols_to_convert_to_text)
        # mod existing df, check change(add and remove), update entire sheet at the end 
        existing_original_df = df_existing.copy()
        
        # print(f"0) existing_original_df: \n{existing_original_df}")
        # existing_original_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/existing_original_df_0.csv', index=False)
        
        # Compare data
        additions, removals = self.data_processor.compare_dataframes(df_full, df_existing)

        has_changes = False
        
        # Process additions
        if len(additions) > 0:
            # Reshape additions to match existing structure
            if len(df_existing) > 0:
                additions = self.data_processor.reshape_dataframe(additions, df_existing)

            self.sf.log_audit_in_db(log_msg=f"found {len(additions)} additions: ", process_type=self.config.report_name, script_file_name=__file__, log_type="Info")

            # append additions to existing_original_df
            existing_original_df = pd.concat([existing_original_df, additions], ignore_index=True)
            has_changes = True
            # print(f"1) existing_original_df: \n{existing_original_df}")
            # existing_original_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/existing_original_df_1.csv', index=False)
            # exit(1)
            # Append to  Sheets
            # self.sheets_manager.append_data(additions, self.config.gsht_report_main_sn)
            # clean_values = self.excel_helper.clean_values_from_none_to_empty_string(additions.values.tolist())
            # populated = self.excel_helper.append_data_to_worksheet(self.excel_session, self.config.gsht_report_main_sn,  clean_values)
            # self.sf.log_audit_in_db(log_msg=f"done populating sheet {self.config.gsht_report_main_sn}: {populated}", process_type=self.config.report_name, script_file_name=__file__, log_type="Info")
            
            # Create Excel file for additions
            file_path = self.config.report_path / self.config.rpt_fn
            self.excel_manager.create_excel_file(
                additions, 
                file_path, 
                self.config.query_date,
                self.xlsx_col_widths
            )
            email_files.append(str(file_path))
            
            # Create email body for additions
            email_body_parts.append(self.create_additions_email_body(additions))
            
        # Process removals
        if len(removals) > 0:
            self.sf.log_audit_in_db(log_msg=f"found {len(removals)} removals: ", process_type=self.config.report_name, script_file_name=__file__, log_type="Info")
            
            # Update Google Sheets to mark removals
            # self.mark_removals_in_sheets(removals)

            # remove removals from existing_original_df
            existing_original_df = existing_original_df[~existing_original_df['Lease ID'].isin(removals['Lease ID'])]
            existing_original_df = existing_original_df.fillna('')
            has_changes = True
            # print(f"2) existing_original_df: \n{existing_original_df}")
            # existing_original_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/existing_original_df_2.csv', index=False)
            # exit(1)   
            
            # Create Excel file for removals
            file_path = self.config.report_path / self.config.rpt_fn_remove
            self.excel_manager.create_excel_file(
                removals, 
                file_path, 
                self.config.query_date,
                self.xlsx_col_widths
            )
            email_files.append(str(file_path))
            
            # Create email body for removals
            email_body_parts.append(self.create_removals_email_body(removals))

        self.sf.log_audit_in_db(log_msg=f"process_data_changes(): has_changes: {has_changes}", process_type=self.config.report_name, script_file_name=__file__, log_type="Info")

        # print(f"0) email_body_parts: \n{email_body_parts}")
        # exit(1)
        
        # update entire sheet since we have changes
        if has_changes:
            # update entire sheet at the end 
            # reformat dates
            existing_original_df = self.safe_date_conversion(existing_original_df, ['Execution', 'Beginning', 'Rent Start', 'Expiration', 'Contingency Date'])

            # existing_original_df[cols_to_convert] = existing_original_df[cols_to_convert].astype(str)

            all_text_columns = cols_to_convert_to_text
            # prefix with ' for all text columns, so excel wont drop leading zeros
            for col in all_text_columns:
                existing_original_df[col] = "'" + existing_original_df[col].fillna('').astype(str)            


            # existing_original_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/existing_original_df_3.csv', index=False)
            self.excel_helper.clear_excel_sheet_content(excel_session=self.excel_session, worksheet_name=self.config.gsht_report_main_sn, sleep_time_secs=self.default_sleep_secs)
            clean_values = self.excel_helper.clean_values_from_none_to_empty_string(existing_original_df.values.tolist())
            # print(f"clean_values: \n{clean_values}")
            populated = self.excel_helper.append_data_to_worksheet(self.excel_session, self.config.gsht_report_main_sn,  clean_values, reset_from_first_row=True, forced_start_row=2)
            self.sf.log_audit_in_db(log_msg=f"done populating sheet {self.config.gsht_report_main_sn}: {populated}", process_type=self.config.report_name, script_file_name=__file__, log_type="Info")
            
        return email_files, email_body_parts
        
    # def mark_removals_in_sheets(self, removals):
    #     """Mark removals in Google Sheets by updating Code column"""
    #     # This would require more complex Google Sheets API calls
    #     # For now, we'll just log the removals
    #     # logging.info(f"Marking {len(removals)} leases for removal")
    #     print(f"@TODO: IMPLEMENT - Marking {len(removals)} leases for removal")
        
    def create_additions_email_body(self, additions):
        """Create email body section for additions"""
        email_data = additions[self.email_body_cols].copy()
        
        # Format dates
        for col in email_data.columns:
            if email_data[col].dtype == 'datetime64[ns]':
                email_data[col] = email_data[col].dt.strftime('%m/%d/%y')
                
        body = f"""
        <h3>*ADDITIONS*</h3>
        <p>New rows have been <B>ADDED</B> to the 
        <a href="{self.config.gdrv_main_url}">{self.config.gsht_report_main_sn}</a>
         sheet based on yesterday's MRI data. 
        <em>If any filters are applied to the sheet they may cause additions to be hidden from view.</em>
        </p>
        """
        
        if len(email_data) <= 30:
            body += f"<p>{email_data.to_html(index=False, table_id='additions-table', border=2)}</p>"
        else:
            body += f"<p><strong><em>There are {len(email_data)} new additions, see attached file for all.</em></strong></p>"
            
        return body
        
    def create_removals_email_body(self, removals):
        """Create email body section for removals"""
        removal_cols = [col for col in self.email_body_cols if col != "Execution"]
        email_data = removals[removal_cols].copy()
        
        # Format dates
        for col in email_data.columns:
            if email_data[col].dtype == 'datetime64[ns]':
                email_data[col] = email_data[col].dt.strftime('%m/%d/%y')
                
        body = f"""
        <h3>*REMOVALS*</h3>
        <p>Rows have been <B>REMOVED</B> from the 
        <a href="{self.config.gdrv_main_url}">{self.config.gsht_report_main_sn}</a>
         sheet. <em>Review as it is possible there may be continuing 
        construction even though these are no longer flagged as SNC!</em>
        </p>
        """
        
        if len(email_data) <= 30:
            body += f"<p>{email_data.to_html(index=False, table_id='removals-table', border=2)}</p>"
        else:
            body += f"<p><strong><em>There are {len(email_data)} rows being REMOVED, see attached file for all.</em></strong></p>"
            
        return body
        
    def send_notification_email(self, email_files, email_body_parts):
        """Send notification email with attachments"""
        subject = self.config.report_name
        
        body = f"<h2>REPORT: {self.config.report_name}</h2>"
        body += "".join(email_body_parts)
        body += f"""
        <p><em>This notification is ONLY sent to members of the 
        {', '.join(self.config.norm_recip)} email group.</em></p>
        {self.get_signature()}
        """
        
        email_client.send_email(
            recipient=self.config.norm_recip,
            subject=subject,
            body=body,
            attachments=email_files,
            test=self.config.testing_emails
        )
        
    def send_error_email(self, error_message):
        """Send error notification email"""
        subject = f"{self.config.report_name} Issue: {error_message}"
        body = f"""
        <p>This is an automated email to inform you that there was an error 
        during the {self.config.report_name} routine!</p>
        <p>Error: {error_message}</p>
        <p>The routine has been aborted.</p>
        {self.get_signature()}
        """
        
        email_client.send_email(
            recipient=self.config.warn_recip,
            subject=subject,
            body=body,
            test=self.config.testing_emails
        )
        
    def get_signature(self):
        """Get email signature"""
        return """
        <h3>Legacy Commercial Property</h3>
        <b>A Division of Highland Ventures, Ltd.</b><br/>
        209 Powell Pl.<br/>
        Brentwood, TN 37027<br/><br/>
        For issues with this report contact Steve Olson: Ph 847/904-9043<br/>
        """
        
    # def run(self):
    #     """Main execution method"""
    #     try:
    #         # Initialize connections
    #         # if not self.initialize_connections():
    #         #     return False
                
    #         # Get data from database
    #         df_full = self.get_snc_leases_from_database()
    #         if df_full is None:
    #             return False
                
    #         # Get existing data from Google Sheets
    #         df_existing = self.get_existing_data_from_sheets()
            
    #         # If no existing data, write all data to sheets
    #         if len(df_existing) == 0:
    #             self.sheets_manager.write_data(df_full, self.config.gsht_report_main_sn)
    #             # logging.info(f"Initialized Google Sheet with {len(df_full)} rows")
    #             return True
                
    #         # Process changes
    #         email_files, email_body_parts = self.process_data_changes(df_full, df_existing)
            
    #         # Send notification email if there are changes
    #         if email_files or email_body_parts:
    #             self.send_notification_email(email_files, email_body_parts)
    #             # logging.info("Notification email sent")
    #         else:
    #             # logging.info("No changes detected")
    #             pass
                
    #         return True
            
    #     except Exception as e:
    #         # logging.error(f"Error in main execution: {e}")
    #         self.send_error_email(f"Unexpected error: {str(e)}")
    #         return False
            
    #     # finally:
    #     #     # Clean up connections
    #     #     self.db_manager.close_connection()

def main():
    """Main function"""
    report = SNCSheetHelper()
    success = report.run()
    
    if success:
        print("Report completed successfully")
        return 0
    else:
        print("Report failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 