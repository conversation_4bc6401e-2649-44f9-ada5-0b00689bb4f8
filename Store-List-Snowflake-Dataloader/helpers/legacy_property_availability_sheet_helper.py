# @todo
# d - fix adding development rows to existing availability sheetx
# d -- The problem is with the source "Development Availability" sheet. It has repeated columns after AD
# d - check why New 'Availability' Spaces email is sent every time
"""
Available Suites Update Script - Python Version
Converted from R script: LEGACY_Available_Suites_update-SF.R
Written by <PERSON> March 2022, converted to Python December 2024

This script:
1. Connects to Snowflake database
2. Queries MRI data for available suites
3. Updates Google Sheets with availability data
4. Sends email notifications for changes
"""

import os

import pandas as pd
import numpy as np

from datetime import datetime, date, timedelta
import re
import time
from typing import Dict, List, Optional, Tuple, Any
import libs.email_client as email_client

import warnings
warnings.filterwarnings('ignore')


class AvailabilitySheetHelper:
    def __init__(self, sf, excel_helper, excel_session):
        """Initialize the Available Suites Updater"""

        self.sf = sf
        self.excel_helper = excel_helper
        self.excel_session = excel_session
        # Configuration flags
        self.testing_emails = False  # Set to True for testing
        self.testing_pc = False # self._is_testing_pc()
        self.okay_to_continue = True
        
        # Report configuration
        self.my_table_name = "STEVE.NA"
        self.my_report_name = "LCP_Available_Suites_update"
        self.my_sheets = ["Available"]
        self.my_sheets_2 = ["Availability"]
        self.my_mri_sheet = "Available"
        self.my_mri_sheet_2 = "Availability"
        self.my_prior_sheet = "Avail-Prior Tenant"
        
        # Google Sheets IDs
        self.gsht_key = "1GL-PMYybfT9iLYNLxyUP6zRWs8Cbl58o1rbkmYMacRM"
        self.gsht_key_2 = "***************************-soaHdfzRxz2mZCQ8"
        
        # Email configuration
        self.warn_recip = ["<EMAIL>"]
        self.norm_recip = ["<EMAIL>", "<EMAIL>", 
                          "<EMAIL>","<EMAIL>"]
        self.removals_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>"]
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Snowflake configuration
        self.sf_environ = "PROD"  # or "STAGE"
        # self._setup_snowflake_config()
        
        # Initialize connections
        self.sf_connection = self.sf.conn
        self.gc = None  # Google Sheets client
        
    
    def check_df_rows(self, df: pd.DataFrame, min_rows: int, report_name: str = None) -> Tuple[bool, int, str]:
        """Check if dataframe has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_rows:
                return True, len(df), f"{report_name}: OKAY"
            else:
                return False, len(df), f"{report_name}: INCOMPLETE"
        else:
            return False, 0, f"{report_name}: ERROR"
    
    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute SQL query and return pandas DataFrame"""
        try:
            cursor = self.sf_connection.cursor()
            cursor.execute(query)
            
            # Fetch results
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            # Create DataFrame
            df = pd.DataFrame(results, columns=columns)
            cursor.close()
            
            return df
            
        except Exception as e:
            # logger.error(f"Query execution failed: {e}")
            self.sf.log_audit_in_db(log_msg=f"execute_query() - Query execution failed: {e}", process_type=self.my_report_name, script_file_name=__file__, log_type="error")
            return pd.DataFrame()
    
    def get_last_change_data(self) -> pd.DataFrame:
        """Get last change data from MRI"""
        query = """
        SELECT /* SNOWFLAKE version */ 
        concat('Last change in MRI was made: ',to_char(MAX(A.LASTDATE), 'MON DD YYYY HH12:MIAM')) as LUPD
        from
        (
            select max(bldg.LASTDATE) AS LASTDATE from MRI.BLDG
            UNION ALL
            select max(suit.LASTDATE) AS LASTDATE FROM MRI.SUIT
            UNION ALL
            select MAX(leas.LASTDATE) AS LASTDATE FROM MRI.LEAS
        ) A
        """
        
        # Get last update info
        update_info = self.execute_query(query)

        return update_info
        
    def get_availability_data(self) -> pd.DataFrame:
        """Get main availability data from MRI"""
        # query = """
        # SELECT /* SNOWFLAKE version */ 
        # concat('Last change in MRI was made: ',to_char(MAX(A.LASTDATE), 'MON DD YYYY HH12:MIAM')) as LUPD
        # from
        # (
        #     select max(bldg.LASTDATE) AS LASTDATE from MRI.BLDG
        #     UNION ALL
        #     select max(suit.LASTDATE) AS LASTDATE FROM MRI.SUIT
        #     UNION ALL
        #     select MAX(leas.LASTDATE) AS LASTDATE FROM MRI.LEAS
        # ) A
        # """
        
        # # Get last update info
        # update_info = self.execute_query(query)
        
        # Main availability query
        main_query = """
        SELECT /* SNOWFLAKE version */
            BLDGID
          ,	BLDG AS "Bldg"
          , HRG as "HRG Interested"
          ,	BLDGADDRESS AS "Primary Address"
          ,	SUITADDRESS AS "Suite Address"
          ,	CITY AS "City"
          ,	STATE AS "State"
          ,	ZIPCODE AS "Zip Code"
          ,	SUITID AS "Suite ID"
          ,	SQFT AS "SQ FT"
          , BLDG_GLA as "BLDG GLA"
          ,	AVAILTYPE AS "Avail Type"
          ,	MAPURL AS "Google Map URL"
      	  ,	CORPPM AS "Corporate PM"
      	  ,	CORPPMEMAIL AS "PM_Email"
      
        from
        (
          select
            B.BLDGID
          ,	CASE WHEN TRY_CAST(B.BLDGID AS INT) IS NULL THEN B.BLDGID ELSE TO_CHAR(TRY_CAST(B.BLDGID AS INT)) END AS BLDG
          , NULL as HRG
          ,	CONCAT(RTRIM(B.ADDRESS1), (CASE WHEN B.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(B.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
          ,	RTRIM(S.ADDRESS) AS SUITADDRESS
          ,	B.CITY
          ,	B.STATE
          ,	B.ZIPCODE
          ,	S.SUITID
          ,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
          , BLDG_SQFT.BLDG_GLA
          , 'Vacancy' AS AVAILTYPE
          ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE rtrim(B.MAP) END,'') AS MAPURL
      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) 
					  ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
          FROM MRI.SUIT S
          JOIN MRI.BLDG B
          ON B.BLDGID = S.BLDGID
		  left join MRI.MNGR
          on b.MNGRID = mngr.MNGRID
          LEFT JOIN MRI.TB_CM_SUITETYPE
          ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
          LEFT JOIN 
          (
          SELECT *
          FROM MRI.SSQF
          WHERE SSQF.EFFDATE = (
              SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
              )
          ) SSQF_TYPE
          ON S.SUITID = SSQF_TYPE.SUITID
          AND S.BLDGID = SSQF_TYPE.BLDGID
        	left join
        	(
            SELECT SSQF.BLDGID
        	,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
            FROM MRI.SSQF
        	join MRI.SUIT
        		ON SSQF.SUITID = SUIT.SUITID
        		AND SSQF.BLDGID = SUIT.BLDGID
        	LEFT JOIN MRI.TB_CM_SUITETYPE								
            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            WHERE SSQF.EFFDATE = (
                  	SELECT MAX(I.EFFDATE) 
        			FROM MRI.SSQF I 
        			WHERE I.BLDGID = SSQF.BLDGID 
        				AND I.SUITID = SSQF.SUITID 
        				AND I.EFFDATE <= CURRENT_DATE
                )
        		AND UPPER(SSQF.SQFTTYPE) = 'GLA'
        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
        	GROUP BY SSQF.BLDGID
            ) BLDG_SQFT
            ON S.BLDGID = BLDG_SQFT.BLDGID
        
            LEFT JOIN
            (
            SELECT 								
                L.BLDGID,							
                RTRIM(P.PORTID) AS Portfolio,
                L.SUITID,							
                L.LEASID,							
                L.OCCPNAME,							
                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
                L.OCCPSTAT,
                CL.CODEDESC AS OCCP_STATUS,							
                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
                TO_DATE(L.VACATE) as VACATEDATE,							
                TO_DATE(L.EXPIR) as EXPIRDATE,																											
                S.SUITETYPE_MRI,							
                TB_CM_SUITETYPE.SUITETYPEUSAGE,							
                TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
                L.GENCODE,
                L.CONTINGENT,
                L.CONTINGENTDT,
                case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= Cast(GetDate() AS date) THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY														
            FROM MRI.LEAS L								
            INNER JOIN MRI.BLDG B								
            ON L.BLDGID = B.BLDGID								
            INNER JOIN MRI.SUIT S								
            ON L.SUITID = S.SUITID								
                AND B.BLDGID = S.BLDGID								
            LEFT JOIN (								
                SELECT * 							
                FROM MRI.SSQF 							
                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
            ) SQF								
            ON S.BLDGID = SQF.BLDGID								
                AND S.SUITID = SQF.SUITID							
            LEFT JOIN MRI.TB_CM_SUITETYPE								
            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
            LEFT JOIN MRI.ENTITY E								
            ON B.ENTITYID = E.ENTITYID								
            LEFT JOIN MRI.PROJ P								
            ON E.PROJID = P.PROJID																
            LEFT JOIN MRI.CODELIST CL								
            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
            WHERE 		
                L.OCCPSTAT NOT IN ('P', 'I') 							
                AND (		/* Next section commented out to allow future tenants to appear in results (also comment out CMRECC.INEFFECT = 'Y' line above)  */
                  		(					
                  			(
                  				L.RENTSTRT <= GETDATE() 
                  				OR L.OCCUPNCY <= GETDATE()
                  				OR L.EXECDATE <= GETDATE()
                  			)
                  			AND (				
                  					L.STOPBILLDATE IS NULL 		
                  					OR L.STOPBILLDATE >= GETDATE() 		
                  					OR (		
                  							L.STOPBILLDATE < GETDATE() 
                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
                  						)	
                  			)			
                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE() 				
                  		)					
                  		OR 					
                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
                  				
                  	)
            )LEASED
            ON B.BLDGID = LEASED.BLDGID
            AND S.SUITID = LEASED.SUITID
            LEFT JOIN
            (
      				SELECT DISTINCT
      				NOTB.BLDGID
      				, NOTB.NOTEDATE
      				FROM MRI.NOTB
      				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
      				AND NOTB.NOTEDATE = (
      					SELECT MIN(I.NOTEDATE) 
      					FROM MRI.NOTB I 
      					WHERE I.BLDGID = NOTB.BLDGID  
      					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
      				)
      				AND NOTB.NOTEDATE <= CURRENT_DATE
            )EXCBLDGNOTE
            ON B.BLDGID = EXCBLDGNOTE.BLDGID
            WHERE (B.INACTIVE <> 'Y' or B.INACTIVE IS NULL)
            AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
            AND B.BLDGID IS NOT NULL
            AND LEASED.OCCP_STATUS IS NULL
            AND TRY_CAST(B.BLDGID AS INT) IS NOT NULL
            AND (CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 OR
                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND S.SUITETYPE_MRI = 'BTS')
        	)
        	AND EXCBLDGNOTE.BLDGID IS NULL /* EXCLUDES BUILDING when 'EXCLUDE' building note in effect */
        
        
        	union all
        
        
        	SELECT	/* BYEBYE note additions */
        	  NOTE.BLDGID
        	,	CASE WHEN TRY_CAST(NOTE.BLDGID AS INT) IS NULL THEN NOTE.BLDGID ELSE TO_CHAR(TRY_CAST(NOTE.BLDGID AS INT)) END AS BLDG
        	,	NULL as HRG
        	,	CONCAT(RTRIM(BLDG.ADDRESS1), (CASE WHEN BLDG.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(BLDG.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
        	,	rtrim(SUIT.ADDRESS) AS SUITADDRESS
            ,	BLDG.CITY
            ,	BLDG.STATE
            ,	BLDG.ZIPCODE
            ,	SUIT.SUITID
        	,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
        	,	BLDG_SQFT.BLDG_GLA
        	,	CONCAT('BYEBYE', ' (', RTRIM(NOTE.NOTETEXT), ') Avail ',TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yy')) AS AVAILTYPE
        	,	ifnull(CASE WHEN BLDG.INACTIVE = 'Y' THEN NULL ELSE rtrim(BLDG.MAP) END,'') AS MAPURL
        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  					ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
        	FROM MRI.NOTE			
        	LEFT JOIN MRI.BLDG			
        	ON NOTE.BLDGID = BLDG.BLDGID
        	left join MRI.MNGR
        	on bldg.MNGRID = mngr.MNGRID
        	LEFT JOIN MRI.LEAS
        	ON NOTE.LEASID = LEAS.LEASID
        		AND NOTE.BLDGID = LEAS.BLDGID
        	LEFT JOIN MRI.CODELIST CL								
        	ON LEAS.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
        	LEFT JOIN MRI.SUIT
        	ON LEAS.SUITID = SUIT.SUITID
        		AND LEAS.BLDGID = SUIT.BLDGID
        	LEFT JOIN 
            (
        		SELECT *
        		FROM MRI.SSQF
        		WHERE SSQF.EFFDATE = (
        			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE 
        			)
            ) SSQF_TYPE
            ON LEAS.SUITID = SSQF_TYPE.SUITID
        		AND LEAS.BLDGID = SSQF_TYPE.BLDGID
        	left join
        	(
        		SELECT SSQF.BLDGID
        		,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
        		FROM MRI.SSQF
        		join MRI.SUIT
        			ON SSQF.SUITID = SUIT.SUITID
        			AND SSQF.BLDGID = SUIT.BLDGID
        		LEFT JOIN MRI.TB_CM_SUITETYPE								
        		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
        		WHERE SSQF.EFFDATE = (
                  		SELECT MAX(I.EFFDATE) 
        				FROM MRI.SSQF I 
        				WHERE I.BLDGID = SSQF.BLDGID 
        					AND I.SUITID = SSQF.SUITID 
        					AND I.EFFDATE <= CURRENT_DATE 
        			)
        			AND UPPER(SSQF.SQFTTYPE) = 'GLA'
        			AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
        		GROUP BY SSQF.BLDGID
            ) BLDG_SQFT
            ON LEAS.BLDGID = BLDG_SQFT.BLDGID
        	LEFT JOIN MRI.TB_CM_SUITETYPE
            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
        
        	LEFT JOIN
            (
            SELECT 								
                L.BLDGID,							
                RTRIM(P.PORTID) AS Portfolio,
                L.SUITID,						
                L.LEASID,							
                L.OCCPNAME,							
                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
                L.OCCPSTAT,
                CL.CODEDESC AS OCCP_STATUS,							
                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
                TO_DATE(L.VACATE) as VACATEDATE,							
                TO_DATE(L.EXPIR) as EXPIRDATE																																						
            FROM MRI.LEAS L							
            INNER JOIN MRI.BLDG B								
            ON L.BLDGID = B.BLDGID								
            INNER JOIN MRI.SUIT S								
            ON L.SUITID = S.SUITID								
                AND L.BLDGID = S.BLDGID								
            LEFT JOIN (								
                SELECT * 							
                FROM MRI.SSQF 							
                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
            ) SQF								
            ON S.BLDGID = SQF.BLDGID								
                AND S.SUITID = SQF.SUITID							
            LEFT JOIN MRI.TB_CM_SUITETYPE								
            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
            LEFT JOIN MRI.ENTITY E								
            ON B.ENTITYID = E.ENTITYID								
            LEFT JOIN MRI.PROJ P								
            ON E.PROJID = P.PROJID																
            LEFT JOIN MRI.CODELIST CL								
            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
            WHERE 
        		L.OCCPSTAT NOT IN ('P', 'I') 							
                AND (
                  		(					
                  			(
                  				L.RENTSTRT <= GETDATE() 
                  				OR L.OCCUPNCY <= GETDATE()
                  				OR L.EXECDATE <= GETDATE()
                  			)
                  			AND (				
                  					L.STOPBILLDATE IS NULL 		
                  					OR L.STOPBILLDATE >= GETDATE()		
                  					OR (		
                  							L.STOPBILLDATE < GETDATE() 
                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
                  						)	
                  			)			
                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()				
                  		)					
                  		OR 					
                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
                  				
                  	)
            )LEASED
        	ON LEAS.BLDGID = LEASED.BLDGID
            AND LEAS.SUITID = LEASED.SUITID
        	AND LEAS.LEASID = LEASED.LEASID
        	LEFT JOIN
          (
    				SELECT DISTINCT
    				NOTE.BLDGID
    				, NOTE.LEASID
    				, NOTE.NOTEDATE
    				FROM MRI.NOTE
    				WHERE (NOTE.REF1 = 'EXCLUDE' OR NOTE.REF2 = 'EXCLUDE')
    				AND NOTE.NOTEDATE = (
    					SELECT MIN(I.NOTEDATE) 
    					FROM MRI.NOTE I 
    					WHERE I.BLDGID = NOTE.BLDGID 
    					AND I.LEASID = NOTE.LEASID 
    					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
    				)
    				AND NOTE.NOTEDATE <= CURRENT_DATE
          )EXCLEASNOTE
          ON NOTE.BLDGID = EXCLEASNOTE.BLDGID
          AND NOTE.LEASID = EXCLEASNOTE.LEASID
        	LEFT JOIN
          (
      			SELECT DISTINCT
      			NOTB.BLDGID
    				, NOTB.NOTEDATE
    				FROM MRI.NOTB
    				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
      			AND NOTB.NOTEDATE = (
      				SELECT MIN(I.NOTEDATE) 
      				FROM MRI.NOTB I 
      				WHERE I.BLDGID = NOTB.BLDGID  
    					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
    				)
    				AND NOTB.NOTEDATE <= CURRENT_DATE
          )EXCBLDGNOTE
          ON NOTE.BLDGID = EXCBLDGNOTE.BLDGID
        	WHERE 			
        		(BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)			
        		AND (NOTE.REF1 = 'BYEBYE' OR NOTE.REF2 = 'BYEBYE')
        		--AND CONVERT(DATE, NOTE.NOTEDATE) <= CONVERT(DATE, GETDATE()) /*20240628 notes now added as soon as entered */
        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)	
        		AND LEASED.OCCP_STATUS IS NOT NULL
        		AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
        		AND 
        		(
        			CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 
        			OR
                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND SUIT.SUITETYPE_MRI = 'BTS')
        		)
        		AND EXCLEASNOTE.LEASID IS NULL
        		AND EXCBLDGNOTE.BLDGID IS NULL
        ) combined
        order by combined.BLDGID, combined.SUITID, combined.AVAILTYPE
        """
        
        df = self.execute_query(main_query)
        
        # # Add update info column
        # if not update_info.empty:
        #     df[update_info.iloc[0]['LUPD']] = ''
        
        return df
    

    
    def get_prior_tenant_data(self) -> pd.DataFrame:
        """Get prior tenant data"""
        # This would be similar to the main query but for prior tenants
        # Implementing abbreviated version for space
        query = """
	        SELECT /* SNOWFLAKE version, Prior Occupant query */
	          combined.BLDGID
	        , combined.BLDG AS "Bldg"
	        , combined.BLDGADDRESS AS "Primary Address"
	        , combined.SUITADDRESS AS "Suite Address"
	        , combined.CITY AS "City"
	        , combined.STATE AS "State"
	        , combined.ZIPCODE AS "Zip Code"
	        , combined.SUITID AS "Suite ID"
	        , combined.SQFT AS "SQ FT"
	        , combined.BLDG_GLA as "BLDG GLA"
	        , combined.AVAILTYPE AS "Avail Type"
	        , LASTTNT.LEASID as "Prior Lease ID"
	        , LASTTNT.OCCPNAME AS "Prior Occupant Name"
	        , LASTTNT.DBA as "Prior DBA"
	        , LASTTNT.RENTSTRT AS "Rent Start"
	        , LASTTNT.VACATEDATE as "Vacate Date"
	        , LASTTNT.EXPIRDATE as "Lease Expiration Date"
	        , combined.SUITETYPEDESC AS "Suite Type Desc"	          
	        , combined.MAPURL AS "Google Map URL"
	        , combined.CORPPM AS "Corporate PM"
	        , combined.CORPPMEMAIL AS "PM_Email"
	     
	        from
	        (
	          select
	            B.BLDGID
	          ,	CASE WHEN TRY_CAST(B.BLDGID AS INT) IS NULL THEN B.BLDGID ELSE TO_CHAR(TRY_CAST(B.BLDGID AS INT)) END AS BLDG
	          , NULL as HRG
	          ,	CONCAT(RTRIM(B.ADDRESS1), (CASE WHEN B.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(B.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
	          ,	RTRIM(S.ADDRESS) AS SUITADDRESS
	          ,	B.CITY
	          ,	B.STATE
	          ,	B.ZIPCODE
	          ,	S.SUITID
	          ,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
	          , BLDG_SQFT.BLDG_GLA
	          , 'Vacancy' AS AVAILTYPE
	          ,	TB_CM_SUITETYPE.DESCRIPTION as SUITETYPEDESC
	          ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE rtrim(B.MAP) END,'') AS MAPURL
	      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
	      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) 
						  ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
	          FROM MRI.SUIT S
	          JOIN MRI.BLDG B
	          ON B.BLDGID = S.BLDGID
	          left join MRI.MNGR
	          on b.MNGRID = mngr.MNGRID
	          LEFT JOIN MRI.TB_CM_SUITETYPE
	          ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	          LEFT JOIN 
	          (
	          SELECT *
	          FROM MRI.SSQF
	          WHERE SSQF.EFFDATE = (
	              SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
	              )
	          ) SSQF_TYPE
	          ON S.SUITID = SSQF_TYPE.SUITID
	          AND S.BLDGID = SSQF_TYPE.BLDGID
	        	left join
	        	(
	            SELECT SSQF.BLDGID
	        	,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
	            FROM MRI.SSQF
	        	join MRI.SUIT
	        		ON SSQF.SUITID = SUIT.SUITID
	        		AND SSQF.BLDGID = SUIT.BLDGID
	        	LEFT JOIN MRI.TB_CM_SUITETYPE								
	            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	            WHERE SSQF.EFFDATE = (
	                  	SELECT MAX(I.EFFDATE) 
	        			FROM MRI.SSQF I 
	        			WHERE I.BLDGID = SSQF.BLDGID 
	        				AND I.SUITID = SSQF.SUITID 
	        				AND I.EFFDATE <= CURRENT_DATE
	                )
	        		AND UPPER(SSQF.SQFTTYPE) = 'GLA'
	        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
	        		GROUP BY SSQF.BLDGID
	            ) BLDG_SQFT
	            ON S.BLDGID = BLDG_SQFT.BLDGID
	        
	            LEFT JOIN
	            (
	            SELECT 								
	                L.BLDGID,							
	                RTRIM(P.PORTID) AS Portfolio,
	                L.SUITID,							
	                L.LEASID,							
	                L.OCCPNAME,							
	                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
	                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
	                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
	                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
	                L.OCCPSTAT,
	                CL.CODEDESC AS OCCP_STATUS,							
	                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
	                TO_DATE(L.VACATE) as VACATEDATE,							
	                TO_DATE(L.EXPIR) as EXPIRDATE,																											
	                S.SUITETYPE_MRI,							
	                TB_CM_SUITETYPE.SUITETYPEUSAGE,							
	                TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
	                L.GENCODE,
	                L.CONTINGENT,
	                L.CONTINGENTDT,
	                case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= Cast(GetDate() AS date) THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY														
	            FROM MRI.LEAS L								
	            INNER JOIN MRI.BLDG B								
	            ON L.BLDGID = B.BLDGID								
	            INNER JOIN MRI.SUIT S								
	            ON L.SUITID = S.SUITID								
	                AND B.BLDGID = S.BLDGID								
	            LEFT JOIN (								
	                SELECT * 							
	                FROM MRI.SSQF 							
	                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
	            ) SQF								
	            ON S.BLDGID = SQF.BLDGID								
	                AND S.SUITID = SQF.SUITID							
	            LEFT JOIN MRI.TB_CM_SUITETYPE								
	            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
	            LEFT JOIN MRI.ENTITY E								
	            ON B.ENTITYID = E.ENTITYID								
	            LEFT JOIN MRI.PROJ P								
	            ON E.PROJID = P.PROJID																
	            LEFT JOIN MRI.CODELIST CL								
	            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	            WHERE 		
	                L.OCCPSTAT NOT IN ('P', 'I') 							
	                AND (		/* Next section commented out to allow future tenants to appear in results (also comment out CMRECC.INEFFECT = 'Y' line above)  */
	                  		(					
	                  			(
	                  				L.RENTSTRT <= GETDATE() 
	                  				OR L.OCCUPNCY <= GETDATE()
	                  				OR L.EXECDATE <= GETDATE()
	                  			)
	                  			AND (				
	                  					L.STOPBILLDATE IS NULL 		
	                  					OR L.STOPBILLDATE >= GETDATE() 		
	                  					OR (		
	                  							L.STOPBILLDATE < GETDATE() 
	                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
	                  						)	
	                  			)			
	                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE() 				
	                  		)					
	                  		OR 					
	                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
	                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
	                  				
	                  	)
	            )LEASED
	            ON B.BLDGID = LEASED.BLDGID
	            AND S.SUITID = LEASED.SUITID
	            LEFT JOIN
              (
        				SELECT DISTINCT
        				NOTB.BLDGID
        				, NOTB.NOTEDATE
        				FROM MRI.NOTB
        				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
        				AND NOTB.NOTEDATE = (
        					SELECT MIN(I.NOTEDATE) 
        					FROM MRI.NOTB I 
        					WHERE I.BLDGID = NOTB.BLDGID  
        					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
        				)
        				AND NOTB.NOTEDATE <= CURRENT_DATE
              )EXCBLDGNOTE
              ON B.BLDGID = EXCBLDGNOTE.BLDGID
	            WHERE (B.INACTIVE <> 'Y' or B.INACTIVE IS NULL)
	            AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
	            AND B.BLDGID IS NOT NULL
	            AND LEASED.OCCP_STATUS IS NULL
	            AND TRY_CAST(B.BLDGID AS INT) IS NOT NULL
	            AND (CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 OR
	                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND S.SUITETYPE_MRI = 'BTS')
	        	)
	        	AND EXCBLDGNOTE.BLDGID IS NULL /* EXCLUDES BUILDING when 'EXCLUDE' building note in effect */
	        
	        
	        	union all
	        
	        
	        	SELECT	/* BYEBYE note additions */
	        	  NOTE.BLDGID
	        	,	CASE WHEN TRY_CAST(NOTE.BLDGID AS INT) IS NULL THEN NOTE.BLDGID ELSE TO_CHAR(TRY_CAST(NOTE.BLDGID AS INT)) END AS BLDG
	        	,	NULL as HRG
	        	,	CONCAT(RTRIM(BLDG.ADDRESS1), (CASE WHEN BLDG.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(BLDG.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
	        	,	rtrim(SUIT.ADDRESS) AS SUITADDRESS
	            ,	BLDG.CITY
	            ,	BLDG.STATE
	            ,	BLDG.ZIPCODE
	            ,	SUIT.SUITID
	        	,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
	        	,	BLDG_SQFT.BLDG_GLA
	        	,	CONCAT('BYEBYE', ' (', RTRIM(NOTE.NOTETEXT), ') Avail ',TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yy')) AS AVAILTYPE
	        	,	TB_CM_SUITETYPE.DESCRIPTION as SUITETYPEDESC
	        	,	ifnull(CASE WHEN BLDG.INACTIVE = 'Y' THEN NULL ELSE rtrim(BLDG.MAP) END,'') AS MAPURL
	        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
	        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
	  					ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
	        	FROM MRI.NOTE			
	        	LEFT JOIN MRI.BLDG			
	        	ON NOTE.BLDGID = BLDG.BLDGID
	        	left join MRI.MNGR
	        	on bldg.MNGRID = mngr.MNGRID
	        	LEFT JOIN MRI.LEAS
	        	ON NOTE.LEASID = LEAS.LEASID
	        		AND NOTE.BLDGID = LEAS.BLDGID
	        	LEFT JOIN MRI.CODELIST CL								
	        	ON LEAS.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	        	LEFT JOIN MRI.SUIT
	        	ON LEAS.SUITID = SUIT.SUITID
	        		AND LEAS.BLDGID = SUIT.BLDGID
	        	LEFT JOIN 
	            (
	        		SELECT *
	        		FROM MRI.SSQF
	        		WHERE SSQF.EFFDATE = (
	        			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE 
	        			)
	            ) SSQF_TYPE
	            ON LEAS.SUITID = SSQF_TYPE.SUITID
	        		AND LEAS.BLDGID = SSQF_TYPE.BLDGID
	        	left join
	        	(
	        		SELECT SSQF.BLDGID
	        		,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
	        		FROM MRI.SSQF
	        		join MRI.SUIT
	        			ON SSQF.SUITID = SUIT.SUITID
	        			AND SSQF.BLDGID = SUIT.BLDGID
	        		LEFT JOIN MRI.TB_CM_SUITETYPE								
	        		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	        		WHERE SSQF.EFFDATE = (
	                  		SELECT MAX(I.EFFDATE) 
	        				FROM MRI.SSQF I 
	        				WHERE I.BLDGID = SSQF.BLDGID 
	        					AND I.SUITID = SSQF.SUITID 
	        					AND I.EFFDATE <= CURRENT_DATE 
	        			)
	        			AND UPPER(SSQF.SQFTTYPE) = 'GLA'
	        			AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
	        		GROUP BY SSQF.BLDGID
	            ) BLDG_SQFT
	            ON LEAS.BLDGID = BLDG_SQFT.BLDGID
	        	LEFT JOIN MRI.TB_CM_SUITETYPE
	            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	        
	        	LEFT JOIN
	            (
	            SELECT 								
	                L.BLDGID,							
	                RTRIM(P.PORTID) AS Portfolio,
	                L.SUITID,						
	                L.LEASID,							
	                L.OCCPNAME,							
	                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
	                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
	                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
	                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
	                L.OCCPSTAT,
	                CL.CODEDESC AS OCCP_STATUS,							
	                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
	                TO_DATE(L.VACATE) as VACATEDATE,							
	                TO_DATE(L.EXPIR) as EXPIRDATE																																						
	            FROM MRI.LEAS L							
	            INNER JOIN MRI.BLDG B								
	            ON L.BLDGID = B.BLDGID								
	            INNER JOIN MRI.SUIT S								
	            ON L.SUITID = S.SUITID								
	                AND L.BLDGID = S.BLDGID								
	            LEFT JOIN (								
	                SELECT * 							
	                FROM MRI.SSQF 							
	                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
	            ) SQF								
	            ON S.BLDGID = SQF.BLDGID								
	                AND S.SUITID = SQF.SUITID							
	            LEFT JOIN MRI.TB_CM_SUITETYPE								
	            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
	            LEFT JOIN MRI.ENTITY E								
	            ON B.ENTITYID = E.ENTITYID								
	            LEFT JOIN MRI.PROJ P								
	            ON E.PROJID = P.PROJID																
	            LEFT JOIN MRI.CODELIST CL								
	            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	            WHERE 
	        		L.OCCPSTAT NOT IN ('P', 'I') 							
	                AND (
	                  		(					
	                  			(
	                  				L.RENTSTRT <= GETDATE() 
	                  				OR L.OCCUPNCY <= GETDATE()
	                  				OR L.EXECDATE <= GETDATE()
	                  			)
	                  			AND (				
	                  					L.STOPBILLDATE IS NULL 		
	                  					OR L.STOPBILLDATE >= GETDATE()		
	                  					OR (		
	                  							L.STOPBILLDATE < GETDATE() 
	                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
	                  						)	
	                  			)			
	                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()				
	                  		)					
	                  		OR 					
	                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
	                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
	                  				
	                  	)
	            )LEASED
	        	ON LEAS.BLDGID = LEASED.BLDGID
	        	AND LEAS.SUITID = LEASED.SUITID
	        	AND LEAS.LEASID = LEASED.LEASID
          	LEFT JOIN
            (
      				SELECT DISTINCT
      				NOTE.BLDGID
      				, NOTE.LEASID
      				, NOTE.NOTEDATE
      				FROM MRI.NOTE
      				WHERE (NOTE.REF1 = 'EXCLUDE' OR NOTE.REF2 = 'EXCLUDE')
      				AND NOTE.NOTEDATE = (
      					SELECT MIN(I.NOTEDATE) 
      					FROM MRI.NOTE I 
      					WHERE I.BLDGID = NOTE.BLDGID 
      					AND I.LEASID = NOTE.LEASID 
      					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
      				)
      				AND NOTE.NOTEDATE <= CURRENT_DATE
            )EXCLEASNOTE
            ON NOTE.BLDGID = EXCLEASNOTE.BLDGID
            AND NOTE.LEASID = EXCLEASNOTE.LEASID
          	LEFT JOIN
            (
        			SELECT DISTINCT
        			NOTB.BLDGID
      				, NOTB.NOTEDATE
      				FROM MRI.NOTB
      				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
        			AND NOTB.NOTEDATE = (
        				SELECT MIN(I.NOTEDATE) 
        				FROM MRI.NOTB I 
        				WHERE I.BLDGID = NOTB.BLDGID  
      					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
      				)
      				AND NOTB.NOTEDATE <= CURRENT_DATE
            )EXCBLDGNOTE
            ON NOTE.BLDGID = EXCBLDGNOTE.BLDGID
	        	WHERE 			
	        		(BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)			
	        		AND (NOTE.REF1 = 'BYEBYE' OR NOTE.REF2 = 'BYEBYE')
	        		--AND CONVERT(DATE, NOTE.NOTEDATE) <= CONVERT(DATE, GETDATE()) /*20240628 notes now added as soon as entered */
	        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)	
	        		AND LEASED.OCCP_STATUS IS NOT NULL
	        		AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
	                AND 
	        		(
	        			CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 
	        			OR
	                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND SUIT.SUITETYPE_MRI = 'BTS')
	        		)
	        		AND EXCLEASNOTE.LEASID IS NULL
	        		AND EXCBLDGNOTE.BLDGID IS NULL
	        ) combined
	       LEFT JOIN
	        (
	        	SELECT 								
	        		L.BLDGID,							
	        		TRIM(P.PORTID) AS Portfolio,
	        		L.SUITID,							
	        		L.LEASID,							
	        		L.OCCPNAME,	
	        		L.DBA,
	        		to_date(L.EXECDATE) AS EXECDATE,
	        		to_date(L.RENTSTRT) AS RENTSTRT,							
	        		CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
	        		CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQFSQFT,							
	        		TRIM(SQF.SQFTTYPE) AS SQFTType,														
	        		L.OCCPSTAT,
	        		CL.CODEDESC AS OCCPSTATUS,							
	        		to_date(L.STOPBILLDATE) AS STOPBILLDATE,							
	        		to_date(L.VACATE) as VACATEDATE,							
	        		to_date(L.EXPIR) as EXPIRDATE,																											
	        		S.SUITETYPE_MRI,							
	        		--TB_CM_SUITETYPE.SUITETYPEUSAGE,							
	        		--TB_CM_SUITETYPE.DESCRIPTION AS [SUITETYPEDESCRIPTION],
	        		L.GENCODE,
	        		L.CONTINGENT,
	        		L.CONTINGENTDT,
	        		case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= CURRENT_DATE THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY
	        	FROM MRI.LEAS L
	        	INNER JOIN MRI.BLDG B								
	        	ON L.BLDGID = B.BLDGID								
	        	INNER JOIN MRI.SUIT S								
	        	ON L.SUITID = S.SUITID								
	        		AND B.BLDGID = S.BLDGID								
	        	LEFT JOIN (								
	        		SELECT * 							
	        		FROM MRI.SSQF 							
	        		WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
	        	) SQF								
	        	ON S.BLDGID = SQF.BLDGID								
	        		AND S.SUITID = SQF.SUITID							
	        	--LEFT JOIN TB_CM_SUITETYPE								
	        	--ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
	        	LEFT JOIN MRI.ENTITY E								
	        	ON B.ENTITYID = E.ENTITYID								
	        	LEFT JOIN MRI.PROJ P								
	        	ON E.PROJID = P.PROJID																
	        	LEFT JOIN MRI.CODELIST CL								
	        	ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	        	WHERE 		
	        		L.RENTSTRT = (select max(i.RENTSTRT) from MRI.LEAS i where i.BLDGID = L.BLDGID and i.SUITID = L.SUITID and i.RENTSTRT < CURRENT_DATE)
	        )LASTTNT
	        ON combined.BLDGID = LASTTNT.BLDGID
	        AND combined.SUITID = LASTTNT.SUITID	        
	        order by combined.BLDGID, combined.SUITID, combined.AVAILTYPE
        """
        
        return self.execute_query(query)
    
    def get_support_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Get PM phone and LCP assignments data"""
        pm_phone_query = """
        SELECT /* SNOWFLAKE version */
          email as "PM_Email"
        , '(847) 904-'||extension as "PM_Phone"
        from CORPORATE.ab_employees
        where status not in ('T','R','D')
        and extension is not null
        and email is not null
        """
        
        lcp_assign_query = """
        SELECT  /* SNOWFLAKE version */
        to_char(a.BLDG) "Bldg"
        , a.RM
        , case when rm.phone is NULL then NULL
            else '('||substr(rm.phone,1,3)||') '||substr(rm.phone,4,3)||'-'||substr(rm.phone,7,4) end as "RM Phone"
        , rm.email as "RM Email"
        , a.SUPPORT_LEASING AS "Support Leasing"
        , case when a.SUPPORT_LEASING = 'BRH' then '(*************' 
            when sl.phone is NULL then NULL
            else '('||substr(sl.phone,1,3)||') '||substr(sl.phone,4,3)||'-'||substr(sl.phone,7,4) end as "SL Phone"
        , case when a.SUPPORT_LEASING = 'BRH' then '<EMAIL>' else sl.email end as "SL Email"
        , a.SUPPORT_PROP_MGMT AS "Support Property Management"
        , case when rm.phone is NULL then NULL
            else '('||substr(spm.phone,1,3)||') '||substr(spm.phone,4,3)||'-'||substr(spm.phone,7,4) end as "SPM Phone"
        , spm.email as "SPM Email"
        from CORPORATE.lcp_assignments_sean a
        left join CORPORATE.ab_employees rm
        on upper(a.rm) = upper(rm.fname)||' '||upper(rm.lname)
        and rm.status not in ('T','R','D')
        left join CORPORATE.ab_employees sl
        on upper(a.SUPPORT_LEASING) = upper(sl.fname)||' '||upper(sl.lname)
        and sl.status not in ('T','R','D')
        left join CORPORATE.ab_employees spm
        on upper(a.SUPPORT_PROP_MGMT) = upper(spm.fname)||' '||upper(spm.lname)
        and spm.status not in ('T','R','D')
        """
        
        pm_phone_df = self.execute_query(pm_phone_query)
        lcp_assign_df = self.execute_query(lcp_assign_query)
        
        return pm_phone_df, lcp_assign_df
    
    def process_data(self, availability_df: pd.DataFrame, pm_phone_df: pd.DataFrame, 
                    lcp_assign_df: pd.DataFrame) -> pd.DataFrame:
        """Process and merge all data"""
        # Remove BLDGID column
        if 'BLDGID' in availability_df.columns:
            availability_df = availability_df.drop('BLDGID', axis=1)
        
        # Strip whitespace
        for col in availability_df.select_dtypes(include=[object]).columns:
            availability_df[col] = availability_df[col].astype(str).str.rstrip()
        
        # Join PM phone data
        availability_df = availability_df.merge(pm_phone_df, on='PM_Email', how='left')
        
        # Reorder columns to put PM Phone before PM Email
        cols = list(availability_df.columns)
        pm_phone_idx = cols.index('PM_Phone')
        pm_email_idx = cols.index('PM_Email')
        if pm_phone_idx > pm_email_idx:
            cols[pm_phone_idx], cols[pm_email_idx] = cols[pm_email_idx], cols[pm_phone_idx]
            availability_df = availability_df[cols]
        
        # Join LCP assignments
        availability_df = availability_df.merge(lcp_assign_df, on='Bldg', how='left')
        
        # Replace underscores with spaces in column names
        availability_df.columns = availability_df.columns.str.replace('_', ' ')
        
        return availability_df
    
    

    def safe_to_int(self, value):
        """
        Convert to integer if possible, otherwise return original value.
        Specifically handles your data format.
        """
        # Handle None/NaN cases
        if pd.isna(value) or value == 'None' or value == '':
            return value
        
        try:
            # Convert string numbers like '1233.0' to integer
            if isinstance(value, str):
                # Remove any whitespace
                value = value.strip()
                if value == '':
                    return value
            
            # Convert to float first, then to int
            return int(float(value))    
        except (ValueError, TypeError, OverflowError):
            # Return original value if conversion fails
            return value
        
    def compare_and_notify_changes(self, current_df: pd.DataFrame, existing_df: pd.DataFrame):
        """Compare current and existing data, send notifications for changes"""
        if existing_df.empty:
            return
        
        # Define comparison columns
        avail_cols = ["Bldg", "City", "State", "Suite ID", "SQ FT", "Avail Type"]
        removal_cols = avail_cols + ["Corporate PM"]

        # Convert Suite ID to integer for comparison
        current_df['Suite ID'] = current_df['Suite ID'].apply(self.safe_to_int)
        existing_df['Suite ID'] = existing_df['Suite ID'].apply(self.safe_to_int)

        # Find additions
        current_subset = current_df[["Bldg", "Suite ID"]].copy()
        existing_subset = existing_df[["Bldg", "Suite ID"]].copy()

        
        # Convert to string for comparison
        current_subset = current_subset.astype(str)
        existing_subset = existing_subset.astype(str)
        
        # Find new additions
        merged = current_subset.merge(existing_subset, on=["Bldg", "Suite ID"], how='left', indicator=True)
        additions = merged[merged['_merge'] == 'left_only'][["Bldg", "Suite ID"]]
        
        if not additions.empty:

            self.sf.log_audit_in_db(log_msg=f"compare_and_notify_changes() - Additions found: {additions}", process_type=self.my_report_name, script_file_name=__file__, log_type="info")

            # Get full details for additions
            additions_detail = current_df.merge(additions, on=["Bldg", "Suite ID"])[avail_cols]
            
            # Create HTML table
            html_table = additions_detail.to_html(table_id="additions", 
                                                border=2, 
                                                index=False,
                                                escape=False)
            
            body_html = f"""
            <html><head></head><body>
            <h2>New Locations Added to 'Availability'!</h2>
            <p>The following locations were added to the Availability sheet:</p>
            {html_table}
            <br>
            <b>LCP Reporting</b>
            </body></html>
            """
            
            email_client.send_email(
                recipient=self.norm_recip,
                subject="New 'Availability' Spaces",
                body=body_html,
                test=self.testing_emails
            )
        
        # print(f"0) compare_and_notify_changes(): removals")
        # Find removals
        merged_removals = existing_subset.merge(current_subset, on=["Bldg", "Suite ID"], how='left', indicator=True)
        # print(f"1) compare_and_notify_changes(): removals")
        removals = merged_removals[merged_removals['_merge'] == 'left_only'][["Bldg", "Suite ID"]]
        removals = removals[removals['Bldg'] != ''][["Bldg", "Suite ID"]] # remove empty rows
        # print(f"2) compare_and_notify_changes(): removals. removal_cols: {removal_cols}")
        
        if not removals.empty:
            # Get full details for removals
            existing_df['Bldg'] = existing_df['Bldg'].astype(str) # convert to str to avoid type mismatch
            removals['Bldg'] = removals['Bldg'].astype(str) # convert to str to avoid type mismatch

            removals_detail = existing_df.merge(removals, on=["Bldg", "Suite ID"])[removal_cols]
            # print(f"3) compare_and_notify_changes(): removals. \nremovals: {removals}\n\nremovals_detail: {removals_detail}")
            if removals_detail.empty:
                self.sf.log_audit_in_db(log_msg=f"compare_and_notify_changes() - No removals details merged. Email not sent to {self.removals_recip}, even though removals were found: {removals}", process_type=self.my_report_name, script_file_name=__file__, log_type="warning")
                return
            # exit(1)
            
            html_table = removals_detail.to_html(table_id="removals",
                                               border=2,
                                               index=False,
                                               escape=False)
            
            body_html = f"""
            <html><head></head><body>
            <h2>Locations REMOVED FROM 'Availability'!</h2>
            <p>The following locations were <strong>removed</strong> from the Availability sheet:</p>
            {html_table}
            <br>
            <b>LCP Reporting</b>
            </body></html>
            """
            
            email_client.send_email(
                recipient=self.removals_recip,
                subject="Removed 'Availability' Spaces ⛔",
                body=body_html,
                test=self.testing_emails
            )
    
    def get_availability_development_sheet_data_from_excel(self):
        """Get availability sheet data from Excel"""
        existing_dev_availability_data_from_sheet_dict = self.excel_helper.get_worksheet_data(self.excel_session, 'Development Availability')
        values_existing_dev_availability_data_from_sheet_list = existing_dev_availability_data_from_sheet_dict.get('values')
        existing_dev_availability_headers = values_existing_dev_availability_data_from_sheet_list[0] 
        existing_dev_availability_data = values_existing_dev_availability_data_from_sheet_list[1:]
        existing_dev_availability_data_df = pd.DataFrame(data=existing_dev_availability_data, columns=existing_dev_availability_headers)
        # print(f"existing_dev_availability_data: {existing_dev_availability_data}")
        # exit(1)

        # df_filtered.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_df_filtered_1.csv', index=False)

        # Remove rows where City is NA or empty
        existing_dev_availability_data_df = existing_dev_availability_data_df[(existing_dev_availability_data_df['City'].notna()) & (existing_dev_availability_data_df['City'].str.strip() != '')]

        # Process development availability data
        processed_dev_availability_data_df = self.process_development_availability(existing_dev_availability_data_df)

        # print(f"processed_dev_availability_data_df: {processed_dev_availability_data_df}")

        if processed_dev_availability_data_df is None:
            self.sf.log_audit_in_db(log_msg=f"get_availability_development_sheet_data_from_excel() - No processed development availability data", process_type=self.my_report_name, script_file_name=__file__, log_type="error")
            return None
        else:
            self.sf.log_audit_in_db(log_msg=f"get_availability_development_sheet_data_from_excel() - Successfully processed {len(processed_dev_availability_data_df)} development availability records", process_type=self.my_report_name, script_file_name=__file__)
            # processed_dev_availability_data_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_after_renaming_new8.csv', index=False)
            return processed_dev_availability_data_df

        # exit(1)

        # existing_dev_availability_data_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_before_renaming.csv', index=False)

        # my_col_names = ["BLDG", "Address", "City", "State", "Zip Code", "Sqft Avail", "Total SF", "Site Link", "Anticipated Close Date"] # , "Available Suite ID", "Suite SQFT"

        # my_col_names_new = ["Bldg", "Primary Address", "City", "State", "Zip Code", "SQ FT", "BLDG GLA", "Google Map URL", "Avail Date"] # , "Suite ID","SQ FT"

        # column_mapping = dict(zip(my_col_names, my_col_names_new))
        # existing_dev_availability_data_df = existing_dev_availability_data_df.rename(columns=column_mapping) 
        # existing_dev_availability_data_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_after_renaming_new.csv', index=False)        
        # existing_dev_availability_data_df = existing_dev_availability_data_df[my_col_names_new].copy() # just get desired columns 
        # existing_dev_availability_data_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_after_renaming_new2.csv', index=False)

        # existing_dev_availability_data_df = existing_dev_availability_data_df[(existing_dev_availability_data_df['City'].notna()) & (existing_dev_availability_data_df['City'].str.strip() != '')]

        # existing_dev_availability_data_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_after_renaming_new3.csv', index=False)

        # exit(1)

        # return existing_dev_availability_data_df

    def get_excel_date_conversion(self, excel_date) -> str:
        """Convert Excel date to formatted date. Excel date is supplied as an integer: eg: 45699"""

        if pd.isna(excel_date):
            return 'Development Avail'
        try:
            excel_epoch = datetime(1899, 12, 30)
            date_value = excel_epoch + timedelta(days=excel_date)
            date_value_formatted = date_value.strftime('%m/%d/%y')

            return f"Development Avail {date_value_formatted}"
        except:
            # return str(excel_date)  # Return as string if formatting fails            
            return 'Development Avail'

        # return date_value_formatted


        # excel_date = 45699
        # # Excel's epoch is December 30, 1899
        # excel_epoch = datetime(1899, 12, 30)
        # date_value = excel_epoch + timedelta(days=excel_date)
        # date_value_formatted = date_value.strftime('%m/%d/%y')
        # print(f"date_value type: {type(date_value)}")
        # print(f"date_value: {date_value}")
        # print(f"date_value_formatted: {date_value_formatted}")


    def process_development_availability(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Process development availability data from Google Sheet"""
        try:

            
            # Define column mappings
            required_cols = [
                "BLDG", "Address", "City", "State", "Zip Code", 
                "Sqft Avail", "Total SF", "Site Link", "Anticipated Close Date"
            ]
            additional_cols = [
                "Avail Type"#, "Suite SQFT"
            ]
            new_col_names = [
                "Bldg", "Primary Address", "City", "State", "Zip Code",
                "SQ FT", "BLDG GLA", "Google Map URL", "Avail Date"
            ]
            
            # Check if required columns exist
            if not all(col in df.columns for col in required_cols):
                # self.logger.warning("Development sheet missing required columns")
                self.sf.log_audit_in_db(log_msg=f"process_development_availability() - Development sheet missing required columns: {required_cols}. Sheet columns: {df.columns}", process_type=self.my_report_name, script_file_name=__file__, log_type="warning")
                return None
            
            # print(f"0) process_development_availability(): ")
            # Filter for rows with data
            df_filtered = df[df[required_cols].notna().any(axis=1)].copy()
            # print(f"1) process_development_availability(): df_filtered: {df_filtered}")
            if df_filtered.empty:
                return None
            
            # print(f"2) process_development_availability(): df_filtered: {df_filtered}")
            # Convert anticipated close date
            # df_filtered['Anticipated Close Date'] = pd.to_datetime(
            #     df_filtered['Anticipated Close Date'], errors='coerce'
            # )
            # print(f"3) process_development_availability(): df_filtered: {df_filtered}")
            # Create Avail Type column
            df_filtered['Avail Type'] = df_filtered['Anticipated Close Date'].apply(
                # lambda x: f"Development Avail {x.strftime('%m/%d/%y')}" if pd.notna(x) else "Development Avail"
                # lambda x: f"Development Avail {x}" if pd.notna(x) else "Development Avail"
                self.get_excel_date_conversion
            )
            # print(f"4) process_development_availability(): df_filtered: {df_filtered}")
            # Process suite information if present
            suite_cols = [col for col in df.columns if 'Available Suite ID' in col]
            sqft_cols = [col for col in df.columns if 'Suite SQFT' in col]

            # print(f"4b) process_development_availability(): suite_cols: {suite_cols}\n\n")
            # print(f"4c) process_development_availability(): sqft_cols: {sqft_cols}\n\n")

            # df_filtered.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_df_filtered_1.csv', index=False)
            # exit(1)
            
            if suite_cols and sqft_cols:
                # Handle individual suites
                expanded_rows = []
                row_i = 0
                for _, row in df_filtered.iterrows():
                    row_i += 1
                    # print(f"5a) process_development_availability(): row: {row}")
                    # exit(1)
                    col_i = 0
                    for suite_col, sqft_col in zip(suite_cols, sqft_cols):
                        col_i += 1
                        # print(f"5b) process_development_availability(): row_i: {row_i}. col_i: {col_i}.")
                        # print(f"5b) process_development_availability(): suite_col: {suite_col}. sqft_col: {sqft_col}")
                        # row.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_existing_row1.csv', index=False)

                        # Extract scalar values explicitly
                        suite_value = row[suite_col]
                        if isinstance(suite_value, pd.Series):


                            # suite_value = suite_value.iloc[0] if not suite_value.empty else None
                            sqft_values_series = row[sqft_col]

                            # if row['Address'] == '660 & 670 W Main St.':
                            #     print(f"5b1) process_development_availability(): \n\nsuite_value: {suite_value}\n\nsqft_values_series: \n{sqft_values_series}\n\n")
                                # exit(1)

                            changed = False
                            # add every suite id and sqft value for this property
                            for _row_i, _row in enumerate(suite_value):
                                suite_id = suite_value.iloc[_row_i] if not suite_value.empty else None
                                sqft_value = sqft_values_series.iloc[_row_i] if not sqft_values_series.empty else None
                                # if row['Address'] == '660 & 670 W Main St.':
                                #     print(f"{_row_i}) - 5b1a) process_development_availability(): \n\nsuite_id: {suite_id}\n\nsqft_value: {sqft_value}\n\n")

                                if pd.notna(suite_id) and str(suite_id).strip() != '':
                                    new_row = row[required_cols + additional_cols].copy()
                                    # print(f"5c1) process_development_availability(): new_row: {new_row}")
                                    # exit(1)
                                    # new_row['Suite ID'] = str(row[suite_col])
                                    new_row['Suite ID'] = str(suite_id)
                                    new_row['Sqft Avail'] = sqft_value if pd.notna(sqft_value) else None
                                    expanded_rows.append(new_row)
                                    changed = True
                                    
                                    # if row['Address'] == '660 & 670 W Main St.':
                                    #     print(f"{_row_i}) - 5b1b) process_development_availability(): \n\nsuite_id: {suite_id}\n\nsqft_value: {sqft_value}\n\nnew_row: {new_row}\n")


                                    # print(f"5b1) process_development_availability(): \n\n_suite_value: \n{_suite_value}\n\n")
                            # property did not have any suites
                            if not changed:
                                new_row = row[required_cols + additional_cols].copy()
                                new_row['Suite ID'] = None
                                # new_row['Sqft Avail'] = None
                                new_row['Sqft Avail'] = row['Sqft Avail']
                                
                                expanded_rows.append(new_row)
                            break # all suites accounted for


                            # print(f"5e) process_development_availability(): expanded_rows: {expanded_rows}")
                # print(f"5) process_development_availability(): 5) df_filtered: {df_filtered}\n\n5) expanded_rows: {expanded_rows}")
                if expanded_rows:
                    # print(f"5f) process_development_availability(): expanded_rows({len(expanded_rows)}): {expanded_rows}")

                    # for _row_i, _row in enumerate(expanded_rows):
                    #     data_ = _row.tolist()
                    #     headers_ = _row.index.tolist()
                    #     if _row_i == 0:
                    #         print(f"headers_: {headers_}\n\ndata_: {data_}\n\n")
                        
                    #     print(f"{_row_i}) data_: {data_}\n")
                        # print(f"5g) process_development_availability(): _row_i: {_row_i}. _row({type(_row)}): {_row}\n\nheaders_: {headers_}\n\ndata_: {data_}\n\n")
                        # exit(1)
                    # exit(1)
                    df_filtered = pd.DataFrame(expanded_rows)
                else:
                    return None
            else:
                return None
            # print(f"6) process_development_availability(): df_filtered: {df_filtered}")
            
            # Add Suite ID column if not present
            if 'Suite ID' not in df_filtered.columns:
                df_filtered['Suite ID'] = ''
                
            # Rename columns
            column_mapping = dict(zip(required_cols, new_col_names))
            df_filtered = df_filtered.rename(columns=column_mapping)
            
            # Add missing columns to match availability data structure
            if hasattr(self, 'avialability_df') and self.avialability_df is not None:
                avail_cols = self.avialability_df.columns.tolist()
                for col in avail_cols:
                    if col not in df_filtered.columns:
                        df_filtered[col] = ''
                        
                # Reorder columns to match availability data
                df_filtered = df_filtered[avail_cols]

            # strip leading and trailing spaces from Primary Address
            df_filtered['Primary Address'] = df_filtered['Primary Address'].fillna('').astype(str).str.strip()            

            # print(f"7) process_development_availability(): df_filtered: {df_filtered}")
            
            # self.logger.info(f"Successfully processed {len(df_filtered)} development availability records")
            # df_filtered.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_df_5.csv', index=False)
            self.sf.log_audit_in_db(log_msg=f"process_development_availability() - Successfully processed {len(df_filtered)} development availability records", process_type=self.my_report_name, script_file_name=__file__)
            return df_filtered
            
        except Exception as e:
            # self.logger.error(f"Failed to process development availability: {str(e)}")
            self.sf.log_audit_in_db(log_msg=f"process_development_availability() - Failed to process development availability: {str(e)}", process_type=self.my_report_name, script_file_name=__file__, log_type="error")
            return None
                
    def get_availability_sheet_data(self):
        """Main execution method"""
        try:
            # Setup connections
            # if not self.connect_to_snowflake():
            #     return
            
            # if not self.setup_google_sheets():
            #     return
            
            # Get data
            # logger.info("Fetching availability data...")
            self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Fetching availability data...", process_type=self.my_report_name, script_file_name=__file__)
            availability_df = self.get_availability_data()
            
            # logger.info("Fetching prior tenant data...")
            self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Fetching prior tenant data...", process_type=self.my_report_name, script_file_name=__file__)
            prior_df = self.get_prior_tenant_data()
            
            # logger.info("Fetching support data...")
            self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Fetching support data...", process_type=self.my_report_name, script_file_name=__file__)
            pm_phone_df, lcp_assign_df = self.get_support_data()
            
            # Check data quality
            avail_status = self.check_df_rows(availability_df, 3, "Availability")
            prior_status = self.check_df_rows(prior_df, 2, "Prior Tenant")
            pm_status = self.check_df_rows(pm_phone_df, 1, "PM Phone")
            lcp_status = self.check_df_rows(lcp_assign_df, 1, "LCP Assignments")
            
            if not all([avail_status[0], prior_status[0], pm_status[0], lcp_status[0]]):
                # logger.error("Data quality checks failed")
                self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Data quality checks failed", process_type=self.my_report_name, script_file_name=__file__, log_type="error")
                return
            
            # Process data
            # logger.info("Processing data...")
            self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Processing data...", process_type=self.my_report_name, script_file_name=__file__)

            # print(f"last_change_data: {last_change_data}")
            # exit(1)
            avialability_df = self.process_data(availability_df, pm_phone_df, lcp_assign_df)
            avialability_prior_df = self.process_data(prior_df, pm_phone_df, lcp_assign_df)

            last_change_data = self.get_last_change_data()
            # Add update info column
            if not last_change_data.empty:
                avialability_df[last_change_data.iloc[0]['LUPD']] = ''
                avialability_prior_df[last_change_data.iloc[0]['LUPD']] = ''

            self.avialability_df = avialability_df

            dev_availability_df = self.get_availability_development_sheet_data_from_excel()
            # print(f"dev_availability_df: {dev_availability_df}")
            # @todo: fix this
            # append development availability data to availability data
            if dev_availability_df is not None:
                avialability_df = pd.concat([avialability_df, dev_availability_df], ignore_index=True) # , ignore_index=True

            avialability_df = avialability_df.fillna('')
            avialability_prior_df = avialability_prior_df.fillna('')

            avialability_df = avialability_df.astype(str).replace('None', '')
            avialability_prior_df = avialability_prior_df.astype(str).replace('None', '')

            all_text_columns = ['Suite ID', 'Prior Lease ID']
            # prefix with ' for all text columns, so excel wont drop leading zeros
            for col in all_text_columns:
                avialability_prior_df[col] = "'" + avialability_prior_df[col].fillna('').astype(str)            


            # columns_to_convert = ['SQ FT', 'BLDG GLA']
            # avialability_df[columns_to_convert] = avialability_df[columns_to_convert].astype(float).astype(int)
            # avialability_prior_df[columns_to_convert] = avialability_prior_df[columns_to_convert].astype(int)

            # Replace both None and NaN
            # avialability_df = avialability_df.replace({None: '', np.nan: ''})
            # avialability_prior_df = avialability_prior_df.replace({None: '', np.nan: ''})
            # avialability_df = avialability_df.where(avialability_df.notna(), '')
            # avialability_prior_df = avialability_prior_df.where(avialability_prior_df.notna(), '')
            # print(f"avialability_df: {avialability_df}")    


            # avialability_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/avialability_df_1.csv', index=False)            
            # dev_availability_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/dev_availability_df_1.csv', index=False)            
            # exit(1)
            # Read existing data for comparison
            # logger.info("Reading existing sheet data...")
            # existing_df = self.read_existing_sheet_data(self.gsht_key_2, self.my_mri_sheet_2)
            
            # Update Google Sheets
            # logger.info("Updating Google Sheets...")
            # self.update_google_sheet(self.gsht_key, self.my_mri_sheet, avialability_df)
            # self.update_google_sheet(self.gsht_key_2, self.my_mri_sheet_2, avialability_df)
            # self.update_google_sheet(self.gsht_key_2, self.my_prior_sheet, avialability_prior_df)
            
            # Compare and notify changes
            # logger.info("Comparing changes and sending notifications...")
            self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Comparing changes and sending notifications...", process_type=self.my_report_name, script_file_name=__file__)
            # self.compare_and_notify_changes(avialability_df, existing_df)
            
            # logger.info("Available Suites update completed successfully!")
            # self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Available Suites update completed successfully!", process_type=self.my_report_name, script_file_name=__file__)
            # print(f"avialability_df: {avialability_df}")
            # print(f"avialability_prior_df: {avialability_prior_df}")
            # exit(1)
            return avialability_df, avialability_prior_df
        except Exception as e:
            # logger.error(f"Error in main execution: {e}")
            self.sf.log_audit_in_db(log_msg=f"get_availability_sheet_data() - Error in main execution: {e}", process_type=self.my_report_name, script_file_name=__file__, log_type="error")
            
            # Send error notification
            error_body = f"""
            <p>An error occurred in the {self.my_report_name} routine:</p>
            <p>Error: {str(e)}</p>
            <p>The routine was unable to complete successfully.</p>
            """
            
            email_client.send_email(
                recipient=self.warn_recip,
                subject=f"{self.my_report_name} Error",
                body=error_body,
                test=self.testing_emails
            )
            
        # finally:
        #     # Clean up connections
        #     if self.sf_connection:
        #         self.sf_connection.close()

        return pd.DataFrame(), pd.DataFrame()


def main():
    """Main entry point"""
    import libs.snowflake_helper as snowflake_helper    
    updater = AvailabilitySheetHelper(snowflake_helper.SnowflakeHelper())
    updater.get_availability_sheet_data()


if __name__ == "__main__":
    main() 