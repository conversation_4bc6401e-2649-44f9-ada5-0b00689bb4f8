# @todos:
# d 1. implement vacate email notification
# d 2. fix compare_data_with_prior_week() - prior NA is being dropped and not included in diff_combined

"""
Legacy Rent Roll to Google Sheets - Python Version
Converted from R script by <PERSON> March 2023
Python conversion: 2024

This script:
1. Connects to Snowflake database
2. Extracts rent roll data
3. Updates Google Sheets
4. Compares with previous week's data
5. Sends email notifications of changes
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
import os
import sys
import libs.email_client as email_client

from pathlib import Path

# Configuration and Constants
class Config:

    WEEKDAY_NUMBER_TO_COMPARE = 1 # Tuesday = 1(default) in Python (Monday = 0). Friday = 4

    def __init__(self, sf):
        self.sf = sf
        self.testing_emails = False  # Set to True for testing
        self.version = "20241008"
        
        # Dates
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.inc_name_chgs = True  # Include name changes in reports
        
        # self.base_dir_path = os.path.dirname(os.path.realpath(__file__))
        # self.data_dir_path = os.path.join(self.base_dir_path, ".." ,"data")
        self.data_dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
        
        # Paths
        # self.script_folder = "LEGACY_Portfolio_to_online_cloud"
        self.script_folder = "LEGACY_Rent_Roll"
        self.rpt_folder = "reports"
        # self.log_path = Path("C:/Users/<USER>/Documents/ReportFiles") / self.script_folder
        self.log_path = Path(self.data_dir_path) / self.script_folder

        
        # Report settings
        self.report_name = "LEGACY Rent Roll"
        self.gsht_id = "1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8"
        self.my_sheets = ["RENT ROLL"]
        
        # Google Sheets authentication
        self.gsht_auth_email = "<EMAIL>"
        
        # Snowflake settings
        self.sf_environ = "PROD"  # or "STAGE"
        # self._set_snowflake_config()
        
        # Email recipients
        self.ap_changes_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        
        # Date calculations
        self._calculate_dates()

        self.sf.log_audit_in_db(log_msg=f"data_dir_path: {self.data_dir_path}", process_type=self.report_name, script_file_name=__file__, log_type="Info")
        

    def _calculate_dates(self):
        today = datetime.now().date()
        self.report_date_text = today.strftime("%m-%d-%Y")
        self.report_date_prior_text = (today - timedelta(days=7)).strftime("%m-%d-%Y")
        self.report_time_txt = datetime.now().strftime("%H:%M:%S %Z")
        
        # Calculate rent roll date (15th of current or next month)
        if today.day > 15:
            next_month = today.replace(day=1) + timedelta(days=32)
            self.rentroll_date = next_month.replace(day=15)
        else:
            self.rentroll_date = today.replace(day=15)
        
        self.rentroll_date_text = self.rentroll_date.strftime("%Y%m%d")
        self.rentroll_date_header_text = self.rentroll_date.strftime("%m-%d-%Y")
        self.date_header_text = f"Updated {self.report_date_text} using Rent Roll date as {self.rentroll_date_header_text}"
        
        self.weekday_number = today.weekday()
        # Check if Tuesday for comparison
        self.comp_prior = today.weekday() == self.WEEKDAY_NUMBER_TO_COMPARE  # Tuesday = 1 in Python (Monday = 0)
        # if self.comp_prior:
        #     self.dow = today.strftime("%A")
        self.dow = today.strftime("%A")    
    # def _adjust_testing_paths(self):
    #     self.log_path = Path("//*************/public/steveo/R Stuff/ReportFiles") / self.script_folder



class RentRollSheetHelper:
    def __init__(self, config, sf, csm_db):
        self.config = config
        self.sf = sf
        self.csm_db = csm_db
        self.conn = self.sf.conn

        self.report_name = "LEGACY Rent Roll"

    def execute_query(self, query, params=None):
        """Execute query and return pandas DataFrame"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(query, params or {})
            
            # Fetch column names
            columns = [desc[0] for desc in cursor.description]
            
            # Fetch all rows

            rows = cursor.fetchall()
            cursor.close()
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=columns)
            df = df.fillna('')
            return df
        except Exception as e:
            self.sf.log_audit_in_db(log_msg=f"execute_query(): Query execution failed: {e}", process_type=self.report_name, script_file_name=__file__, log_type="Error")
            return None    
    def check_df_rows(self, df, min_rows, report_name=None):
        """Check if DataFrame has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_rows:
                return True, len(df), f"{report_name}: OKAY"
            else:
                return False, len(df), f"{report_name}: INCOMPLETE"
        else:
            return False, 0, f"{report_name}: ERROR"
    
    def safe_datetime_format(self, x):
        if pd.isna(x):
            return ''
        try:
            return x.strftime('%m/%d/%Y')
        except:
            return str(x)  # Return as string if formatting fails    
        
    def safe_currency_format(self, value):
        """Format as currency with 2 decimal places"""
        if pd.isna(value) or value == '' or value is None:
            return ''
        
        try:
            float_val = float(str(value).strip())
            return f"{float_val:.2f}"
        except (ValueError, TypeError, OverflowError):
            return str(value)
        
    def get_rent_roll_data(self):
        """Process rent roll data from Snowflake"""
        
        # Set report date variable
        set_rptdt_query = f"SET rptdt = to_date('{self.config.rentroll_date_text}','yyyyMMdd')"
        self.conn.cursor().execute(set_rptdt_query)
        
        # Get main rent roll data
        main_query = self._get_main_query() # old: 2836. NEW: 1709
        main_data = self.execute_query(main_query)

        # print(f"main_query: {main_query}\n\nmain_data: {main_data}")
        # exit(1)
        
        if main_data is None:
            self.sf.log_audit_in_db(log_msg="get_rent_roll_data(): Failed to retrieve main rent roll data", process_type=self.report_name, script_file_name=__file__, log_type="Error")
            return None
        
        # Get PM Phone data
        pm_phone_query = self._get_pm_phone_query()
        pm_phone_data = self.execute_query(pm_phone_query)
        
        # Get LCP Assignment data
        lcp_assign_query = self._get_lcp_assign_query()
        lcp_assign_data = self.execute_query(lcp_assign_query)
        # print(f"lcp_assign_data: {lcp_assign_data}")
        # exit(1)
        
        # Get ACQUIS building data
        acquis_query = self._get_acquis_query()
        acquis_data = self.execute_query(acquis_query)
        
        # print(f"0) main_data: {len(main_data.columns.values.tolist())}")
        # Combine ACQUIS data with main data
        if acquis_data is not None and not acquis_data.empty:
            main_data = pd.concat([acquis_data, main_data], ignore_index=True)
        
        # print(f"1) main_data: {len(main_data.columns.values.tolist())}")

        # Join with PM phone data
        if pm_phone_data is not None and not pm_phone_data.empty:
            # Insert PM Phone column before PM Email
            pm_email_idx = main_data.columns.get_loc('PM_Email')
            main_data = main_data.merge(pm_phone_data, on='PM_Email', how='left')
            
            # Reorder columns to put PM Phone before PM Email
            cols = main_data.columns.tolist()
            pm_phone_col = cols.pop(cols.index('PM_Phone'))
            cols.insert(pm_email_idx, pm_phone_col)
            main_data = main_data[cols]
        
        # print(f"2) main_data: {len(main_data.columns.values.tolist())}")

        # Join with LCP assignments
        if lcp_assign_data is not None and not lcp_assign_data.empty:
            main_data = main_data.merge(lcp_assign_data, on='Bldg_ID', how='left')

        # print(f"3) main_data: {len(main_data.columns.values.tolist())}") # problem adding dup columns (fixed)


        # Clean column names (replace underscores with spaces)
        main_data.columns = main_data.columns.str.replace('_', ' ')


        # Add updated column at the end
        main_data[self.config.date_header_text] = None
        
        # Clean trailing spaces from string columns (jorge: generates error, so commented out)
        # for col in main_data.select_dtypes(include=['object']).columns:
        #     main_data[col] = main_data[col].astype(str).str.rstrip()

        # print(f"main_query: {main_query}\n\nmain_data: {main_data}\n\nmain_data.columns: {main_data.columns}")
        # exit(1)

        # Define decimal columns that need safe conversion
        decimal_columns = ['base rent', 'rate psf', 'addl rents']
        date_columns = ['expiration','rent start']

        for col in main_data.columns:
            if hasattr(main_data[col], 'dtype'): # and 'datetime' in str(main_data[col].dtype):
                # print(f"col: {col} is a datetime")
                # print(f"HAS dtype: {col} \t\t: {main_data[col].dtype}")
                # convert to literal date to avoid excel push error 
                if col.lower() in date_columns: # date_columns = ['expiration','rent start']
                    # print(f"converting to date string:{col}") #  data: {main_data[col]}
                    # main_data[col] = main_data[col].dt.strftime('%m/%d/%Y')
                    main_data[col] = main_data[col].apply(self.safe_datetime_format)
                    # exit(1)
                elif col.lower() in decimal_columns: # decimal_columns: ['base rent', 'rate psf', 'addl rents']
                    main_data[col] = main_data[col].apply(self.safe_currency_format)
                    # exit(1)
                # print(f"main_data[col]: {main_data[col]}")
                # exit(1)

                # if  'datetime' in str(main_data[col].dtype):
                # main_data[col] = main_data[col].dt.strftime('%m/%d/%Y')
            else:
                # print(f"NO dtype: {col}")
                pass

        # exit(1)
        main_data = main_data.fillna('')

        all_text_columns = ['Suite', 'Lease ID']
        # prefix with ' for all text columns, so excel wont drop leading zeros
        for col in all_text_columns:
            main_data[col] = "'" + main_data[col].fillna('').astype(str)            

        
        self.sf.log_audit_in_db(log_msg=f"get_rent_roll_data(): Processed {len(main_data)} rows of rent roll data", process_type=self.report_name, script_file_name=__file__, log_type="Info")
        return main_data
    
    

    def _get_main_query(self):
        """Get the main rent roll SQL query - simplified version"""
        return f"""
            SELECT /* SNOWFLAKE version */
              CASE WHEN TRY_CAST(u.BLDG_ID AS INT) IS NULL THEN u.BLDG_ID ELSE TO_CHAR(TRY_CAST(u.BLDG_ID AS INT)) END as "Bldg_ID"
            , NULL as "HRG Interested"
            , u.SUITE AS "Suite"
            , u.MASTER_ID AS "Master_ID"
            , u.OCCUPANT_NAME AS "Occupant_Name"
            , u.RENT_START as "Rent_Start"
            , u.EXPIR AS "Expiration"
            , u.SQFT AS "Sqft"
            , u.BASE_RENT AS "Base_Rent"
            , u.RATE_PSF AS "Rate_PSF"
            , u.ADDL_RENTS AS "Addl_Rents"
            , u.LEASID AS "Lease_ID"
            , u.ADDRESS AS "Address"
            , u.CITY AS "City"
            , u.STATE AS "State"
            , u.ZIPCODE AS "Postal Code"
            , u.MAP_HL AS "MAP HYPERLINK"
            , u.CORP_PM AS "Corporate PM"
            , u.CORP_PM_EMAIL AS "PM_Email"
            
            from
            (	
            	SELECT /* standard active leases and holdovers where exec date or rent start before $rptdt and end date (or stopbill date) is null or in future */
            	  RTRIM(leas.BLDGID) AS BLDG_ID
            	,	RTRIM(leas.SUITID) AS SUITE
            	,	RTRIM(leas.MOCCPID) as MASTER_ID
            	,	concat(RTRIM(leas.OCCPNAME), 
            			case 
            				when TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' and LEAS.RENTSTRT IS NOT NULL
            					then concat(' ',MONTH(LEAS.RENTSTRT),'/',DAY(LEAS.RENTSTRT),'/',YEAR(LEAS.RENTSTRT)) 
            				when IFNULL(leas.VACATE,to_date('2999-12-31','yyyy-mm-dd')) < $rptdt
            					then concat(' Stop Bill Date: ', case when leas.STOPBILLDATE IS NULL THEN CONCAT('NA Vacate: ',MONTH(LEAS.VACATE),'/',DAY(LEAS.VACATE),'/',YEAR(LEAS.VACATE)) ELSE CONCAT(MONTH(LEAS.STOPBILLDATE),'/',DAY(LEAS.STOPBILLDATE),'/',YEAR(LEAS.STOPBILLDATE)) END) 
            				ELSE ''
            			end
            		) as OCCUPANT_NAME
            	,	to_date(leas.RENTSTRT) as RENT_START
            	,	to_date(leas.EXPIR) as EXPIR
            	,	CAST(IFNULL(SSQF_TYPE.SQFT,0) AS INT) AS SQFT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE IFNULL(RENT.AMOUNT,0) END) AS BASE_RENT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE ROUND(IFNULL(RENT.AMOUNT,0)*12/(case when SSQF_TYPE.SQFT = 0 then 1 else SSQF_TYPE.SQFT END),2) END) AS RATE_PSF
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE NULL END) AS ADDL_RENTS
            	,	RTRIM(leas.LEASID) AS LEASID
            	,	RTRIM(suit.ADDRESS) as ADDRESS
            	,	RTRIM(bldg.CITY) as CITY
            	,	RTRIM(bldg.STATE) as STATE
            	,	RTRIM(bldg.ZIPCODE) as ZIPCODE
            	,	rtrim(bldg.MAP) as MAP_HL
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE rtrim(MNGR.MNGRNAME) END AS CORP_PM
  			      , CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  			        ELSE rtrim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS CORP_PM_EMAIL
            	from MRI.LEAS
            	left join MRI.SUIT
            	on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
            	left join MRI.BLDG
            	on leas.BLDGID = bldg.BLDGID
            	left join MRI.MNGR
            	on bldg.MNGRID = mngr.MNGRID
            	LEFT JOIN MRI.TB_CM_SUITETYPE
            		ON suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            	LEFT JOIN 
            	(
            		SELECT *
            		FROM MRI.SSQF
            		WHERE SSQF.EFFDATE = (
            			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= to_date($rptdt)
            			)
            	) SSQF_TYPE
            		ON suit.SUITID = SSQF_TYPE.SUITID
            		AND suit.BLDGID = SSQF_TYPE.BLDGID
            	left join								
            	(								
            		SELECT CMRECC.LEASID
            		,	SUM(CMRECC.AMOUNT) AS AMOUNT
            		FROM MRI.CMRECC
            		INNER JOIN MRI.INCH_LCP_REPORTS
            		ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
            		JOIN MRI.LEAS 
            		ON CMRECC.LEASID = LEAS.LEASID
            		WHERE CMRECC.EFFDATE = (
            					SELECT MAX(IC.EFFDATE) AS EFFDATE
            					FROM MRI.CMRECC IC 
            					WHERE to_date(LEAS.RENTSTRT) <= $rptdt
            					AND IC.BLDGID=CMRECC.BLDGID 
            					AND IC.LEASID=CMRECC.LEASID 
            					AND IC.INCCAT=CMRECC.INCCAT 
            					--AND IC.EFFDATE <= SYSDATETIME() /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					AND 
            					(
            						(IC.ENDDATE IS NULL AND IC.EFFDATE <= $rptdt)
            						OR 
            						IC.ENDDATE >= to_date($rptdt)
            					) /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					--AND IC.INEFFECT = 'Y'
            			)
            			AND INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT')
            		GROUP BY
            			CMRECC.LEASID					
            	) RENT	
            		ON leas.LEASID = RENT.LEASID
            	where
            		(leas.EXECDATE <= $rptdt or leas.RENTSTrt <= $rptdt)
            		and
            		(
            			(COALESCE(leas.VACATE,leas.EXPIR) >= $rptdt or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
            			OR
            			(leas.EXPIR < $rptdt and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers */
            			OR
            			(
            				(
            					leas.VACATE >= $rptdt 
            					or 
            					(leas.VACATE < $rptdt and leas.STOPBILLDATE is NULL)
            					or 
            					leas.STOPBILLDATE >= $rptdt
            				)
            				AND leas.OCCPSTAT != 'I'
            			)
            		)
            		and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
            
            	UNION ALL
            
            	SELECT /* future leases */
            	  RTRIM(leas.BLDGID) AS BLDG_ID
            	,	RTRIM(leas.SUITID) AS SUITE
            	,	RTRIM(leas.MOCCPID) as MASTER_ID
            	,	concat(RTRIM(leas.OCCPNAME), 
            			case 
            				when TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' and LEAS.RENTSTRT IS NOT NULL
            					then concat(' ',MONTH(LEAS.RENTSTRT),'/',DAY(LEAS.RENTSTRT),'/',YEAR(LEAS.RENTSTRT)) 
            				when IFNULL(leas.VACATE,to_date('2999-12-31','yyyy-mm-dd')) < $rptdt
            					then concat(' Stop Bill Date: ', case when leas.STOPBILLDATE IS NULL THEN CONCAT('NA Vacate: ',MONTH(LEAS.VACATE),'/',DAY(LEAS.VACATE),'/',YEAR(LEAS.VACATE)) ELSE CONCAT(MONTH(LEAS.STOPBILLDATE),'/',DAY(LEAS.STOPBILLDATE),'/',YEAR(LEAS.STOPBILLDATE)) END) 
            				ELSE ''
            			end
            		) as OCCUPANT_NAME
            	,	to_date(leas.RENTSTRT) as RENT_START
            	,	to_date(leas.EXPIR) as EXPIR
            	,	CAST(IFNULL(SSQF_TYPE.SQFT,0) AS INT) AS SQFT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE IFNULL(RENT.AMOUNT,0) END) AS BASE_RENT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE ROUND(IFNULL(RENT.AMOUNT,0)*12/(case when SSQF_TYPE.SQFT = 0 then 1 else SSQF_TYPE.SQFT END),2) END) AS RATE_PSF
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE NULL END) AS ADDL_RENTS
            	,	RTRIM(leas.LEASID) AS LEASID
            	,	RTRIM(suit.ADDRESS) as ADDRESS
            	,	RTRIM(bldg.CITY) as CITY
            	,	RTRIM(bldg.STATE) as STATE
            	,	RTRIM(bldg.ZIPCODE) as ZIPCODE
            	,	trim(bldg.MAP) as MAP_HL
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE trim(MNGR.MNGRNAME) END AS CORP_PM
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  			        ELSE trim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS CORP_PM_EMAIL
            	from MRI.LEAS
            	left join MRI.SUIT
            	on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
            	left join MRI.BLDG
            	on leas.BLDGID = bldg.BLDGID
            	left join MRI.MNGR
            	on bldg.MNGRID = mngr.MNGRID
            	LEFT JOIN MRI.TB_CM_SUITETYPE
            	on suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            	LEFT JOIN 
            	(
            		SELECT *
            		FROM MRI.SSQF
            		WHERE SSQF.EFFDATE = (
            			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
            			)
            	) SSQF_TYPE
            		ON suit.SUITID = SSQF_TYPE.SUITID
            		AND suit.BLDGID = SSQF_TYPE.BLDGID
            	left join								
            	(								
            		SELECT CMRECC.LEASID
            		,	SUM(CMRECC.AMOUNT) AS AMOUNT
            		FROM MRI.CMRECC
            		INNER JOIN MRI.INCH_LCP_REPORTS
            		ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
            		JOIN MRI.LEAS 
            		ON CMRECC.LEASID = LEAS.LEASID
            		WHERE CMRECC.EFFDATE = (
            					SELECT MAX(IC.EFFDATE) AS EFFDATE
            					FROM MRI.CMRECC IC 
            					WHERE LEAS.RENTSTRT <= $rptdt
            					AND IC.BLDGID=CMRECC.BLDGID 
            					AND IC.LEASID=CMRECC.LEASID 
            					AND IC.INCCAT=CMRECC.INCCAT 
            					--AND IC.EFFDATE <= SYSDATETIME() /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					AND 
            					(
            						(IC.ENDDATE IS NULL AND IC.EFFDATE <= $rptdt)
            						OR 
            						IC.ENDDATE >= to_date($rptdt)
            					) /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					--AND IC.INEFFECT = 'Y' /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            			)
            			AND INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT')
            		GROUP BY
            			CMRECC.LEASID					
            	) RENT	
            		ON leas.LEASID = RENT.LEASID
            	left join
            	(
            		select
            			leas.BLDGID
            		,	RTRIM(leas.SUITID) AS SUITID
            		,	RTRIM(leas.MOCCPID) as MOCCPID
            		,	RTRIM(leas.LEASID) AS LEASID
            		from MRI.LEAS
            		left join MRI.SUIT
            		on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
            		LEFT JOIN MRI.TB_CM_SUITETYPE
            			ON suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            		where
            			(leas.EXECDATE <= $rptdt or leas.RENTSTrt <= $rptdt)
            			and
            			(
            				(COALESCE(leas.VACATE,leas.EXPIR) >= $rptdt or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
            				OR
            				(leas.EXPIR < $rptdt and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers */
            			)
            			and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
            	) exist
            	on leas.BLDGID = exist.BLDGID
            		and leas.SUITID = exist.SUITID
            		and leas.MOCCPID = exist.MOCCPID
            		and leas.LEASID = exist.LEASID /* includes renewals of existing MOCCPID that are under a new LEASID */
            	where (leas.RENTSTRT >= $rptdt or leas.RENTSTRT is NULL)
            	and (leas.EXECDATE is NULL or leas.EXECDATE >= $rptdt)
            	and exist.MOCCPID is NULL
            
            	UNION ALL
            	
            	SELECT /* VACANT */
            	  RTRIM(SUIT.BLDGID) AS BLDG_ID
            	,	SUIT.SUITID AS SUITE
            	,	NULL as MASTER_ID
            	,	'Vacant' as OCCUPANT_NAME
            	,	NULL as RENT_START
            	,	NULL as EXPIR
            	,	CAST(IFNULL(SSQF_TYPE.SQFT,0) AS INT) AS SQFT
            	,	NULL AS BASE_RENT
            	,	NULL AS RATE_PSF
            	,	NULL AS ADDL_RENTS
            	,	NULL AS LEASID
            	,	RTRIM(suit.ADDRESS) as ADDRESS
            	,	RTRIM(bldg.CITY) as CITY
            	,	RTRIM(bldg.STATE) as STATE
            	,	RTRIM(bldg.ZIPCODE) as ZIPCODE
            	,	trim(bldg.MAP) as MAP_HL
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE trim(MNGR.MNGRNAME) END AS CORP_PM
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  			        ELSE trim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS CORP_PM_EMAIL
            	FROM MRI.SUIT
            	LEFT JOIN MRI.BLDG
            	ON SUIT.BLDGID = BLDG.BLDGID
            	left join MRI.MNGR
            	on bldg.MNGRID = mngr.MNGRID
            	left join
            	(
            		SELECT *
            		FROM MRI.SSQF
            		WHERE SSQF.EFFDATE = 
            			(
            				SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= $rptdt
            			)
            	) SSQF_TYPE
            	ON SUIT.SUITID = SSQF_TYPE.SUITID
            		AND SUIT.BLDGID = SSQF_TYPE.BLDGID
            	LEFT JOIN 
            	(
            		SELECT LEAS.*
            		FROM MRI.LEAS
            		JOIN MRI.SUIT
            		ON LEAS.BLDGID = SUIT.BLDGID
            		AND LEAS.SUITID = SUIT.SUITID
            		LEFT JOIN MRI.TB_CM_SUITETYPE
            		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            		WHERE 
            			--(leas.EXECDATE <= $rptdt or leas.RENTSTrt <= $rptdt)
            			leas.RENTSTrt <= $rptdt
            			and
            			(
            				(COALESCE(leas.VACATE,leas.EXPIR) >= $rptdt or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
            				OR
            				(leas.EXPIR < $rptdt and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers NOT counted as Vacant in MRI Rent Roll*/
            			)
            			and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
            	) LEASED
            	ON SUIT.BLDGID = LEASED.BLDGID
            	AND SUIT.SUITID = LEASED.SUITID
            	LEFT JOIN MRI.TB_CM_SUITETYPE
            	ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            	WHERE LEASED.SUITID IS NULL
            		AND (BLDG.INACTIVE is NULL or BLDG.INACTIVE = 'N') /* Active Buildings only */
            		AND
            		(
            			(
            				TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL 
            				or 
            				(TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' and TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'N') 
            			) /* Suitetypeusage is NULL or not in 'E' or 'N' */
            			or
            			(
            				TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' 
            				AND SSQF_TYPE.SQFT > 0 
            				AND SSQF_TYPE.SQFTTYPE = 'GLA'
            			)
            		)
            		AND NOT (SUIT.SUITETYPE_MRI is NULL and (COALESCE(SSQF_TYPE.SQFT,0) = 0 or SSQF_TYPE.SQFTTYPE != 'GLA') )
            ) u
            order by
            	u.BLDG_ID
            ,	case when u.Occupant_Name = 'Vacant' then 0 else 1 end
            ,	u.SUITE
        """
        
    def _get_pm_phone_query(self):
        """Get PM phone query"""
        return f"""
        SELECT
            email as "PM_Email",
            '(847) 904-'||extension as "PM_Phone"
        FROM {self.csm_db}.CORPORATE.ab_employees
        WHERE status not in ('R','T')
        AND extension is not null
        AND email is not null
        """
    
    
    
    def _get_lcp_assign_query(self):
        """Get LCP assignment query"""
        return f"""

          SELECT  /* SNOWFLAKE version */
          to_char(a.BLDG) AS "Bldg_ID"
          , a.RM
          , case when rm.phone is NULL then NULL
              else '('||substr(rm.phone,1,3)||') '||substr(rm.phone,4,3)||'-'||substr(rm.phone,7,4) end as "RM Phone"
          , rm.email as "RM Email"
          , a.SUPPORT_LEASING AS "Support Leasing"
          , case when a.SUPPORT_LEASING = 'BRH' then '(*************' 
              when sl.phone is NULL then NULL
              else '('||substr(sl.phone,1,3)||') '||substr(sl.phone,4,3)||'-'||substr(sl.phone,7,4) end as "SL Phone"
          , case when a.SUPPORT_LEASING = 'BRH' then '<EMAIL>' else sl.email end as "SL Email"
          , a.SUPPORT_PROP_MGMT AS "Support Property Management"
          , case when rm.phone is NULL then NULL
              else '('||substr(spm.phone,1,3)||') '||substr(spm.phone,4,3)||'-'||substr(spm.phone,7,4) end as "SPM Phone"
          , spm.email as "SPM Email"
          from {self.csm_db}.CORPORATE.lcp_assignments_sean a
          left join {self.csm_db}.CORPORATE.ab_employees rm
          on upper(a.rm) = upper(rm.fname)||' '||upper(rm.lname)
          and rm.status not in ('T','R','D')
          left join {self.csm_db}.CORPORATE.ab_employees sl
          on upper(a.SUPPORT_LEASING) = upper(sl.fname)||' '||upper(sl.lname)
          and sl.status not in ('T','R','D')
          left join {self.csm_db}.CORPORATE.ab_employees spm
          on upper(a.SUPPORT_PROP_MGMT) = upper(spm.fname)||' '||upper(spm.lname)
          and spm.status not in ('T','R','D')
        """    
    
    

    def _get_acquis_query(self):
        """Get ACQUIS building query"""
        return f"""
        SELECT /* SNOWFLAKE VERSION */
          CASE WHEN TRY_CAST(SUIT.BLDGID AS INT) IS NULL THEN SUIT.BLDGID ELSE TO_CHAR(TRY_CAST(SUIT.BLDGID AS INT)) END AS "Bldg_ID"
        ,	NULL as "HRG Interested"
        ,	RTRIM(SUIT.SUITID) AS "Suite"
        ,	NULL as "Master_ID"
        ,	RTRIM(SUIT.ADDRESS) as "Occupant_Name"
        ,	TO_DATE(NULL) as "Rent_Start"
        ,	TO_DATE(NULL) as "Expiration"
        ,	TRY_CAST(NULL AS INT) AS "Sqft"
        ,	NULL AS "Base_Rent"
        ,	NULL AS "Rate_PSF"
        ,	NULL AS "Addl_Rents"
        ,	NULL AS "Lease_ID"
        ,	NULL as "Address"
        ,	NULL as "City"
        ,	NULL AS "State"
        ,	NULL AS "Postal Code"
        ,	NULL as "MAP HYPERLINK"
        ,	NULL AS "Corporate PM"
        ,	NULL AS "PM_Email"
        from MRI.SUIT 
        left join MRI.BLDG
        on SUIT.BLDGID = BLDG.BLDGID
        LEFT JOIN MRI.TB_CM_SUITETYPE
        on SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
        where SUIT.BLDGID = 'ACQUIS'
        and (BLDG.INACTIVE is NULL or BLDG.INACTIVE = 'N')
        and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
        order by SUIT.SUITID
        """    
    
    def compare_data_with_prior_week(self, current_data):
        """Compare current week's data with previous week and send change notifications"""
        # self.report_date_text = today.strftime("%m-%d-%Y")
        # self.report_date_prior_text = (today - timedelta(days=7)).strftime("%m-%d-%Y")

        if not self.config.comp_prior:
            # logging.info("Skipping weekly comparison (not Tuesday)")
            self.sf.log_audit_in_db(log_msg=f"compare_data_with_prior_week() - Skipping weekly comparison (not Tuesday. Today is {self.config.dow}). weekday_number: {self.config.weekday_number}. WEEKDAY_NUMBER_TO_COMPARE: {self.config.WEEKDAY_NUMBER_TO_COMPARE}. report_date_text: {self.config.report_date_text}, report_date_prior_text: {self.config.report_date_prior_text}", process_type=self.report_name, script_file_name=__file__)
            return False

        # logging.info("Comparing with previous week's data...")
        self.sf.log_audit_in_db(log_msg=f"compare_data_with_prior_week() - Comparing with previous week's({self.config.report_date_prior_text}) data...Today is {self.config.dow} weekday_number: {self.config.weekday_number}. WEEKDAY_NUMBER_TO_COMPARE: {self.config.WEEKDAY_NUMBER_TO_COMPARE}. report_date_text: {self.config.report_date_text}", process_type=self.report_name, script_file_name=__file__)
            
        # Create reports directory
        report_path = self.config.log_path / self.config.rpt_folder
        report_path.mkdir(parents=True, exist_ok=True)
        
        # Add vacate date to current data for CSV
        vacate_query = f"""
            SELECT /* SNOWFLAKE version */
                CASE WHEN TRY_CAST(leas.BLDGID AS INT) IS NULL THEN leas.BLDGID ELSE TO_CHAR(TRY_CAST(leas.BLDGID AS INT)) END as "Bldg ID"
            ,	leas.SUITID as "Suite"
            , leas.LEASID as "Lease ID"
            ,	to_date(leas.vacate) as "Vacate Date"
            from MRI.LEAS
            where leas.VACATE is not NULL
        """
        
        vacate_data = self.execute_query(vacate_query)
        
        if vacate_data is not None:
            # Join vacate dates and prepare CSV data
            csv_data = current_data.copy()
            
            # Merge vacate data
            csv_data = csv_data.merge(vacate_data, on=['Bldg ID', 'Suite', 'Lease ID'], how='left')
            
            # Select columns for comparison CSV
            csv_columns = ['Bldg ID', 'Suite', 'Master ID', 'Occupant Name', 
                          'Rent Start', 'Expiration', 'Vacate Date', 'Address', 'City', 'State']
            
            csv_data = csv_data[csv_columns]
            
            # Save current week's CSV
            current_filename = f"{self.config.my_sheets[0]} {self.config.report_date_text}.csv" # .replace(" ", "_")
            current_filepath = report_path / current_filename
            csv_data.to_csv(current_filepath, index=False, na_rep='')
            
            # Check for previous week's file
            prior_filename = f"{self.config.my_sheets[0]} {self.config.report_date_prior_text}.csv" #.replace(" ", "_")

            prior_filepath = report_path / prior_filename
            
            if prior_filepath.exists():
                # Read previous week's data. 
                # Use keep_default_na=False to keep NA values. 
                # Not using this was removing NA values from prior_data, which was triggering an unnecessary record change.
                prior_data = pd.read_csv(prior_filepath, keep_default_na=False)
                
                # Compare data
                if self.config.inc_name_chgs:
                    compare_cols = csv_columns[:-3]  # Exclude address columns but include name
                else:
                    compare_cols = [col for col in csv_columns[:-3] if col != 'Occupant Name']
                
                self.sf.log_audit_in_db(log_msg=f"compare_data_with_prior_week() - compare_cols: {compare_cols}", process_type=self.report_name, script_file_name=__file__, log_type="Info")
                
                # Find differences
                current_subset = csv_data[compare_cols].fillna('')
                prior_subset = prior_data[compare_cols].fillna('')

                # current_subset.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/rent_roll_current_subset.csv', index=False)
                # prior_subset.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/rent_roll_prior_subset.csv', index=False)
                # prior_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/rent_roll_prior_data_2.csv', index=False)
                
                # Convert to string for comparison
                for col in compare_cols:
                    if col in current_subset.columns:
                        current_subset[col] = current_subset[col].astype(str)
                    if col in prior_subset.columns:
                        prior_subset[col] = prior_subset[col].astype(str)
                
                # Find new records (in current but not in prior)
                merged_current = current_subset.merge(prior_subset, how='outer', indicator=True)
                diff_current = merged_current[merged_current['_merge'] == 'left_only'].drop('_merge', axis=1)
                
                # Find removed records (in prior but not in current)
                merged_prior = prior_subset.merge(current_subset, how='outer', indicator=True)
                diff_prior = merged_prior[merged_prior['_merge'] == 'left_only'].drop('_merge', axis=1)
                
                # Add roll dates
                if not diff_current.empty:
                    diff_current['Roll Date'] = self.config.report_date_text
                
                if not diff_prior.empty:
                    diff_prior['Roll Date'] = self.config.report_date_prior_text
                
                # Combine differences
                if not diff_current.empty or not diff_prior.empty:
                    diff_combined = pd.concat([diff_current, diff_prior], ignore_index=True)

                    # print(f"diff_combined: \n{diff_combined}\n\ndiff_current: \n{diff_current}\n\ndiff_prior({prior_filepath}): \n{diff_prior}")
                    # exit(1)
                    
                    # Add occupant names back if they were excluded
                    if not self.config.inc_name_chgs:
                        # Add names from original data
                        current_names = csv_data[['Master ID', 'Occupant Name']].drop_duplicates()
                        prior_names = prior_data[['Master ID', 'Occupant Name']].drop_duplicates()
                        all_names = pd.concat([current_names, prior_names]).drop_duplicates()
                        
                        diff_combined = diff_combined.merge(all_names, on='Master ID', how='left')
                    
                    # Save changes file
                    changes_filename = f"{self.config.my_sheets[0]} - Changes.csv"
                    changes_filepath = report_path / changes_filename
                    diff_combined.to_csv(changes_filepath, index=False, na_rep='')
                    
                    # Send email notification
                    self._send_changes_email(diff_combined, changes_filepath)
                    
                    # logging.info(f"Found {len(diff_combined)} changes, email sent")
                    self.sf.log_audit_in_db(log_msg=f"compare_data_with_prior_week() - Found {len(diff_combined)} changes, email sent", process_type=self.report_name, script_file_name=__file__)
                else:
                    # logging.info("No changes found between weeks")
                    self.sf.log_audit_in_db(log_msg=f"compare_data_with_prior_week() - No changes found between weeks", process_type=self.report_name, script_file_name=__file__)
            else:
                # logging.warning(f"Previous week file not found: {prior_filepath}")
                self.sf.log_audit_in_db(log_msg=f"compare_data_with_prior_week() - Previous week file not found: {prior_filepath}", process_type=self.report_name, script_file_name=__file__, log_type="warning")

                
                # Send warning email
                body = f"""
                <p>This is an automated email to inform you that the previous week's Rent Roll 
                comparison file '{prior_filename}' was not found.</p>
                <p>Expected location: {prior_filepath}</p>
                <p>The routine won't be able to determine weekly changes to the Rent Roll.</p>
                <p>This week's comparison file has been saved.</p>
                {self.get_signature()}
                """
                
                email_client.send_email(
                    recipient=self.config.warn_recip,
                    subject=f"{self.config.report_name}: Missing comparison file",
                    body=body,
                    test=self.config.testing_emails
                )
        
        return True
    
    def _send_changes_email(self, changes_df, attachment_path):
        """Send email with rent roll changes"""
        
        # Create HTML table for email body (limit to 40 rows)
        if len(changes_df) <= 40:
            table_html = changes_df.to_html(index=False, table_id="changes_table", 
                                          classes="table table-striped")
            body_table = f"<p>{table_html}</p>"
        else:
            body_table = f"""
            <p><b>There are {len(changes_df)} results, see attached file for them. 
            They are ONLY in the attachment in order to keep the body of this email shorter.</b></p>
            """
        
        # Construct email body
        body = f"""
        <p>A file with the <b>RENT ROLL</b> changes is attached. 
        The file contains changes made since last {self.config.dow}.</p>
        
        <p>The results are sorted by BLDG, SUITE and then by ROLL DATE. 
        The ROLL DATE is the date the rent roll was posted. 
        Changes in a particular SUITE will normally have a row with 
        last week's data followed by a row with this week's data. 
        If it doesn't have a row for both dates, it was most likely 
        added or removed in the last week.</p>
        
        {body_table}
        
        <br>{self.get_signature()}
        """
        
        # Send email
        email_client.send_email(
            recipient=self.config.ap_changes_recip,
            subject="RENT ROLL Changes",
            body=body,
            # attachment=str(attachment_path),
            attachments=[attachment_path],
            test=self.config.testing_emails
        )

    def get_signature(self):
        """Get email signature"""
        return """
        <b><span style='font-weight:bold'>Steve Olson</span></b><br/>
        Sr. Analytics Mgr.<br/>
        <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
        2500 Lehigh Ave.<br/>
        Glenview, IL 60026<br/>
        Ph: 847/904-9043<br/>
        """


def main():
    """Main execution function"""
    pass
    

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 