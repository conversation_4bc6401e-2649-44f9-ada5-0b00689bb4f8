"""
Marcos Weekly Banking - Create Worksheets (SharePoint Version)

This script automates the weekly creation and management of Excel workbooks in SharePoint 
for <PERSON>'s Pizza banking transaction data entry. It creates a structured system where 
bank statement data can be manually entered into organized Excel workbooks, which then 
feeds into Snowflake database for further processing.

Converted from R script: MARCOS_Weekly_Banking-Create_Worksheets-SF LOAD.R
Author: Converted by AI Assistant
Date: 2025-06-30

Scheduling: Supposed to run on Wednesdays

Sample files for 2025:
    https://highlandventuresltd442.sharepoint.com/sites/finance/Shared%20Documents/Forms/AllItems.aspx?id=%2Fsites%2Ffinance%2FShared%20Documents%2FMarcos%20Bank%20Transactions%2FWeekly%20Marcos%20Bank%20Transactions%2F2025&newTargetListUrl=%2Fsites%2Ffinance%2FShared%20Documents&viewpath=%2Fsites%2Ffinance%2FShared%20Documents%2FForms%2FAllItems%2Easpx&ovuser=704b8542%2Dab1c%2D45fb%2Da7d6%2D90964ea9c759%2Cjgarifuna%40hv%2Eltd&OR=Teams%2DHL&CT=*************&clickparams=****************************************************************************************************************
    
"""

import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import tempfile
import uuid

# Add the libs directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'libs'))

from libs.snowflake_helper import SnowflakeHelper
from libs.sharepoint_helper import SharePointClient
from libs.excel_helper import SharePointExcelOnline


class WeeklyBankingCreateWorksheetsSF:
    """
    Class to handle the creation of weekly banking worksheets in SharePoint.
    Converts from Google Sheets to SharePoint Excel Online workbooks.
    """

    PROCESS_NAME = 'Marcos Weekly Banking - Create Worksheets'
    PROCESS_TYPE = 'Create'
    LOG_TO_DB = True

    # SharePoint configuration - aligned with transaction loader
    SHAREPOINT_FINANCE_URL = 'https://highlandventuresltd442.sharepoint.com/sites/finance'
    SHAREPOINT_BANKING_FOLDER = 'Documents/Marcos Bank Transactions/Weekly Marcos Bank Transactions'
    DIRECTORY_FOLDER_NAME = 'Directory Files'

    # Database configuration
    ENVM = os.environ['DATABASE_ENVM']
    DATABASE_CSM_DATABASE = os.environ['DATABASE_CSM_DATABASE']
    DATABASE_RAW_DATABASE = os.environ['DATABASE_RAW_DATABASE']


    DATABASE_CORPORATE_SCHEMA = 'CORPORATE' #os.environ['DATABASE_CORPORATE_SCHEMA']
    LOG_SCHEMA = None # os.environ['DATABASE_LOG_SCHEMA']
    LOG_TABLE = None # os.environ['DATABASE_LOG_TABLE']
    
    DATABASE_WAREHOUSE = os.environ['DATABASE_WAREHOUSE']
    DATABASE_ROLE = os.environ['DATABASE_ROLE']
    DATABASE_USER = os.environ['DATABASE_USER']
    DATABASE_PASSWORD = os.environ['DATABASE_PASSWORD']

    PRINT_STATEMENTS = True
    TEST_MODE = False  # Set to True to override date to 6/18/2025 for testing

    def __init__(self):
        """Initialize the worksheet creator with necessary clients and configurations."""
        
        # Initialize clients
        self.sf = SnowflakeHelper()
        self.LOG_SCHEMA = self.sf.batch_audit_schema
        self.LOG_TABLE = self.sf.moms_log_tbl

        # Initialize SharePoint client with error suppression and authentication
        self.sp = SharePointClient(suppress_false_errors=True)
        self.sp.authenticate()
        self.excel = SharePointExcelOnline(sharepoint_client=self.sp)

        self.log_buffer = []
        
        # Date calculations
        self.setup_date_ranges()
        
        # Storage for processing results
        self.expected_files_sheets = pd.DataFrame()
        self.processing_results = []
        self.successful_uploads = []  # Track successful uploads for batch reporting

    def setup_date_ranges(self):
        """
        Setup date ranges for weekly processing.
        Calculates 13-day period around current week (Sunday to Tuesday).
        """
        if self.TEST_MODE:
            # Override for testing with existing SharePoint files
            query_date = datetime(2025, 5, 15)
            self.log_audit_in_db(
                log_msg='TEST_MODE: Using fixed date 5/15/2025 for testing',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
        else:
            query_date = datetime.now()

        # Calculate week boundaries (Sunday start)
        days_since_sunday = query_date.weekday() + 1  # Monday = 0, so Sunday = 6, adjust
        if days_since_sunday == 7:
            days_since_sunday = 0
        
        week_start = query_date - timedelta(days=days_since_sunday)
        
        # 13-day period: Sunday 11 days before to Tuesday 2 days after week end
        self.query_start_date = week_start - timedelta(days=11)
        self.query_end_date = week_start + timedelta(days=9)  # Sunday + 9 = Tuesday of next week
        
        # Format dates for different uses
        self.report_year = str(self.query_start_date.year)
        self.rpt_start = self.query_start_date.strftime('%Y%m%d')
        self.rpt_end = self.query_end_date.strftime('%Y%m%d')
        self.email_start = self.query_start_date.strftime('%m/%d/%Y')
        self.email_end = self.query_end_date.strftime('%m/%d/%Y')
        self.email_execute_date = (self.query_end_date + timedelta(days=1)).strftime('%a, %b %d')
        
        # Report filename
        self.report_filename_no_ext = f"MP Weekly Banking Files Created {self.rpt_start}-{self.rpt_end}"
        self.report_filename = f"{self.report_filename_no_ext}.xlsx"

    def query_expected_files_sheets(self) -> bool:
        """
        Query Snowflake to get expected files and sheets configuration.
        
        Returns:
            True if query successful, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg='Querying Snowflake for expected files and sheets configuration',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Format dates for Oracle-style date format in Snowflake
            start_date_str = self.query_start_date.strftime('%d-%b-%y').upper()
            end_date_str = self.query_end_date.strftime('%d-%b-%y').upper()

            query = f"""
                SELECT M.NAME_LONG AS BANK,
                    CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' 
                         THEN 'JUST THIS STORE in this sheet' 
                         ELSE 'MULTIPLE stores in this sheet' END AS IND_OR_MUL,
                    L.LOC_NUM AS LOCATION,
                    NULL as EXCEL_LINK,
                    TO_CHAR(TO_DATE('{start_date_str}','DD-MON-YY'), 'YYYYMMDD')||'-'||
                    TO_CHAR(TO_DATE('{end_date_str}','DD-MON-YY'), 'YYYYMMDD')||' '||M.NAME_SHORT AS FILENAME,
                    CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' 
                         THEN M.NAME_SHORT||'_'||L.LOC_NUM 
                         ELSE M.NAME_SHORT END AS SHEETNAME,
                    L.ACCOUNT AS ACCOUNT,
                    CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' 
                         THEN NULL 
                         ELSE NVL(L.IMPORT_ALT_ID, L.ACCOUNT) END AS IDENTIFIER,
                    L.B_ID_LOCAL,
                    M.LOGIN_URL as BANK_LOGIN_URL
                FROM {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.MP_BANK_ID_LOCAL L
                JOIN {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.MP_BANK_ID_MASTER M
                    ON L.B_ID_MASTER = M.B_ID_MASTER
                WHERE 
                    L.S_DATE <= TO_DATE('{end_date_str}','DD-MON-YY')
                    AND (L.E_DATE IS NULL OR L.E_DATE >= TO_DATE('{start_date_str}','DD-MON-YY'))
                ORDER BY M.NAME_LONG, M.IMPORT_METHOD, L.LOC_NUM
            """
            # print(query)
            # exit()

            query_result = self.sf.execute_snowflake_query(query)

            # Convert list result to DataFrame
            if isinstance(query_result, list) and len(query_result) > 0:
                # The query result should be a list of dictionaries with column names as keys
                self.expected_files_sheets = pd.DataFrame(query_result)
                self.expected_files_sheets.to_csv('expected_files_sheets.csv', index=False)

                # Log the columns we received for debugging
                self.log_audit_in_db(
                    log_msg=f'Query returned {len(query_result)} rows with columns: {list(self.expected_files_sheets.columns)}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
            else:
                self.expected_files_sheets = pd.DataFrame()

            if self.expected_files_sheets.empty:
                self.log_audit_in_db(
                    log_msg='No expected files returned by Snowflake query',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return False

            self.log_audit_in_db(
                log_msg=f'Found {len(self.expected_files_sheets)} expected file/sheet combinations',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            
            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error querying expected files and sheets: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def ensure_sharepoint_folder_structure(self) -> bool:
        """
        Ensure required SharePoint folder structure exists.
        Creates year folder and directory folder if they don't exist.
        
        Returns:
            True if folder structure is ready, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg='Ensuring SharePoint folder structure exists',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Check if base banking folder exists
            self.log_audit_in_db(
                log_msg=f'Attempting to access SharePoint folder: {self.SHAREPOINT_BANKING_FOLDER}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Try different folder path variations that might work
            folder_path_variations = [
                self.SHAREPOINT_BANKING_FOLDER,  # Documents/Marcos Bank Transactions/Weekly Marcos Bank Transactions
                'Shared Documents/Marcos Bank Transactions/Weekly Marcos Bank Transactions',
                'Marcos Bank Transactions/Weekly Marcos Bank Transactions'
            ]

            base_folder = None
            working_path = None

            for path_variation in folder_path_variations:
                self.log_audit_in_db(
                    log_msg=f'Trying folder path: {path_variation}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                try:
                    base_folder = self.sp.get_folder(
                        site_url=self.SHAREPOINT_FINANCE_URL,
                        folder_path=path_variation
                    )

                    if base_folder:
                        working_path = path_variation
                        self.log_audit_in_db(
                            log_msg=f'Successfully accessed folder using path: {path_variation}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME
                        )
                        # Update the working path for future use
                        self.SHAREPOINT_BANKING_FOLDER = working_path
                        break
                except Exception as e:
                    self.log_audit_in_db(
                        log_msg=f'Failed to access folder with path {path_variation}: {e}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Warning'
                    )

            if not base_folder:
                self.log_audit_in_db(
                    log_msg=f'Base SharePoint folder not found: {self.SHAREPOINT_BANKING_FOLDER}. This folder needs to be created manually in SharePoint before running this script.',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )

                # Provide detailed instructions for folder creation
                folder_creation_msg = f"""
REQUIRED SHAREPOINT FOLDER STRUCTURE:
The following folder structure needs to be created manually in SharePoint:

Site: {self.SHAREPOINT_FINANCE_URL}
Base Path: {self.SHAREPOINT_BANKING_FOLDER}

Required Subfolders:
1. {self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}/ (for Excel workbooks)
2. {self.SHAREPOINT_BANKING_FOLDER}/{self.DIRECTORY_FOLDER_NAME}/ (for directory files)

STEPS TO CREATE:
1. Go to {self.SHAREPOINT_FINANCE_URL}
2. Navigate to Documents library
3. Create folder: "Marcos Bank Transactions"
4. Inside that, create folder: "Weekly Marcos Bank Transactions"
5. Inside "Weekly Marcos Bank Transactions", create folders: "{self.report_year}" and "{self.DIRECTORY_FOLDER_NAME}"

Once these folders exist, re-run this script.
                """

                self.log_audit_in_db(
                    log_msg=folder_creation_msg,
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Info'
                )

                return False

            # Check/create year folder
            year_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}"
            year_folder = self.sp.get_folder(
                site_url=self.SHAREPOINT_FINANCE_URL,
                folder_path=year_folder_path
            )
            
            if not year_folder:
                self.log_audit_in_db(
                    log_msg=f'Creating year folder: {self.report_year}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
                # Create year folder (implementation would depend on SharePoint helper capabilities)
                # For now, log that it needs to be created manually
                self.log_audit_in_db(
                    log_msg=f'Year folder {self.report_year} needs to be created manually in SharePoint',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )

            # Check/create directory folder
            directory_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.DIRECTORY_FOLDER_NAME}"
            directory_folder = self.sp.get_folder(
                site_url=self.SHAREPOINT_FINANCE_URL,
                folder_path=directory_folder_path
            )
            
            if not directory_folder:
                self.log_audit_in_db(
                    log_msg=f'Directory folder {self.DIRECTORY_FOLDER_NAME} needs to be created manually in SharePoint',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error ensuring SharePoint folder structure: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def check_existing_workbooks(self) -> Tuple[List[str], List[str]]:
        """
        Check which expected workbooks already exist in SharePoint.

        Returns:
            Tuple of (existing_files, missing_files)
        """
        try:
            self.log_audit_in_db(
                log_msg='Checking for existing workbooks in SharePoint',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Get unique expected filenames
            expected_files = self.expected_files_sheets['FILENAME'].unique().tolist()

            # Check year folder for existing files
            year_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}"
            year_folder = self.sp.get_folder(
                site_url=self.SHAREPOINT_FINANCE_URL,
                folder_path=year_folder_path
            )

            existing_files = []
            missing_files = []

            if year_folder and 'items' in year_folder:
                existing_file_names = [item.get('name', '') for item in year_folder['items']
                                     if 'file' in item and item.get('name', '').endswith('.xlsx')]

                for expected_file in expected_files:
                    if f"{expected_file}.xlsx" in existing_file_names:
                        existing_files.append(expected_file)
                    else:
                        missing_files.append(expected_file)
            else:
                missing_files = expected_files

            self.log_audit_in_db(
                log_msg=f'Found {len(existing_files)} existing workbooks, {len(missing_files)} need to be created',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return existing_files, missing_files

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error checking existing workbooks: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return [], self.expected_files_sheets['FILENAME'].unique().tolist()

    def create_excel_workbook(self, filename: str, required_sheets: List[str]) -> bool:
        """
        Create a new Excel workbook in SharePoint with required worksheets.

        Args:
            filename: Name of the workbook to create
            required_sheets: List of worksheet names to create

        Returns:
            True if successful, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg=f'Creating Excel workbook: {filename} with {len(required_sheets)} sheets',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Create workbook locally first, then upload to SharePoint
            year_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}"

            # Create Excel workbook locally using openpyxl
            wb = openpyxl.Workbook()

            # Remove default sheet
            if 'Sheet' in wb.sheetnames:
                wb.remove(wb['Sheet'])

            # Add required sheets with banking transaction headers
            for sheet_name in required_sheets:
                ws = wb.create_sheet(title=sheet_name)

                # Add headers for banking transactions
                headers = ['Date', 'Description', 'Amount', 'Balance', 'Notes']
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    # Style the header
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # Auto-adjust column widths
                for col in range(1, len(headers) + 1):
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                wb.save(tmp_file.name)
                tmp_file_path = tmp_file.name

            try:
                # Add comprehensive logging to debug the SharePoint upload issue
                self.log_audit_in_db(
                    log_msg=f'=== UPLOAD DEBUG START for {filename} ===',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Log current SharePoint configuration
                self.log_audit_in_db(
                    log_msg=f'SharePoint Site: {self.SHAREPOINT_FINANCE_URL}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                self.log_audit_in_db(
                    log_msg=f'Base Banking Folder: {self.SHAREPOINT_BANKING_FOLDER}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                self.log_audit_in_db(
                    log_msg=f'Report Year: {self.report_year}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                self.log_audit_in_db(
                    log_msg=f'Local file path: {tmp_file_path}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                self.log_audit_in_db(
                    log_msg=f'Remote filename: {filename}.xlsx',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Try multiple upload approaches with detailed logging
                upload_attempts = [
                    {
                        'name': 'Year Folder Path',
                        'path': f"{self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}"
                    },
                    {
                        'name': 'Base Folder Path',
                        'path': self.SHAREPOINT_BANKING_FOLDER
                    },
                    {
                        'name': 'Simple Year Path',
                        'path': self.report_year
                    }
                ]

                success = False
                upload_location = "FAILED"

                for attempt in upload_attempts:
                    if success:
                        break

                    self.log_audit_in_db(
                        log_msg=f'--- Attempting {attempt["name"]}: {attempt["path"]} ---',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )

                    try:
                        success = self.sp.upload_file_to_folder_path(
                            site_url=self.SHAREPOINT_FINANCE_URL,
                            local_file_path=tmp_file_path,
                            folder_path=attempt['path'],
                            remote_file_name=f"{filename}.xlsx"
                        )

                        if success:
                            upload_location = attempt['path']
                            self.log_audit_in_db(
                                log_msg=f'SUCCESS: {attempt["name"]} worked with path: {attempt["path"]}',
                                process_type=self.PROCESS_TYPE,
                                print_msg=self.PRINT_STATEMENTS,
                                script_file_name=self.PROCESS_NAME
                            )
                        else:
                            self.log_audit_in_db(
                                log_msg=f'FAILED: {attempt["name"]} failed with path: {attempt["path"]}',
                                process_type=self.PROCESS_TYPE,
                                print_msg=self.PRINT_STATEMENTS,
                                script_file_name=self.PROCESS_NAME,
                                log_type='Warning'
                            )
                    except Exception as upload_error:
                        self.log_audit_in_db(
                            log_msg=f'EXCEPTION: {attempt["name"]} threw error: {upload_error}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Error'
                        )

                self.log_audit_in_db(
                    log_msg=f'=== UPLOAD DEBUG END: Final result = {upload_location} ===',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                if success:
                    # Track successful upload for batch reporting
                    upload_info = {
                        'filename': f"{filename}.xlsx",
                        'location': upload_location,
                        'sheets_count': len(required_sheets),
                        'sheets': required_sheets.copy(),
                        'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    self.successful_uploads.append(upload_info)

                    self.log_audit_in_db(
                        log_msg=f'Successfully uploaded {filename}.xlsx to {upload_location}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )
                else:
                    self.log_audit_in_db(
                        log_msg=f'Upload failed for {filename}.xlsx to both year and base folders',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )
            finally:
                # Clean up temporary file
                if os.path.exists(tmp_file_path):
                    os.unlink(tmp_file_path)

            if success:
                self.log_audit_in_db(
                    log_msg=f'Successfully created workbook: {filename} with {len(required_sheets)} sheets: {required_sheets}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Add some delay to prevent SharePoint throttling
                time.sleep(1.5)

                return True
            else:
                self.log_audit_in_db(
                    log_msg=f'Failed to create workbook: {filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error creating workbook {filename}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def validate_existing_workbook_sheets(self, filename: str, required_sheets: List[str]) -> bool:
        """
        Validate that an existing workbook has all required sheets, create missing ones.

        Args:
            filename: Name of the workbook to validate
            required_sheets: List of required worksheet names

        Returns:
            True if validation successful, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg=f'Validating sheets in existing workbook: {filename}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            year_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}"
            workbook_path = f"{year_folder_path}/{filename}.xlsx"

            # Get existing sheet names using file access
            # First get the file info to get file ID
            file_info = self.sp.get_file(
                site_url=self.SHAREPOINT_FINANCE_URL,
                file_path=workbook_path
            )

            if not file_info:
                self.log_audit_in_db(
                    log_msg=f'Could not find workbook file: {filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Create Excel session using file ID
            excel_session = self.excel.get_excel_file_by_id(
                site_url=self.SHAREPOINT_FINANCE_URL,
                file_id=file_info['id']
            )

            if not excel_session:
                self.log_audit_in_db(
                    log_msg=f'Could not create Excel session for: {filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Get existing sheet names using list_worksheets
            worksheets = self.excel.list_worksheets(excel_session)
            existing_sheets = [ws.get('name', '') for ws in worksheets] if worksheets else []

            if existing_sheets is None:
                self.log_audit_in_db(
                    log_msg=f'Could not access workbook: {filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Check for missing sheets
            missing_sheets = [sheet for sheet in required_sheets if sheet not in existing_sheets]

            if missing_sheets:
                self.log_audit_in_db(
                    log_msg=f'Adding {len(missing_sheets)} missing sheets to {filename}: {missing_sheets}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Add missing sheets
                for sheet_name in missing_sheets:
                    success = self.excel.add_worksheet(
                        site_url=self.SHAREPOINT_FINANCE_URL,
                        workbook_path=workbook_path,
                        sheet_name=sheet_name
                    )

                    if not success:
                        self.log_audit_in_db(
                            log_msg=f'Failed to add sheet {sheet_name} to {filename}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Error'
                        )
                        return False

                    # Small delay between sheet creations
                    time.sleep(0.5)

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error validating workbook {filename}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def process_workbooks_and_sheets(self) -> bool:
        """
        Main method to process all workbooks and sheets.
        Creates missing workbooks and validates existing ones.

        Returns:
            True if processing successful, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg='Starting workbook and sheet processing',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Check existing workbooks
            existing_files, missing_files = self.check_existing_workbooks()

            # Process existing workbooks - validate sheets
            for filename in existing_files:
                # Get required sheets for this workbook
                workbook_data = self.expected_files_sheets[
                    self.expected_files_sheets['FILENAME'] == filename
                ]
                required_sheets = workbook_data['SHEETNAME'].unique().tolist()

                success = self.validate_existing_workbook_sheets(filename, required_sheets)
                if not success:
                    self.log_audit_in_db(
                        log_msg=f'Failed to validate workbook: {filename}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )

                # Update Excel links in the data
                self._update_excel_links_for_workbook(filename, workbook_data)

                # Add delay to prevent throttling
                time.sleep(1.0)

            # Create missing workbooks
            for filename in missing_files:
                # Get required sheets for this workbook
                workbook_data = self.expected_files_sheets[
                    self.expected_files_sheets['FILENAME'] == filename
                ]
                required_sheets = workbook_data['SHEETNAME'].unique().tolist()

                success = self.create_excel_workbook(filename, required_sheets)
                if success:
                    # Update Excel links in the data
                    self._update_excel_links_for_workbook(filename, workbook_data)
                else:
                    self.log_audit_in_db(
                        log_msg=f'Failed to create workbook: {filename}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )

                # Add delay to prevent throttling
                time.sleep(2.0)

            # Save updated DataFrame with populated Excel links
            self.expected_files_sheets.to_csv('expected_files_sheets.csv', index=False)

            self.log_audit_in_db(
                log_msg='Completed workbook and sheet processing. Updated expected_files_sheets.csv with Excel links.',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error processing workbooks and sheets: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def _update_excel_links_for_workbook(self, filename: str, workbook_data: pd.DataFrame):
        """
        Update Excel links in the expected_files_sheets DataFrame for a specific workbook.

        Args:
            filename: Name of the workbook
            workbook_data: DataFrame subset for this workbook
        """
        try:
            year_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.report_year}"
            workbook_path = f"{year_folder_path}/{filename}.xlsx"

            # Get SharePoint URL for the workbook
            file_info = self.sp.get_file(
                site_url=self.SHAREPOINT_FINANCE_URL,
                file_path=f"{filename}.xlsx",
                folder_name=year_folder_path
            )

            if file_info and 'webUrl' in file_info:
                base_url = file_info['webUrl']

                # Update each row for this workbook
                for idx, row in workbook_data.iterrows():
                    sheet_name = row['SHEETNAME']
                    # Construct Excel Online URL with sheet reference
                    excel_url = f"{base_url}?web=1&sheet={sheet_name}"

                    # Update the DataFrame
                    self.expected_files_sheets.loc[
                        (self.expected_files_sheets['FILENAME'] == filename) &
                        (self.expected_files_sheets['SHEETNAME'] == sheet_name),
                        'EXCEL_LINK'
                    ] = excel_url

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error updating Excel links for {filename}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )

    def report_successful_uploads(self):
        """
        Report all successful uploads in one batch for easy SharePoint verification.
        """
        if not self.successful_uploads:
            self.log_audit_in_db(
                log_msg='No successful uploads to report',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )
            return

        # Create summary report
        total_files = len(self.successful_uploads)
        total_sheets = sum(upload['sheets_count'] for upload in self.successful_uploads)

        # Separate workbooks and directory files
        workbooks = [u for u in self.successful_uploads if u.get('file_type') != 'Directory File']
        directory_files = [u for u in self.successful_uploads if u.get('file_type') == 'Directory File']

        # Build detailed report
        report_lines = [
            f"=== SUCCESSFUL UPLOADS SUMMARY ===",
            f"Total Files Uploaded: {total_files}",
            f"  - Banking Workbooks: {len(workbooks)}",
            f"  - Directory Files: {len(directory_files)}",
            f"Total Sheets Created: {total_sheets}",
            f"Upload Locations:",
            f"  - Workbooks: {workbooks[0]['location'] if workbooks else 'N/A'}",
            f"  - Directory: {directory_files[0]['location'] if directory_files else 'N/A'}",
            f"",
            f"=== BANKING WORKBOOKS ({len(workbooks)}) ===",
        ]

        # Add workbook details
        for i, upload in enumerate(workbooks, 1):
            report_lines.extend([
                f"{i:2d}. {upload['filename']}",
                f"    Sheets ({upload['sheets_count']}): {', '.join(upload['sheets'])}",
                f"    Uploaded: {upload['upload_time']}",
                f""
            ])

        # Add directory files section
        if directory_files:
            report_lines.extend([
                f"=== DIRECTORY FILES ({len(directory_files)}) ===",
            ])
            for i, upload in enumerate(directory_files, 1):
                report_lines.extend([
                    f"{i:2d}. {upload['filename']}",
                    f"    Sheet: {', '.join(upload['sheets'])}",
                    f"    Uploaded: {upload['upload_time']}",
                    f""
                ])

        # Log the complete report
        full_report = "\n".join(report_lines)
        self.log_audit_in_db(
            log_msg=full_report,
            process_type=self.PROCESS_TYPE,
            print_msg=True,  # Always print this summary
            script_file_name=self.PROCESS_NAME
        )

        # Also create a simple list for easy copy-paste verification
        file_list = [upload['filename'] for upload in self.successful_uploads]
        self.log_audit_in_db(
            log_msg=f"Files for SharePoint verification: {', '.join(file_list)}",
            process_type=self.PROCESS_TYPE,
            print_msg=True,
            script_file_name=self.PROCESS_NAME
        )

    def create_directory_file(self) -> bool:
        """
        Create the Excel directory file with links to all workbooks and sheets.

        Returns:
            True if successful, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg='Creating directory Excel file',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Prepare data for directory file
            directory_data = self.expected_files_sheets.copy()

            # Rename columns for Excel display
            directory_data = directory_data.rename(columns={
                'BANK': 'Bank',
                'IND_OR_MUL': 'Ind. or Mult. Per Sheet',
                'LOCATION': 'MP#',
                'EXCEL_LINK': 'Paste Trans Into This Sheet',
                'FILENAME': 'Excel File Name',
                'SHEETNAME': 'Sheet Name',
                'ACCOUNT': 'Account Number',
                'IDENTIFIER': 'Identifier In Statement',
                'BANK_LOGIN_URL': 'Bank Login URL'
            })

            # Remove the Sheet Name column (as in original R script)
            if 'Sheet Name' in directory_data.columns:
                directory_data = directory_data.drop('Sheet Name', axis=1)

            # Create directory file in SharePoint
            directory_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.DIRECTORY_FOLDER_NAME}"
            directory_file_path = f"{directory_folder_path}/{self.report_filename}"

            # Create Excel file with hyperlinks
            success = self._create_excel_with_hyperlinks(directory_file_path, directory_data)

            if success:
                # Track directory file upload for batch reporting
                upload_info = {
                    'filename': self.report_filename,
                    'location': directory_folder_path,
                    'sheets_count': 1,
                    'sheets': [f"{self.rpt_start} to {self.rpt_end}"],
                    'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'file_type': 'Directory File'
                }
                self.successful_uploads.append(upload_info)

                self.log_audit_in_db(
                    log_msg=f'Successfully created directory file: {self.report_filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
                return True
            else:
                self.log_audit_in_db(
                    log_msg=f'Failed to create directory file: {self.report_filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error creating directory file: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def _create_excel_with_hyperlinks(self, file_path: str, data: pd.DataFrame) -> bool:
        """
        Create Excel file with hyperlinks in SharePoint.

        Args:
            file_path: Full SharePoint path for the Excel file
            data: DataFrame with the data to write

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create Excel file locally first, then upload to SharePoint
            # The SharePointExcelOnline class doesn't have create_workbook_from_dataframe method

            # Create local Excel file using openpyxl
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                tmp_file_path = tmp_file.name

            # Create workbook with openpyxl
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = f"{self.rpt_start} to {self.rpt_end}"

            # Add headers and data
            headers = data.columns.tolist()
            ws.append(headers)

            # Add data rows
            for _, row in data.iterrows():
                ws.append(row.tolist())

            # Save workbook locally
            wb.save(tmp_file_path)

            # Upload to SharePoint
            success = self.sp.upload_file_to_folder_path(
                site_url=self.SHAREPOINT_FINANCE_URL,
                local_file_path=tmp_file_path,
                folder_path=os.path.dirname(file_path),
                remote_file_name=os.path.basename(file_path)
            )

            # Clean up temporary file
            try:
                os.unlink(tmp_file_path)
            except:
                pass

            return success

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error creating Excel file with hyperlinks: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def log_completion_summary(self) -> bool:
        """
        Log completion summary with directory file information and instructions.
        Uses the same logging pattern as other Python scripts instead of email.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get directory file URL
            directory_folder_path = f"{self.SHAREPOINT_BANKING_FOLDER}/{self.DIRECTORY_FOLDER_NAME}"
            directory_file_info = self.sp.get_file(
                site_url=self.SHAREPOINT_FINANCE_URL,
                file_path=self.report_filename,
                folder_name=directory_folder_path
            )

            directory_url = directory_file_info.get('webUrl', 'SharePoint directory file') if directory_file_info else 'SharePoint directory file'
            year_folder_url = f"{self.SHAREPOINT_FINANCE_URL}/_layouts/15/onedrive.aspx?id=%2Fsites%2Ffinance%2F{self.SHAREPOINT_BANKING_FOLDER.replace('/', '%2F')}%2F{self.report_year}"

            # Also log key metrics
            unique_files = self.expected_files_sheets['FILENAME'].nunique()
            total_sheets = len(self.expected_files_sheets)
            unique_banks = self.expected_files_sheets['BANK'].nunique()
            unique_locations = self.expected_files_sheets['LOCATION'].nunique()

            metrics_msg = f"Worksheet Creation Metrics: {unique_files} Excel files created, {total_sheets} total sheets, {unique_banks} banks, {unique_locations} store locations"

            self.log_audit_in_db(
                log_msg=metrics_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error logging completion summary: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def run(self) -> bool:
        """
        Main execution method to create weekly banking worksheets.

        Returns:
            True if all processing successful, False if any failures
        """
        start_time = time.time()
        start_datetime = datetime.now()

        try:
            self.log_audit_in_db(
                log_msg=f'Starting {self.PROCESS_NAME} for period {self.email_start} to {self.email_end}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Step 1: Query expected files and sheets from Snowflake
            if not self.query_expected_files_sheets():
                self.log_audit_in_db(
                    log_msg='Failed to query expected files and sheets - aborting',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Step 2: Ensure SharePoint folder structure
            if not self.ensure_sharepoint_folder_structure():
                self.log_audit_in_db(
                    log_msg='Failed to ensure SharePoint folder structure - aborting',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Step 3: Process workbooks and sheets
            if not self.process_workbooks_and_sheets():
                self.log_audit_in_db(
                    log_msg='Failed to process workbooks and sheets - continuing with directory creation',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )

            # Step 4: Create directory file
            if not self.create_directory_file():
                self.log_audit_in_db(
                    log_msg='Failed to create directory file',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )

            # Step 5: Report all successful uploads for SharePoint verification
            self.report_successful_uploads()

            # Step 6: Log completion summary with instructions
            if not self.log_completion_summary():
                self.log_audit_in_db(
                    log_msg='Failed to log completion summary',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )

            # Calculate duration
            duration = self.sf.get_duration(start_time, show_secs=True)

            self.log_audit_in_db(
                log_msg=f'Completed {self.PROCESS_NAME}. Total processing time: {duration}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                start_upload=True
            )

            return True

        except Exception as e:
            duration = self.sf.get_duration(start_time, show_secs=True)
            self.log_audit_in_db(
                log_msg=f'Error in main processing: {e}. Total processing time: {duration}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error',
                start_upload=True
            )
            return False
        

    def log_audit_in_db(self, log_msg, log_type='Info', print_msg=False, print_data_list=True, start_upload=False,
                                process_type=None, script_file_name=None):
        """
        Store log messages in a buffer and upload them in bulk when requested
        
        Args:
            log_msg (str): Message to log
            log_type (str): Type of log (Info, Warning, Error, etc.)
            print_msg (bool): Whether to print the log message
            print_data_list (bool): Whether to print the data list
            start_upload (bool): Whether to upload the collected logs
        
        Returns:
            bool: Success status of the operation
        """
        try:            
            # Print log message if requested
            if print_msg:
                print(f"{self.PROCESS_NAME} - {log_type}: {log_msg}")
                
            # Create the log record and add to buffer
            uuid_str = str(uuid.uuid4())
            script_name = self.PROCESS_NAME
            rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            record = [uuid_str, script_name, log_type, self.PROCESS_TYPE, log_msg, rec_ins_date]
            self.log_buffer.append(record)

            
            if print_data_list:
                print(f"Added to log buffer. Current size: {len(self.log_buffer)}")

            if not self.LOG_TO_DB:
                return True
            
            # Upload logs if requested
            if start_upload and self.log_buffer:
                columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
                
                try:
                    self.sf.bulk_insert(
                        columns_list=columns_list, 
                        data_list=self.log_buffer, 
                        database=self.DATABASE_RAW_DATABASE, 
                        schema=self.LOG_SCHEMA, 
                        table=self.LOG_TABLE
                    )
                    print(f"Uploaded {len(self.log_buffer)} log entries in bulk")
                    
                    # Clear the buffer after successful upload
                    self.log_buffer = []
                    return True
                    
                except Exception as e:
                    print(f"Error uploading logs for WeeklyBankingImportTransactionsSFLoader: {e}")
                    return False
                    
            return True
            
        except Exception as e:
            print(f"Error in log_audit_in_db for WeeklyBankingImportTransactionsSFLoader: {e}")
            return False

def main():
    """Main execution function"""
    try:
        # Create and run processor
        processor = WeeklyBankingCreateWorksheetsSF()

        success = processor.run()
        print(f"Worksheet creation successful: {success}")

    except Exception as e:
        print(f"Critical error in main execution: {e}")


if __name__ == "__main__":
    main()
