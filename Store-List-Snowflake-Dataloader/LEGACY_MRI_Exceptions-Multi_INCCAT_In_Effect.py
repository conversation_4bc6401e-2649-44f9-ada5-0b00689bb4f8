"""
LEGACY MRI Exceptions - Multiple Income Categories In Effect
Converted from R to Python

This script identifies MRI Income Categories with multiple dates marked as 'In Effect'
and generates an Excel report that is emailed to recipients.

Version: 20241101 (Converted to Python)
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, date

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.utils.dataframe import dataframe_to_rows
from pathlib import Path

import libs.snowflake_helper as sf
import libs.email_client as email_client

# Configuration
# TESTING_EMAILS = False  # Set to True for testing
# TESTING_PC = False  # Will be determined based on computer name
OVERRIDE_EMAIL_RECIPIENTS = False


# Gmail API scopes
# GMAIL_SCOPES = ['https://www.googleapis.com/auth/gmail.send']

class MRIExceptionsMultiIncCatEffect:
    def __init__(self):
        self.sf_obj = sf.<PERSON>flake<PERSON>elper()
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S%Z")
        self.okay_to_continue = True
        
        # Report configuration
        self.rpt_folder = "LEGACY_MRI_Exceptions-Recurring_Charges"
        self.report_name = "MRI Income Categories - Multiple 'IN EFFECT'"
        self.rpt_filename = "MRI_Multi_INCCAT_In_Effect.xlsx"
        self.report_criteria = (
            "<p><b>Criteria for inclusion in the report:</b><ul>"
            "<li>Recurring Charges (Billing) lists as Income Category "
            "with multiple dates marked as 'In Effect'</li>"
            "</ul></p><br/>"
        )
        
        # Determine environment
        self.setup_environment()
        
        # Email configuration
        self.setup_email_config()
        
        # Snowflake configuration
        # self.setup_snowflake_config()
        
        print(f"Beginning '{self.report_name}' routine")

    def setup_environment(self):
        """Setup file paths based on computer name"""
        # self.this_computer = os.environ.get('COMPUTERNAME', socket.gethostname())
        # test_computers = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
        # prod_computers = ["DESKTOP-TABLEAU"]
        
        # if self.this_computer in test_computers:
        #     self.testing_pc = True
        #     self.main_path = Path("//*************/public/steveo/R Stuff/ReportFiles")
        # else:
        self.testing_pc = False
        # self.main_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
        
        self.log_path = self.main_path / self.rpt_folder
        self.rpt_path = self.log_path
        self.hv_sig_path = self.main_path / "HTML_signatures.csv"

    def setup_email_config(self):
        """Setup email configuration"""
        self.gmail_auth_email = "<EMAIL>"
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Default signature
        self.norm_sig = (
            "<b><span style='font-weight:bold'>Steve Olson</span></b><br/>"
            "Sr. Analytics Mgr.<br/>"
            "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>"
            "2500 Lehigh Ave.<br/>"
            "Glenview, IL 60026<br/>"
            "Ph: 847/904-9043<br/></span></font>"
        )
        
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        
        # Try to load signature from file
        self.load_email_signature()

    

    def load_email_signature(self):
        """Load email signature from CSV file if available"""
        try:
            if self.hv_sig_path.exists():
                sig_df = pd.read_csv(self.hv_sig_path)
                lcp_sig = sig_df[sig_df['Desc'] == 'LCP Reporting']['HTML'].iloc[0]
                self.norm_sig = lcp_sig
        except Exception as e:
            print(f"Warning: Could not load signature file: {e}")

    def get_snowflake_connection(self):
        """Create Snowflake connection"""
        try:
            
            # sf_obj = sf.SnowflakeHelper()
            conn = self.sf_obj.conn

            # Set timezone
            cursor = conn.cursor()
            cursor.execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
            cursor.close()
            
            return conn
        except Exception as e:
            print(f"Error connecting to Snowflake: {e}")
            return None

    def check_df_rows(self, df, min_num_rows, report_name=None):
        """Check if dataframe has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_num_rows:
                return {
                    'status': True,
                    'row_count': len(df),
                    'error_status': f"{report_name}: OKAY"
                }
            else:
                return {
                    'status': False,
                    'row_count': len(df),
                    'error_status': f"{report_name}: INCOMPLETE"
                }
        else:
            return {
                'status': False,
                'row_count': 0,
                'error_status': f"{report_name}: ERROR"
            }

    def write_excel_report(self, df, filepath, sheet_name="Sheet1", col_widths=None):
        """Write DataFrame to Excel with formatting"""
        try:
            # Ensure directory exists
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert datetime columns to date
            for col in df.columns:
                if df[col].dtype == 'datetime64[ns]':
                    df[col] = df[col].dt.date
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data to worksheet
            for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=True), 1):
                for c_idx, value in enumerate(row, 1):
                    ws.cell(row=r_idx, column=c_idx, value=value)
            
            # Format header row
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_font = Font(name="Arial Narrow", size=12, bold=True)
            header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            
            for cell in ws[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
            
            # Set column widths
            if col_widths:
                for col_name, width in col_widths.items():
                    col_idx = None
                    for idx, col in enumerate(df.columns, 1):
                        if col == col_name:
                            col_idx = idx
                            break
                    if col_idx:
                        ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
            
            # Add auto filter
            ws.auto_filter.ref = ws.dimensions
            
            # Freeze top row
            ws.freeze_panes = "A2"
            
            # Save workbook
            wb.save(filepath)
            return True
            
        except Exception as e:
            print(f"Error writing Excel file: {e}")
            self.send_warning_email(
                subject=f"{self.report_name} : REPORT FILE SAVING ERROR",
                body=f"Error saving file {filepath}: {str(e)}"
            )
            return False



    def send_warning_email(self, subject, body):
        """Send warning email to administrators"""
        warning_body = f"{body}<br/>{self.warn_sig}"
        email_client.send_email(
            recipient=self.warn_recip,
            subject=subject,
            body=warning_body,
            # test=TESTING_EMAILS
            override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
        )

    def get_exceptions_data(self):
        """Query Snowflake for income category exceptions"""
        query = """
        SELECT IC.LEASID
        ,	IC.INCCAT
        ,	INCH.DESCRPTN AS "INCCAT DESC"
        ,	COUNT(IC.EFFDATE) AS CNT
        ,	LISTAGG(concat(MONTH(IC.EFFDATE),'/',DAY(IC.EFFDATE),'/',TO_CHAR(IC.EFFDATE, 'yy'),'-', IFNULL(concat(MONTH(IC.ENDDATE),'/',DAY(IC.ENDDATE),'/',TO_CHAR(IC.ENDDATE, 'yy')),'NA')), '; ' ) WITHIN GROUP (ORDER BY IC.EFFDATE) AS "IN EFFECT DATES"
        ,	LISTAGG(concat(MONTH(IC.LASTDATE),'/',DAY(IC.LASTDATE),'/',TO_CHAR(IC.LASTDATE, 'yy')), '; ' ) WITHIN GROUP (ORDER BY IC.EFFDATE)  AS "UPDATED DATES (Not necessarily IN EFFECT chg)"
        ,	LISTAGG(TRIM(IC.USERID), '; ')  WITHIN GROUP (ORDER BY IC.EFFDATE) AS "UPDATED BY"
        ,	LEAS.BLDGID
        ,	LEAS.SUITID
        ,	LEAS.OCCPNAME AS "OCCUPANT NAME"
        ,	TO_DATE(LEAS.RENTSTRT) AS "RENT START"
        ,	TO_DATE(LEAS.STOPBILLDATE) AS "STOP BILL DATE"
        ,	TO_DATE(LEAS.VACATE) as "VACATE DATE"
        ,	TO_DATE(LEAS.EXPIR) as "EXPIRE DATE"
        FROM MRI.CMRECC IC
        JOIN MRI.LEAS
        ON IC.LEASID = LEAS.LEASID
        JOIN MRI.INCH ON IC.INCCAT = INCH.INCCAT
        WHERE TO_DATE(LEAS.RENTSTRT) < CURRENT_DATE
        AND IC.INEFFECT = 'Y'
        GROUP BY IC.LEASID
        ,	LEAS.BLDGID
        ,	LEAS.SUITID
        ,	LEAS.OCCPNAME
        ,	LEAS.RENTSTRT
        ,	LEAS.STOPBILLDATE
        ,	LEAS.VACATE
        ,	LEAS.EXPIR
        ,	IC.INCCAT
        ,	INCH.DESCRPTN
        ,	TO_DATE(LEAS.LASTDATE)
        ,	LEAS.USERID
        HAVING COUNT(IC.EFFDATE) > 1
        ORDER BY IC.INCCAT, LEAS.BLDGID
        """
        
        conn = self.get_snowflake_connection()
        if not conn:
            return None
        
        try:
            df = pd.read_sql(query, conn)
            # Trim string columns
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].astype(str).str.strip()
            
            # conn.close()
            return df
            
        except Exception as e:
            print(f"Error executing query: {e}")
            # conn.close()
            return None

    def format_data_for_email(self, df):
        """Format dataframe for email body display"""
        # Select columns for email body
        email_cols = [df.columns[0], df.columns[1], df.columns[3], 
                     df.columns[7], df.columns[8], df.columns[9]]  # Indices 0,1,3,7,8,9
        email_df = df[email_cols].drop_duplicates()
        
        # Format date columns
        for col in email_df.columns:
            if 'DATE' in col.upper() and email_df[col].dtype == 'datetime64[ns]':
                email_df[col] = email_df[col].dt.strftime('%m/%d/%y')
        
        return email_df

    def create_html_table(self, df):
        """Create HTML table from dataframe"""
        if len(df) < 40:
            html_table = df.to_html(
                index=False, 
                table_id="exception_table",
                classes="table table-striped table-bordered",
                border=2
            )
            return f"<p>The info below contains MRI data (from yesterday) that appears to be an exception. <b>See attached Excel file for full details.</b></p>{html_table}"
        else:
            return f"<p><strong><em>There are {len(df)} results, see attached file for all.</em></strong></p>"

    def get_column_widths(self, df):
        """Calculate optimal column widths for Excel"""
        col_widths = {}
        
        col_width_map = {
            "LEASID": 10,
            "INCCAT": 10,
            "INCCAT DESC": 24,
            "CNT": 6,
            "BLDGID": 8,
            "SUITID": 8,
            "RENT START": 10.5,
            "STOP BILL DATE": 10.5,
            "VACATE DATE": 10.5,
            "EXPIRE DATE": 10.5
        }
        
        for col in df.columns:
            if col in col_width_map:
                col_widths[col] = col_width_map[col]
            elif "IN EFFECT DATES" in col:
                max_len = df[col].astype(str).str.len().max() if not df[col].isna().all() else 16
                col_widths[col] = min(32, max(16, max_len))
            elif "UPDATED DATES" in col:
                max_len = df[col].astype(str).str.len().max() if not df[col].isna().all() else 27
                col_widths[col] = min(32, max(27, max_len))
            elif "UPDATED BY" in col:
                max_len = df[col].astype(str).str.len().max() if not df[col].isna().all() else 12
                col_widths[col] = min(26, max(12, max_len))
            elif "OCCUPANT NAME" in col:
                max_len = df[col].astype(str).str.len().max() if not df[col].isna().all() else 21
                col_widths[col] = min(36, max(21, max_len))
        
        return col_widths

    def run_report(self):
        """Main method to run the report"""
        if not self.okay_to_continue:
            return
        
        print("Querying Snowflake for exceptions...")
        mydata = self.get_exceptions_data()
        
        if mydata is None:
            print("Failed to retrieve data from Snowflake")
            return
        
        data_status = self.check_df_rows(mydata, 1, self.report_name)
        
        if data_status['status']:
            print(f"Found {data_status['row_count']} exceptions")
            
            # Create Excel file
            excel_path = self.rpt_path / self.rpt_filename
            col_widths = self.get_column_widths(mydata)
            
            success = self.write_excel_report(
                df=mydata,
                filepath=excel_path,
                sheet_name=self.query_date,
                col_widths=col_widths
            )
            
            if success:
                print(f"Excel report created: {excel_path}")
                
                # Prepare email
                email_df = self.format_data_for_email(mydata)
                html_table = self.create_html_table(email_df)
                
                body_text = f"""
                <p><h2>REPORT: {self.report_name}</h2></p>
                {self.report_criteria}
                {html_table}
                <br/>
                {self.norm_sig}
                """
                
                # Send email
                email_client.send_email(
                    recipient=self.norm_recip,
                    subject=self.report_name,
                    body=body_text,
                    attachments=[str(excel_path)],
                    # test=TESTING_EMAILS
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
                # if success:
                #     print("Report emailed successfully")
                # else:
                #     print("Failed to send email")
            else:
                print("Failed to create Excel report")
                self.sf_obj.log_audit_in_db(log_msg="Failed to create Excel report", process_type=self.report_name, script_file_name=__file__, log_type='Error')
        else:
            # print(f"No exceptions found or data retrieval error: {data_status['error_status']}")
            self.sf_obj.log_audit_in_db(log_msg=f"No exceptions found or data retrieval error: {data_status['error_status']}", process_type=self.report_name, script_file_name=__file__, log_type='Error')


def main():
    """Main function"""
    try:
        reporter = MRIExceptionsMultiIncCatEffect()
        reporter.run_report()
        # print("Report process completed")
        reporter.sf_obj.log_audit_in_db(log_msg=f"Report process completed", process_type=reporter.report_name, script_file_name=__file__, log_type='Info')
    except Exception as e:
        print(f"Error in main execution: {e}")


if __name__ == "__main__":
    main() 