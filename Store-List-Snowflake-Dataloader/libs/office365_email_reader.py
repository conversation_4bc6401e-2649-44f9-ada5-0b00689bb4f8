"""
Office 365 Email Reader
Supports both IMAP and Microsoft Graph API approaches for reading emails from Office 365
"""

import os
import imaplib
import email
import json
import base64
import mimetypes
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

# For Microsoft Graph API
try:
    import requests
    from requests.auth import HTTPBasicAuth
    GRAPH_API_AVAILABLE = True
except ImportError:
    GRAPH_API_AVAILABLE = False
    print("Warning: requests library not available. Microsoft Graph API functionality will be limited.")

# For email parsing
try:
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from email.utils import parsedate_to_datetime
    EMAIL_PARSING_AVAILABLE = True
except ImportError:
    EMAIL_PARSING_AVAILABLE = False
    print("Warning: Email parsing libraries not available.")


class EmailReaderMethod(Enum):
    """Available methods for reading emails"""
    IMAP = "imap"
    GRAPH_API = "graph_api"


@dataclass
class EmailMessage:
    """Data class for email messages"""
    message_id: str
    sender: str
    recipients: List[str]
    subject: str
    body: str
    html_body: Optional[str] = None
    received_date: Optional[datetime] = None
    attachments: List[Dict] = None
    is_read: bool = False
    folder: str = "INBOX"
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        data = asdict(self)
        if self.received_date:
            data['received_date'] = self.received_date.isoformat()
        return data


class Office365EmailReader:
    """
    Office 365 Email Reader supporting both IMAP and Microsoft Graph API
    """
    
    def __init__(self, method: EmailReaderMethod = EmailReaderMethod.IMAP):
        """
        Initialize the email reader
        
        Args:
            method: EmailReaderMethod - Either IMAP or GRAPH_API
        """
        self.method = method
        self.connection = None
        self.access_token = None
        
        # Load configuration from environment variables
        self._load_config()
        
    def _load_config(self):
        """Load configuration from environment variables"""
        if self.method == EmailReaderMethod.IMAP:
            self.imap_server = os.environ.get("OFFICE365_IMAP_SERVER", "outlook.office365.com")
            self.imap_port = int(os.environ.get("OFFICE365_IMAP_PORT", "993"))
            self.email_address = os.environ.get("OFFICE365_EMAIL_ADDRESS", "")
            self.email_password = os.environ.get("OFFICE365_EMAIL_PASSWORD", "")
            
        elif self.method == EmailReaderMethod.GRAPH_API:
            self.client_id = os.environ.get("OFFICE365_CLIENT_ID", "")
            self.client_secret = os.environ.get("OFFICE365_CLIENT_SECRET", "")
            self.tenant_id = os.environ.get("OFFICE365_TENANT_ID", "")
            self.email_address = os.environ.get("OFFICE365_EMAIL_ADDRESS", "")
            
        if not self.email_address:
            raise ValueError("OFFICE365_EMAIL_ADDRESS environment variable is required")
    
    def connect(self) -> bool:
        """
        Connect to Office 365 email service
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            if self.method == EmailReaderMethod.IMAP:
                return self._connect_imap()
            elif self.method == EmailReaderMethod.GRAPH_API:
                return self._connect_graph_api()
            else:
                raise ValueError(f"Unsupported method: {self.method}")
        except Exception as e:
            print(f"Connection failed: {str(e)}")
            return False
    
    def _connect_imap(self) -> bool:
        """Connect using IMAP"""
        if not self.email_password:
            raise ValueError("OFFICE365_EMAIL_PASSWORD environment variable is required for IMAP")
            
        self.connection = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
        self.connection.login(self.email_address, self.email_password)
        print("Successfully connected to Office 365 via IMAP")
        return True
    
    def _connect_graph_api(self) -> bool:
        """Connect using Microsoft Graph API"""
        if not GRAPH_API_AVAILABLE:
            raise ValueError("requests library is required for Microsoft Graph API")
            
        if not all([self.client_id, self.client_secret, self.tenant_id]):
            raise ValueError("OFFICE365_CLIENT_ID, OFFICE365_CLIENT_SECRET, and OFFICE365_TENANT_ID are required for Graph API")
        
        # Get access token
        token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        token_data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        
        response = requests.post(token_url, data=token_data)
        if response.status_code == 200:
            self.access_token = response.json()['access_token']
            print("Successfully connected to Office 365 via Microsoft Graph API")
            return True
        else:
            raise Exception(f"Failed to get access token: {response.text}")
    
    def get_emails(self, 
                   folder: str = "INBOX",
                   limit: int = 10,
                   since: Optional[datetime] = None,
                   unread_only: bool = False,
                   search_criteria: Optional[str] = None) -> List[EmailMessage]:
        """
        Get emails from specified folder
        
        Args:
            folder: Folder name (default: "INBOX")
            limit: Maximum number of emails to retrieve (default: 10)
            since: Only get emails newer than this date
            unread_only: Only get unread emails
            search_criteria: Search criteria (subject, sender, etc.)
            
        Returns:
            List[EmailMessage]: List of email messages
        """
        if not self.connection and not self.access_token:
            if not self.connect():
                return []
        
        if self.method == EmailReaderMethod.IMAP:
            return self._get_emails_imap(folder, limit, since, unread_only, search_criteria)
        elif self.method == EmailReaderMethod.GRAPH_API:
            return self._get_emails_graph_api(folder, limit, since, unread_only, search_criteria)
        else:
            return []
    
    def _get_emails_imap(self, folder: str, limit: int, since: Optional[datetime], 
                        unread_only: bool, search_criteria: Optional[str]) -> List[EmailMessage]:
        """Get emails using IMAP"""
        try:
            # Select folder
            self.connection.select(folder)
            
            # Build search criteria
            search_parts = []
            if unread_only:
                search_parts.append("UNSEEN")
            if since:
                date_str = since.strftime("%d-%b-%Y")
                search_parts.append(f"SINCE {date_str}")
            if search_criteria:
                search_parts.append(f"SUBJECT \"{search_criteria}\"")
            
            search_string = " ".join(search_parts) if search_parts else "ALL"
            
            # Search for emails
            status, message_ids = self.connection.search(None, search_string)
            if status != 'OK':
                print(f"Search failed: {status}")
                return []
            
            message_ids = message_ids[0].split()
            
            # Limit results
            if limit > 0:
                message_ids = message_ids[-limit:]  # Get most recent
            
            emails = []
            for msg_id in message_ids:
                try:
                    # Fetch email
                    status, msg_data = self.connection.fetch(msg_id, '(RFC822)')
                    if status != 'OK':
                        continue
                    
                    # Parse email
                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)
                    
                    # Extract email data
                    email_obj = self._parse_email_message(email_message, folder)
                    if email_obj:
                        emails.append(email_obj)
                        
                except Exception as e:
                    print(f"Error processing email {msg_id}: {str(e)}")
                    continue
            
            return emails
            
        except Exception as e:
            print(f"Error retrieving emails via IMAP: {str(e)}")
            return []
    
    def _get_emails_graph_api(self, folder: str, limit: int, since: Optional[datetime], 
                             unread_only: bool, search_criteria: Optional[str]) -> List[EmailMessage]:
        """Get emails using Microsoft Graph API"""
        try:
            # Build API URL
            if folder.upper() == "INBOX":
                url = f"https://graph.microsoft.com/v1.0/users/{self.email_address}/messages"
            else:
                # For other folders, you might need to get the folder ID first
                url = f"https://graph.microsoft.com/v1.0/users/{self.email_address}/mailFolders/{folder}/messages"
            
            # Build query parameters
            params = {
                '$top': limit,
                '$orderby': 'receivedDateTime desc'
            }
            
            # Add filters
            filters = []
            if unread_only:
                filters.append("isRead eq false")
            if since:
                since_str = since.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                filters.append(f"receivedDateTime gt {since_str}")
            if search_criteria:
                filters.append(f"contains(subject,'{search_criteria}')")
            
            if filters:
                params['$filter'] = " and ".join(filters)
            
            # Make API request
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(url, headers=headers, params=params)
            if response.status_code != 200:
                print(f"Graph API request failed: {response.status_code} - {response.text}")
                return []
            
            data = response.json()
            emails = []
            
            for item in data.get('value', []):
                try:
                    email_obj = self._parse_graph_api_message(item, folder)
                    if email_obj:
                        emails.append(email_obj)
                except Exception as e:
                    print(f"Error parsing Graph API message: {str(e)}")
                    continue
            
            return emails
            
        except Exception as e:
            print(f"Error retrieving emails via Graph API: {str(e)}")
            return []
    
    def _parse_email_message(self, email_message, folder: str) -> Optional[EmailMessage]:
        """Parse email message from IMAP"""
        try:
            # Extract basic info
            message_id = email_message.get('Message-ID', '')
            sender = email_message.get('From', '')
            recipients = email_message.get('To', '').split(',')
            subject = email_message.get('Subject', '')
            
            # Extract date
            date_str = email_message.get('Date', '')
            received_date = None
            if date_str:
                try:
                    received_date = parsedate_to_datetime(date_str)
                except:
                    pass
            
            # Extract body
            body = ""
            html_body = None
            attachments = []
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition', ''))
                    
                    if content_type == 'text/plain' and 'attachment' not in content_disposition:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == 'text/html' and 'attachment' not in content_disposition:
                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif 'attachment' in content_disposition:
                        filename = part.get_filename()
                        if filename:
                            attachments.append({
                                'filename': filename,
                                'content_type': content_type,
                                'size': len(part.get_payload(decode=True))
                            })
            else:
                body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            
            return EmailMessage(
                message_id=message_id,
                sender=sender,
                recipients=recipients,
                subject=subject,
                body=body,
                html_body=html_body,
                received_date=received_date,
                attachments=attachments,
                folder=folder
            )
            
        except Exception as e:
            print(f"Error parsing email message: {str(e)}")
            return None
    
    def _parse_graph_api_message(self, item: Dict, folder: str) -> Optional[EmailMessage]:
        """Parse email message from Graph API response"""
        try:
            # Extract basic info
            message_id = item.get('id', '')
            sender = item.get('from', {}).get('emailAddress', {}).get('address', '')
            recipients = [r.get('emailAddress', {}).get('address', '') for r in item.get('toRecipients', [])]
            subject = item.get('subject', '')
            
            # Extract date
            received_date = None
            if item.get('receivedDateTime'):
                try:
                    received_date = datetime.fromisoformat(item['receivedDateTime'].replace('Z', '+00:00'))
                except:
                    pass
            
            # Extract body
            body = item.get('body', {}).get('content', '')
            html_body = None
            if item.get('body', {}).get('contentType') == 'html':
                html_body = body
                # Strip HTML for plain text body
                import re
                body = re.sub('<[^<]+?>', '', body)
            
            # Extract attachments info
            attachments = []
            if item.get('hasAttachments', False):
                attachments = [{'filename': 'attachment', 'content_type': 'unknown', 'size': 0}]
            
            return EmailMessage(
                message_id=message_id,
                sender=sender,
                recipients=recipients,
                subject=subject,
                body=body,
                html_body=html_body,
                received_date=received_date,
                attachments=attachments,
                is_read=item.get('isRead', False),
                folder=folder
            )
            
        except Exception as e:
            print(f"Error parsing Graph API message: {str(e)}")
            return None
    
    def get_folders(self) -> List[str]:
        """Get list of available folders"""
        if not self.connection and not self.access_token:
            if not self.connect():
                return []
        
        if self.method == EmailReaderMethod.IMAP:
            return self._get_folders_imap()
        elif self.method == EmailReaderMethod.GRAPH_API:
            return self._get_folders_graph_api()
        else:
            return []
    
    def _get_folders_imap(self) -> List[str]:
        """Get folders using IMAP"""
        try:
            status, folders = self.connection.list()
            if status != 'OK':
                return []
            
            folder_names = []
            for folder in folders:
                # Parse folder name from IMAP response
                folder_parts = folder.decode().split('"')
                if len(folder_parts) >= 3:
                    folder_names.append(folder_parts[-2])
            
            return folder_names
            
        except Exception as e:
            print(f"Error getting folders via IMAP: {str(e)}")
            return []
    
    def _get_folders_graph_api(self) -> List[str]:
        """Get folders using Microsoft Graph API"""
        try:
            url = f"https://graph.microsoft.com/v1.0/users/{self.email_address}/mailFolders"
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                print(f"Graph API folder request failed: {response.status_code}")
                return []
            
            data = response.json()
            return [folder['displayName'] for folder in data.get('value', [])]
            
        except Exception as e:
            print(f"Error getting folders via Graph API: {str(e)}")
            return []
    
    def mark_as_read(self, message_id: str) -> bool:
        """Mark email as read"""
        if self.method == EmailReaderMethod.GRAPH_API:
            return self._mark_as_read_graph_api(message_id)
        else:
            print("Mark as read not implemented for IMAP method")
            return False
    
    def _mark_as_read_graph_api(self, message_id: str) -> bool:
        """Mark email as read using Graph API"""
        try:
            url = f"https://graph.microsoft.com/v1.0/users/{self.email_address}/messages/{message_id}"
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            data = {'isRead': True}
            
            response = requests.patch(url, headers=headers, json=data)
            return response.status_code == 200
            
        except Exception as e:
            print(f"Error marking email as read: {str(e)}")
            return False
    
    def close(self):
        """Close connection"""
        if self.connection:
            try:
                self.connection.logout()
            except:
                pass
            self.connection = None
        self.access_token = None


# Example usage and helper functions
def print_email_summary(emails: List[EmailMessage]):
    """Print a summary of emails"""
    print(f"\nFound {len(emails)} emails:")
    print("-" * 80)
    
    for i, email in enumerate(emails, 1):
        print(f"{i}. From: {email.sender}")
        print(f"   Subject: {email.subject}")
        print(f"   Date: {email.received_date}")
        print(f"   Read: {email.is_read}")
        print(f"   Body preview: {email.body[:100]}...")
        if email.attachments:
            print(f"   Attachments: {len(email.attachments)}")
        print("-" * 80)


def save_emails_to_json(emails: List[EmailMessage], filename: str):
    """Save emails to JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump([email.to_dict() for email in emails], f, indent=2, ensure_ascii=False)
        print(f"Saved {len(emails)} emails to {filename}")
    except Exception as e:
        print(f"Error saving emails to JSON: {str(e)}")


# Example usage
if __name__ == "__main__":
    # Example 1: Using IMAP
    print("Example 1: Reading emails using IMAP")
    
    # Set environment variables (you would set these in your environment)
    os.environ["OFFICE365_EMAIL_ADDRESS"] = "<EMAIL>"
    os.environ["OFFICE365_EMAIL_PASSWORD"] = "your_app_password"
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        emails = reader.get_emails(limit=5, unread_only=True)
        print_email_summary(emails)
        reader.close()
    except Exception as e:
        print(f"IMAP example failed: {str(e)}")
    
    print("\n" + "="*80 + "\n")
    
    # Example 2: Using Microsoft Graph API
    print("Example 2: Reading emails using Microsoft Graph API")
    
    # Set environment variables for Graph API
    os.environ["OFFICE365_CLIENT_ID"] = "your_client_id"
    os.environ["OFFICE365_CLIENT_SECRET"] = "your_client_secret"
    os.environ["OFFICE365_TENANT_ID"] = "your_tenant_id"
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.GRAPH_API)
        emails = reader.get_emails(limit=5, unread_only=True)
        print_email_summary(emails)
        
        # Save to JSON
        save_emails_to_json(emails, "office365_emails.json")
        
        reader.close()
    except Exception as e:
        print(f"Graph API example failed: {str(e)}") 