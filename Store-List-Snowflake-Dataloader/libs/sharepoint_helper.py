import requests
#import json
from typing import Optional, Dict, List
#import base64
import os
import pandas as pd
from openpyxl import load_workbook
import tempfile
import os

class SharePointClient:
    def __init__(self, tenant_id=None, client_id=None, client_secret=None, suppress_false_errors=True):
        """
        Initialize SharePoint client with Azure app credentials

        Args:
            tenant_id: Azure tenant ID
            client_id: Azure app client ID
            client_secret: Azure app client secret
            suppress_false_errors: If True, suppress intermediate errors when trying multiple endpoints
        """
        self.tenant_id = tenant_id if tenant_id else os.environ.get('SHAREPOINT_TENANT_ID')
        self.client_id = client_id if tenant_id else os.environ.get('SHAREPOINT_CLIENT_ID')
        self.client_secret = client_secret if tenant_id else os.environ.get('SHAREPOINT_CLIENT_SECRET')
        self.suppress_false_errors = suppress_false_errors

        self.access_token = None
        self.graph_url = "https://graph.microsoft.com/v1.0"
    
    def authenticate(self) -> bool:
        """
        Authenticate with Microsoft Graph using client credentials flow
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        print("Authenticating with Microsoft Graph...")     
        
        token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        
        try:
            response = requests.post(token_url, headers=headers, data=data)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get('access_token')
                print("Authentication successful")
                return True
            else:
                print(f"Authentication failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Authentication error: {e}")
            return False
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Graph API requests"""
        if not self.access_token:
            raise ValueError("Not authenticated. Call authenticate() first.")
        
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def _make_request(self, endpoint: str, method: str = 'GET', data: dict = None, suppress_errors: bool = None) -> Optional[dict]:
        """
        Make a request to Microsoft Graph API

        Args:
            endpoint: API endpoint (without base URL)
            method: HTTP method (GET, POST, etc.)
            data: Request body data for POST requests
            suppress_errors: If True, suppress error messages (uses instance default if None)

        Returns:
            dict: Response data or None if failed
        """
        # Determine if we should suppress errors
        should_suppress = suppress_errors if suppress_errors is not None else self.suppress_false_errors

        try:
            headers = self._get_headers()
            url = f"{self.graph_url}/{endpoint}"

            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            if response.status_code in [200, 201, 204]:
                # print(f"0) _make_request():    Successfully made request to \n\tendpoint: {endpoint}\n\turl: {url}\n\tmethod: {method}\n\tdata: {data}\n\tstatus_code: {response.status_code}\n\n")
                # _txt = response.text

                if len(response.text) > 0:
                    return response.json()
                else:
                    return {}

                # print(f"\t1) _make_request():    _txt{len(_txt)}: {_txt}\n\n")
                # _json = response.json()
                # print(f"2) _make_request():    Successfully made request to \n\tendpoint: {endpoint}\n\turl: {url}\n\tmethod: {method}\n\tdata: {data}\n\tstatus_code: {response.status_code}\n\n")
                # return _json
            else:
                if not should_suppress:
                    print(f"API request failed: {response.status_code} - {endpoint}")
                    print(f"Error: {response.text}")
                return None

        except Exception as e:
            if not should_suppress:
                print(f"_make_request(): Error making request to \n\tendpoint: {endpoint}\n\turl: {url}\n\terror: {e}\n\tmethod: {method}\n\tdata: {data}\n\tstatus_code: {response.status_code}\n")
            return None
    
    def get_site(self, site_url: str) -> Optional[Dict]:
        """
        Get SharePoint site information by URL
        
        Args:
            site_url: Full SharePoint site URL (e.g., "https://tenant.sharepoint.com/sites/sitename")
            
        Returns:
            dict: Site information including site ID, or None if failed
        """
        print(f"Accessing site: {site_url}")
        
        try:
            from urllib.parse import urlparse
            parsed = urlparse(site_url)
            hostname = parsed.netloc
            site_path = parsed.path.strip('/')
            
            # Try different endpoint formats
            endpoints_to_try = [
                f"sites/{hostname}:/{site_path}",
                f"sites/{hostname}:/sites/{site_path.split('/sites/')[-1]}" if '/sites/' in site_path else None
            ]
            
            for endpoint in endpoints_to_try:
                if endpoint:
                    print(f"Trying endpoint: {endpoint}")
                    response_data = self._make_request(endpoint)
                    
                    if response_data:
                        site_id = response_data.get('id')
                        site_name = response_data.get('displayName', 'Unknown')
                        print(f"Found site: {site_name} (ID: {site_id})")
                        return response_data
            
            print("Site not accessible or doesn't exist")
            return None
            
        except Exception as e:
            print(f"Error accessing site: {e}")
            return None
    
    def get_folder(self, site_url: str, folder_path: str) -> Optional[Dict]:
        """
        Get a specific folder in a SharePoint site
        
        Args:
            site_url: SharePoint site URL
            folder_path: Path to the folder (e.g., "Documents/Reports" or "/sites/sitename/Shared Documents/Folder")
            
        Returns:
            dict: Folder information including folder ID and items, or None if failed
        """
        print(f"Attempting to Accessing folder: {folder_path}")
        
        # First get the site
        site_info = self.get_site(site_url)
        if not site_info:
            return None
        
        site_id = site_info.get('id')
        
        # Get the default drive (usually "Documents")
        drives_response = self._make_request(f"sites/{site_id}/drives")
        if not drives_response:
            print("Could not get site drives")
            return None
        
        drives = drives_response.get('value', [])
        if not drives:
            print("No drives found in site")
            return None
        
        # Try to find the folder in each drive
        all_errors = []  # Collect errors to show only if all attempts fail

        for drive in drives:
            drive_id = drive.get('id')
            drive_name = drive.get('name', 'Unknown')

            print(f"Searching in drive: {drive_name}")

            # Clean up the folder path
            clean_path = folder_path.strip('/')

            # Try different path formats
            path_variations = [
                clean_path,
                f"root:/{clean_path}",
                clean_path.replace('Shared Documents/', '').replace('Documents/', ''),
            ]

            for path in path_variations:
                if path:
                    endpoint = f"sites/{site_id}/drives/{drive_id}/root:/{path}"
                    print(f"Trying path: {path}")

                    # Try with suppressed errors first
                    folder_response = self._make_request(endpoint, suppress_errors=True)
                    if folder_response:
                        folder_name = folder_response.get('name', 'Unknown')
                        print(f"Found folder: {folder_name}")

                        # Get folder contents
                        children_endpoint = f"sites/{site_id}/drives/{drive_id}/root:/{path}:/children"
                        children_response = self._make_request(children_endpoint)

                        if children_response:
                            items = children_response.get('value', [])
                            print(f"Found {len(items)} items in folder")

                            folder_response['items'] = items
                            folder_response['site_id'] = site_id
                            folder_response['drive_id'] = drive_id

                            return folder_response
                    else:
                        # Store error info for potential later display
                        all_errors.append(f"Path '{path}' in drive '{drive_name}' failed")

        # If we get here, all attempts failed - show errors if not suppressing
        if not self.suppress_false_errors and all_errors:
            print("All folder access attempts failed:")
            for error in all_errors:
                print(f"  {error}")

        print("Folder not found in any drive")
        return None
    
    def get_file(self, site_url: str, file_path: str, drive_name: str = None, folder_name: str = None) -> Optional[Dict]:
        """
        Get a specific file from SharePoint
        
        Args:
            site_url: SharePoint site URL
            file_path: Name of the file (e.g., "file.xlsx") or full path if drive_name/folder_name not specified
            drive_name: Specific drive name to search in (optional)
            folder_name: Specific folder name to search in (optional)
            
        Returns:
            dict: File information including download URL, or None if failed
        """
        print(f"Accessing file: {file_path}")
        if drive_name:
            print(f"  In drive: {drive_name}")
        if folder_name:
            print(f"  In folder: {folder_name}")
        
        # First get the site
        site_info = self.get_site(site_url)
        if not site_info:
            return None
        
        site_id = site_info.get('id')
        
        # Get all drives
        drives_response = self._make_request(f"sites/{site_id}/drives")
        if not drives_response:
            return None
        
        drives = drives_response.get('value', [])
        if not drives:
            return None
        
        # Filter drives if drive_name is specified
        if drive_name:
            drives = [drive for drive in drives if drive.get('name', '').lower() == drive_name.lower()]
            if not drives:
                print(f"Drive '{drive_name}' not found")
                return None
        
        # Try to find the file
        all_errors = []  # Collect errors to show only if all attempts fail

        for drive in drives:
            drive_id = drive.get('id')
            current_drive_name = drive.get('name', 'Unknown')

            print(f"Searching for file in drive: {current_drive_name}")

            # Build the file path based on parameters
            if folder_name and drive_name:
                # Specific drive and folder
                file_paths_to_try = [
                    f"{folder_name}/{file_path}",
                    f"root:/{folder_name}/{file_path}",
                ]
            elif folder_name:
                # Specific folder, any drive
                file_paths_to_try = [
                    f"{folder_name}/{file_path}",
                    f"root:/{folder_name}/{file_path}",
                ]
            else:
                # Original behavior - search everywhere
                clean_path = file_path.strip('/')
                file_paths_to_try = [
                    clean_path,
                    clean_path.replace('Shared Documents/', '').replace('Documents/', ''),
                ]

            for path in file_paths_to_try:
                if path:
                    endpoint = f"sites/{site_id}/drives/{drive_id}/root:/{path}"
                    print(f"Trying file path: {path}")

                    # Try with suppressed errors first
                    file_response = self._make_request(endpoint, suppress_errors=True)
                    if file_response:
                        file_name = file_response.get('name', 'Unknown')
                        file_size = file_response.get('size', 0)
                        download_url = file_response.get('@microsoft.graph.downloadUrl')

                        print(f"Found file: {file_name} ({file_size:,} bytes)")

                        file_response['site_id'] = site_id
                        file_response['drive_id'] = drive_id
                        file_response['download_url'] = download_url

                        return file_response
                    else:
                        # Store error info for potential later display
                        all_errors.append(f"File path '{path}' in drive '{current_drive_name}' failed")

        # If we get here, all attempts failed - show errors if not suppressing
        if not self.suppress_false_errors and all_errors:
            print("All file access attempts failed:")
            for error in all_errors:
                print(f"  {error}")

        print("File not found")
        return None

    def download_file(self, site_url: str, file_path: str, local_path: str = None, drive_name: str = None, folder_name: str = None, file_info=None) -> bool:
        """
        Download a file from SharePoint to local storage
        
        Args:
            site_url: SharePoint site URL
            file_path: Name of the file or full path
            local_path: Local path to save the file (optional, uses original filename if not provided)
            drive_name: Specific drive name to search in (optional)
            folder_name: Specific folder name to search in (optional)
            
        Returns:
            bool: True if download successful, False otherwise
        """
        print(f"Attempting to Download file: {file_path}")

        if file_info is None:
            print(f"File info was not passed to method ~ download file. Attempting to grab file info from method ~ get_file")
            # Get file information
            file_info = self.get_file(site_url, file_path, drive_name, folder_name)
            print(f'file info: {file_info}')
            if not file_info:
                return False
            
        else:
            print(f"File info was passed in via download_file method call. Using file info: {file_info}")
        
        download_url = file_info.get('download_url')
        if not download_url:
            print("No download URL available")
            return False
        
        try:
            # Download the file
            response = requests.get(download_url)
            if response.status_code == 200:
                # Determine local filename
                if not local_path:
                    local_path = file_info.get('name', 'downloaded_file')
                
                # Save the file
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"File downloaded successfully: {local_path}")
                return True
            else:
                print(f"Download failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"Error downloading file: {e}")
            return False    
    

    def list_all_folders(self, site_url: str) -> List[Dict]:
        """
        List all folders in a SharePoint site across all drives
        
        Args:
            site_url: SharePoint site URL
            
        Returns:
            list: List of all folders found in the site
        """
        print(f"Listing all folders in site: {site_url}")
        
        # Get site information
        site_info = self.get_site(site_url)
        if not site_info:
            return []
        
        site_id = site_info.get('id')
        
        # Get all drives in the site
        drives_response = self._make_request(f"sites/{site_id}/drives")
        if not drives_response:
            print("Could not get site drives")
            return []
        
        drives = drives_response.get('value', [])
        if not drives:
            print("No drives found in site")
            return []
        
        all_folders = []
        
        for drive in drives:
            drive_id = drive.get('id')
            drive_name = drive.get('name', 'Unknown')
            
            print(f"Scanning drive: {drive_name}")
            
            # Get root items in the drive
            items_response = self._make_request(f"sites/{site_id}/drives/{drive_id}/root/children")
            
            if items_response:
                items = items_response.get('value', [])
                print(f"Found {len(items)} items in drive root")
                
                for item in items:
                    item_name = item.get('name')
                    if 'folder' in item:  # This is a folder
                        folder_info = {
                            'name': item_name,
                            'drive_name': drive_name,
                            'drive_id': drive_id,
                            'site_id': site_id,
                            'path': item_name,
                            'id': item.get('id'),
                            'webUrl': item.get('webUrl', ''),
                            'full_path': f"{drive_name}/{item_name}"
                        }
                        all_folders.append(folder_info)
                        print(f"  Found folder: {item_name}")
            else:
                print(f"Could not access items in drive: {drive_name}")
        
        print(f"Total folders found: {len(all_folders)}")
        return all_folders

    def list_drive_contents(self, site_url: str, drive_name: str = None) -> Dict:
        """
        List contents of a specific drive or all drives
        
        Args:
            site_url: SharePoint site URL
            drive_name: Specific drive name to list (optional, lists all if None)
            
        Returns:
            dict: Drive contents organized by drive
        """
        print(f"Listing drive contents for site: {site_url}")
        
        # Get site information
        site_info = self.get_site(site_url)
        if not site_info:
            return {}
        
        site_id = site_info.get('id')
        
        # Get all drives
        drives_response = self._make_request(f"sites/{site_id}/drives")
        if not drives_response:
            return {}
        
        drives = drives_response.get('value', [])
        result = {}
        
        for drive in drives:
            current_drive_name = drive.get('name', 'Unknown')
            drive_id = drive.get('id')
            
            # Skip if we're looking for a specific drive and this isn't it
            if drive_name and current_drive_name.lower() != drive_name.lower():
                continue
            
            print(f"Listing contents of drive: {current_drive_name}")
            
            # Get all items in the drive root
            items_response = self._make_request(f"sites/{site_id}/drives/{drive_id}/root/children")
            
            if items_response:
                items = items_response.get('value', [])
                
                folders = []
                files = []
                
                for item in items:
                    item_info = {
                        'name': item.get('name'),
                        'id': item.get('id'),
                        'size': item.get('size', 0),
                        'modified': item.get('lastModifiedDateTime', ''),
                        'webUrl': item.get('webUrl', ''),
                        'path': item.get('name')
                    }
                    
                    if 'folder' in item:
                        item_info['childCount'] = item.get('folder', {}).get('childCount', 0)
                        folders.append(item_info)
                    else:
                        files.append(item_info)
                
                result[current_drive_name] = {
                    'drive_id': drive_id,
                    'folders': folders,
                    'files': files,
                    'total_items': len(items)
                }
                
                print(f"  Drive '{current_drive_name}': {len(folders)} folders, {len(files)} files")
                
                # Print folder names
                if folders:
                    print("  Folders:")
                    for folder in folders:
                        child_count = folder.get('childCount', 0)
                        print(f"    - {folder['name']} ({child_count} items)")
            else:
                print(f"Could not access drive: {current_drive_name}")
        
        return result
    
    def upload_file(self, site_url: str, local_file_path: str, remote_file_name: str = None, 
                drive_name: str = None, folder_name: str = None) -> bool:
        """
        Upload a file to SharePoint
        
        Args:
            site_url: SharePoint site URL
            local_file_path: Path to the local file to upload
            remote_file_name: Name for the file in SharePoint (optional, uses local filename if not provided)
            drive_name: Specific drive name to upload to (optional, uses first available drive)
            folder_name: Specific folder name to upload to (optional, uploads to drive root)
            
        Returns:
            bool: True if upload successful, False otherwise
        """
        print(f"Uploading file: {local_file_path}")
        
        # Check if local file exists
        if not os.path.exists(local_file_path):
            print(f"Local file not found: {local_file_path}")
            return False
        
        # Get file size
        file_size = os.path.getsize(local_file_path)
        print(f"File size: {file_size:,} bytes")
        
        # Determine remote filename
        if not remote_file_name:
            remote_file_name = os.path.basename(local_file_path)
        
        # First get the site
        site_info = self.get_site(site_url)
        if not site_info:
            return False
        
        site_id = site_info.get('id')
        
        # Get all drives
        drives_response = self._make_request(f"sites/{site_id}/drives")
        if not drives_response:
            return False
        
        drives = drives_response.get('value', [])
        if not drives:
            return False
        
        # Filter drives if drive_name is specified
        if drive_name:
            drives = [drive for drive in drives if drive.get('name', '').lower() == drive_name.lower()]
            if not drives:
                print(f"Drive '{drive_name}' not found")
                return False
        
        # Use the first available drive
        drive = drives[0]
        drive_id = drive.get('id')
        current_drive_name = drive.get('name', 'Unknown')
        
        print(f"Uploading to drive: {current_drive_name}")
        if folder_name:
            print(f"Target folder: {folder_name}")
        
        # Build the upload path
        if folder_name:
            upload_path = f"{folder_name}/{remote_file_name}"
        else:
            upload_path = remote_file_name
        
        try:
            # Read the file content
            with open(local_file_path, 'rb') as f:
                file_content = f.read()
            
            # Choose upload method based on file size
            if file_size <= 4 * 1024 * 1024:  # 4MB or less - simple upload
                return self._simple_upload(site_id, drive_id, upload_path, file_content)
            else:
                # For larger files, use resumable upload
                return self._resumable_upload(site_id, drive_id, upload_path, local_file_path)
        
        except Exception as e:
            print(f"Error reading file: {e}")
            return False

    def _simple_upload(self, site_id: str, drive_id: str, upload_path: str, file_content: bytes) -> bool:
        """
        Simple upload for files <= 4MB
        
        Args:
            site_id: SharePoint site ID
            drive_id: Drive ID
            upload_path: Path where to upload the file
            file_content: File content as bytes
            
        Returns:
            bool: True if successful, False otherwise
        """
        print("Using simple upload method")
        
        try:
            headers = self._get_headers()
            headers['Content-Type'] = 'application/octet-stream'
            
            # Remove JSON content type for file upload
            del headers['Content-Type']
            headers['Content-Type'] = 'application/octet-stream'
            
            url = f"{self.graph_url}/sites/{site_id}/drives/{drive_id}/root:/{upload_path}:/content"
            
            response = requests.put(url, headers=headers, data=file_content)
            
            if response.status_code in [200, 201]:
                response_data = response.json()
                file_name = response_data.get('name', 'Unknown')
                print(f"File uploaded successfully: {file_name}")
                return True
            else:
                print(f"Upload failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error during simple upload: {e}")
            return False

    def _resumable_upload(self, site_id: str, drive_id: str, upload_path: str, local_file_path: str) -> bool:
        """
        Resumable upload for files > 4MB
        
        Args:
            site_id: SharePoint site ID
            drive_id: Drive ID
            upload_path: Path where to upload the file
            local_file_path: Path to local file
            
        Returns:
            bool: True if successful, False otherwise
        """
        print("Using resumable upload method")
        
        try:
            # Step 1: Create upload session
            headers = self._get_headers()
            url = f"{self.graph_url}/sites/{site_id}/drives/{drive_id}/root:/{upload_path}:/createUploadSession"
            
            upload_session_data = {
                "item": {
                    "@microsoft.graph.conflictBehavior": "replace"
                }
            }
            
            response = requests.post(url, headers=headers, json=upload_session_data)
            
            if response.status_code not in [200, 201]:
                print(f"Failed to create upload session: {response.status_code}")
                print(f"Error: {response.text}")
                return False
            
            session_data = response.json()
            upload_url = session_data.get('uploadUrl')
            
            if not upload_url:
                print("No upload URL received")
                return False
            
            print("Upload session created successfully")
            
            # Step 2: Upload file in chunks
            file_size = os.path.getsize(local_file_path)
            chunk_size = 320 * 1024  # 320KB chunks (must be multiple of 320KB)
            
            with open(local_file_path, 'rb') as f:
                bytes_uploaded = 0
                
                while bytes_uploaded < file_size:
                    chunk_start = bytes_uploaded
                    chunk_end = min(bytes_uploaded + chunk_size - 1, file_size - 1)
                    chunk_data = f.read(chunk_end - chunk_start + 1)
                    
                    # Prepare headers for this chunk
                    chunk_headers = {
                        'Content-Range': f'bytes {chunk_start}-{chunk_end}/{file_size}',
                        'Content-Length': str(len(chunk_data))
                    }
                    
                    # Upload chunk
                    chunk_response = requests.put(upload_url, headers=chunk_headers, data=chunk_data)
                    
                    if chunk_response.status_code in [200, 201, 202]:
                        bytes_uploaded += len(chunk_data)
                        progress = (bytes_uploaded / file_size) * 100
                        print(f"Upload progress: {progress:.1f}% ({bytes_uploaded:,}/{file_size:,} bytes)")
                        
                        # Check if upload is complete
                        if chunk_response.status_code in [200, 201]:
                            response_data = chunk_response.json()
                            file_name = response_data.get('name', 'Unknown')
                            print(f"File uploaded successfully: {file_name}")
                            return True
                    else:
                        print(f"Chunk upload failed: {chunk_response.status_code}")
                        print(f"Error: {chunk_response.text}")
                        return False
            
            return True
            
        except Exception as e:
            print(f"Error during resumable upload: {e}")
            return False

    def get_file_by_id(self, site_url: str, file_id: str) -> Optional[Dict]:
        """
        Get a specific file by its ID
        
        Args:
            site_url: SharePoint site URL
            file_id: The file's unique identifier
            
        Returns:
            dict: File information including download URL, or None if failed
        """
        print(f"Accessing file by ID: {file_id}")
        
        # First get the site
        site_info = self.get_site(site_url)
        if not site_info:
            return None
        
        site_id = site_info.get('id')
        
        # Try different endpoint formats for different ID types
        endpoints_to_try = [
            f"sites/{site_id}/drive/items/{file_id}",  # Most common - drive item ID
            f"sites/{site_id}/drives/b!{file_id.split('!')[-1] if '!' in file_id else file_id}/root",  # If it's a drive ID
        ]
        
        # Get all drives first to try each one
        drives_response = self._make_request(f"sites/{site_id}/drives")
        if drives_response:
            drives = drives_response.get('value', [])
            for drive in drives:
                drive_id = drive.get('id')
                endpoints_to_try.append(f"sites/{site_id}/drives/{drive_id}/items/{file_id}")
        
        for endpoint in endpoints_to_try:
            try:
                print(f"Trying endpoint: {endpoint}")
                file_response = self._make_request(endpoint)
                
                if file_response:
                    file_name = file_response.get('name', 'Unknown')
                    file_size = file_response.get('size', 0)
                    download_url = file_response.get('@microsoft.graph.downloadUrl')
                    
                    print(f"Found file: {file_name} ({file_size:,} bytes)")
                    
                    file_response['site_id'] = site_id
                    file_response['download_url'] = download_url
                    
                    return file_response
                    
            except Exception as e:
                print(f"Error with endpoint {endpoint}: {e}")
                continue
        
        print("File not found with any endpoint format")
        return None

    def get_folder_file_list(self, site_url: str, folder_path: str) -> List[Dict]:
        """
        Get simplified list of file names and IDs within a specified folder
        
        Args:
            site_url: SharePoint site URL
            folder_path: Path to the folder
            
        Returns:
            list: List of dictionaries with 'name' and 'id' keys only
        """
        print(f"Getting file list from folder: {folder_path}")
        
        # Use existing get_folder method
        folder_info = self.get_folder(site_url, folder_path)
        if not folder_info:
            return []
        
        items = folder_info.get('items', [])
        file_list = []
        
        for item in items:
            # Only include files (not folders)
            if 'file' in item:
                file_info = {
                    'name': item.get('name'),
                    'id': item.get('id'),
                    # Add additional information for debugging
                    'webUrl': item.get('webUrl', ''),
                    'download_url': item.get('@microsoft.graph.downloadUrl', ''),
                    'parentReference': item.get('parentReference', {})
                }
                file_list.append(file_info)
                
                # Debug print
                print(f"  File: {item.get('name')} | ID: {item.get('id')}")
        
        print(f"Found {len(file_list)} files")
        return file_list
    
    
    
    def modify_excel_file(self, site_url: str, folder_path: str, file_name: str, 
                     modifications_func, local_temp_path: str = None) -> bool:
        """
        Download an Excel file, modify it, and upload it back to SharePoint
        
        Args:
            site_url: SharePoint site URL
            folder_path: Path to the folder containing the Excel file
            file_name: Name of the Excel file
            modifications_func: Function that takes the file path and modifies the Excel file
            local_temp_path: Temporary local path (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """

        
        # Set up temporary file path
        if not local_temp_path:
            temp_dir = tempfile.gettempdir()
            local_temp_path = os.path.join(temp_dir, f"temp_{file_name}")
        
        try:
            print(f"Downloading Excel file: {file_name}")
            
            # Download the file
            download_success = self.download_file(
                site_url=site_url,
                file_path=file_name,
                local_path=local_temp_path,
                folder_name=folder_path
            )
            
            if not download_success:
                print("Failed to download Excel file")
                return False
            
            print(f"Modifying Excel file locally...")
            
            # Apply modifications using the provided function
            modifications_func(local_temp_path)
            
            print(f"Uploading modified Excel file back to SharePoint...")
            
            # Upload the modified file back
            upload_success = self.upload_file(
                site_url=site_url,
                local_file_path=local_temp_path,
                remote_file_name=file_name,
                folder_name=folder_path
            )
            
            if upload_success:
                print("Excel file successfully modified and uploaded")
                return True
            else:
                print("Failed to upload modified Excel file")
                return False
                
        except Exception as e:
            print(f"Error modifying Excel file: {e}")
            return False
        finally:
            # Clean up temporary file
            if os.path.exists(local_temp_path):
                os.remove(local_temp_path)
                print(f"Cleaned up temporary file: {local_temp_path}")

    def get_first_file_from_folder(self, site_url: str, folder_path: str) -> Optional[Dict]:
        """
        Get the first file found in a specific folder
        
        Args:
            site_url: SharePoint site URL
            folder_path: Path to the folder
            
        Returns:
            dict: First file information or None if no files found
        """
        print(f"Looking for first file in folder: {folder_path}")
        
        folder_info = self.get_folder(site_url, folder_path)
        if not folder_info:
            return None
        
        items = folder_info.get('items', [])
        
        # Find the first file
        for item in items:
            if 'file' in item:  # This is a file, not a folder
                file_name = item.get('name')
                file_size = item.get('size', 0)
                print(f"Found first file: {file_name} ({file_size:,} bytes)")
                
                # Add additional metadata
                item['site_id'] = folder_info.get('site_id')
                item['drive_id'] = folder_info.get('drive_id')
                item['download_url'] = item.get('@microsoft.graph.downloadUrl')
                
                return item
        
        print("No files found in folder")
        return None
    
    def list_all_sites(self) -> List[Dict]:
        """
        List all SharePoint sites available in the tenant
        
        Returns:
            list: List of site information including URLs, names, and IDs
        """
        print("=== Retrieving all sites in tenant ===")
        
        try:
            # Get all sites in the tenant
            endpoint = "sites?search=*"
            response = self._make_request(endpoint)
            
            sites = []
            if response:
                site_list = response.get('value', [])
                print(f"Found {len(site_list)} sites in tenant")
                
                for site in site_list:
                    site_info = {
                        'id': site.get('id', ''),
                        'name': site.get('name', ''),
                        'displayName': site.get('displayName', ''),
                        'webUrl': site.get('webUrl', ''),
                        'description': site.get('description', ''),
                        'createdDateTime': site.get('createdDateTime', ''),
                        'lastModifiedDateTime': site.get('lastModifiedDateTime', ''),
                        'siteCollection': site.get('siteCollection', {})
                    }
                    sites.append(site_info)
                    
                    print(f"  📍 {site_info['displayName']} ({site_info['name']})")
                    print(f"     URL: {site_info['webUrl']}")
                    print(f"     ID: {site_info['id']}")
                    print(f"     Description: {site_info['description']}")
                    print()
            
            return sites
            
        except Exception as e:
            print(f"Error retrieving sites: {e}")
            return []

    def analyze_specific_site(site_url: str):
        """
        Analyze a specific site in detail
        
        Args:
            site_url: URL of the SharePoint site to analyze
        """
        sp_client = SharePointClient()
        
        if not sp_client.authenticate():
            print("Authentication failed")
            return
        
        print(f"🔍 ANALYZING SITE: {site_url}")
        print("=" * 60)
        
        # Get site information
        site_info = sp_client.get_site(site_url)
        if site_info:
            print("📋 SITE INFORMATION")
            print(f"Name: {site_info.get('displayName', 'Unknown')}")
            print(f"Description: {site_info.get('description', 'No description')}")
            print(f"Created: {site_info.get('createdDateTime', 'Unknown')}")
            print(f"Modified: {site_info.get('lastModifiedDateTime', 'Unknown')}")
            print()
        
        # Get all drives and their contents
        print("📁 DRIVES AND CONTENTS")
        drive_contents = sp_client.list_drive_contents(site_url)
        
        if drive_contents:
            for drive_name, content in drive_contents.items():
                print(f"\n🗂️  Drive: {drive_name}")
                
                # List folders
                if content['folders']:
                    print(f"   📂 Folders ({len(content['folders'])}):")
                    for folder in content['folders']:
                        print(f"      - {folder['name']} ({folder.get('childCount', 0)} items)")
                
                # List files
                if content['files']:
                    print(f"   📄 Files ({len(content['files'])}):")
                    for file in content['files']:
                        file_type = "📊 Excel" if file['name'].endswith(('.xlsx', '.xls')) else "📄 Other"
                        size_mb = file.get('size', 0) / (1024 * 1024)
                        print(f"      - {file_type} {file['name']} ({size_mb:.1f} MB)")
        
        return site_info

    def get_site_collections(self) -> List[Dict]:
        """
        Get all site collections (root sites) in the tenant
        
        Returns:
            list: List of site collection information
        """
        print("=== Retrieving site collections ===")
        
        try:
            # Get site collections using a different endpoint
            endpoint = "sites?$filter=siteCollection/root ne null"
            response = self._make_request(endpoint)
            
            site_collections = []
            if response:
                collections = response.get('value', [])
                print(f"Found {len(collections)} site collections")
                
                for collection in collections:
                    collection_info = {
                        'id': collection.get('id', ''),
                        'name': collection.get('name', ''),
                        'displayName': collection.get('displayName', ''),
                        'webUrl': collection.get('webUrl', ''),
                        'hostname': collection.get('siteCollection', {}).get('hostname', ''),
                        'root': collection.get('siteCollection', {}).get('root', {})
                    }
                    site_collections.append(collection_info)
                    
                    print(f"  🏢 {collection_info['displayName']}")
                    print(f"     URL: {collection_info['webUrl']}")
                    print(f"     Hostname: {collection_info['hostname']}")
                    print()
            
            return site_collections
            
        except Exception as e:
            print(f"Error retrieving site collections: {e}")
            return []

    def search_sites_by_name(self, search_term: str) -> List[Dict]:
        """
        Search for sites by name or keyword
        
        Args:
            search_term: Term to search for in site names
            
        Returns:
            list: List of matching sites
        """
        print(f"=== Searching sites for term: '{search_term}' ===")
        
        try:
            # Search sites using the search parameter
            endpoint = f"sites?search={search_term}"
            response = self._make_request(endpoint)
            
            matching_sites = []
            if response:
                sites = response.get('value', [])
                print(f"Found {len(sites)} sites matching '{search_term}'")
                
                for site in sites:
                    site_info = {
                        'id': site.get('id', ''),
                        'name': site.get('name', ''),
                        'displayName': site.get('displayName', ''),
                        'webUrl': site.get('webUrl', ''),
                        'description': site.get('description', '')
                    }
                    matching_sites.append(site_info)
                    
                    print(f"  🔍 {site_info['displayName']} ({site_info['name']})")
                    print(f"     URL: {site_info['webUrl']}")
                    print()
            
            return matching_sites
            
        except Exception as e:
            print(f"Error searching sites: {e}")
            return []

    def get_my_sites(self) -> List[Dict]:
        """
        Get sites that the current user has access to
        
        Returns:
            list: List of sites the user can access
        """
        print("=== Retrieving sites user has access to ===")
        
        try:
            # Get sites the user follows or has recent activity on
            endpoint = "me/followedSites"
            followed_response = self._make_request(endpoint)
            
            # Also get sites from recent activity
            recent_endpoint = "me/insights/used?$filter=resourceVisualization/containerWebUrl ne null"
            recent_response = self._make_request(recent_endpoint)
            
            user_sites = []
            
            # Process followed sites
            if followed_response:
                followed_sites = followed_response.get('value', [])
                for site in followed_sites:
                    site_info = {
                        'id': site.get('id', ''),
                        'name': site.get('name', ''),
                        'displayName': site.get('displayName', ''),
                        'webUrl': site.get('webUrl', ''),
                        'source': 'followed'
                    }
                    user_sites.append(site_info)
            
            # Process recent sites (from insights)
            if recent_response:
                recent_items = recent_response.get('value', [])
                for item in recent_items:
                    resource = item.get('resourceVisualization', {})
                    container_url = resource.get('containerWebUrl', '')
                    if container_url and 'sharepoint.com' in container_url:
                        site_info = {
                            'webUrl': container_url,
                            'displayName': resource.get('containerDisplayName', 'Recent Site'),
                            'source': 'recent_activity'
                        }
                        # Avoid duplicates
                        if not any(s.get('webUrl') == container_url for s in user_sites):
                            user_sites.append(site_info)
            
            print(f"Found {len(user_sites)} sites for current user")
            for site in user_sites:
                print(f"  👤 {site.get('displayName', 'Unknown')} ({site.get('source', 'unknown')})")
                print(f"     URL: {site.get('webUrl', 'Unknown')}")
                print()
            
            return user_sites
            
        except Exception as e:
            print(f"Error retrieving user sites: {e}")
            return []
        
    
    def move_file(self, site_url: str, file_id: str, destination_folder_path: str) -> bool:
        """
        Move a file to a different folder within the same SharePoint site
        
        Args:
            site_url: SharePoint site URL
            file_id: ID of the file to move
            destination_folder_path: Path to the destination folder
            
        Returns:
            bool: True if move successful, False otherwise
        """
        print(f"Moving file {file_id} to folder: {destination_folder_path}")
        
        try:
            # Get site information
            site_info = self.get_site(site_url)
            if not site_info:
                return False
            
            site_id = site_info.get('id')
            
            # Get all drives to find the file
            drives_response = self._make_request(f"sites/{site_id}/drives")
            if not drives_response:
                return False
            
            drives = drives_response.get('value', [])
            file_found = False
            source_drive_id = None
            
            # Find which drive contains the file
            for drive in drives:
                drive_id = drive.get('id')
                
                # Try to get the file from this drive
                file_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{file_id}"
                file_response = self._make_request(file_endpoint)
                
                if file_response:
                    file_found = True
                    source_drive_id = drive_id
                    file_name = file_response.get('name', 'Unknown')
                    print(f"Found file '{file_name}' in drive: {drive.get('name')}")
                    break
            
            if not file_found:
                print("File not found in any drive")
                return False
            
            # Get or create the destination folder
            destination_folder_info = self.get_folder(site_url, destination_folder_path)
            if not destination_folder_info:
                print(f"Destination folder '{destination_folder_path}' not found")
                return False
            
            destination_folder_id = destination_folder_info.get('id')
            destination_drive_id = destination_folder_info.get('drive_id')
            
            # Check if we're moving within the same drive
            if source_drive_id != destination_drive_id:
                print("Moving files between different drives is not supported")
                return False
            
            # Prepare the move request
            move_data = {
                "parentReference": {
                    "id": destination_folder_id
                }
            }
            
            # Execute the move
            move_endpoint = f"sites/{site_id}/drives/{source_drive_id}/items/{file_id}"
            headers = self._get_headers()
            
            url = f"{self.graph_url}/{move_endpoint}"
            response = requests.patch(url, headers=headers, json=move_data)
            
            if response.status_code == 200:
                moved_file_info = response.json()
                print(f"File moved successfully to: {destination_folder_path}")
                print(f"New location: {moved_file_info.get('webUrl', 'Unknown')}")
                return True
            else:
                print(f"Move failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error moving file: {e}")
            return False

    def copy_file(self, site_url: str, file_id: str, destination_folder_path: str, new_name: str = None) -> bool:
        """
        Copy a file to a different folder within the same SharePoint site
        
        Args:
            site_url: SharePoint site URL
            file_id: ID of the file to copy
            destination_folder_path: Path to the destination folder
            new_name: New name for the copied file (optional)
            
        Returns:
            bool: True if copy successful, False otherwise
        """
        print(f"Copying file {file_id} to folder: {destination_folder_path}")
        
        try:
            # Get site information
            site_info = self.get_site(site_url)
            if not site_info:
                return False
            
            site_id = site_info.get('id')
            
            # Get all drives to find the file
            drives_response = self._make_request(f"sites/{site_id}/drives")
            if not drives_response:
                return False
            
            drives = drives_response.get('value', [])
            file_found = False
            source_drive_id = None
            original_file_name = None
            
            # Find which drive contains the file
            for drive in drives:
                drive_id = drive.get('id')
                
                # Try to get the file from this drive
                file_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{file_id}"
                file_response = self._make_request(file_endpoint)
                
                if file_response:
                    file_found = True
                    source_drive_id = drive_id
                    original_file_name = file_response.get('name', 'Unknown')
                    print(f"Found file '{original_file_name}' in drive: {drive.get('name')}")
                    break
            
            if not file_found:
                print("File not found in any drive")
                return False
            
            # Get the destination folder
            destination_folder_info = self.get_folder(site_url, destination_folder_path)
            if not destination_folder_info:
                print(f"Destination folder '{destination_folder_path}' not found")
                return False
            
            destination_folder_id = destination_folder_info.get('id')
            destination_drive_id = destination_folder_info.get('drive_id')
            
            # Check if we're copying within the same drive
            if source_drive_id != destination_drive_id:
                print("Copying files between different drives is not supported")
                return False
            
            # Prepare the copy request
            copy_name = new_name if new_name else original_file_name
            copy_data = {
                "parentReference": {
                    "id": destination_folder_id
                },
                "name": copy_name
            }
            
            # Execute the copy
            copy_endpoint = f"sites/{site_id}/drives/{source_drive_id}/items/{file_id}/copy"
            headers = self._get_headers()
            
            url = f"{self.graph_url}/{copy_endpoint}"
            response = requests.post(url, headers=headers, json=copy_data)
            
            if response.status_code == 202:  # Copy operation started (async)
                print(f"File copy initiated successfully")
                
                # The copy operation is asynchronous, so we get a location header to check progress
                location_header = response.headers.get('Location')
                if location_header:
                    print(f"Copy operation monitoring URL: {location_header}")
                    # You could implement polling logic here to check completion
                
                return True
            else:
                print(f"Copy failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error copying file: {e}")
            return False

    def delete_file(self, site_url: str, file_id: str) -> bool:
        """
        Delete a file from SharePoint
        
        Args:
            site_url: SharePoint site URL
            file_id: ID of the file to delete
            
        Returns:
            bool: True if delete successful, False otherwise
        """
        print(f"Deleting file {file_id}")
        
        try:
            # Get site information
            site_info = self.get_site(site_url)
            if not site_info:
                return False
            
            site_id = site_info.get('id')
            
            # Get all drives to find the file
            drives_response = self._make_request(f"sites/{site_id}/drives")
            if not drives_response:
                return False
            
            drives = drives_response.get('value', [])
            file_found = False
            
            # Find which drive contains the file and delete it
            for drive in drives:
                drive_id = drive.get('id')
                
                # Try to get the file from this drive
                file_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{file_id}"
                file_response = self._make_request(file_endpoint)
                
                if file_response:
                    file_found = True
                    file_name = file_response.get('name', 'Unknown')
                    print(f"Found file '{file_name}' in drive: {drive.get('name')}")
                    
                    # Delete the file
                    headers = self._get_headers()
                    url = f"{self.graph_url}/{file_endpoint}"
                    delete_response = requests.delete(url, headers=headers)
                    
                    if delete_response.status_code == 204:  # No Content = successful deletion
                        print(f"File '{file_name}' deleted successfully")
                        return True
                    else:
                        print(f"Delete failed: {delete_response.status_code}")
                        print(f"Error: {delete_response.text}")
                        return False
            
            if not file_found:
                print("File not found in any drive")
                return False
                
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False

    def create_folder(self, site_url: str, parent_folder_path: str, new_folder_name: str) -> bool:
        """
        Create a new folder in SharePoint
        
        Args:
            site_url: SharePoint site URL
            parent_folder_path: Path to the parent folder where new folder will be created
            new_folder_name: Name of the new folder
            
        Returns:
            bool: True if creation successful, False otherwise
        """
        print(f"Creating folder '{new_folder_name}' in '{parent_folder_path}'")
        
        try:
            # Get site information
            site_info = self.get_site(site_url)
            if not site_info:
                return False
            
            site_id = site_info.get('id')
            
            # Get parent folder info
            parent_folder_info = self.get_folder(site_url, parent_folder_path)
            if not parent_folder_info:
                print(f"Parent folder '{parent_folder_path}' not found")
                return False
            
            parent_folder_id = parent_folder_info.get('id')
            drive_id = parent_folder_info.get('drive_id')
            
            # Prepare folder creation data
            folder_data = {
                "name": new_folder_name,
                "folder": {},
                "@microsoft.graph.conflictBehavior": "rename"  # Rename if folder already exists
            }
            
            # Create the folder
            create_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{parent_folder_id}/children"
            headers = self._get_headers()
            
            url = f"{self.graph_url}/{create_endpoint}"
            response = requests.post(url, headers=headers, json=folder_data)
            
            if response.status_code == 201:  # Created
                folder_info = response.json()
                created_folder_name = folder_info.get('name', 'Unknown')
                print(f"Folder created successfully: '{created_folder_name}'")
                return True
            else:
                print(f"Folder creation failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error creating folder: {e}")
            return False
        
    def upload_file_to_folder_path(self, site_url: str, local_file_path: str, folder_path: str, remote_file_name: str = None) -> bool:
            """
            Upload a file to a specific SharePoint folder using full folder path.
            This method finds the exact folder by path and uploads there.
    
            Args:
                site_url: SharePoint site URL
                local_file_path: Path to the local file to upload
                folder_path: Full path to the target folder (e.g., "Documents/Marcos Bank Recs/Regional/District/Store/Bank Transaction Reports")
                remote_file_name: Name for the file in SharePoint (optional, uses local filename if not provided)
    
            Returns:
                bool: True if upload successful, False otherwise
            """
            print(f"Uploading file: {local_file_path}")
    
            if not os.path.exists(local_file_path):
                print(f"Local file not found: {local_file_path}")
                return False
    
            # Get file info
            file_size = os.path.getsize(local_file_path)
            print(f"File size: {file_size:,} bytes")
    
            if not remote_file_name:
                remote_file_name = os.path.basename(local_file_path)
    
            print(f"Accessing site: {site_url}")
    
            # Get site info
            site_info = self.get_site(site_url)
            if not site_info:
                return False
    
            site_id = site_info.get('id')
    
            # Get the target folder using the existing get_folder method
            print(f"Finding target folder: {folder_path}")
            folder_info = self.get_folder(site_url, folder_path)
            if not folder_info:
                print(f"Target folder not found: {folder_path}")
                return False
    
            # Get the drive ID from the folder info (where the folder was actually found)
            # This is the new improved functionality that uses the correct drive
            drive_id = folder_info.get('drive_id')
            drive_name = 'Unknown'
    
            if drive_id:
                # New functionality: Use the drive where folder was found
                print("Using drive ID from folder info (improved functionality)")
    
                # Get drive name for logging
                drives_response = self._make_request(f"sites/{site_id}/drives")
                if drives_response:
                    drives = drives_response.get('value', [])
                    for drive in drives:
                        if drive.get('id') == drive_id:
                            drive_name = drive.get('name', 'Unknown')
                            break
            else:
                # Fallback to original behavior for backward compatibility
                print("Drive ID not found in folder info, falling back to original behavior")
                drives_response = self._make_request(f"sites/{site_id}/drives")
                if not drives_response:
                    print("Could not get site drives")
                    return False
    
                drives = drives_response.get('value', [])
                if not drives:
                    print("No drives found in site")
                    return False
    
                # Use the first drive (original behavior)
                drive = drives[0]
                drive_id = drive.get('id')
                drive_name = drive.get('name', 'Unknown')
                print(f"Using first drive for backward compatibility: {drive_name}")
    
            print(f"Uploading to drive: {drive_name}")
            print(f"Target folder: {folder_path.split('/')[-1]}")
    
            # Get folder ID from the folder info
            folder_id = folder_info.get('id')
            if not folder_id:
                print("Could not get folder ID")
                return False
    
            try:
                # Choose upload method based on file size
                if file_size < 4 * 1024 * 1024:  # Less than 4MB
                    print("Using simple upload method")
                    return self._simple_upload_to_folder_id(site_id, drive_id, folder_id, local_file_path, remote_file_name)
                else:
                    print("Using resumable upload method")
                    return self._resumable_upload_to_folder_id(site_id, drive_id, folder_id, local_file_path, remote_file_name)
    
            except Exception as e:
                print(f"Upload failed: {e}")
                return False
            

    def _simple_upload_to_folder_id(self, site_id: str, drive_id: str, folder_id: str, local_file_path: str, remote_file_name: str) -> bool:
            """Upload small files directly to a specific folder ID"""
            try:
                with open(local_file_path, 'rb') as file:
                    file_content = file.read()
    
                # Upload directly to the folder using folder ID
                upload_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{folder_id}:/{remote_file_name}:/content"
                headers = self._get_headers()
                headers['Content-Type'] = 'application/octet-stream'
    
                url = f"{self.graph_url}/{upload_endpoint}"
                response = requests.put(url, headers=headers, data=file_content)
    
                if response.status_code in [200, 201]:
                    uploaded_file_info = response.json()
                    print(f"File uploaded successfully: {uploaded_file_info.get('webUrl', 'Unknown URL')}")
                    return True
                else:
                    print(f"Upload failed: {response.status_code}")
                    if response.text:
                        print(f"Error: {response.text}")
                    return False
    
            except Exception as e:
                print(f"Simple upload error: {e}")
                return False

    # Example modification function
def example_excel_modifications(file_path: str):
    """
    Example function to modify an Excel file using pandas or openpyxl
    """

    
    # Method 1: Using pandas (good for data manipulation)
    try:
        # Read the Excel file
        df = pd.read_excel(file_path, sheet_name='Sheet1')  # Adjust sheet name
        
        # Make modifications - example: add a new column
        df['Last_Modified'] = pd.Timestamp.now()
        
        # Add a new row
        new_row = {'Column1': 'New Value', 'Last_Modified': pd.Timestamp.now()}
        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        
        # Save back to Excel
        df.to_excel(file_path, sheet_name='Sheet1', index=False)
        
        print("Excel modifications completed using pandas")
        
    except Exception as e:
        print(f"Pandas method failed, trying openpyxl: {e}")
        
        # Method 2: Using openpyxl (good for formatting and complex operations)
        try:
            workbook = load_workbook(file_path)
            worksheet = workbook.active  # Or specify: workbook['Sheet1']
            
            # Example modifications
            # Add data to a specific cell
            worksheet['A1'] = 'Modified on:'
            worksheet['B1'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Add data to next available row
            max_row = worksheet.max_row
            worksheet[f'A{max_row + 1}'] = 'New entry'
            worksheet[f'B{max_row + 1}'] = 'Added programmatically'
            
            # Save the workbook
            workbook.save(file_path)
            workbook.close()
            
            print("Excel modifications completed using openpyxl")
            
        except Exception as e2:
            print(f"openpyxl method also failed: {e2}")
            raise

# Example usage
def test_sharepoint_client():
    """Test the SharePoint client"""

    # site_url = "https://highlandventuresltd442.sharepoint.com/sites/hrg"
    site_url = "https://highlandventuresltd442.sharepoint.com/sites/hrg"

    file_id = "29ec76c4-f91e-41d9-8a4d-d3a3ce96d8d3"

    target_file_path="2025 HRG Plan MASTER.xlsx"
    target_drive_name="Documents"
    target_folder_name="Test"
    local_download_path = "downloaded_2025 HRG Plan MASTER.xlsx"

    local_upload_file = "test_upload.txt"  # Create a test file
    remote_upload_name = "uploaded_test_file.txt"
    
    # Initialize client
    client = SharePointClient()

    print("\nTesting HRG site access")
    print("=" * 60)
    
    # Get site information
    site_info = client.get_site(site_url)
    if not site_info:
        print("Could not access HRG site")
        return
    
    # List all drives and their contents
    print("\nListing all drive contents...")
    drive_contents = client.list_drive_contents(site_url)
    
    # List all folders specifically
    print("\nListing all folders...")
    all_folders = client.list_all_folders(site_url)
    
    if all_folders:
        print("Available folders:")
        for folder in all_folders:
            print(f"  {folder['full_path']}")

    return

def test_excel_modification():
    client = SharePointClient()
    
    if client.authenticate():
        site_url = "https://highlandventuresltd442.my-sharepoint.com/personal/solson_hv_ltd"
        #site_url = "https://highlandventuresltd442.sharepoint.com/sites/dev"

        success = client.copy_file(
            site_url=site_url,
            file_id="29ec76c4-f91e-41d9-8a4d-d3a3ce96d8d3",  # Replace with your actual file ID
            new_name="Annual Principal Payments - Property Summary - prod backup.xlsx"  # Optional new name for the copied file
        )

        # Modify an Excel file
        # success = client.modify_excel_file(
        #     site_url=site_url,
        #     folder_path='General',
        #     file_name='Book.xlsx',  # Replace with your actual file name
        #     modifications_func=example_excel_modifications
        # )
        
        if success:
            print("Excel file modification completed successfully!")
        else:
            print("Excel file modification failed!")

if __name__ == "__main__":
    #test_sharepoint_client()
    test_excel_modification()
    #