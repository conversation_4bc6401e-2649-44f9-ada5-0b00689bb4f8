import requests
from typing import Optional, Dict, List, Any
import json
from libs.sharepoint_helper import SharePointClient
# from sharepoint_helper import SharePointClient
import os
from datetime import datetime
import time
import random

class SharePointExcelOnline:
    def __init__(self, sharepoint_client=None):
        """
        Initialize Excel Online helper with an authenticated SharePoint client
        
        Args:
            sharepoint_client: Authenticated SharePointClient instance
        """

        if sharepoint_client is None: # create new sharepoint client
            self.sp_client = SharePointClient()
            
            if not self.sp_client.authenticate():
                print("SharePointClient Authentication failed")
                raise ValueError("SharePointClient authentication failed")
        else: # use existing sharepoint client
            self.sp_client = sharepoint_client

        self.graph_url = "https://graph.microsoft.com/v1.0"
    
    def get_excel_file_session(self, site_url: str, file_path: str, 
                              drive_name: str = None, folder_name: str = None) -> Optional[Dict]:
        """
        Get Excel file information and create a session for operations
        
        Args:
            site_url: SharePoint site URL
            file_path: Excel file name or path
            drive_name: Specific drive name (optional)
            folder_name: Specific folder name (optional)
            
        Returns:
            dict: File information with session details, or None if failed
        """
        print(f"Creating Excel session for: {file_path}")
        
        # Get file information using existing SharePoint client
        file_info = self.sp_client.get_file(site_url, file_path, drive_name, folder_name)
        if not file_info:
            return None
        
        # Check if it's an Excel file
        file_name = file_info.get('name', '')
        if not file_name.endswith(('.xlsx', '.xls')):
            print(f"File {file_name} is not an Excel file")
            return None
        
        site_id = file_info.get('site_id')
        drive_id = file_info.get('drive_id')
        item_id = file_info.get('id')

        print(f"Found site_id {site_id}")
        print(f"Found drive_id {drive_id}")
        print(f"Found item_id {item_id}")
        
        return {
            'site_id': site_id,
            'drive_id': drive_id,
            'item_id': item_id,
            'file_name': file_name,
            'file_info': file_info
        }
    
    def get_excel_file_by_id(self, site_url: str, file_id: str) -> Optional[Dict]:
        """
        Get Excel file session information by file ID
        
        Args:
            site_url: SharePoint site URL
            file_id: Excel file ID
            
        Returns:
            dict: File information with session details, or None if failed
        """
        print(f"Creating Excel session for file ID: {file_id}")
        
        # Get file information using existing SharePoint client
        file_info = self.sp_client.get_file_by_id(site_url, file_id)
        if not file_info:
            return None
        
        # Check if it's an Excel file
        file_name = file_info.get('name', '')
        if not file_name.endswith(('.xlsx', '.xls')):
            print(f"File {file_name} is not an Excel file")
            return None
        
        site_id = file_info.get('site_id')
        item_id = file_info.get('id')
        
        # Get drive_id from the file info
        parent_ref = file_info.get('parentReference', {})
        drive_id = parent_ref.get('driveId')
        
        return {
            'site_id': site_id,
            'drive_id': drive_id,
            'item_id': item_id,
            'file_name': file_name,
            'file_info': file_info
        }
    
    def list_worksheets(self, excel_session: Dict) -> List[Dict]:
        """
        List all worksheets in an Excel file
        
        Args:
            excel_session: Excel session information from get_excel_file_session
            
        Returns:
            list: List of worksheet information
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets"
        response = self.sp_client._make_request(endpoint)
        
        if response:
            worksheets = response.get('value', [])
            print(f"\nFound {len(worksheets)} worksheets")
            for ws in worksheets:
                print(f"  - {ws.get('name')} (ID: {ws.get('id')})")
            return worksheets
        
        return []
    
    def get_worksheet_data(self, excel_session: Dict, worksheet_name: str = None, 
                          range_address: str = None) -> Optional[Dict]:
        """
        Get data from a specific worksheet or range
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name of the worksheet (optional, uses first if not specified)
            range_address: Specific range like "A1:C10" (optional, gets used range if not specified)
            
        Returns:
            dict: Worksheet data including values and formatting
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        # Build the endpoint
        if worksheet_name:
            base_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')"
        else:
            base_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets"
        
        if range_address:
            endpoint = f"{base_endpoint}/range(address='{range_address}')"
        else:
            endpoint = f"{base_endpoint}/usedRange"
        
        print(f"get_worksheet_data() - Getting data from: {worksheet_name or 'first worksheet'}")
        if range_address:
            print(f"Range: {range_address}")
        
        response = self.sp_client._make_request(endpoint)
        
        if response:
            values = response.get('values', [])
            print(f"Retrieved {len(values)} rows of data")
            return response
        
        return None
    
    def update_worksheet_range(self, excel_session: Dict, worksheet_name: str, 
                              range_address: str, values: List[List]) -> bool:
        """
        Update a specific range in a worksheet
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name of the worksheet
            range_address: Range to update (e.g., "A1:C3")
            values: 2D array of values to write
            
        Returns:
            bool: True if successful, False otherwise
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/range(address='{range_address}')"
        
        data = {
            "values": values
        }
        
        print(f"Updating range {range_address} in worksheet '{worksheet_name}'")
        
        try:
            headers = self.sp_client._get_headers()
            url = f"{self.graph_url}/{endpoint}"
            
            response = requests.patch(url, headers=headers, json=data)
            
            if response.status_code == 200:
                print("Range updated successfully")
                return True
            else:
                print(f"Update failed: {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error updating range: {e}")
            return False
    
    def add_worksheet(self, excel_session: Dict, worksheet_name: str) -> bool:
        """
        Add a new worksheet to the Excel file
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name for the new worksheet
            
        Returns:
            bool: True if successful, False otherwise
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets"
        
        data = {
            "name": worksheet_name
        }
        
        print(f"Adding new worksheet: {worksheet_name}")
        
        response = self.sp_client._make_request(endpoint, method='POST', data=data)
        
        if response:
            print(f"Worksheet '{worksheet_name}' added successfully")
            return True
        else:
            print(f"Failed to add worksheet '{worksheet_name}'")
            return False
    
    def append_data_to_worksheet(self, excel_session: Dict, worksheet_name: str, 
                                data: List[List], reset_from_first_row: bool = False, forced_start_row: int = 1, forced_end_column: str = None) -> bool:
        """
        Append data to the end of a worksheet
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name of the worksheet
            data: 2D array of data to append
            
        Returns:
            bool: True if successful, False otherwise
        """
        print(f"Appending {len(data)} rows to worksheet '{worksheet_name}'. reset_from_first_row: {reset_from_first_row}")
        
        if reset_from_first_row:
            start_row = forced_start_row
        else:
            # First, get the used range to find where to append
            used_range = self.get_worksheet_data(excel_session, worksheet_name)
            if not used_range:
                # If no used range, start at A1
                start_row = 1
            else:
                # Get the last row and add 1
                row_count = used_range.get('rowCount', 0)
                start_row = row_count + 1

        # start_row = 3 # test
        # end_row = 100 # test

        # Calculate the range for the new data
        start_col = 'A'
        # end_col = chr(ord('A') + len(data[0]) - 1) if data else 'A'
        calculated_end_col = chr(ord('A') + len(data[0]) - 1) if data else 'A'
        end_col = forced_end_column if forced_end_column else chr(ord('A') + len(data[0]) - 1) if data else 'A'
        end_row = start_row + len(data) - 1

        data_len = len(data)
        data_last_row_len = len(data[data_len - 1])
        data_first_row_len = len(data[0])
        first_row_data = data[0]
        last_row_data = data[data_len - 1]

        
        range_address = f"{start_col}{start_row}:{end_col}{end_row}" # append_data_to_worksheet() - Appending data to worksheet 'Sheet2' at range A74:D83


        print(f"\n\nappend_data_to_worksheet() - Appending data to worksheet '{worksheet_name}' at range {range_address}. forced_end_column: {forced_end_column}. \ndata_first_row_len: {data_first_row_len}. calculated_end_col: {calculated_end_col}. data_len: {data_len}. data_last_row_len: {data_last_row_len}. \n\nfirst_row_data({data_first_row_len}): {first_row_data}\n\nlast_row_data({data_last_row_len}): {last_row_data}\n\n")
        
        return self.update_worksheet_range(excel_session, worksheet_name, range_address, data)
    
    def create_table(self, excel_session: Dict, worksheet_name: str, 
                    range_address: str, table_name: str, has_headers: bool = True) -> bool:
        """
        Create a table from a range in the worksheet
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name of the worksheet
            range_address: Range to convert to table
            table_name: Name for the table
            has_headers: Whether the range includes headers
            
        Returns:
            bool: True if successful, False otherwise
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/tables/add"
        
        data = {
            "address": range_address,
            "hasHeaders": has_headers,
            "name": table_name
        }
        
        print(f"Creating table '{table_name}' from range {range_address}")
        
        response = self.sp_client._make_request(endpoint, method='POST', data=data)
        
        if response:
            print(f"Table '{table_name}' created successfully")
            return True
        else:
            print(f"Failed to create table '{table_name}'")
            return False
    
    # def save_workbook(self, excel_session: Dict) -> bool:
    #     """
    #     Save the workbook (equivalent to Ctrl+S)
        
    #     Args:
    #         excel_session: Excel session information
            
    #     Returns:
    #         bool: True if successful, False otherwise
    #     """
    #     site_id = excel_session['site_id']
    #     drive_id = excel_session['drive_id']
    #     item_id = excel_session['item_id']
        
    #     endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/application/calculate"
        
    #     print("Saving workbook...")
        
    #     # Force calculation and save
    #     response = self.sp_client._make_request(endpoint, method='POST', data={})
        
    #     if response is not None:  # Success returns empty response
    #         print("Workbook saved successfully")
    #         return True
    #     else:
    #         print("Failed to save workbook")
    #         return False


    def save_workbook(self, excel_session: Dict) -> bool:
        """
        Save the workbook (forces recalculation and ensures changes are persisted)
        
        Args:
            excel_session: Excel session information
            
        Returns:
            bool: True if successful, False otherwise
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        print("Saving workbook...")
        
        # Method 1: Force calculation (this ensures formulas are updated)
        calc_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/application/calculate"
        calc_response = self.sp_client._make_request(calc_endpoint, method='POST', data={})
        
        # Method 2: Create a session to ensure persistence (optional but safer)
        session_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/createSession"
        session_data = {"persistChanges": True}
        session_response = self.sp_client._make_request(session_endpoint, method='POST', data=session_data)
        
        if session_response:
            session_id = session_response.get('id')
            if session_id:
                # Close the session to commit changes
                # start: commented due to 400 error
                # close_endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/closeSession"
                # close_response = self.sp_client._make_request(close_endpoint, method='POST', data={})
                # end: commented due to 400 error
                print("Workbook saved and session closed")
                return True
        
        # Fallback - just the calculation should be enough for most cases
        if calc_response is not None:
            print("Workbook calculated (changes should be auto-saved)")
            return True
        
        print("Save operation may have failed")
        return False

    def clear_excel_sheet_cells(self, excel_session: Dict, worksheet_name: str, range_address: str) -> bool:
        """
        Clear the contents of a specific range in a worksheet
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name of the worksheet
            range_address: Range to clear (e.g., "A1:C10")
            
        Returns:
            bool: True if successful, False otherwise
        """

        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']


        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/range(address='{range_address}')/clear"
        
        print(f"Clearing range {range_address} in worksheet '{worksheet_name}'")
        
        response = self.sp_client._make_request(endpoint, method='POST', data={})
        
        if response is not None:  # Success returns empty response
            print(f"Range cleared successfully; {worksheet_name}")
            return True
        else:
            print(f"Failed to clear range: {worksheet_name}")
            return False
            
    def clear_excel_sheet_content(self, excel_session: Dict, worksheet_name: str, sleep_time_secs: int = 5, forced_end_column: str = None):
        
        # Get current data from the worksheet
        print(f"Getting current data for {worksheet_name}...")
        current_data = self.get_worksheet_data(excel_session, worksheet_name)
        
        if current_data:
            values = current_data.get('values', [])
            print(f"Current worksheet, {worksheet_name}, has {len(values)} rows")
            
            # Print first few rows as sample
            if values:
                # print("Sample data (first 3 rows):")
                headers = []
                for i, row in enumerate(values):
                    if i == 0: # skip header
                        headers = row
                        continue
                    # set every value to nothing to clear
                    for v in range(len(row)):
                        values[i][v] = ''

                    # print(f"  Row {i+1}: {row}")
                    # print(f"  Row {i}: {values[i]}")

                start_col = 'A'
                start_row = 1
                if forced_end_column:
                    end_col = forced_end_column
                else:
                    end_col = chr(ord('A') + len(headers) - 1) if headers else 'A'
                # end_col = forced_end_column if forced_end_column else chr(ord('A') + len(data[0]) - 1) if data else 'A'
                end_row = len(values)
                
                range_address = f"{start_col}{start_row}:{end_col}{end_row}" # append_data_to_worksheet() - Appending data to worksheet 'Sheet2' at range A74:D83

                self.update_worksheet_range(excel_session, worksheet_name, range_address, values)
                print(f"updating {worksheet_name}, range_address: {range_address}\n\n")
                time.sleep(sleep_time_secs)

        
    def get_excel_sheet_row_count(self, excel_session: Dict, worksheet_name: str) -> int:
        """
        Get the number of rows in a specific worksheet
        
        Args:
            excel_session: Excel session information
            worksheet_name: Name of the worksheet
            
        Returns:
            int: Number of rows in the worksheet
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/usedRange"
        
        response = self.sp_client._make_request(endpoint)
        
        if response:
            row_count = response.get('rowCount', 0)
            print(f"Worksheet '{worksheet_name}' has {row_count} rows")
            return row_count
        
        return 0
    
    def clear_excel_sheet_cells_custom(self, excel_session: Dict, worksheet_name: str, starting_data_row: int, first_data_column: str, last_data_column: str, sleep_time_secs: int = 5) -> tuple[int, bool]:
        """
        Clear cells in a specific worksheet
        """
        # First, get the used range to find where to append
        existing_row_count = self.get_excel_sheet_row_count(excel_session, worksheet_name)

        if existing_row_count > starting_data_row: 
            clearing_data_row_start = starting_data_row + 1  # Start clearing from row 3 (A3)            
            _range_address = f"{first_data_column}{clearing_data_row_start}:{last_data_column}{existing_row_count}" # e.g., "A3:D120"
            print(f"Clearing existing range: {_range_address} in worksheet '{worksheet_name}'")
            # Clear the range first
            cleared = self.clear_excel_sheet_cells(excel_session, worksheet_name, _range_address)
        
            if not cleared:
                print(f"\tFailed to clear the range for the worksheet {worksheet_name}.")
                return existing_row_count, False
            sleep_secs = 5
            print(f"\tExisting range cleared successfully for {worksheet_name}. Sleeeping for {sleep_secs} seconds before populating new data...")
            time.sleep(sleep_secs)
            return existing_row_count, True
        
        return existing_row_count, False
    

    def clean_values_from_none_to_empty_string(self, values_list: List[List]) -> List[List]:
        
        
            # Print first few rows as sample
        if values_list:
            # print("Sample data (first 3 rows):")

            for i, row in enumerate(values_list):

                # set every value to nothing to clear
                for v in range(len(row)):
                    if values_list[i][v] is None or values_list[i][v] == 'None' or str(values_list[i][v]).lower() == 'nan': # or values_list[i][v] == 'NaN'
                        values_list[i][v] = ''
        # print(f"clean_values_from_none_to_empty_string() - values_list: {values_list}")
        return values_list

    def get_worksheet_data_lightweight_by_path(self, site_url: str, folder_path: str, 
                                         file_name: str, worksheet_name: str = None, 
                                         max_rows: int = 1000, range_address: str = None) -> Optional[Dict]:
        """
        Get worksheet data with row limits to prevent crashes on large files
        
        Args:
            site_url: SharePoint site URL
            folder_path: Path to folder containing the file
            file_name: Name of the Excel file
            worksheet_name: Specific worksheet name (if None, uses first worksheet)
            max_rows: Maximum number of rows to retrieve (ignored if range_address is provided)
            range_address: Specific range like "A1:C100" (overrides max_rows if provided)
            
        Returns:
            dict: Limited worksheet data
        """
        print(f"Getting lightweight data from '{file_name}' in folder '{folder_path}'")
        
        # First, get the Excel session
        excel_session = self.get_excel_file_session(
            site_url=site_url,
            file_path=file_name,
            folder_name=folder_path
        )
        
        if not excel_session:
            print(f"Could not access Excel file: {file_name}")
            return None
        
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        # If no worksheet specified, get the first one
        if not worksheet_name:
            worksheets = self.list_worksheets(excel_session)
            if not worksheets:
                print("No worksheets found in file")
                return None
            worksheet_name = worksheets[0]['name']
            print(f"Using first worksheet: {worksheet_name}")
        
        # Use provided range_address or create one based on max_rows
        if not range_address:
            range_address = f"A1:ZZ{max_rows}"  # Adjust columns as needed
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/range(address='{range_address}')"
        
        print(f"Getting data from worksheet '{worksheet_name}' with range '{range_address}'")
        
        response = self.sp_client._make_request(endpoint)
        
        if response:
            values = response.get('values', [])
            # Filter out completely empty rows
            non_empty_values = [row for row in values if any(cell for cell in row if cell is not None and str(cell).strip())]
            print(f"Retrieved {len(non_empty_values)} non-empty rows from {len(values)} total rows")
            response['values'] = non_empty_values
            return response
        
        print("Failed to retrieve worksheet data")
        return None

    def get_worksheet_data_lightweight_by_id(self, site_url: str, file_id: str, 
                                            worksheet_name: str = None, 
                                            max_rows: int = 1000, range_address: str = None) -> Optional[Dict]:
        """
        Get worksheet data with row limits using file ID directly
        
        Args:
            site_url: SharePoint site URL
            file_id: ID of the Excel file
            worksheet_name: Specific worksheet name (if None, uses first worksheet)
            max_rows: Maximum number of rows to retrieve (ignored if range_address is provided)
            range_address: Specific range like "A1:C100" (overrides max_rows if provided)
            
        Returns:
            dict: Limited worksheet data
        """
        print(f"Getting lightweight data from file ID: {file_id}")
        
        # Get the Excel session using file ID
        excel_session = self.get_excel_file_by_id(site_url, file_id)
        
        if not excel_session:
            print(f"Could not access Excel file with ID: {file_id}")
            return None
        
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        # If no worksheet specified, get the first one
        if not worksheet_name:
            worksheets = self.list_worksheets(excel_session)
            if not worksheets:
                print("No worksheets found in file")
                return None
            worksheet_name = worksheets[0]['name']
            print(f"Using first worksheet: {worksheet_name}")
        
        # Use provided range_address or create one based on max_rows
        if not range_address:
            range_address = f"A1:ZZ{max_rows}"  # Adjust columns as needed
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/range(address='{range_address}')"
        
        print(f"Getting data from worksheet '{worksheet_name}' with range '{range_address}'")
        
        response = self.sp_client._make_request(endpoint)
        
        if response:
            values = response.get('values', [])
            # Filter out completely empty rows
            non_empty_values = [row for row in values if any(cell for cell in row if cell is not None and str(cell).strip())]
            print(f"Retrieved {len(non_empty_values)} non-empty rows from {len(values)} total rows")
            response['values'] = non_empty_values
            return response
        
        print("Failed to retrieve worksheet data")
        return None


    def get_sheet_mapping(self, excel_session: Dict) -> Dict[str, str]:
        """
        Get mapping between internal sheet IDs and actual sheet names
        
        Returns:
            dict: Mapping of sheet_id -> sheet_name
        """
        site_id = excel_session['site_id']
        drive_id = excel_session['drive_id']
        item_id = excel_session['item_id']
        
        endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets"
        response = self.sp_client._make_request(endpoint)
        
        sheet_mapping = {}
        if response:
            worksheets = response.get('value', [])
            for i, ws in enumerate(worksheets):
                sheet_id = ws.get('id', '')
                sheet_name = ws.get('name', '')
                position = ws.get('position', i)
                
                # Excel sometimes uses sheet<number> format based on position
                internal_name = f"sheet{position + 1}"
                sheet_mapping[internal_name] = sheet_name
                
                print(f"Sheet {position + 1}: '{sheet_name}' (ID: {sheet_id})")
        
        return sheet_mapping
    
    def diagnose_sheet_issues(self, excel_session: Dict) -> Dict:
        """
        Diagnose which sheets have formula issues
        """
        print("=== Diagnosing Sheet Issues ===")
        
        sheet_mapping = self.get_sheet_mapping(excel_session)
        problematic_sheets = []
        
        # Based on your error messages
        problem_sheet_numbers = [30, 36, 37, 40, 44, 45, 47]
        
        for sheet_num in problem_sheet_numbers:
            internal_name = f"sheet{sheet_num}"
            actual_name = sheet_mapping.get(internal_name, f"Unknown - Position {sheet_num}")
            
            print(f"❌ Problem Sheet {sheet_num}: '{actual_name}'")
            problematic_sheets.append({
                'position': sheet_num,
                'internal_name': internal_name,
                'actual_name': actual_name
            })
        
        return {
            'mapping': sheet_mapping,
            'problematic_sheets': problematic_sheets
        }
    
    def check_sheet_health(self, excel_session: Dict, worksheet_name: str) -> Dict:
        """
        Check if a specific sheet has issues
        """
        try:
            print(f"Checking health of sheet: '{worksheet_name}'")
            
            # Try to get basic sheet info
            site_id = excel_session['site_id']
            drive_id = excel_session['drive_id']
            item_id = excel_session['item_id']
            
            # Test 1: Can we access the sheet?
            endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')"
            response = self.sp_client._make_request(endpoint)
            
            if not response:
                return {'healthy': False, 'error': 'Cannot access sheet'}
            
            # Test 2: Can we get used range?
            try:
                used_range_endpoint = f"{endpoint}/usedRange"
                used_range = self.sp_client._make_request(used_range_endpoint)
                if not used_range:
                    return {'healthy': False, 'error': 'Cannot get used range'}
            except:
                return {'healthy': False, 'error': 'Used range error'}
            
            # Test 3: Try a small read operation
            try:
                test_data = self.get_worksheet_data_lightweight_by_id(
                    excel_session['site_id'], 
                    excel_session['item_id'],
                    worksheet_name=worksheet_name,
                    range_address="A1:C5"
                )
                if not test_data:
                    return {'healthy': False, 'error': 'Cannot read data'}
            except:
                return {'healthy': False, 'error': 'Data read error'}
            
            return {'healthy': True, 'error': None}
            
        except Exception as e:
            return {'healthy': False, 'error': str(e)}
        

def debug_tenant_sites():
    """
    Debug function to explore all available SharePoint sites in the tenant
    """
    sp_client = SharePointClient()
    
    if not sp_client.authenticate():
        print("Authentication failed")
        return
    
    print("🔍 COMPREHENSIVE TENANT SITE DISCOVERY")
    print("=" * 60)
    
    # 1. Get all sites in tenant
    print("\n1️⃣ ALL SITES IN TENANT")
    all_sites = sp_client.list_all_sites()
    
    # 2. Get site collections
    print("\n2️⃣ SITE COLLECTIONS")
    site_collections = sp_client.get_site_collections()
    
    # 3. Get user's accessible sites
    print("\n3️⃣ USER'S ACCESSIBLE SITES")
    user_sites = sp_client.get_my_sites()
    
    # 4. Search for specific terms
    print("\n4️⃣ SEARCHING FOR COMMON TERMS")
    search_terms = ['dev', 'test', 'prod', 'legacy', 'hrg', 'data', 'reports']
    
    for term in search_terms:
        print(f"\n--- Searching for '{term}' ---")
        matching_sites = sp_client.search_sites_by_name(term)
        if not matching_sites:
            print(f"No sites found matching '{term}'")
    
    # 5. Summary
    print("\n📊 SUMMARY")
    print("=" * 30)
    print(f"Total sites found: {len(all_sites)}")
    print(f"Site collections: {len(site_collections)}")
    print(f"User accessible sites: {len(user_sites)}")
    
    # 6. Create a comprehensive list of unique sites
    print("\n📋 COMPREHENSIVE SITE LIST")
    print("=" * 40)
    
    unique_sites = {}
    
    # Combine all sites
    for site in all_sites:
        url = site.get('webUrl', '')
        if url:
            unique_sites[url] = site
    
    # Sort by URL for easier reading
    sorted_sites = sorted(unique_sites.values(), key=lambda x: x.get('webUrl', ''))
    
    for i, site in enumerate(sorted_sites, 1):
        print(f"{i:2d}. {site.get('displayName', 'Unknown')}")
        print(f"     URL: {site.get('webUrl', 'Unknown')}")
        print(f"     Name: {site.get('name', 'Unknown')}")
        print(f"     Description: {site.get('description', 'No description')}")
        print(f"     Created: {site.get('createdDateTime', 'Unknown')}")
        print()
    
    return sorted_sites


#     Example of how to use the SharePointExcelOnline class
#     """

#     tenant_id = os.environ.get('TENANT_ID') 
#     client_id = os.environ.get('CLIENT_ID')
#     client_secret = os.environ.get('CLIENT_SECRET')
    

#     # Initialize SharePoint client
#     #sp_client = SharePointClient(tenant_id=tenant_id, client_id=client_id, client_secret=client_secret)
#     sp_client = SharePointClient()
    
#     if not sp_client.authenticate():
#         print("Authentication failed")
#         return
    
#     # Initialize Excel Online helper
#     excel_helper = SharePointExcelOnline(sp_client)
    
#     # Site and file information
#     #site_url = "https://highlandventuresltd442.sharepoint.com/sites/hrg"
#     site_url = "https://highlandventuresltd442.sharepoint.com/sites/dev"
#     file_name = "Book.xlsx"
#     drive_name = 'Documents'
#     folder_name = "General"
    
#     # Get Excel file session
#     excel_session = excel_helper.get_excel_file_session(
#         site_url=site_url,
#         file_path=file_name,
#         drive_name = drive_name,
#         folder_name=folder_name
#     )
    
#     if not excel_session:
#         print("Could not access Excel file")
#         return
    
#     print(f"Working with Excel file: {excel_session['file_name']}")
    
#     # List all worksheets
#     worksheets = excel_helper.list_worksheets(excel_session)
    
#     if worksheets:
#         # Work with the first worksheet
#         worksheet_name = worksheets[0]['name']
        
#         # Get current data
#         current_data = excel_helper.get_worksheet_data(excel_session, worksheet_name)
#         if current_data:
#             print(f"Current data has {len(current_data.get('values', []))} rows")
        
#         # Add new data
#         new_data = [
#             ["New Item", "Category", 100],
#             ["Another Item", "Category2", 200]
#         ]
        
#         # Append data to worksheet
#         success = excel_helper.append_data_to_worksheet(
#             excel_session, worksheet_name, new_data
#         )
        
#         if success:
#             print("Data appended successfully")
        
#         # Update a specific range
#         update_data = [["Updated Value"]]
#         excel_helper.update_worksheet_range(
#             excel_session, worksheet_name, "A1", update_data
#         )
        
#         # Save the workbook
#         excel_helper.save_workbook(excel_session)



# def example_excel_online_operations():
#     """
#     Example of how to use the SharePointExcelOnline class
#     """
#     tenant_id = os.environ.get('TENANT_ID') 
#     client_id = os.environ.get('CLIENT_ID')
#     client_secret = os.environ.get('CLIENT_SECRET')
    
#     sp_client = SharePointClient()
    
#     if not sp_client.authenticate():
#         print("Authentication failed")
#         return
    
#     excel_helper = SharePointExcelOnline(sp_client)
    
#     # Site and file information - corrected
#     site_url = "https://highlandventuresltd442.sharepoint.com/sites/hrg"
#     file_name = "Books.xlsx"  # Changed from "Book.xlsx" to "Books.xlsx"
#     drive_name = "Documents"  # This should match the SharePoint drive name
#     folder_name = "General"
    
#     # First, let's debug the available structure
#     print("=== Debugging SharePoint Structure ===")
#     drives = sp_client.list_drive_contents(site_url)
#     print("Available drives and their contents:")
#     for drive_name_key, content in drives.items():
#         print(f"Drive: {drive_name_key}")
#         print(f"  Folders: {[f['name'] for f in content['folders']]}")
#         print(f"  Files: {[f['name'] for f in content['files']]}")
    
#     # Get Excel file session
#     excel_session = excel_helper.get_excel_file_session(
#         site_url=site_url,
#         file_path=file_name,
#         drive_name=drive_name,
#         folder_name=folder_name
#     )
    
#     if not excel_session:
#         print("Could not access Excel file")
#         return
    
def debug_sharepoint_structure(site_url=None):
    """
    Debug function to explore SharePoint structure and find available files
    """
    tenant_id = os.environ.get('SHAREPOINT_TENANT_ID') 
    client_id = os.environ.get('SHAREPOINT_CLIENT_ID')
    client_secret = os.environ.get('SHAREPOINT_CLIENT_SECRET')
    
    sp_client = SharePointClient()
    
    if not sp_client.authenticate():
        print("Authentication failed")
        return
    
    if not site_url:
        site_url = "https://highlandventuresltd442.sharepoint.com/sites/hrg"
        print(f"Using helper predetermined site: {site_url}")
    
    # List all drives
    print("=== Available Drives and Contents ===")
    drive_contents = sp_client.list_drive_contents(site_url)
    
    if drive_contents:
        for drive_name, content in drive_contents.items():
            print(f"\nDrive: {drive_name}")
            print(f"  Folders ({len(content['folders'])}):")
            for folder in content['folders']:
                print(f"    - {folder['name']} ({folder.get('childCount', 0)} items)")
            
            print(f"  Files ({len(content['files'])}):")
            for file in content['files']:
                file_type = "Excel" if file['name'].endswith(('.xlsx', '.xls')) else "Other"
                print(f"    - {file['name']} ({file_type}, {file.get('size', 0):,} bytes)")
    
    # List all folders and their files
    print("\n=== All Folders and Their Files ===")
    all_folders = sp_client.list_all_folders(site_url)
    
    if all_folders:
        for folder in all_folders:
            print(f"\nFolder: {folder['full_path']}")
            
            # Get files in this specific folder
            try:
                folder_files = sp_client.get_folder_file_list(site_url, folder['name'])
                if folder_files:
                    print(f"  Files ({len(folder_files)}):")
                    for file in folder_files:
                        file_type = "Excel" if file['name'].endswith(('.xlsx', '.xls')) else "Other"
                        file_size = file.get('size', 0)
                        print(f"    - {file['name']} ({file_type}, {file_size:,} bytes, ID: {file['id']})")
                else:
                    print("    No files found")
            except Exception as e:
                print(f"    Error accessing folder: {e}")
    
    # Additional detailed check for specific folders
    print("\n=== Detailed Folder Analysis ===")
    
    # Check common folder names
    common_folders = ["General", "Documents", "Shared Documents", "Reports", "Data"]
    
    for folder_name in common_folders:
        print(f"\nChecking folder: {folder_name}")
        try:
            folder_files = sp_client.get_folder_file_list(site_url, folder_name)
            if folder_files:
                print(f"  Found {len(folder_files)} files:")
                for file in folder_files:
                    file_type = "Excel" if file['name'].endswith(('.xlsx', '.xls')) else "Other"
                    print(f"    - {file['name']} ({file_type}, ID: {file['id']})")
            else:
                print("  Folder not found or empty")
        except Exception as e:
            print(f"  Error: {e}")
    
    # Search for all Excel files across the entire site
    print("\n=== All Excel Files Found ===")
    excel_files_found = []
    
    if drive_contents:
        for drive_name, content in drive_contents.items():
            # Check files in drive root
            for file in content['files']:
                if file['name'].endswith(('.xlsx', '.xls')):
                    excel_files_found.append({
                        'name': file['name'],
                        'location': f"{drive_name} (root)",
                        'id': file['id'],
                        'size': file.get('size', 0)
                    })
    
    # Check files in all folders
    if all_folders:
        for folder in all_folders:
            try:
                folder_files = sp_client.get_folder_file_list(site_url, folder['name'])
                if folder_files:
                    for file in folder_files:
                        if file['name'].endswith(('.xlsx', '.xls')):
                            excel_files_found.append({
                                'name': file['name'],
                                'location': folder['full_path'],
                                'id': file['id'],
                                'size': file.get('size', 0)
                            })
            except:
                continue
    
    if excel_files_found:
        print(f"Found {len(excel_files_found)} Excel files total:")
        for excel_file in excel_files_found:
            print(f"  📊 {excel_file['name']}")
            print(f"     Location: {excel_file['location']}")
            print(f"     ID: {excel_file['id']}")
            print(f"     Size: {excel_file['size']:,} bytes")
            print()
    else:
        print("No Excel files found in the site")


## TEST


def work_with_book_excel():
    """
    Work with the Book.xlsx file found in the General folder
    """
    sp_client = SharePointClient()
    
    if not sp_client.authenticate():
        print("Authentication failed")
        return
    
    excel_helper = SharePointExcelOnline(sp_client)
    
    # Site and file information (now we know these are correct)
    site_url = "https://highlandventuresltd442.sharepoint.com/sites/dev"
    file_name = "Book.xlsx"  # Exact name from the output
    drive_name = "Documents"
    folder_name = "General"
    
    print(f"=== Working with {file_name} ===")
    
    # Method 1: Get Excel file session using file path
    excel_session = excel_helper.get_excel_file_session(
        site_url=site_url,
        file_path=file_name,
        drive_name=drive_name,
        folder_name=folder_name
    )
    
    # Method 2: Alternative - Get Excel file session using file ID (if Method 1 fails)
    if not excel_session:
        print("Trying with file ID instead...")
        file_id = "01NHAQJOGEO3WCSHXZ3FAYUTOTUPHJNWGT"
        excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)
    
    if not excel_session:
        print("Could not access Excel file")
        return
    
    print(f"Successfully accessed Excel file: {excel_session['file_name']}")
    print(f"Site ID: {excel_session['site_id']}")
    print(f"Drive ID: {excel_session['drive_id']}")
    print(f"Item ID: {excel_session['item_id']}")
    
    # List all worksheets
    print("\n=== Worksheets ===")
    worksheets = excel_helper.list_worksheets(excel_session)
    
    if not worksheets:
        print("No worksheets found or access denied")
        return
    
    # Work with the first worksheet
    worksheet_name = worksheets[0]['name'] # 'Sheet1'
    worksheet_name = worksheets[1]['name'] # 'Sheet2'
    print(f"\n=== Working with worksheet: {worksheet_name} ===")
    
    # Get current data from the worksheet
    print("Getting current data...")
    current_data = excel_helper.get_worksheet_data(excel_session, worksheet_name)
    
    if current_data:
        values = current_data.get('values', [])
        print(f"Current worksheet has {len(values)} rows")
        
        # Print first few rows as sample
        if values:
            print("Sample data (first 3 rows):")
            for i, row in enumerate(values[:3]):
                print(f"  Row {i+1}: {row}")
    
    # Example operations you can perform:
    
    # 1. Update a specific cell
    print("\n=== Updating cell A1 ===")
    update_data = [["Updated by Python!!"]]
    success = excel_helper.update_worksheet_range(
        excel_session, worksheet_name, "A1", update_data
    )
    
    if success:
        print("✅ Cell A1 updated successfully")
    
    # 2. Add new data rows
    print("\n=== Adding new data ===")
    new_data = [
        ["Python Item 1", "Category A", 100, "2024-01-01"],
        ["Python Item 2", "Category B", 200, "2024-01-02"],
        ["Python Item 3", "Category C", 300, "2024-01-03"]
    ]
    
    new_data = []
    _counter = 100
    for i in range(5):
        start_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')    
        _counter += 1
        new_data.append([f"Python Item {i}", f"Category {i}", _counter, start_now])
    
    success = excel_helper.append_data_to_worksheet(
        excel_session, worksheet_name, new_data
    )
    
    if success:
        print("✅ New data added successfully")
    
    # 3. Update a range of cells
    print("\n=== Updating range B2:D2 ===")
    range_data = [["Updated Range", 999, "2025-12-31"]]
    success = excel_helper.update_worksheet_range(
        excel_session, worksheet_name, "B2:D2", range_data
    )
    
    if success:
        print("✅ Range updated successfully")
    
    # 4. Get data from a specific range
    print("\n=== Getting data from range A1:D5 ===")
    range_data = excel_helper.get_worksheet_data(
        excel_session, worksheet_name, "A1:D5"
    )
    
    if range_data:
        values = range_data.get('values', [])
        print(f"Range A1:D5 contains:")
        for i, row in enumerate(values):
            print(f"  Row {i+1}: {row}")
    
    # 5. Save the workbook
    print("\n=== Saving workbook ===")
    success = excel_helper.save_workbook(excel_session)
    # _success = excel_helper.close_workbook(excel_session)
    
    if success:
        print("✅ Workbook saved successfully")
    
    # if _success:
    #     print("✅ Workbook closed successfully")
    
    print("\n=== Excel operations completed ===")

# Alternative function to work with the file by ID directly
def work_with_book_excel_by_id(file_id=None, site_url=None):
    """
    Work with Book.xlsx using its file ID directly
    """
    sp_client = SharePointClient()
    
    if not sp_client.authenticate():
        print("Authentication failed")
        return
    
    excel_helper = SharePointExcelOnline(sp_client)
    
    if not site_url:
        site_url = "https://highlandventuresltd442.sharepoint.com/sites/dev"
    if not file_id:
        file_id = "01NHAQJOGEO3WCSHXZ3FAYUTOTUPHJNWGT"  # From your debug output
    
    print(f"=== Working with Excel file by ID: {file_id} ===")
    
    # Get Excel session using file ID
    excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)
    
    if not excel_session:
        print("Could not access Excel file by ID")
        return
    
    print(f"✅ Successfully accessed Excel file: {excel_session['file_name']}")
    
    # Continue with the same operations as above...
    worksheets = excel_helper.list_worksheets(excel_session)
    
    if worksheets:
        worksheet_name = worksheets[0]['name']
        
        # Get and display current data
        current_data = excel_helper.get_worksheet_data(excel_session, worksheet_name)
        if current_data:
            values = current_data.get('values', [])
            print(f"Worksheet '{worksheet_name}' has {len(values)} rows")

# def clear_excel_sheet_cells(site_url:str, file_id: str, worksheet_name: str, range_address: str) -> bool:
#     """
#     Clear the contents of a specific range in a worksheet
    
#     Args:
#         excel_session: Excel session information
#         worksheet_name: Name of the worksheet
#         range_address: Range to clear (e.g., "A1:C10")
        
#     Returns:
#         bool: True if successful, False otherwise
#     """
#     sp_client = SharePointClient()
    
#     if not sp_client.authenticate():
#         print("Authentication failed")
#         return
    
#     excel_helper = SharePointExcelOnline(sp_client)

#     # Get Excel session using file ID
#     excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)

#     # # First, get the used range to find where to append
#     # row_count = -1
#     # used_range = excel_helper.get_worksheet_data(excel_session, worksheet_name)
#     # if not used_range:
#     #     # If no used range, start at A1
#     #     start_row = 1
#     # else:
#     #     # Get the last row and add 1
#     #     row_count = used_range.get('rowCount', 0)
#     #     start_row = row_count + 1

#     # print(f"Clearing range {range_address} in worksheet '{worksheet_name}'\nExisting row count: {row_count}")
#     # exit(1)

#     site_id = excel_session['site_id']
#     drive_id = excel_session['drive_id']
#     item_id = excel_session['item_id']


    
#     endpoint = f"sites/{site_id}/drives/{drive_id}/items/{item_id}/workbook/worksheets('{worksheet_name}')/range(address='{range_address}')/clear"
    
#     print(f"Clearing range {range_address} in worksheet '{worksheet_name}'")
    
#     response = sp_client._make_request(endpoint, method='POST', data={})
    
#     if response is not None:  # Success returns empty response
#         print("Range cleared successfully")
#         return True
#     else:
#         print("Failed to clear range")
#         return False
    
def populate_excel_sheet_cells(site_url:str, file_id: str, worksheet_name: str, range_address: str, values: List[List]) -> bool:
    """
    Populate a specific range in a worksheet with values
    
    Args:
        site_url: SharePoint site URL
        file_id: Excel file ID
        worksheet_name: Name of the worksheet
        range_address: Range to populate (e.g., "A1:C10")
        values: 2D array of values to write
        
    Returns:
        bool: True if successful, False otherwise
    """
    sp_client = SharePointClient()
    
    if not sp_client.authenticate():
        print("Authentication failed")
        return
    
    excel_helper = SharePointExcelOnline(sp_client)

    # Get Excel session using file ID
    excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)

    print(f"Populating range {range_address} in worksheet '{worksheet_name}' with {len(values)} rows")
    
    return excel_helper.update_worksheet_range(excel_session, worksheet_name, range_address, values)

def process_excel_sheet_cells(site_url:str, file_id: str, worksheet_name: str, range_address: str) -> bool:
    """
    Process a specific range in a worksheet (e.g., clear and then populate)
    
    Args:
        site_url: SharePoint site URL
        file_id: Excel file ID
        worksheet_name: Name of the worksheet
        range_address: Range to process (e.g., "A1:C10")
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    excel_helper = SharePointExcelOnline()

    # Get Excel session using file ID
    excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)

    starting_data_row = 2
    first_data_column = 'A'  # Assuming we want to clear from column A
    last_data_column = 'E'  # Assuming we want to clear up to column E
    clearing_data_row_start = starting_data_row + 1  # Start clearing from row 3 (A3)

    # First, get the used range to find where to append
    existing_row_count = excel_helper.get_excel_sheet_row_count(excel_session, worksheet_name)

    if existing_row_count > starting_data_row: 
        _range_address = f"{first_data_column}{clearing_data_row_start}:{last_data_column}{existing_row_count}" # e.g., "A3:D120"
        print(f"Clearing existing range: {_range_address} in worksheet '{worksheet_name}'")
        # Clear the range first
        cleared = excel_helper.clear_excel_sheet_cells(excel_session, worksheet_name, _range_address)
    
        if not cleared:
            print("Failed to clear the range")
            return False
        sleep_secs = 5
        print(f"Existing range cleared successfully. Sleeeping for {sleep_secs} seconds before populating new data...")
        time.sleep(sleep_secs)
    
    # Now populate with new data
    # new_values = [
    #     ["New Item 1", "Category A", 100],
    #     ["New Item 2", "Category B", 200],
    #     ["New Item 3", "Category C", 300]
    # ]

    new_values = []
    _counter = 100
    for i in range(10):
        start_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')    
        _counter += 1
        id = random.randint(1, 1000)
        new_values.append([f"Python Item {i}", f"Category {i}", _counter, start_now, id])

    
    populated = excel_helper.append_data_to_worksheet(
        excel_session, worksheet_name, new_values
    )
    # populated = populate_excel_sheet_cells(site_url, file_id, worksheet_name, range_address, new_values)
    
    return populated

if __name__ == "__main__":

    start_time = time.time()
    start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    

    print(f"\n\nStarting: main()\t[time: {start_now}]")

    potential_id = '01NHAQJOGEO3WCSHXZ3FAYUTOTUPHJNWGT' # test book by Julian: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/dev/_layouts/15/Doc.aspx?sourcedoc=%7B29EC76C4-F91E-41D9-8A4D-D3A3CE96D8D3%7D&file=Book.xlsx&action=default&mobileredirect=true
    # potential_id = '013USHB7VZ6D5NXGNXOBALFMOSTWOVBLHW' # store list    

    site_url = 'https://highlandventuresltd442.sharepoint.com/sites/dev'
    # site_url = 'https://highlandventuresltd442.sharepoint.com/sites/hrg'


    #example_excel_online_operations()
    # debug_sharepoint_structure(site_url=site_url)
    # work_with_book_excel()
    # work_with_book_excel_by_id(file_id=potential_id, site_url=site_url)

    # clear_excel_sheet_cells(
    #     site_url=site_url, 
    #     file_id=potential_id, 
    #     worksheet_name='Sheet2', 
    #     range_address='A3:D120'
    # )

    process_excel_sheet_cells(
        site_url=site_url, 
        file_id=potential_id, 
        worksheet_name='Sheet2', 
        range_address='A3:D120'
    )
    start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    

    print(f"\nDone: main()\t[time: {start_now}]\n")

    # Method 1: Discover all sites in your tenant
    sites = debug_tenant_sites()
    sp = SharePointClient()

    # Method 2: Analyze a specific site
    sp.analyze_specific_site("https://highlandventuresltd442.sharepoint.com/sites/dev")
    
    # Method 3: Direct API calls
    sp.authenticate()
    
    # Get all sites
    all_sites = sp.list_all_sites()
    
    # Search for specific sites
    dev_sites = sp.search_sites_by_name("dev")
    
    # Get user's sites
    my_sites = sp.get_my_sites()