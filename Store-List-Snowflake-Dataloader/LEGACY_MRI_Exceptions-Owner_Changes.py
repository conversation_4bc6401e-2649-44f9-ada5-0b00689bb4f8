"""
LEGACY MRI Exceptions - Owner Changes
Python conversion of R script for monthly MRI ownership change reporting
Version: 20241204 (converted from R)
"""

import os
import sys
import pandas as pd
# import numpy as np
from datetime import datetime, timedelta, date

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo

from pathlib import Path

import libs.snowflake_helper as sf
import libs.email_client as email_client


# Configuration
TESTING_EMAILS = False  # Set to True for testing
VERSION = "20241204"

OVERRIDE_EMAIL_RECIPIENTS = False

class MRIOwnershipChangeReporter:
    def __init__(self):
        # self.setup_logging()
        self.sf_obj = sf.SnowflakeHelper()
        self.sf_conn = self.sf_obj.conn
        self.setup_parameters()
        self.setup_paths()
        self.setup_email_config()
        

        
    def setup_parameters(self):
        """Setup basic parameters"""
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.script_folder = "LEGACY_MRI_Exceptions-Owner-Landlord"
        self.report_name = "Monthly MRI Ownership Changes"
        self.report_filename = "MRI_Ownership_Changes_Monthly.xlsx"
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S%Z")
        
        self.report_criteria = """<p><b>Criteria for inclusion in the report:</b> (note: rented buildings not included)<ul>
<li>Ownership started or ended in the previous month</li>
<li>'Acquired' date since beginning of last month</li>
<li>'Disposed' date since beginning of last month (sold)</li>
</ul></p>"""
        
        # self.logger.info(f"Routine Starting: {self.report_name}")
        
    def setup_paths(self):
        """Setup file paths based on computer type"""
        # self.central_path = Path("//*************/public/steveo/R Stuff/ReportFiles")
        # self.tableau_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        
        # test_computers = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
        # prod_computers = ["DESKTOP-TABLEAU"]
        # this_computer = platform.node()
        
        # if this_computer in test_computers:
        #     self.testing_pc = True
        #     self.main_path = self.central_path
        # else:
        self.testing_pc = False
        # self.main_path = self.tableau_path
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
            
        self.log_path = self.main_path / self.script_folder
        self.report_path = self.log_path
        
    def setup_email_config(self):
        """Setup email configuration"""
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.norm_sig = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
Sr. Analytics Mgr.<br/>
<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
2500 Lehigh Ave.<br/>
Glenview, IL 60026<br/>
Ph: 847/904-9043<br/>"""
        
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Gmail OAuth settings
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Gmail API scopes
        self.gmail_scopes = ['https://www.googleapis.com/auth/gmail.send']
        

            
    def check_dataframe_rows(self, df, min_num_rows, report_name=None):
        """Check if dataframe has minimum required rows"""
        if isinstance(df, pd.DataFrame):
            if len(df) >= min_num_rows:
                error_status = f"{report_name}: OKAY"
                return True, len(df), error_status
            else:
                error_status = f"{report_name}: INCOMPLETE"
                return False, len(df), error_status
        else:
            error_status = f"{report_name}: ERROR"
            return False, 0, error_status
            
    def create_excel_report(self, df, file_path, sheet_name="Sheet1", col_widths=None):
        """Create formatted Excel report"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Convert datetime columns to dates for Excel
            for col in df.select_dtypes(include=['datetime64']).columns:
                df[col] = pd.to_datetime(df[col]).dt.date
                
            # Add data to worksheet
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
                
            # Format header row
            header_font = Font(name='Arial Narrow', size=12, bold=True, color='000000')
            header_fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                
            # Set column widths
            if col_widths:
                for col_name, width in col_widths.items():
                    try:
                        col_letter = openpyxl.utils.get_column_letter(df.columns.get_loc(col_name) + 1)
                        ws.column_dimensions[col_letter].width = width
                    except (KeyError, ValueError):
                        continue
                        
            # Freeze top row
            ws.freeze_panes = 'A2'
            
            # Add auto filter
            ws.auto_filter.ref = ws.dimensions
            
            # Save workbook
            wb.save(file_path)
            # self.logger.info(f"Excel report saved: {file_path}")
            self.sf_obj.log_audit_in_db(log_msg=f"Excel report saved: {file_path}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            return True
            
        except Exception as e:
            # self.logger.error(f"Failed to create Excel report: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel report, {file_path}: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return False
            
    def get_ownership_changes_query(self):
        """Get the SQL query for ownership changes"""
        return """
        SELECT /* Snowflake version, Changes in ownership table GOWN */
            BLDG.BLDGID AS BLDGID
        ,   array_to_string(
                array_construct_compact(
                CASE WHEN TO_DATE(CONCAT(GOWN.BEGPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
                    THEN CONCAT(TRIM(GOWN.OWNERID),' Ownership Begins ',GOWN.BEGPD, (CASE WHEN POWN.PREV_OWNER <> 'na' THEN CONCAT(' (Previous Owner: ', POWN.PREV_OWNER, ')') END) ) END,
                CASE WHEN TO_DATE(CONCAT(GOWN.ENDPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
                    THEN CONCAT(TRIM(GOWN.OWNERID),' Ownership Ends ', GOWN.ENDPD, (CASE WHEN NOWN.NEXT_OWNER <> 'na' THEN CONCAT(' (Next Owner: ', NOWN.NEXT_OWNER, ')') END) ) END,
                CASE WHEN TO_DATE(ENTITY.ACQUIRED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
                    THEN 'Newly Acquired' END,
                CASE WHEN TO_DATE(ENTITY.DISPOSED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
                    THEN CONCAT('Disposed ', TO_CHAR(ENTITY.DISPOSED)) END
                )
            , '; ') AS CHANGE
        ,   RTRIM(GOWN.OWNERID) AS OWNER_ID
        ,   CASE WHEN BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL THEN 'Active' ELSE 'Inactive' END as BLDG_ACTIVE
        ,   CASE WHEN GOWN.BEGPD IS NULL THEN 'NA' ELSE GOWN.BEGPD END AS BEGINNING_MONTH
        ,   CASE WHEN GOWN.ENDPD IS NULL THEN 'NA' ELSE GOWN.ENDPD END AS ENDING_MONTH
        ,   GOWN.PERCENT
        ,   GOWN.PRIMARYOWN AS PRIMARY_OWNER_CHECKED
        ,   TO_DATE(ENTITY.ACQUIRED) AS ACQUIRED_DATE
        ,   TO_DATE(ENTITY.DISPOSED) AS DISPOSED_DATE
        ,   TO_DATE(GOWN.LASTDATE) AS LAST_UPDATE_ENTERED
        ,   GOWN.USERID AS UPDATED_BY
        FROM MRI.GOWN
        LEFT JOIN MRI.BLDG
        ON GOWN.ENTITYID = BLDG.ENTITYID
        LEFT JOIN MRI.ENTITY
        ON GOWN.ENTITYID = ENTITY.ENTITYID
        LEFT JOIN
        (
            SELECT
            O.ENTITYID
            ,   trim(O.OWNERID) AS OWNERID
            ,   O.BEGPD
            ,   O.ENDPD
            ,   LAG(TRIM(O.OWNERID), 1, 'na') OVER (PARTITION BY O.ENTITYID ORDER BY O.BEGPD, O.ENDPD) AS PREV_OWNER
            FROM MRI.GOWN O
        ) POWN
        ON GOWN.ENTITYID = POWN.ENTITYID
            AND ((GOWN.BEGPD = POWN.BEGPD) or (GOWN.BEGPD IS NULL AND POWN.BEGPD IS NULL))
        LEFT JOIN
        (
            SELECT
            O.ENTITYID
            ,   trim(O.OWNERID) as OWNERID
            ,   O.BEGPD
            ,   O.ENDPD
            ,   LEAD(TRIM(O.OWNERID), 1, 'na') OVER (PARTITION BY O.ENTITYID ORDER BY O.BEGPD, O.ENDPD) AS NEXT_OWNER
            FROM MRI.GOWN O
        ) NOWN
        ON GOWN.ENTITYID = NOWN.ENTITYID
            AND ((GOWN.ENDPD = NOWN.ENDPD) or (GOWN.ENDPD IS NULL AND NOWN.ENDPD IS NULL))
        WHERE 
            (
                TO_DATE(CONCAT(GOWN.BEGPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* BEGPD since start of previous month */
                OR TO_DATE(CONCAT(GOWN.ENDPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* ENDPD since start of previous month */
                OR TO_DATE(ENTITY.ACQUIRED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* Start of previous month */
                OR TO_DATE(ENTITY.DISPOSED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* Start of previous month */
            )
            AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
        order by BLDG.BLDGID, LAST_UPDATE_ENTERED DESC, BEGINNING_MONTH ASC
        """
        
    def process_data(self, df):
        """Process and clean the data"""
        # Strip trailing whitespace from string columns
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].astype(str).str.rstrip()
            
        # Replace underscores in column names with spaces
        df.columns = df.columns.str.replace('_', ' ')
        
        return df
        
    def generate_html_table(self, df, max_rows=20):
        """Generate HTML table for email body"""
        if len(df) <= max_rows:
            # Create HTML table
            html_table = df.to_html(
                index=False,
                table_id="report_table",
                classes="table table-striped",
                escape=False,
                border=2
            )
            return f"<p>{html_table}</p>"
        else:
            return f"<p>There are {len(df)} results, see attached file for all.</p>"
            
    def run_report(self):
        """Main report execution function"""
        try:
            # Setup Snowflake connection
            # self.setup_snowflake_connection()
            
            # Execute query
            query = self.get_ownership_changes_query()
            df = pd.read_sql(query, self.sf_conn)
            
            # Check data quality
            data_status = self.check_dataframe_rows(df, 1, self.report_name)
            
            if data_status[0]:  # If data is okay
                # Process data
                df = self.process_data(df)
                
                # Define column widths for Excel
                col_widths = {
                    "BLDGID": 8,
                    "CHANGE": min(75, max(35, df['CHANGE'].astype(str).str.len().max() if 'CHANGE' in df.columns else 35)),
                    "OWNER ID": 10.5,
                    "BLDG ACTIVE": 10.5,
                    "BEGINNING MONTH": 11,
                    "ENDING MONTH": 10,
                    "PERCENT": 10,
                    "PRIMARY OWNER CHECKED": 10,
                    "ACQUIRED DATE": 11,
                    "DISPOSED DATE": 11,
                    "LAST UPDATE ENTERED": 11,
                    "UPDATED BY": 9.5
                }
                
                # Create Excel file
                excel_file_path = self.report_path / self.report_filename
                sheet_name = self.query_date
                
                if self.create_excel_report(df, excel_file_path, sheet_name, col_widths):
                    # Prepare email
                    email_cols = ["BLDGID", "CHANGE", "ACQUIRED DATE", "DISPOSED DATE"]
                    email_df = df[email_cols].copy()
                    
                    # Format dates for email
                    for col in email_df.select_dtypes(include=['datetime64', 'object']).columns:
                        if 'DATE' in col:
                            email_df[col] = pd.to_datetime(email_df[col], errors='coerce').dt.strftime('%m/%d/%y')
                            
                    # Generate email body
                    body_table = self.generate_html_table(email_df)
                    
                    body_text = f"""<p><b>REPORT: {self.report_name}</b></p>
{self.report_criteria}
<p>The info below contains ownership changes in the last month as noted in MRI. 
MRI ownership periods are noted as YYYYMM. <b>See attached Excel file for more details.</b></p>
{body_table}
<br/>
{self.norm_sig}"""
                    
                    # Send email
                    email_client.send_email(
                        recipient=self.norm_recip,
                        subject=self.report_name,
                        body=body_text,
                        attachments=[str(excel_file_path)],
                        replyto=self.gmail_reply_to,
                        # test=TESTING_EMAILS
                        override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                    )
                    
                    # if success:
                    #     self.logger.info("Report completed successfully")
                    # else:
                    #     self.logger.error("Failed to send email")
                        
                else:
                    # self.logger.error("Failed to create Excel report")
                    self.sf_obj.log_audit_in_db(log_msg="Failed to create Excel report", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    
            else:
                # self.logger.warning(f"Data quality check failed: {data_status[2]}")
                self.sf_obj.log_audit_in_db(log_msg=f"Data quality check failed: {data_status[2]}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                
        except Exception as e:
            # self.logger.error(f"Report execution failed: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Report execution failed: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            raise
        # finally:
            # Close database connection
            # if hasattr(self, 'sf_conn'):
                # self.sf_conn.close()
                
def main():
    """Main execution function"""
    try:
        reporter = MRIOwnershipChangeReporter()
        reporter.run_report()
    except Exception as e:
        # logging.error(f"Script execution failed: {e}")
        print(f"Script execution failed: {e}")
        sys.exit(1)
        
if __name__ == "__main__":
    main() 