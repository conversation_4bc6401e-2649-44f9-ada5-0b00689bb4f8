# Converted from  R to Python: 5/28/2025
import os
import sys
import re
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
# import keyring
# import pyodbc

# from email.mime.text import MIMEText
# from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON>art
# from email.mime.application import MI<PERSON>Application
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill
from openpyxl.utils import get_column_letter
import tabulate
# import json
import libs.snowflake_helper as sf
import libs.email_client as email_client

# dir_path = os.path.dirname(os.path.realpath(__file__))
dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
csm_db = os.environ["DATABASE_CSM_DATABASE"]

# Version 20241120
# Converted from R to Python

# Configuration
TESTING_EMAILS = False
SCRIPT_FOLDER = "LEGACY_MRI_Exceptions-Suites"
REPORT_FOLDER = "reports"
SAVE_TO_SHARED = False

# Column definitions
COL_NAMES = [
    "BLDGID",
    "SUITID",
    "ADDRESS",
    "SUITENO",
    "FLOORNO",
    "TAXFID",
    "SUITSQFT",
    "MRKTRATE",
    "LASTDATE",
    "USERID",
    "INCCAT",
    "VACPERIOD",
    "TAXEXEMPT",
    "ANNINCOME",
    "SUITETYPE_MRI"
]

# Email configuration
NORMAL_RECIPIENTS = [
    "Sean Coyle <<EMAIL>>",
    "<EMAIL>",
    "Bobbi Steiner <<EMAIL>>",
    "Rachael Heironimus <<EMAIL>>"
]

WARNING_RECIPIENTS = [
    "<EMAIL>",
    "<EMAIL>"
]

TEST_RECIPIENTS = ["<EMAIL>"]
TEST_CC_RECIPIENTS = ["<EMAIL>"]

# Gmail configuration
GMAIL_AUTH_EMAIL = "<EMAIL>"
GMAIL_REPLY_TO = "<EMAIL>"

# Path configuration
CENTRAL_PATH = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles")
TABLEAU_PATH = os.path.join("C:", "Users", "table", "Documents", "ReportFiles")

TEST_COMPUTERS = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
PROD_COMPUTERS = ["DESKTOP-TABLEAU"]



def write_excel(dirpath, fname, sname="Sheet1", df=None, colnames=True, colwidths=None, writeover=True):
    """Write DataFrame to Excel file with formatting"""
    my_fn = os.path.join(dirpath, fname)
    
    if not os.path.exists(dirpath):
        os.makedirs(dirpath)
    
    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sname
    
    # Write headers
    for col_num, column in enumerate(df.columns, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = column
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
    
    # Write data
    for row_num, row in enumerate(df.values, 2):
        for col_num, value in enumerate(row, 1):
            ws.cell(row=row_num, column=col_num).value = value
    
    # Set column widths
    if colwidths is not None:
        for col_name, width in colwidths.items():
            col_idx = df.columns.get_loc(col_name) + 1
            ws.column_dimensions[get_column_letter(col_idx)].width = width
    
    # Save workbook
    try:
        wb.save(my_fn)
    except Exception as e:
        print(f"Error saving file: {e}")
        # Handle error and send warning email
        body_text = f"""
        <html><head></head><body>
        <h2>MRI Suite Changes - REPORT FILE SAVING ERROR</h2>
        <p>This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b>
        during the <b>MRI Suite Changes</b> routine.</p>
        <p>{my_fn}</p>
        <p>Either the path wasn't accessible or the file was open in another process.</p>
        <p>The routine should continue without saving this file.</p>
        <br/>
        <b>Steve Olson</b><br/>
        (847)904-9043 Office<br/>
        (715)379-8525 Cell
        </body></html>
        """
        email_client.send_email(
            recipient=WARNING_RECIPIENTS,
            subject="MRI Suite Changes Exceptions : REPORT FILE SAVING ERROR",
            body=body_text,
            replyto=GMAIL_REPLY_TO
        )

def get_current_suite_data(conn):
    """Get current suite data from Snowflake"""
    query = f"""
    select
        dateadd(DAY, -1, CURRENT_DATE) as AS_OF
    ,   S.*
    ,   CONCAT(T.SUITETYPEUSAGE,
        CASE 
            WHEN T.SUITETYPEUSAGE = 'I' THEN ' (Include)' 
            WHEN T.SUITETYPEUSAGE = 'N' THEN ' (Include Not Counted)' 
            WHEN T.SUITETYPEUSAGE = 'E' THEN ' (Exclude)' 
        END) AS SUITETYPEUSAGE
    from {csm_db}.MRI.SUIT S
    LEFT JOIN {csm_db}.MRI.TB_CM_SUITETYPE T ON S.SUITETYPE_MRI = T.SUITETYPEID
    """
    return pd.read_sql(query, conn)

def save_suite_data(df, report_path, report_date, save_to_shared=False, shared_path=None):
    """Save suite data to CSV file"""
    filename = f"MRI_SUIT_data_as_of_{report_date}.csv"
    filepath = os.path.join(report_path, filename)
    
    df.to_csv(filepath, index=False)
    
    if save_to_shared and shared_path:
        shared_filepath = os.path.join(shared_path, filename)
        df.to_csv(shared_filepath, index=False)
    
    return filename

def get_previous_suite_data(report_path, shared_path=None):
    """Get the most recent previous suite data file"""
    files = []
    
    # Get files from main report path
    if os.path.exists(report_path):
        files.extend([f for f in os.listdir(report_path) if f.endswith('.csv')])
    
    # Get files from shared path if available
    if shared_path and os.path.exists(shared_path):
        files.extend([f for f in os.listdir(shared_path) if f.endswith('.csv')])
    
    if not files:
        return None
    
    # Get the most recent file
    latest_file = max(files)
    
    # Read the file
    if os.path.exists(os.path.join(report_path, latest_file)):
        return pd.read_csv(os.path.join(report_path, latest_file))
    elif shared_path and os.path.exists(os.path.join(shared_path, latest_file)):
        return pd.read_csv(os.path.join(shared_path, latest_file))
    
    return None

def find_suite_changes(current_df, previous_df):
    """Find changes between current and previous suite data"""
    # Define columns for comparison
    order_cols = [
        "AS_OF",
        "BLDGID",
        "SUITID",
        "SUITENO",
        "ADDRESS",
        "SUITETYPE_MRI",
        "SUITETYPEUSAGE"
    ]
    
    compare_cols = [col for col in order_cols if col not in ["AS_OF", "SUITETYPE_MRI"]]
    
    # Find differences
    current_diff = current_df[~current_df[compare_cols].apply(tuple, 1).isin(
        previous_df[compare_cols].apply(tuple, 1))]
    previous_diff = previous_df[~previous_df[compare_cols].apply(tuple, 1).isin(
        current_df[compare_cols].apply(tuple, 1))]
    
    # Combine differences
    all_diffs = pd.concat([current_diff, previous_diff])
    
    # Sort results
    all_diffs = all_diffs.sort_values(["BLDGID", "SUITID"])
    
    return all_diffs[order_cols]

def cleanup_old_files(report_path, shared_path=None):
    """Delete old suite data files"""
    old_dates = pd.date_range(
        end=datetime.now() - timedelta(days=181),
        start=datetime.now() - timedelta(days=366),
        freq='D'
    ).strftime('%Y%m%d').tolist()
    
    # Clean up main report path
    if os.path.exists(report_path):
        for file in os.listdir(report_path):
            if file.endswith('.csv'):
                file_date = re.search(r'\d{8}', file)
                if file_date and file_date.group() in old_dates:
                    os.remove(os.path.join(report_path, file))
    
    # Clean up shared path
    if shared_path and os.path.exists(shared_path):
        for file in os.listdir(shared_path):
            if file.endswith('.csv'):
                file_date = re.search(r'\d{8}', file)
                if file_date and file_date.group() in old_dates:
                    os.remove(os.path.join(shared_path, file))

def main():
    # Get current date and time
    query_date = datetime.now().strftime("%d-%b-%y")
    report_time = datetime.now().strftime("%H%M%S%Z")
    report_date = datetime.now().strftime("%Y%m%d")
    
    # Determine computer type and paths
    # testing_pc, main_path = get_computer_type()
    
    # Set up paths
    # log_path = os.path.join(main_path, SCRIPT_FOLDER)
    # report_path = os.path.join(log_path, REPORT_FOLDER)
    # report_path_shared = os.path.join(CENTRAL_PATH, SCRIPT_FOLDER, REPORT_FOLDER)

    report_path = f"{dir_path}/reports/LEGACY_MRI_Exceptions-Suites"
    report_path_shared = report_path
    
    # Create report directory if it doesn't exist
    if not os.path.exists(report_path):
        os.makedirs(report_path)
    

    try:
        # Connect to Snowflake

        sf_obj = sf.SnowflakeHelper()
        conn = sf_obj.conn
        
        # Set timezone
        cursor = conn.cursor()
        cursor.execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
        conn.commit()
        
        # Get current suite data
        current_data = get_current_suite_data(conn)
        if current_data.empty:
            raise Exception("No current suite data found")
        
        # Save current data
        current_filename = save_suite_data(
            current_data, 
            report_path, 
            report_date,
            SAVE_TO_SHARED,
            report_path_shared
        )
        
        # Get previous suite data
        previous_data = get_previous_suite_data(report_path, report_path_shared)
        if previous_data is None:
            raise Exception("No previous suite data found")
        
        # Find changes
        changes = find_suite_changes(current_data, previous_data)
        
        if not changes.empty:
            # Create Excel report
            excel_filename = "MRI_Suite_Changes.xlsx"
            colwidths = {
                "AS_OF": 10,
                "BLDGID": 9,
                "ADDRESS": 25,
                "SUITETYPEUSAGE": 25,
                "SUITETYPE_MRI": 18
            }
            
            write_excel(
                report_path,
                excel_filename,
                query_date,
                changes,
                True,
                colwidths,
                True
            )
            
            # Prepare email
            if len(changes) <= 30:
                body_table = tabulate.tabulate(
                    changes,
                    headers='keys',
                    tablefmt='html',
                    showindex=False
                )
                body_text = f"""
                <html><head></head><body>
                <p>The <b>MRI Suite Changes</b> data is attached. The attached file contains MRI Suite Changes
                from yesterday vs the most recent prior data (as indicated in the AS_OF column).</p>
                <p>The results are sorted by BLDG ID, SUITE ID and then by AS_OF date.</p>
                {body_table}
                <br/>
                <b>Steve Olson</b><br/>
                Sr. Analytics Mgr.<br/>
                <b>Highland Ventures, Ltd.</b><br/>
                2500 Lehigh Ave.<br/>
                Glenview, IL 60026<br/>
                Ph: 847/904-9043<br/>
                </body></html>
                """
            else:
                body_text = f"""
                <html><head></head><body>
                <p>The <b>MRI Suite Changes</b> data is attached. The attached file contains MRI Suite Changes
                from yesterday vs the most recent prior data (as indicated in the AS_OF column).</p>
                <p>The results are sorted by BLDG ID, SUITE ID and then by AS_OF date.</p>
                <p>There are {len(changes)} results, see attached file for all.</p>
                <br/>
                <b>Steve Olson</b><br/>
                Sr. Analytics Mgr.<br/>
                <b>Highland Ventures, Ltd.</b><br/>
                2500 Lehigh Ave.<br/>
                Glenview, IL 60026<br/>
                Ph: 847/904-9043<br/>
                </body></html>
                """
            
            # Send email
            email_client.send_email(
                NORMAL_RECIPIENTS,
                "MRI Suite Changes",
                body_text,
                attachments=[os.path.join(report_path, excel_filename)],
                test=TESTING_EMAILS,
                test_recipient=TEST_RECIPIENTS,
                replyto=GMAIL_REPLY_TO
            )
        
        # Clean up old files
        cleanup_old_files(report_path, report_path_shared)
        
    except Exception as e:
        # Send error email
        error_body = f"""
        <html><head></head><body>
        <h2>MRI Suite Changes - ERROR</h2>
        <p>An error occurred during the MRI Suite Changes routine:</p>
        <p>{str(e)}</p>
        <br/>
        <b>Steve Olson</b><br/>
        Sr. Analytics Mgr.<br/>
        <b>Highland Ventures, Ltd.</b><br/>
        2500 Lehigh Ave.<br/>
        Glenview, IL 60026<br/>
        Ph: 847/904-9043<br/>
        </body></html>
        """
        email_client.send_email(
            WARNING_RECIPIENTS,
            "MRI Suite Changes - ERROR",
            error_body,
            test=TESTING_EMAILS,
            test_recipient=TEST_RECIPIENTS,
            replyto=GMAIL_REPLY_TO
        )
    
    finally:
        # Close connection
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main() 