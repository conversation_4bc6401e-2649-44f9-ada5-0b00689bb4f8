select store_number,
               to_char(sun_o,'HH24:MI') sun_o,
               to_char(sun_c,'HH24:MI') sun_c,
               to_char(mon_o,'HH24:MI') mon_o,
               to_char(mon_c,'HH24:MI') mon_c,
               to_char(tue_o,'HH24:MI') tue_o,
               to_char(tue_c,'HH24:MI') tue_c,
               to_char(wed_o,'HH24:MI') wed_o,
               to_char(wed_c,'HH24:MI') wed_c,
               to_char(thu_o,'HH24:MI') thu_o,
               to_char(thu_c,'HH24:MI') thu_c,
               to_char(fri_o,'HH24:MI') fri_o,
               to_char(fri_c,'HH24:MI') fri_c,
               to_char(sat_o,'HH24:MI') sat_o,
               to_char(sat_c,'HH24:MI') sat_c, 
               timediff('hour',sun_o , sun_c) +  timediff('hour',mon_o , mon_c) +  timediff('hour',tue_o , tue_c) + 
               timediff('hour',wed_o , wed_c) +  timediff('hour',thu_o , thu_c) + timediff('hour',fri_o , fri_c) +  timediff('hour',sat_o , sat_c) as tot_open_hours
        from $envm_csm_db.corporate.ab_store_hours
        where store_number in (select to_number(location_code)
                               from $envm_csm_db.corporate.hr_locations_all
                               where attribute14 = 'Y'
                                 and inactive_date is null
                                 and date(attribute5 ,'DD-MON-YY') < current_date
                                 and substr(loc_information15,1,2) in ('HF',
                                                                       'FV'))
        order by store_number

;
