"""
LEGACY MRI Exceptions - New Buildings
Converted from R to Python
Version: 20250108

This script connects to Snow<PERSON>lake, runs a query to find building exceptions,
creates an Excel report, and sends email notifications.
"""

import pandas as pd
import numpy as np

import datetime
import os
import sys
import platform

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo

from pathlib import Path

import libs.snowflake_helper as sf
import libs.email_client as email_client

OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration
TESTING_EMAILS = False  # Set to False for production
SCRIPT_FOLDER = "LEGACY_MRI_Exceptions-New_Buildings"
REPORT_NAME = "MRI New Building Exceptions"
REPORT_FILENAME = "MRI_New_Building_Exception_Details.xlsx"

# Get current date in required format
QUERY_DATE = datetime.datetime.now().strftime("%d-%b-%y")

# Report criteria for email
REPORT_CRITERIA = """<p><b>Criteria for inclusion in the report:</b><ul>
<li>Building has mismatched BSQF-SSQF table sqft values</li>
</ul></p>"""

class MRIExceptionsReporter:
    def __init__(self):
        # self.setup_logging()

        self.sf_obj = sf.SnowflakeHelper()
        self.sf_connection = self.sf_obj.conn

        self.setup_paths()
        self.setup_email_config()
        
        
    def setup_paths(self):
        """Setup file paths based on computer type"""

        self.testing_pc = False
        # self.main_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
            
        self.log_path = self.main_path / SCRIPT_FOLDER
        self.report_path = self.log_path
        
        # Create directories if they don't exist
        self.log_path.mkdir(parents=True, exist_ok=True)
        self.report_path.mkdir(parents=True, exist_ok=True)
        
    def setup_email_config(self):
        """Setup email configuration"""
        self.norm_recipients = [
            "<EMAIL>",
            "<EMAIL>","<EMAIL>"
        ]
        self.test_recipients = ["<EMAIL>"]
        self.warn_recipients = ["<EMAIL>","<EMAIL>"]
        
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Email signature
        self.norm_signature = """
        <b><span style='font-weight:bold'>Steve Olson</span></b><br/>
        Sr. Analytics Mgr.<br/>
        <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
        2500 Lehigh Ave.<br/>
        Glenview, IL 60026<br/>
        Ph: 847/904-9043<br/>
        """        
            
    def execute_query(self):
        """Execute the main query and return results"""
        query = """
        SELECT BLDGID AS "BLDG ID"
        ,	ISSUE as "Issue"
        ,	ISSUE_DETAILS AS "Issue Details"
        FROM
        (
            SELECT /* NEW BUILDINGS WITH MISMATCHED 'GLA' SQFT */ 
                BLDG.BLDGID
            ,	'Mismatched Building-Suite GLA Sqft' as ISSUE
            ,	CONCAT(
                    'BSQF: ', 
                    ifnull(to_char(cast(bsqf.SQFT AS int)), 'NA'), 
                    ', SSQF: ',  
                    ifnull(to_char(SUM(cast(Ssqf.SQFT AS int))), 'NA'),
                    ' | BSQF Effdate: ',
                    ifnull(to_char(bsqf.EFFDATE,'yyyy-mm-dd'), 'NA'),
                    ', SSQF Effdate: ',
                    ifnull(to_char(ssqf.EFFDATE,'yyyy-mm-dd'), 'NA')
                    ) AS ISSUE_DETAILS
            FROM MRI.ENTITY
            left join MRI.BLDG on entity.ENTITYID = BLDG.ENTITYID
            left join MRI.BSQF 
            on bldg.BLDGID = bsqf.BLDGID
            AND BSQF.EFFDATE = (
                SELECT MAX(I.EFFDATE) 
                FROM MRI.BSQF I 
                WHERE I.BLDGID = BSQF.BLDGID 
                AND I.SQFTTYPE = 'GLA' 
                AND I.EFFDATE <= TO_DATE(GETDATE()) 
            )
            AND BSQF.SQFTTYPE = 'GLA'
            left join MRI.ssqf 
            on bldg.BLDGID = SSQF.BLDGID and ssqf.SQFTTYPE = 'GLA'
            AND SSQF.EFFDATE = (
                SELECT MAX(II.EFFDATE) 
                FROM MRI.SSQF II 
                WHERE II.BLDGID = SSQF.BLDGID 
                AND II.SQFTTYPE = 'GLA' 
                AND II.EFFDATE <= TO_DATE(GETDATE()) 
            )
            AND SSQF.SQFTTYPE = 'GLA'
            WHERE ENTITY.ACQUIRED >= DATEADD(month, -1, GETDATE())
            GROUP BY 
              BLDG.BLDGID
            , bsqf.sqft
            , to_char(bsqf.EFFDATE,'yyyy-mm-dd')
            , to_char(ssqf.EFFDATE,'yyyy-mm-dd')
            HAVING ifnull(bsqf.sqft, 0) != SUM(cast(Ssqf.SQFT AS int))
        ) DETAILS
        order by DETAILS.BLDGID, DETAILS.ISSUE
        """
        # print(f"query: {query}")
        # exit(1)
        try:
            df = pd.read_sql(query, self.sf_connection)
            
            # Trim whitespace from string columns
            for col in df.select_dtypes(include=['object']).columns:
                df[col] = df[col].astype(str).str.strip()
                
            # self.logger.info(f"Query executed successfully. Retrieved {len(df)} rows.")
            self.sf_obj.log_audit_in_db(log_msg=f"Query executed successfully. Retrieved {len(df)} rows.", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            return df
            
        except Exception as e:
            # self.logger.error(f"Query execution failed: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Query execution failed: {str(e)}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            return None
            
    def create_excel_file(self, df, filename):
        """Create Excel file with formatting"""
        try:
            filepath = self.report_path / filename
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = QUERY_DATE
            
            # Add data to worksheet
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Format header row
            header_font = Font(bold=True, color="000000", size=12, name="Arial Narrow")
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # Set column widths
            column_widths = {
                'A': 10,  # BLDG ID
                'B': max(25, min(108, df.iloc[:, 1].astype(str).str.len().max() if len(df) > 0 else 25)),  # Issue
                'C': max(25, min(108, df.iloc[:, 2].astype(str).str.len().max() if len(df) > 0 else 25))   # Issue Details
            }
            
            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width
            
            # Freeze panes and add filters
            ws.freeze_panes = "A2"
            ws.auto_filter.ref = ws.dimensions
            
            # Save workbook
            wb.save(filepath)
            # self.logger.info(f"Excel file created successfully: {filepath}")
            self.sf_obj.log_audit_in_db(log_msg=f"Excel file created successfully: {filepath}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            return str(filepath)
            
        except Exception as e:
            # self.logger.error(f"Failed to create Excel file: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel file, {filepath}: {str(e)}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            return None
            
    def create_html_table(self, df):
        """Create HTML table for email body"""
        if len(df) == 0:
            return "<p>No exceptions found.</p>"
        
        # Use only first 2 columns for email summary
        email_df = df.iloc[:, :2].drop_duplicates()
        
        if len(email_df) < 20:
            # Create HTML table
            html_table = email_df.to_html(
                index=False,
                table_id="exceptions_table",
                border=2,
                classes="table table-striped"
            )
            return f"""
            <p>The info below contains MRI data (from yesterday) that appears to be an exception. 
            <b>See attached Excel file for full details.</b></p>
            {html_table}
            """
        else:
            return f"""
            <p><strong><em>There are {len(email_df)} results, see attached file for all.</em></strong></p>
            """
            
            
    def check_data_quality(self, df, min_rows=1):
        """Check if data meets quality requirements"""
        if df is None:
            return False, 0, f"{REPORT_NAME}: NO RESULTS"
        elif len(df) >= min_rows:
            return True, len(df), f"{REPORT_NAME}: COMPLETE"
        else:
            return False, len(df), f"{REPORT_NAME}: INCOMPLETE RESULTS"
            
    def run_report(self):
        """Main method to run the entire report process"""
        try:
            # self.logger.info(f"Starting routine: {REPORT_NAME}")
            self.sf_obj.log_audit_in_db(log_msg=f"Starting routine: {REPORT_NAME}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            
                
            # Execute query
            df = self.execute_query()
            
            # Check data quality
            data_ok, row_count, status = self.check_data_quality(df)
            # self.logger.info(f"Data quality check: {status} ({row_count} rows)")
            self.sf_obj.log_audit_in_db(log_msg=f"Data quality check: {status} ({row_count} rows)", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            
            if data_ok and row_count > 0:
                # Create Excel file
                excel_file = self.create_excel_file(df, REPORT_FILENAME)
                
                if excel_file:
                    # Create email content
                    table_html = self.create_html_table(df)
                    
                    email_body = f"""
                    <p><h2>REPORT: {REPORT_NAME}</h2></p>
                    {REPORT_CRITERIA}
                    {table_html}
                    <br/>
                    {self.norm_signature}
                    """
                    
                    # Determine recipients
                    recipients = self.test_recipients if TESTING_EMAILS else self.norm_recipients
                    
                    if TESTING_EMAILS:
                        email_body = f"""
                        <p><b>TEST SEND (normal recipients: {', '.join(self.norm_recipients)})</b></p>
                        {email_body}
                        """
                    
                    # Send email
                    email_client.send_email(
                        recipient=recipients,
                        subject=REPORT_NAME,
                        body=email_body,
                        attachments=[excel_file],
                        # sender=self.gmail_auth_email,
                        replyto=self.gmail_reply_to,
                        override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                    )
                    
            elif row_count == 0:
                # self.logger.info("No exceptions found - no email will be sent")
                self.sf_obj.log_audit_in_db(log_msg="No exceptions found - no email will be sent", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            else:
                # self.logger.warning(f"Data quality issue: {status}")
                self.sf_obj.log_audit_in_db(log_msg=f"Data quality issue: {status}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Warning')
                
            return True
            
        except Exception as e:
            # self.logger.error(f"Report execution failed: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Report execution failed: {str(e)}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            return False
            
def main():
    """Main function"""
    reporter = MRIExceptionsReporter()
    success = reporter.run_report()
    
    if success:
        print(f"{REPORT_NAME} completed successfully")
        return 0
    else:
        print(f"{REPORT_NAME} failed")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 