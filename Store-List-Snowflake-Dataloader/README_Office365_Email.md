# Office 365 Email Reader

A Python library for reading emails from Office 365 using either IMAP or Microsoft Graph API.

## Features

- **Two connection methods**: IMAP (simple) and Microsoft Graph API (powerful)
- **Email filtering**: Filter by date, read status, subject, sender
- **Multiple folder support**: Read from Inbox, Sent, or custom folders
- **Attachment handling**: Detect and process email attachments
- **JSON export**: Save emails to JSON format for further processing
- **Business logic integration**: Easy integration with existing systems

## Quick Start

### 1. IMAP Method (Recommended for getting started)

```python
from libs.office365_email_reader import Office365EmailReader, EmailReaderMethod

# Set up environment variables
import os
os.environ["OFFICE365_EMAIL_ADDRESS"] = "<EMAIL>"
os.environ["OFFICE365_EMAIL_PASSWORD"] = "your_app_password"

# Create reader and get emails
reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
emails = reader.get_emails(limit=10, unread_only=True)

# Process emails
for email in emails:
    print(f"From: {email.sender}")
    print(f"Subject: {email.subject}")
    print(f"Date: {email.received_date}")
    print(f"Body: {email.body[:100]}...")
    print("-" * 50)

reader.close()
```

### 2. Microsoft Graph API Method (Recommended for production)

```python
from libs.office365_email_reader import Office365EmailReader, EmailReaderMethod

# Set up environment variables
import os
os.environ["OFFICE365_EMAIL_ADDRESS"] = "<EMAIL>"
os.environ["OFFICE365_CLIENT_ID"] = "your_client_id"
os.environ["OFFICE365_CLIENT_SECRET"] = "your_client_secret"
os.environ["OFFICE365_TENANT_ID"] = "your_tenant_id"

# Create reader and get emails
reader = Office365EmailReader(method=EmailReaderMethod.GRAPH_API)
emails = reader.get_emails(limit=10, unread_only=True)

# Process emails
for email in emails:
    print(f"From: {email.sender}")
    print(f"Subject: {email.subject}")
    print(f"Date: {email.received_date}")
    
    # Mark as read (Graph API only)
    reader.mark_as_read(email.message_id)

reader.close()
```

## Setup Instructions

### Option 1: IMAP Setup (Simpler)

1. **Enable IMAP in Office 365**:
   - Go to Outlook web interface
   - Settings > View all Outlook settings > Mail > Sync email
   - Enable IMAP access

2. **Create App Password**:
   - Go to Microsoft Account Security settings
   - Advanced security options > App passwords
   - Create new app password

3. **Set Environment Variables**:
   ```bash
   export OFFICE365_EMAIL_ADDRESS="<EMAIL>"
   export OFFICE365_EMAIL_PASSWORD="your_app_password"
   ```

### Option 2: Microsoft Graph API Setup (Recommended)

1. **Register Azure Application**:
   - Go to [Azure Portal](https://portal.azure.com)
   - Azure Active Directory > App registrations > New registration

2. **Configure Permissions**:
   - API permissions > Add permission > Microsoft Graph
   - Application permissions: Mail.Read, Mail.ReadWrite
   - Grant admin consent

3. **Create Client Secret**:
   - Certificates & secrets > New client secret
   - Copy the secret value

4. **Set Environment Variables**:
   ```bash
   export OFFICE365_EMAIL_ADDRESS="<EMAIL>"
   export OFFICE365_CLIENT_ID="your_client_id"
   export OFFICE365_CLIENT_SECRET="your_client_secret"
   export OFFICE365_TENANT_ID="your_tenant_id"
   ```

## Examples

### Search for Specific Emails

```python
from datetime import datetime, timedelta

# Search for emails with "report" in subject from last 7 days
emails = reader.get_emails(
    limit=20,
    search_criteria="report",
    since=datetime.now() - timedelta(days=7)
)
```

### Process Emails by Category

```python
# Categorize emails
report_emails = []
urgent_emails = []

for email in emails:
    if any(keyword in email.subject.lower() for keyword in ['report', 'summary']):
        report_emails.append(email)
    elif any(keyword in email.subject.lower() for keyword in ['urgent', 'asap']):
        urgent_emails.append(email)

print(f"Found {len(report_emails)} report emails")
print(f"Found {len(urgent_emails)} urgent emails")
```

### Save Emails to JSON

```python
from libs.office365_email_reader import save_emails_to_json

emails = reader.get_emails(limit=50)
save_emails_to_json(emails, "office365_emails.json")
```

### Get Available Folders

```python
folders = reader.get_folders()
print(f"Available folders: {folders}")

# Read from specific folder
sent_emails = reader.get_emails(folder="Sent", limit=10)
```

## Integration with Existing Systems

This email reader integrates seamlessly with your existing email infrastructure:

```python
# Use with existing email client patterns
from libs.office365_email_reader import Office365EmailReader
import libs.email_client as email_client

# Read emails
reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
emails = reader.get_emails(unread_only=True)

# Process and respond using existing email client
for email in emails:
    if "exception" in email.subject.lower():
        # Send alert using existing email system
        email_client.send_email(
            recipient=["<EMAIL>"],
            subject=f"Alert: {email.subject}",
            body=f"Exception email received from {email.sender}"
        )
```

## Error Handling

```python
try:
    reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
    emails = reader.get_emails(limit=10)
    
    for email in emails:
        # Process email
        pass
        
except ValueError as e:
    print(f"Configuration error: {e}")
except Exception as e:
    print(f"Error reading emails: {e}")
finally:
    reader.close()
```

## Files

- `libs/office365_email_reader.py` - Main email reader library
- `office365_email_example.py` - Complete examples
- `office365_config.py` - Configuration helper and setup instructions

## Testing

Run the example script to test your setup:

```bash
python office365_email_example.py
```

Or run the configuration helper to see setup instructions:

```bash
python office365_config.py
```

## Dependencies

The email reader uses Python standard library modules and optionally the `requests` library for Microsoft Graph API functionality.

## Troubleshooting

- **Authentication failed**: Check your credentials and app password
- **Connection refused**: Verify IMAP is enabled and ports are correct
- **Permission denied**: Check Azure app permissions and admin consent
- **No emails found**: Check folder names and search criteria

For detailed troubleshooting, run:

```bash
python office365_config.py
``` 