"""
MARCOS 3rd Party Management Import Script
Converted from R to Python

Version: 20230616
Author: Converted to Python

Description:
- Reads data from Google Sheets
- Updates Oracle database table
- Sends email notifications
- Creates Excel reports
"""

import pandas as pd
import numpy as np
import os
import sys
import datetime

from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo

from typing import List, Dict, Tuple, Optional, Any
import time
import libs.snowflake_helper as sf
import libs.email_client as email_client
from libs.excel_helper import SharePointExcelOnline

OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration
# testing_emails = False  # NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
# testing_emails = True

# Version 20230616
### 20230616 change:
### adds column to df for YourFare if not present in gSht
### 20230329 change:
### new file based on LEGACY_Portfolio_Details_import_update - 20230328.R

# Parameters
myReportName = "<PERSON>'s 3rd Party Management"
scriptfolder = "MARCOS_3rd_Party_Mgmt"
rptfolder = "reports"
gSht_auth_email = "<EMAIL>"
# https://docs.google.com/spreadsheets/d/1bwHw4veu5ITupVTInScGfHtTPLGJgka0fAVSiPlA6Ho/edit#gid=109859760
gSht_mainURL = 'https://docs.google.com/spreadsheets/d/1bwHw4veu5ITupVTInScGfHtTPLGJgka0fAVSiPlA6Ho/' # old
gSht_id = '1bwHw4veu5ITupVTInScGfHtTPLGJgka0fAVSiPlA6Ho' # old


file_id = '012HBEZLJNTC2GCLZO6ZJNP4M7C5AIH2B7' # prod  : https://highlandventuresltd442-my.sharepoint.com/:x:/g/personal/jbogdan_hv_ltd/ES2YtGEvLvZS1_GfF0CD6D8B2WgljKKO85yOAxc4-WY_Ig?e=prYQcO&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDYwMjA2NjE2IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9
site_url = 'https://highlandventuresltd442-my.sharepoint.com/personal/jbogdan_hv_ltd'
public_site_url = 'https://highlandventuresltd442-my.sharepoint.com/:x:/g/personal/jbogdan_hv_ltd/ES2YtGEvLvZS1_GfF0CD6D8B2WgljKKO85yOAxc4-WY_Ig?e=prYQcO&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDYwMjA2NjE2IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9'
mySheets = ["Store List"]

logpath = os.path.join(os.environ["SCRIPTS_BASE_DATA_DIR"], scriptfolder)
HVSigLogopath = os.path.join(os.environ["SCRIPTS_BASE_DATA_DIR"], "HV Logo Email Signature.png")
okaytocontinue = True

# NOTE myColNames order dictates column order in resulting dataframe when filtered
# myColNames are the needed names in source Google Sheet
myColNames = [
    "ST",
    "DoorDash",
    "GrubHub", 
    "UberEats",
    "EzCater",
    "BiteSquad",
    "EatStreet",
    "YourFare"
    # ,"TEST DOESN'T EXIST"
]

myColName_bldg = ["ST"]
myColName_bldgid_New = "ST"

# myColNames_New are Oracle db column names
myColNames_New = [
    "ST",
    "DOORDASH",
    "GRUBHUB",
    "UBEREATS", 
    "EZCATER",
    "BITESQUAD",
    "EATSTREET",
    "YOURFARE"
]

# Oracle connection parameters
os.environ['TZ'] = 'America/Chicago'

sf_obj = sf.SnowflakeHelper()

db_name = os.environ["DATABASE_CSM_DATABASE"]
db_schema = "CORPORATE"

db_table = "MP_3RD_PARTY_MGMT"
db_table_name = f"{db_schema}.{db_table}"

# Email parameters
warn_recip = ["<EMAIL>", "<EMAIL>","<EMAIL>"]
warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip = ["<EMAIL>","<EMAIL>"]
test_recip = ["<EMAIL>","<EMAIL>"]
test_cc_recip = ["<EMAIL>"]

report_time_txt = datetime.datetime.now().strftime("%H:%M:%S %Z")


myReportPath = os.path.join(logpath, rptfolder)

# Append signature logo to signatures
if os.path.exists(HVSigLogopath):
    warn_sig += f'<br/><img src="{HVSigLogopath}" width="420"> '

### Function Definitions ###



def check_mydata_rows(mydata: pd.DataFrame, MinNumRows: int, ReportName: Optional[str] = None) -> Tuple[bool, int, str]:
    """Check if dataframe has minimum required rows"""
    if mydata is not None and isinstance(mydata, pd.DataFrame):
        if len(mydata) >= MinNumRows:
            error_status = f"{ReportName}: COMPLETE"
            tempnrow = len(mydata)
            tempbool = True
        else:
            tempbool = False
            tempnrow = len(mydata)
            error_status = f"{ReportName}: INCOMPLETE RESULTS"
    else:
        # problem with data load
        tempbool = False
        tempnrow = 0
        error_status = f"{ReportName}: NO RESULTS"
    
    return tempbool, tempnrow, error_status

def check_mydf_rows(mydf: pd.DataFrame, MinNumRows: int, ReportName: Optional[str] = None) -> Tuple[bool, int, str]:
    """Check if dataframe has minimum required rows"""
    if isinstance(mydf, pd.DataFrame):
        if len(mydf) >= MinNumRows:
            error_status = f"{ReportName}: OKAY"
            tempnrow = len(mydf)
            tempbool = True
        else:
            tempbool = False
            tempnrow = len(mydf)
            error_status = f"{ReportName}: INCOMPLETE"
    else:
        # problem with data load
        tempbool = False
        tempnrow = 0
        error_status = f"{ReportName}: ERROR"
    
    return tempbool, tempnrow, error_status

def find_hdr_row(mydf: pd.DataFrame, hdr_colnames: List[str]) -> int:
    """Find header row in dataframe"""
    output = 0
    if len(mydf) > 1:
        # Check each row for matches with header column names
        for idx, row in mydf.iterrows():
            matches = sum(1 for val in row.values if str(val) in hdr_colnames)
            if matches > 0:
                output = idx + 1  # +1 for 1-based indexing like R
                break
    elif len(mydf) == 1:
        row = mydf.iloc[0]
        matches = sum(1 for val in row.values if str(val) in hdr_colnames)
        if matches > 0:
            output = 1
    
    return output

def writeXLSX(dirpath: str, fname: str, sname: str = "Sheet1", RptDF: pd.DataFrame = pd.DataFrame(), colnames: bool = True, colwidths: Optional[pd.DataFrame] = None, writeover: bool = True) -> None:
    """Write DataFrame to Excel file with formatting"""
    
    myFN = os.path.join(dirpath, fname)
    
    # Create directory if it doesn't exist
    if not os.path.exists(dirpath):
        os.makedirs(dirpath, exist_ok=True)
    
    try:

        RptDF = RptDF.replace({None: '', np.nan: '', pd.NA: ''}) # replace None with empty string
        # RptDF = RptDF.replace({None: pd.NA, np.nan: pd.NA}) # replace None with empty string

        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = sname
        
        # Add data to worksheet
        for r in dataframe_to_rows(RptDF, index=False, header=colnames):
            ws.append(r)
        
        # Format header row
        if colnames:
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_font = Font(name="Arial Narrow", size=12, bold=True)
            header_alignment = Alignment(wrap_text=True, vertical="center")
            
            for cell in ws[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
        
        # Set column widths
        if colwidths is not None and isinstance(colwidths, pd.DataFrame) and len(colwidths) > 0:
            for _, row in colwidths.iterrows():
                col_name = row.iloc[0]
                width = row.iloc[1]
                try:
                    col_idx = list(RptDF.columns).index(col_name) + 1
                    col_letter = chr(64 + col_idx)  # Convert to letter
                    ws.column_dimensions[col_letter].width = width
                except (ValueError, IndexError):
                    continue
        else:
            # Set default width
            for col in ws.columns:
                ws.column_dimensions[col[0].column_letter].width = 15
        
        # Freeze panes
        ws.freeze_panes = 'A2'
        
        # Add autofilter
        if len(RptDF) > 0:
            ws.auto_filter.ref = f"A1:{chr(64 + len(RptDF.columns))}{len(RptDF) + 1}"
        
        # Save file
        wb.save(myFN)
        print(f"Excel file saved: {myFN}")
        sf_obj.log_audit_in_db(log_msg=f"Excel file saved: {myFN}", process_type=myReportName, script_file_name=__file__, log_type='Info')
        
    except Exception as e:
        print(f"Error saving Excel file {myFN}: {e}")
        sf_obj.log_audit_in_db(log_msg=f"Error saving Excel file {myFN}: {e}", process_type=myReportName, script_file_name=__file__, log_type='Error')
        # Try with alternate filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        alt_fname = f"{timestamp}-{fname}"
        alt_myFN = os.path.join(dirpath, alt_fname)
        
        try:
            wb.save(alt_myFN)
            print(f"Excel file saved with alternate name: {alt_myFN}")
            sf_obj.log_audit_in_db(log_msg=f"Excel file saved with alternate name: {alt_myFN}", process_type=myReportName, script_file_name=__file__, log_type='Info')
            # Send warning email
            bodytext = f"""This is an automated email to inform you that it appears <b>the following file WAS SAVED 
                         WITH AN ALTERNATE FILENAME</b> during the <b>{myReportName}</b> routine.<br/><br/>
                         {alt_myFN}<br/><br/>
                         It appears that the original filename ({fname}) was open in another process or locked.<br/><br/>
                         The routine should continue.<br/><br/>
                         {warn_sig}"""
            
            email_client.send_email(
                recipient=warn_recip,
                subject=f"{myReportName} : REPORT FILE SAVING ERROR",
                body=bodytext,
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
            
        except Exception as e2:
            print(f"Error saving Excel file with alternate name: {e2}")
            sf_obj.log_audit_in_db(log_msg=f"Error saving Excel file with alternate name: {e2}", process_type=myReportName, script_file_name=__file__, log_type='Error')
            # Send failure email
            bodytext = f"""This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> 
                         during the <b>{myReportName}</b> routine.<br/><br/>
                         {myFN}<br/><br/>
                         Either the path wasn't accessible or the file was open in another process.<br/><br/>
                         The routine should continue without saving this file.<br/><br/>
                         {warn_sig}"""
            
            email_client.send_email(
                recipient=warn_recip,
                subject=f"{myReportName} : REPORT FILE SAVING ERROR",
                body=bodytext,
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )

### Main Execution ###

def main():
    global okaytocontinue
    myemailfiles = []
    
    print(f"Starting {myReportName} routine at {report_time_txt}")
    # sf_obj = sf.SnowflakeHelper()
    # sf_obj.LOG_TO_DB = False
    sf_conn = sf_obj.conn

    sf_obj.log_audit_in_db(log_msg=f"Starting {myReportName} routine at {report_time_txt}", process_type=myReportName, script_file_name=__file__, log_type='Info')
    

    # Query existing table data to compare to new run
    if okaytocontinue:
        try:
            myquery = f"""
                SELECT * 
                FROM {db_table_name}
                ORDER BY ST
            """
            priordata = pd.read_sql(myquery, sf_conn)
            # remove unneeded columns
            priordata = priordata.drop(['_DLT_LOAD_ID','_DLT_ID'], axis=1) 
            priordata['ST'] = priordata['ST'].astype(int) # cast store as int
            # priordata = priordata.replace({None: '', np.nan: ''}) # replace None with empty string
            priordata = priordata.replace({None: pd.NA, np.nan: pd.NA}) # replace None with empty string
            

            # print(f"priordata: {priordata}")
            # exit()
            
            # Create Excel file with prior data
            myXLSXColWidths = pd.DataFrame({
                'colname': priordata.columns,
                'width': [15] * len(priordata.columns)
            })
            
            mySN = datetime.datetime.now().strftime('%b-%d-%Y')
            myFN = f"{db_table} Prior Data.xlsx"
            writeXLSX(dirpath=myReportPath, fname=myFN, sname=mySN, RptDF=priordata, 
                     colnames=True, colwidths=myXLSXColWidths, writeover=True)
            myemailfiles.append(os.path.join(myReportPath, myFN))
            
        except Exception as e:
            print(f"Error querying prior data: {e}")
            sf_obj.log_audit_in_db(log_msg=f"Error querying prior data: {e}", process_type=myReportName, script_file_name=__file__, log_type='Error')
            okaytocontinue = False
    

    
    # Get Google Sheet data
    if okaytocontinue:
        try:
            # Read the sheet

            
            # log_audit_in_db(log_msg=f"Start: Getting excel session for file ID {file_id} from {site_url}")
            
            excel_helper = SharePointExcelOnline()
            # Get Excel session using file ID
            excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)
            existing_data_from_sheet_dict = excel_helper.get_worksheet_data(excel_session, mySheets[0])
            values_existing_data_from_sheet_list = existing_data_from_sheet_dict.get('values')
            existing_data_from_sheet_df = pd.DataFrame(data=values_existing_data_from_sheet_list[1:], columns=values_existing_data_from_sheet_list[0])
            existing_data_from_sheet_df = existing_data_from_sheet_df[existing_data_from_sheet_df['ST'] != '']  # # remove rows where all values are empty strings



            
            # Check if "YourFare" is present, add if missing
            # if "YourFare" not in gSht_Curr.columns:
            if "YourFare" not in existing_data_from_sheet_df.columns:
                existing_data_from_sheet_df["YourFare"] = pd.NA

            # todo: remove after testing
            # existing_data_from_sheet_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/MARCOS_3rd_Party_Mgmt_import.csv', index=False)
            # print(f"existing_data_from_sheet_df: {existing_data_from_sheet_df}")
            # exit()

            
            # Check that expected column names are present
            gSht_missing_colnames = [col for col in myColNames if col not in existing_data_from_sheet_df.columns]
            
            if len(gSht_missing_colnames) > 0:
                okaytocontinue = False
                bodytext = f"""<p>This is an automated email to inform you that it appears that 
                             one or more expected column names could not be found 
                             in the '{myReportName}' routine!</p>
                             <p><b>The routine is aborting without an update.</b></p> 
                             <p>Check the <a href='{public_site_url}'>{mySheets[0]}</a>  sheet for the 
                             following columns expected, but NOT found 
                             (check for case or spelling): <br><br><b>
                             {'<br>'.join(gSht_missing_colnames)}
                             </b></p>
                             {warn_sig}"""
                
                email_client.send_email(
                    recipient=warn_recip,
                    subject="Issue: Issue with Data in Sheet",
                    body=bodytext,
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
        except Exception as e:
            print(f"Error reading Google Sheet data: {e}")
            sf_obj.log_audit_in_db(log_msg=f"Error reading Google Sheet data: {e}", process_type=myReportName, script_file_name=__file__, log_type='Error')
            okaytocontinue = False
    
    # Upload data into Oracle table
    if okaytocontinue:
        try:
            # Filter sheet data to needed columns and rename to match Oracle
            # mydata_upload = gSht_Curr[myColNames].copy()
            mydata_upload = existing_data_from_sheet_df[myColNames].copy()
            mydata_upload.columns = myColNames_New
            
            # Remove potentially blank rows - keep rows where ST is numeric and not 0
            st_valid = pd.to_numeric(mydata_upload['ST'], errors='coerce').notna() & \
                      (pd.to_numeric(mydata_upload['ST'], errors='coerce') != 0)
            mydata_upload = mydata_upload[st_valid]

            mydata_upload = mydata_upload.replace({'': pd.NA,None: pd.NA, np.nan: pd.NA}) # replace empty strings with null with empty string

            # mydata_upload.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/MARCOS_3rd_Party_Mgmt_import_cleaned.csv', index=False)
            # print(f"mydata_upload: {mydata_upload}")
            # exit()
            
            # Get initial table row count
            cursor = sf_conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {db_table_name}")
            select_cnt = cursor.fetchone()[0]

            # print(f"select_cnt: {select_cnt}")
            # exit()
            
            # Attempt delete of old data
            cursor.execute(f"DELETE FROM {db_table_name} WHERE ST IS NOT NULL")
            rows_deleted = cursor.rowcount
            
            send_warning = False
            
            if rows_deleted != select_cnt:
                # Delete failed
                print("Warning: dubious deletion -- rolling back transaction")
                sf_obj.log_audit_in_db(log_msg=f"Warning: dubious deletion -- rolling back transaction", process_type=myReportName, script_file_name=__file__, log_type='Warning')
                sf_conn.rollback()
                send_warning = True
                
                bodytext = f"""<p>This is an automated email to inform you that it appears that 
                             data for the '{db_table_name}' table was not updated 
                             in the '{myReportName}' routine!</p>
                             <p><b>The routine was unable to delete existing data 
                             in the table so it aborted the table update.</b></p> 
                             <p>It will attempt to update the sheet 
                             with the updated data.</p>
                             {warn_sig}"""
            else:
                # Delete was successful, commit and proceed with load
                sf_conn.commit()
                
                # Insert new data                

                sf_obj.bulk_insert(columns_list=mydata_upload.columns, data_list=mydata_upload.values, database=db_name, schema=db_schema, table=db_table)
                sf_conn.commit()
                
                # Compare Oracle count vs dataframe rows to ensure all rows loaded
                cursor.execute(f"SELECT COUNT(*) FROM {db_table_name}")
                myload_numrows = cursor.fetchone()[0]
                mydata_numrows = len(mydata_upload)
                
                if myload_numrows != mydata_numrows:
                    # Mismatch in rows loaded, delete and try to restore previous data
                    send_warning = True
                    cursor.execute(f"DELETE FROM {db_table_name} WHERE ST IS NOT NULL")
                    sf_conn.commit()
                    
                    # Restore prior data
                    if len(priordata) > 0:
                        sf_obj.bulk_insert(columns_list=priordata.columns, data_list=priordata.values, database=db_name, schema=db_schema, table=db_table)
                        sf_conn.commit()
                        
                        cursor.execute(f"SELECT COUNT(*) FROM {db_table_name}")
                        restored_count = cursor.fetchone()[0]
                        
                        if restored_count != len(priordata):
                            # Both load and restore failed
                            myXLSXColWidths = pd.DataFrame({
                                'colname': priordata.columns,
                                'width': [15] * len(priordata.columns)
                            })
                            myFN = f"{db_table} Prior Data - {mySN}.xlsx"
                            writeXLSX(dirpath=myReportPath, fname=myFN, sname=mySN, RptDF=priordata,
                                     colnames=True, colwidths=myXLSXColWidths, writeover=True)
                            
                            bodytext = f"""<p>This is an automated email to inform you that it appears that 
                                         data for the '{db_table_name}' table failed to load properly 
                                         in the '{myReportName}' routine!</p>
                                         <p><b>The routine attempted to restore the previous 
                                         data in the table but that also seems to have failed.</b></p> 
                                         <p>Check the attached 'Failures' file for improper data that 
                                         might have caused the load to fail. A file with prior 
                                         data from the table (which failed to restore) is also attached.</p>
                                         {warn_sig}"""
                        else:
                            # Load failed but prior data restored
                            bodytext = f"""<p>This is an automated email to inform you that it appears that 
                                         data for the '{db_table_name}' table failed to load properly 
                                         in the '{myReportName}' routine!</p>
                                         <p><b>The routine WAS ABLE to restore the previous 
                                         data in the table, but that data is likely out-of-date.</b></p> 
                                         <p>Check the attached 'Failures' file for improper data that 
                                         might have caused the load to fail. A file with the prior 
                                         data from the table is also attached for comparison.</p>
                                         {warn_sig}"""
                    
                    # Create Excel files of failed data
                    myXLSXColWidths = pd.DataFrame({
                        'colname': mydata_upload.columns,
                        'width': [15] * len(mydata_upload.columns)
                    })
                    myFN = f"{db_table} Failures - {mySN}.xlsx"
                    writeXLSX(dirpath=myReportPath, fname=myFN, sname=mySN, RptDF=mydata_upload,
                             colnames=True, colwidths=myXLSXColWidths, writeover=True)
                    myemailfiles.append(os.path.join(myReportPath, myFN))
            
            cursor.close()
            
            if send_warning:
                # Send email of failure if appropriate
                email_client.send_email(
                    recipient=warn_recip,
                    subject="Issue: Issue with Snowflake table load",
                    body=bodytext,
                    attachments=myemailfiles,
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
        except Exception as e:
            print(f"Error during Oracle operations: {e}")
            sf_obj.log_audit_in_db(log_msg=f"Error during Oracle operations: {e}", process_type=myReportName, script_file_name=__file__, log_type='Error')
            if 'sf_conn' in locals():
                sf_conn.rollback()
    
    
    print(f"Completed {myReportName} routine")
    sf_obj.log_audit_in_db(log_msg=f"Completed {myReportName} routine", process_type=myReportName, script_file_name=__file__, log_type='Info')

    # Clean up
    if 'sf_conn' in locals():
        sf_conn.close()

if __name__ == "__main__":
    main() 