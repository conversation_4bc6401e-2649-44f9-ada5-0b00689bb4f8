# Converted from  R to Python: 5/28/2025
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter

# from email.mime.text import MIMEText
# from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
# from email.mime.application import MIMEApplication

# import pickle
# import json
import libs.snowflake_helper as sf
import libs.email_client as email_client

# dir_path = os.path.dirname(os.path.realpath(__file__))
dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
envm = os.environ["DATABASE_ENVM"]
csm_db = os.environ["DATABASE_CSM_DATABASE"]


# Constants and Configuration
TESTING_EMAILS = False
ALLOWED_LIST = ["Default", "Delinquent", "Dark But Paying", "Vacating", "Rent Relief", "Not Renewing", "Requested Termination"]

# Version 20241112
# Converted from R to Python
# Uses Snowflake DB
# Uses Gmail API for email
# Updated signature for emails to standard requested by Stephanie Forsberg in March 2024

# Parameters
QUERY_DATE = datetime.now().strftime("%d-%b-%y")
MY_REPORT_NAME = "MRI Availability BYEBYE Lease Note Exceptions"
SCRIPT_FOLDER = f"{dir_path}/LEGACY_Available_Suites"
RPT_FOLDER = "reports"
RPT_FN = "MRI_Availability_BYEBYE_Lease_Note_Exceptions.xlsx"

# Email configuration
GMAIL_AUTH_EMAIL = "<EMAIL>"
GMAIL_REPLY_TO = "<EMAIL>"
NORM_RECIP = ["<EMAIL>"]
WARN_RECIP = ["<EMAIL>"]
TEST_RECIP = ["<EMAIL>"]
TEST_CC_RECIP = ["<EMAIL>"]


def write_excel(dirpath, fname, sname, df, colnames=True, colwidths=None, writeover=True):
    """Write DataFrame to Excel file with formatting."""
    my_fn = os.path.join(dirpath, fname)
    
    if not os.path.exists(dirpath):
        os.makedirs(dirpath)
    
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sname
    
    # Write headers
    for col_num, column in enumerate(df.columns, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = column
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Write data
    for row_num, row in enumerate(df.values, 2):
        for col_num, value in enumerate(row, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = value
    
    # Set column widths
    if colwidths is not None:
        for col_name, width in colwidths.items():
            col_idx = df.columns.get_loc(col_name) + 1
            ws.column_dimensions[get_column_letter(col_idx)].width = width
    
    # Freeze panes and add filter
    ws.freeze_panes = 'A2'
    ws.auto_filter.ref = ws.dimensions
    
    try:
        wb.save(my_fn)
    except Exception as e:
        # Handle file saving errors
        alt_fn = f"{datetime.now().strftime('%Y%m%d-%H%M%S%Z')}-{my_fn}"
        try:
            wb.save(alt_fn)
            body_text = f"""This is an automated email to inform you that it appears <b>the following file WAS SAVED 
            WITH AN ALTERNATE FILENAME</b> during the <b>{MY_REPORT_NAME}</b> routine.<br/><br/>
            {alt_fn}<br/><br/>It appears that the original filename ({fname}) was open in another process or locked.
            <br/><br/>The routine should continue.<br/> <br/>"""
        except:
            body_text = f"""This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> 
            during the <b>{MY_REPORT_NAME}</b> routine.<br/><br/>
            {my_fn}<br/><br/>Either the path wasn't accessible or the file was open in another process.
            <br/><br/>The routine should continue without saving this file.<br/> <br/>"""
        
        send_email(WARN_RECIP, f"{MY_REPORT_NAME} : REPORT FILE SAVING ERROR", body_text)

def main():
    # Connect to Snowflake

    sf_obj = sf.SnowflakeHelper()
    conn = sf_obj.conn
    
    # Set timezone
    conn.cursor().execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
    
    # Build query
    query_wherelist = ", ".join([f"'{x.upper()}'" for x in ALLOWED_LIST])
    query = f"""
    SELECT 
        NOTE.BLDGID,
        NOTE.LEASID,
        MNGR.MNGRNAME AS "PROPERTY_MGR",
        TO_DATE(NOTE.LASTDATE) AS "NOTE CREATED OR EDITED",
        '"BYEBYE" notetext not recognized as one of the allowable types ({", ".join(ALLOWED_LIST)})' AS ISSUE,
        TO_DATE(NOTE.NOTEDATE) AS "NOTE DATE",
        NOTE.NOTETEXT AS "NOTE TEXT"
    FROM {csm_db}.MRI.NOTE
    LEFT JOIN {csm_db}.MRI.BLDG ON BLDG.BLDGID = NOTE.BLDGID
    LEFT JOIN {csm_db}.MRI.ENTITY ON BLDG.ENTITYID = ENTITY.ENTITYID
    LEFT JOIN {csm_db}.MRI.MNGR ON BLDG.MNGRID = MNGR.MNGRID
    WHERE
        (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
        AND (NOTE.REF1 = 'BYEBYE' OR NOTE.REF2 = 'BYEBYE')
        AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
        AND (
            NOTE.NOTETEXT IS NULL
            or 
            UPPER(RTRIM(NOTE.NOTETEXT)) NOT IN ({query_wherelist})
        ) 
    ORDER BY BLDG.BLDGID, NOTEDATE
    """

    # print(f"query: {query}")
    # exit(1)

    # Execute query and get results
    df = pd.read_sql(query, conn)

    if len(df) > 0:
        # Process data
        #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
        # mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
        # df = df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)
        df["NOTE TEXT"] = df["NOTE TEXT"].fillna('')
        # Define column widths
        colwidths = {
            "BLDGID": 8.5,
            "LEASID": 9,
            "PROPERTY_MGR": 18,
            "NOTE CREATED OR EDITED": 11,
            "ISSUE": min(135, max(df["ISSUE"].str.len())),
            "NOTE DATE": 11,
            "NOTE TEXT": min(52, max(df["NOTE TEXT"].str.len()))
        }
        
        # Write to Excel
        write_excel(
            dirpath=os.path.join(SCRIPT_FOLDER, RPT_FOLDER),
            fname=RPT_FN,
            sname=QUERY_DATE,
            df=df,
            colwidths=colwidths
        )
        
        # Prepare email
        email_cols = [0, 1, 2, 3, 6]  # Corresponding to BLDGID, LEASID, PROPERTY_MGR, NOTE CREATED OR EDITED, NOTE TEXT
        email_df = df.iloc[:, email_cols].drop_duplicates()
        
        if len(email_df) < 31:
            # Create HTML table for email body
            body_table = email_df.to_html(index=False, border=2)
            body_text = f"""
            <p>The info below contains MRI data (from yesterday) that appears to be an exception. 
            <b>See attached Excel file for full details.</b></p>
            <p>{body_table}</p>
            """
        else:
            body_text = f"""
            <p><strong><em>There are {len(email_df)} results, see attached file for all.</em></strong></p>
            """
        
        # Send email
        email_client.send_email(
            recipient=NORM_RECIP,
            subject=MY_REPORT_NAME,
            body=body_text,
            attachments=[os.path.join(SCRIPT_FOLDER, RPT_FOLDER, RPT_FN)],
            test=TESTING_EMAILS,
            test_recipient=TEST_RECIP,
            replyto=GMAIL_REPLY_TO
        )
    
    # Close Snowflake connection
    conn.close()

if __name__ == "__main__":
    main() 