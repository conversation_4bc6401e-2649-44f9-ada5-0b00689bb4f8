# Converted from  R to Python: 5/28/2025
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
# import pytz
# import smtplib
# from email.mime.text import MIMEText
# from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
# from email.mime.application import MIMEApplication
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment

# import snowflake.connector
# from snowflake.connector.pandas_tools import write_pandas
import warnings
import libs.snowflake_helper as sf
import libs.email_client as email_client

warnings.filterwarnings('ignore')

# Configuration
TESTING_EMAILS = True  # Set to False in production
VERSION = "20250321"

# Parameters
# dir_path = os.path.dirname(os.path.realpath(__file__))
dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
csm_db = os.environ["DATABASE_CSM_DATABASE"]

QUERY_DATE = datetime.now().strftime("%d-%b-%y")
OKAY_TO_CONTINUE = True
# SCRIPT_FOLDER = "MARCOS_Exceptions-Leases"
SCRIPT_FOLDER = f"{dir_path}/MARCOS_Exceptions-Leases"
REPORT_NAME = "Marco's Lease Options Approaching"
REPORT_FILENAME = "MARCOS_Lease_Options.xlsx"

NOTICE_DAYS = '180, 60, 10, 1'

REPORT_CRITERIA = f"""
<p><b>Criteria for inclusion in the report:</b><ul>
<li>Rented location with a lease renewal option notice coming up AND no renewal lease found in MRI</li>
</li>
</ul></p>
<p><em>(notifications generated at {NOTICE_DAYS} days out)</em></p>
"""

# Email configuration
NORMAL_RECIPIENTS = ["<EMAIL>"]
WARNING_RECIPIENTS = ["<EMAIL>"]
TEST_RECIPIENTS = ["Steve Olson<<EMAIL>>"]
TEST_CC_RECIPIENTS = ["<EMAIL>"]

# Snowflake configuration


# Email authentication
GMAIL_AUTH_EMAIL = "<EMAIL>"
GMAIL_REPLY_TO = "<EMAIL>"

def get_signature(template_html, name='', title='', email='', phone=''):
    """Get formatted email signature"""
    sig = template_html
    sig = sig.replace('[NAME]', name)
    sig = sig.replace('[TITLE]', title)
    sig = sig.replace('[EMAIL_FULL]', email)
    sig = sig.replace('[TEL (*************]', phone)
    return sig

def check_dataframe_rows(df, min_rows, report_name=None):
    """Check if dataframe has minimum required rows"""
    if isinstance(df, pd.DataFrame):
        if len(df) >= min_rows:
            error_status = f"{report_name}: OKAY"
            temp_nrow = len(df)
            temp_bool = True
        else:
            temp_bool = False
            temp_nrow = len(df)
            error_status = f"{report_name}: INCOMPLETE"
    else:
        temp_bool = False
        temp_nrow = 0
        error_status = f"{report_name}: ERROR"
    
    return temp_bool, temp_nrow, error_status

def write_excel(dirpath, filename, sheet_name, df, colnames=True, colwidths=None, writeover=True):
    """Write dataframe to Excel file with formatting"""
    full_path = os.path.join(dirpath, filename)
    
    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sheet_name
    
    # Write headers
    for col_num, column in enumerate(df.columns, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = column
        cell.font = Font(bold=True, name='Arial Narrow', size=12)
        cell.fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Write data
    for row_num, row in enumerate(df.values, 2):
        for col_num, value in enumerate(row, 1):
            cell = ws.cell(row=row_num, column=col_num)
            if isinstance(value, datetime):
                cell.value = value.date()
            else:
                cell.value = value
    
    # Set column widths
    if colwidths is not None:
        for col_name, width in colwidths.items():
            col_idx = df.columns.get_loc(col_name) + 1
            ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
    
    # Create directory if it doesn't exist
    os.makedirs(dirpath, exist_ok=True)
    
    # Save file
    try:
        wb.save(full_path)
        return full_path
    except Exception as e:
        print(f"Error saving file: {e}")
        return None

def main():
    if not OKAY_TO_CONTINUE:
        return
    

    sf_obj = sf.SnowflakeHelper()
    conn = sf_obj.conn
    
    # Set timezone
    conn.cursor().execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
    
    # Query data
    query = f"""
    SELECT /* SNOWFLAKE version */
        LEAS.BLDGID as "Bldg",
        LEAS.LEASID as "Lease ID",
        LEAS.SUITID as "Suite",
        LEAS.OCCPNAME as "Name",
        leas.GENERATION AS "Curr Lease Gen",
        OPTNTYPE.DESCRPN AS "Type",
        CONCAT(LEASOPTS.OPTNNUM, ' of ', max_opt.MAX_OPTNUM) AS "Option",
        DATEDIFF(day, CURRENT_DATE(), leasopts.noticedate) as "Days Until Notice Due",
        TO_DATE(LEASOPTS.NOTICEDATE) AS "Notice By Date",
        TO_DATE(LEASOPTS.OPTNDATE) AS "Renewal Start Date",
        LEASOPTS.SQFT,
        LEASOPTS.TERM as "Term",
        LEASOPTS.RATE as "Rate",
        LEASOPTS.RATEDESC AS "Desc",
        LEASOPTS.NOTES as "Option Notes",
        LEAS.CONTNAME as "Contact Name",
        LEAS.PHONENO1 as "Contact Ph 1",
        LEAS.PHONENO2 AS "Contact Ph 2"
    from {csm_db}.MRI.LEAS
    LEFT JOIN {csm_db}.MRI.SUIT
        ON LEAS.BLDGID = SUIT.BLDGID
        AND LEAS.SUITID = SUIT.SUITID
    LEFT JOIN {csm_db}.MRI.LEASOPTS
    ON LEAS.BLDGID = LEASOPTS.BLDGID AND LEAS.LEASID = LEASOPTS.LEASID
    LEFT JOIN
    (
        select
          LEASOPTS.BLDGID,
        LEASOPTS.LEASID,
        max(LEASOPTS.OPTNNUM) as MAX_OPTNUM
        from {csm_db}.MRI.LEASOPTS
        group by LEASOPTS.BLDGID,
        LEASOPTS.LEASID
    ) max_opt
    on LEAS.BLDGID = max_opt.BLDGID AND LEAS.LEASID = max_opt.LEASID
    LEFT JOIN {csm_db}.MRI.OPTNTYPE
    ON OPTNTYPE.OPTNTYPE = LEASOPTS.OPTNTYPE
    LEFT JOIN
    (
        select
          leas.BLDGID,
        leas.SUITID,
        leas.LEASID,
        leas.MOCCPID,
        leas.EXPIR,
        leas.GENCODE,
        leas.GENERATION,
        leas.OCCPSTAT
        from {csm_db}.MRI.leas
        LEFT JOIN {csm_db}.MRI.SUIT
        ON LEAS.BLDGID = SUIT.BLDGID
        AND LEAS.SUITID = SUIT.SUITID
        where 
        (
            (
                UPPER(LEAS.OCCPNAME) LIKE '%MARCO%' 
                AND 
                (LEAS.COMPANYGRPID = 8 OR SUIT.BLDGID LIKE 'RNT%')
            )
            OR 
            UPPER(LEAS.TENTID) = 'MARCO'
        )
        and leas.OCCPSTAT <> 'I'
        and try_cast(leas.BLDGID AS INT) is null
    ) exist
    on LEAS.BLDGID = exist.BLDGID
    AND LEAS.SUITID = exist.SUITID
    AND LEAS.MOCCPID = exist.MOCCPID
    AND LEAS.GENERATION < exist.GENERATION
    where 
    (
        (
            UPPER(LEAS.OCCPNAME) LIKE '%MARCO%' 
            AND 
            (LEAS.COMPANYGRPID = 8 OR SUIT.BLDGID LIKE 'RNT%')
        )
        OR 
        UPPER(LEAS.TENTID) = 'MARCO'
    )
    and leas.OCCPSTAT = 'C'
    and try_cast(leas.BLDGID AS INT) is null
    AND DATEDIFF(day, CURRENT_DATE(), leasopts.noticedate) in ({NOTICE_DAYS})
    -- AND DATEDIFF(day, CURRENT_DATE(), leasopts.noticedate) < 300 -- test
    and exist.GENERATION is NULL
    order by "Days Until Notice Due"
    """
    
    df = pd.read_sql(query, conn)

    # print(f"df: {df}\n\nquery: {query}\n\n")
    
    # Check if we have data
    status, nrows, error_status = check_dataframe_rows(df, 1, REPORT_NAME)
    
    if status:
        # Define column widths
        colwidths = {
            "Bldg": 8.5,
            "Lease ID": 8.5,
            "Suite": 8,
            "Name": min(75, max(df["Name"].str.len().max(), 30)),
            "Curr Lease Gen": 7.5,
            "Type": 16,
            "Option": 9,
            "Days Until Notice Due": 9.25,
            "Notice By Date": 11,
            "Renewal Start Date": 11,
            "SQFT": 7,
            "Term": 7,
            "Rate": 8.5,
            "Desc": 8.5,
            "Option Notes": min(110, max(df["Option Notes"].str.len().max(), 39)),
            "Contact Name": min(60, max(df["Contact Name"].str.len().max(), 25)),
            "Contact Ph 1": 12,
            "Contact Ph 2": 12
        }
        
        # Write Excel file
        excel_path = write_excel(
            dirpath=SCRIPT_FOLDER,
            filename=REPORT_FILENAME,
            sheet_name=QUERY_DATE,
            df=df,
            colwidths=colwidths
        )
        
        if excel_path:
            # Prepare email body
            email_cols = ["Suite", "Name", "Days Until Notice Due", "Notice By Date", "Renewal Start Date"]
            email_df = df[email_cols].drop_duplicates()
            
            if len(email_df) < 31:
                # Create HTML table
                html_table = email_df.to_html(
                    index=False,
                    border=2,
                    classes='dataframe',
                    escape=False
                )
                body = f"""
                <p>The info below contains MRI data (from yesterday) that indicates the following leases have an upcoming renewal notice date. <b>See attached Excel file for full details.</b></p>
                <p>{html_table}</p>
                """
            else:
                body = f"""
                <p><strong><em>There are {len(email_df)} results, see attached file for all.</em></strong></p>
                """
            
            # Add report criteria and signature
            body += f"""
            {REPORT_CRITERIA}
            <br/><br/>
            <b>Steve Olson</b><br/>
            Sr. Analytics Mgr.<br/>
            <b>Highland Ventures, Ltd.</b><br/>
            2500 Lehigh Ave.<br/>
            Glenview, IL 60026<br/>
            Ph: 847/904-9043<br/>
            """
            
            # Send email:  send_email(recipient, subject, body, attachments =[], test=None, test_recipient= None):
            email_client.send_email(
                recipient=NORMAL_RECIPIENTS,
                subject=REPORT_NAME,
                body=body,
                attachments=[excel_path],
                test=TESTING_EMAILS,
                test_recipient=TEST_RECIPIENTS,
                replyto=GMAIL_REPLY_TO
            )            
    
    # Close Snowflake connection
    conn.close()

if __name__ == "__main__":
    main() 