#--------------------------------------------------------------------------------------
# Rev. No     Date      Author   Description
#---------------------------------------------------------------------------------------
#  1.0    6/7/25   Jorge <PERSON>   Converted to MS 365
#---------------------------------------------------------------------------------------

# 📊 Legacy_Property Mgmt PORTFOLIO - prod backup.xlsx
#      Location: Documents (root)
#      ID: 0173OHFF3BKQWGGDWDERCKXPNWQ3WPJWPQ
#      Size: 9,233,941 bytes
#      URL: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7B632C5461-C30E-4424-ABBD-B686ECF4D9F0%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO%20-%20prod%20backup.xlsx&action=default&mobileredirect=true&isSPOFile=1&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDUxODAwMjE5IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9
 
#   📊 Legacy_Property Mgmt PORTFOLIO - prod test.xlsx
#      Location: Documents (root)
#      ID: 0173OHFFZLRATGVDI2Q5B2CO3EFFLBNULD
#      Size: 9,233,941 bytes
#      URL: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7B6A26882B-1A8D-4387-A13B-64295616D163%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO%20-%20prod%20test.xlsx&action=default&mobileredirect=true&isSPOFile=1&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDUxODAwMjE5IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9

#   📊 (old) Legacy_Property Mgmt PORTFOLIO.xlsx
#      Location: Documents (root)
#      ID: 0173OHFFZFDLP2FLYPO5GJ236ZXA3P27L4
#      URL: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7BA2DF1A25-0FAF-4C77-9D6F-D9B836FD7D7C%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO.xlsx&action=default&mobileredirect=true&isSPOFile=1&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDUxODAwMjE5IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9
# 
#   📊 (new) Legacy_Property Mgmt PORTFOLIO.xlsx
#      Location: Documents (root)
#      ID: 0173OHFF6ONFD236PSNFHLLLLRRFHJR4MQ
#      Size: 11,801,832 bytes
#      URL: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7BAD4769CE-F2F9-4E69-B5AD-71894E98F190%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO.xlsx&action=default&mobileredirect=true

# site_url: https://highlandventuresltd442.sharepoint.com/sites/legacypro

import os
import pandas as pd
import datetime
import time
import uuid

from libs.snowflake_helper import SnowflakeHelper
from libs.excel_helper import SharePointExcelOnline
from helpers.legacy_property_portfolio_sheet_helper import PortfolioSheetHelper
from helpers.legacy_property_rent_roll_sheet_helper import RentRollSheetHelper, Config
from helpers.legacy_property_availability_sheet_helper import AvailabilitySheetHelper
from helpers.legacy_property_snc_sheet_helper import SNCSheetHelper

class DataProcessor:

    LOG_TO_DB=True
    DEFAULT_SLEEP_SECS = 5

    def __init__(self):
        
        self.sf = SnowflakeHelper()
        # self.sf.LOG_TO_DB = False # @todo: remove or comment after testing
        self.excel_helper = SharePointExcelOnline()

        self.uuid_str = uuid.uuid4()
        self.dir_path = os.path.dirname(os.path.realpath(__file__))

        
        self.sql_file_path = os.path.join(self.dir_path, "Data_transformation", "sql_files")

        self.conn = self.sf.conn
        self.cs = self.conn.cursor()
        self.envm = os.environ["DATABASE_ENVM"]
        self.raw_database = os.environ["DATABASE_RAW_DATABASE"]
        self.csm_database = os.environ["DATABASE_CSM_DATABASE"]

        self.script_name = __file__
        self.process_type = 'Legacy Portfolio Management'       


        # self.file_id = '01NHAQJOGEO3WCSHXZ3FAYUTOTUPHJNWGT' # test book by Julian: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/dev/_layouts/15/Doc.aspx?sourcedoc=%7B29EC76C4-F91E-41D9-8A4D-D3A3CE96D8D3%7D&file=Book.xlsx&action=default&mobileredirect=true
        # file_id = '013USHB7VZ6D5NXGNXOBALFMOSTWOVBLHW' # store list    : https://highlandventuresltd442.sharepoint.com/:x:/r/_layouts/15/guestaccess.aspx?e=idNgBP&share=EdGLYvvK0jRHiQB72ffw90QB8qMNvo328bkA0NWjQoVEgw&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1749202116661&web=1
        # file_id = '0173OHFF5QHUWO2M6EC5FK65JBIGNL2MX6' # legacy property management test    : https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/doc2.aspx?sourcedoc=%7Bed2c3db0-c433-4a17-af75-21419abd32fe%7D&action=view&activeCell=%27Portfolio_Gen%27!C2&wdinitialsession=07b2530b-285f-6495-e4b0-b1529cc78765&wdrldsc=2&wdrldc=1&wdrldr=AccessTokenExpiredWarningUnauthenticated%2CRefreshin
            # generates error: Error: {"error":{"code":"FileCorruptTryRepair","message":"We're sorry, but something went wrong with this file.","innerError":{"code":"unsupportedWorkbook","message":"The request failed. The workbook contains unsupported features or exceeds the size limit.","innerError":{"code":"FileCorruptTryRepair","message":"We're sorry, but something went wrong with this file."},"date":"2025-06-07T07:50:52","request-id":"99fe721a-c258-49a7-b35b-3dbbdc3c4b7c","client-request-id":"99fe721a-c258-49a7-b35b-3dbbdc3c4b7c"}}}
        # self.file_id = '01NHAQJODNA4CA3IH67JCYXR7ZE2HDCJSP' # property management dev test: https://highlandventuresltd442.sharepoint.com/:x:/s/dev/EW0HBA2g_vpFi8f5Jo4xJk8BJOCSYKMrkQybWjIizlThbQ?email=jgarifuna%40hv.ltd&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1749529969115&web=1
        # self.file_id = '0173OHFFZLRATGVDI2Q5B2CO3EFFLBNULD' # prod test : https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7B6A26882B-1A8D-4387-A13B-64295616D163%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO%20-%20prod%20test.xlsx&action=default&mobileredirect=true&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1749805387562&web=1
        self.file_id = '0173OHFF6ONFD236PSNFHLLLLRRFHJR4MQ' # prod  : https://highlandventuresltd442.sharepoint.com/:x:/r/sites/legacypro/_layouts/15/Doc.aspx?sourcedoc=%7BA2DF1A25-0FAF-4C77-9D6F-D9B836FD7D7C%7D&file=Legacy_Property%20Mgmt%20PORTFOLIO.xlsx&action=default&mobileredirect=true&isSPOFile=1&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDUxODAwMjE5IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9

        # self.site_url = 'https://highlandventuresltd442.sharepoint.com/sites/dev'
        # self.site_url = 'https://highlandventuresltd442.sharepoint.com/sites/hrg'
        self.site_url = 'https://highlandventuresltd442.sharepoint.com/sites/legacypro'
        
        self.log_audit_in_db(log_msg=f"Start: Getting excel session for file ID {self.file_id} from {self.site_url}")

        # Get Excel session using file ID
        self.excel_session = self.excel_helper.get_excel_file_by_id(self.site_url, self.file_id)

        self.log_audit_in_db(log_msg=f"Obtained excel session for {self.file_id} from {self.site_url}: {self.excel_session}")       

        self.portfolio_sheet_helper = PortfolioSheetHelper(self.sf, self.csm_database)
        self.rent_roll_sheet_helper = RentRollSheetHelper(Config(self.sf), self.sf, self.csm_database)
        self.availability_sheet_helper = AvailabilitySheetHelper(self.sf, self.excel_helper, self.excel_session)
        self.snc_sheet_helper = SNCSheetHelper(self.sf, self.excel_helper, self.excel_session, self.DEFAULT_SLEEP_SECS)

   
    
    def log_audit_in_db(self, log_msg, log_type='Info', print_msg=True):
        
        # only log to db if indicated
        if not self.LOG_TO_DB:
            return 
        
        uuid_str = str(uuid.uuid4())
        script_name = os.path.basename(self.script_name)
        rec_ins_date = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        data_list = []
        record = [uuid_str, script_name, log_type, self.process_type, log_msg, rec_ins_date]
        columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
        data_list.append(record)
        # print(f"data_list: {data_list}")

        self.sf.bulk_insert(columns_list=columns_list, data_list=data_list,database=self.raw_database, schema=self.sf.batch_audit_schema, table=self.sf.moms_log_tbl)    

        if print_msg:
            print(log_msg)

    def execute_sql_scripts(self):

        # portfolio_data = self.portfolio_sheet_helper.get_portfolio_data()
        # print(f"portfolio_data: {portfolio_data}")  
        # exit(1)

        # rent_roll_data = self.rent_roll_sheet_helper.get_rent_roll_data()
        # print(f"rent_roll_data: {rent_roll_data}")  
        # exit(1)



        for file in ["SNC","Availability","RENT ROLL","PORTFOLIO"]:
        # for file in ["RENT ROLL"]:

            print(f"\n\n")
            log_type='Info'
            self.log_audit_in_db(log_msg=f"Processing file: {file}")
            if file == "PORTFOLIO":
                sheet_name = file
                # df = pd.DataFrame(data=result, columns=['ST' , 'Store Name' , 'Address' , 'City' , 'State' , 'Zip' , 'Phone' ,'Store Email' ,'Street Corners' ,'Open Date' ,'Company' ,'Rent' ,'Lease' ,'RM' ,'RM Email' ,'RM Report Name' ,'DM' ,'DM Email' ,'DM Report Name' ,'Manager' ,'MIT'], dtype=str)
                df = self.portfolio_sheet_helper.get_portfolio_data()
                # df = df.fillna('')
                # print(f"df.dtypes: {df.dtypes}")
                # df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/portfolio.csv', index=False)
                # print(f"df: {df}")
                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'Z'  # Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_content(excel_session=self.excel_session, worksheet_name=sheet_name, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                # replace none with empty string
                clean_values = self.excel_helper.clean_values_from_none_to_empty_string(df.values.tolist())

                populated = self.excel_helper.append_data_to_worksheet(
                    # self.excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                    self.excel_session, sheet_name, [df.columns.values.tolist()] + clean_values, reset_from_first_row=True
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                if not populated:
                    log_type='Error'
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}", log_type=log_type)
                # exit(1)
            elif file == "RENT ROLL":
                sheet_name = file
                # df = pd.DataFrame(data=result, columns=['ST' , 'Store Name' , 'Address' , 'City' , 'State' , 'Zip' , 'Phone' ,'Store Email' ,'Street Corners' ,'Open Date' ,'Company' ,'Rent' ,'Lease' ,'RM' ,'RM Email' ,'RM Report Name' ,'DM' ,'DM Email' ,'DM Report Name' ,'Manager' ,'MIT'], dtype=str)
                df = self.rent_roll_sheet_helper.get_rent_roll_data()
                self.rent_roll_sheet_helper.compare_data_with_prior_week(df)
                
                # print(f"df.dtypes: {df.dtypes}")
                # print(f"df: {df}")
                # df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/rent_roll.csv', index=False)
                
                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'AD'   # AD Assuming we want to clear up to column E
                # last_data_column = None   # AD Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_content(excel_session=self.excel_session, worksheet_name=sheet_name, sleep_time_secs=self.DEFAULT_SLEEP_SECS, forced_end_column=last_data_column)

                # replace none with empty string
                clean_values = self.excel_helper.clean_values_from_none_to_empty_string(df.values.tolist())
                # print(f"clean_values: {clean_values}\n\n") # df: {df}
                # exit(1)
                populated = self.excel_helper.append_data_to_worksheet(
                    # self.excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                    self.excel_session, sheet_name, [df.columns.values.tolist()] + clean_values, reset_from_first_row=True, forced_end_column=last_data_column
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                # self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                if not populated:
                    log_type='Error'
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}", log_type=log_type)
                # exit(1)   
                
            elif file == "Availability":
                sheet_name = file

                # get existing availability data from sheet
                existing_availability_data_from_sheet = self.excel_helper.get_worksheet_data(self.excel_session, sheet_name)
                values_existing_availability_data_from_sheet_list = existing_availability_data_from_sheet.get('values')
                existing_availability_headers = values_existing_availability_data_from_sheet_list[0] 
                existing_availability_data = values_existing_availability_data_from_sheet_list[1:]
                existing_availability_data_df = pd.DataFrame(data=existing_availability_data, columns=existing_availability_headers)
                # existing_availability_data_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/existing_availability_data_df.csv', index=False)
                
                # df = pd.DataFrame(data=result, columns=['ST' , 'Store Name' , 'Address' , 'City' , 'State' , 'Zip' , 'Phone' ,'Store Email' ,'Street Corners' ,'Open Date' ,'Company' ,'Rent' ,'Lease' ,'RM' ,'RM Email' ,'RM Report Name' ,'DM' ,'DM Email' ,'DM Report Name' ,'Manager' ,'MIT'], dtype=str)
                avialability_df, avialability_prior_df = self.availability_sheet_helper.get_availability_sheet_data()

                # avialability_df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/avialability_df.csv', index=False)
                # exit(1)
                
                # email if changes are found
                # self.availability_sheet_helper.compare_and_notify_changes(avialability_df, existing_availability_data_df)                

                # print(f"values_existing_availability_data_from_sheet({type(existing_availability_data_from_sheet)}): {values_existing_availability_data_from_sheet_list}\n\nexisting_availability_data_df:\n{existing_availability_data_df}")
                # exit(1)

                # proccess avialability_df
                df = avialability_df
                # print(f"df.dtypes: {df.dtypes}")
                # print(f"1. avialability_df: {avialability_df}")
                # print(f"2. avialability_prior_df: {avialability_prior_df}")
                # exit(1)
                # df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/Availability.csv', index=False)
                
                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'Y'   # AD Assuming we want to clear up to column E
                # last_data_column = None   # AD Assuming we want to clear up to column E
                
                # print(f"0) sheet_name: {sheet_name}")
                self.excel_helper.clear_excel_sheet_content(excel_session=self.excel_session, worksheet_name=sheet_name, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                # print(f"1) sheet_name: {sheet_name}")

                # replace none with empty string
                clean_values = self.excel_helper.clean_values_from_none_to_empty_string(df.values.tolist())
                # clean_values = df.values.tolist()
                # print(f"clean_values: {clean_values}\n\ndf: {df}")
                # exit(1)
                populated = self.excel_helper.append_data_to_worksheet(
                    # self.excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                    self.excel_session, sheet_name, [df.columns.values.tolist()] + clean_values, reset_from_first_row=True, forced_end_column=last_data_column
                )

                # print(f"2) sheet_name: {sheet_name}")

                # print(f"done populating sheet {sheet_name}: {populated}")
                # self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                if not populated:
                    log_type='Error'
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}", log_type=log_type)

                # proccess avialability_prior_df    
                sheet_name   = 'Avail-Prior Tenant'   
                self.log_audit_in_db(log_msg=f"Start: populating sheet {sheet_name}")      
                df = avialability_prior_df

                # df.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/Store-List-Snowflake-Dataloader/output/Availability_prior.csv', index=False)

                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'AE'   # AD Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_content(excel_session=self.excel_session, worksheet_name=sheet_name, sleep_time_secs=self.DEFAULT_SLEEP_SECS, forced_end_column=last_data_column)

                # replace none with empty string
                clean_values = self.excel_helper.clean_values_from_none_to_empty_string(df.values.tolist())

                populated = self.excel_helper.append_data_to_worksheet(
                    # self.excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                    self.excel_session, sheet_name, [df.columns.values.tolist()] + clean_values, reset_from_first_row=True, forced_end_column=last_data_column
                )


                # email if changes are found
                self.availability_sheet_helper.compare_and_notify_changes(avialability_df, existing_availability_data_df)                

                # self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                log_type='Info'
                if not populated:
                    log_type='Error'
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}", log_type=log_type)

                # exit(1)        

            elif file == "SNC":
                sheet_name = file

                df = self.snc_sheet_helper.get_snc_leases_from_database()
                
                # print(f"df: {df}")
                # exit(1)
                if df is None:
                    self.log_audit_in_db(log_msg=f"No data found for SNC sheet", log_type='Warning')
                    continue
                
                df = df.fillna('')

                # get existing availability data from sheet
                existing_snc_data_from_sheet = self.excel_helper.get_worksheet_data(self.excel_session, sheet_name)
                values_existing_snc_data_from_sheet_list = existing_snc_data_from_sheet.get('values')
                existing_snc_headers = values_existing_snc_data_from_sheet_list[0] 
                existing_snc_data = values_existing_snc_data_from_sheet_list[1:]
                existing_snc_data_df = pd.DataFrame(data=existing_snc_data, columns=existing_snc_headers)
                existing_snc_data_df = existing_snc_data_df.fillna('')
                
                # print(f"existing_snc_data_df: {existing_snc_data_df}")
                # exit(1)
                # If no existing data, write all data to sheets
                if len(existing_snc_data) == 0:
                    self.log_audit_in_db(log_msg=f"No existing data found for SNC sheet", log_type='Warning')
                    

                    starting_data_row = 1
                    first_data_column = 'A'  # Assuming we want to clear from column A
                    last_data_column = 'R'   # AD Assuming we want to clear up to column E
                    # last_data_column = None   # AD Assuming we want to clear up to column E
                    
                    # print(f"0) sheet_name: {sheet_name}")
                    self.excel_helper.clear_excel_sheet_content(excel_session=self.excel_session, worksheet_name=sheet_name, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                    # print(f"1) sheet_name: {sheet_name}")

                    # replace none with empty string
                    clean_values = self.excel_helper.clean_values_from_none_to_empty_string(df.values.tolist())
                    # clean_values = df.values.tolist()
                    # print(f"clean_values: {clean_values}\n\ndf: {df}")
                    # exit(1)
                    populated = self.excel_helper.append_data_to_worksheet(
                        # self.excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                        self.excel_session, sheet_name, [df.columns.values.tolist()] + clean_values, reset_from_first_row=True, forced_end_column=last_data_column
                    )
                    # self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                    if not populated:
                        log_type='Error'
                    self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}", log_type=log_type)
                    continue


                # Process changes
                email_files, email_body_parts = self.snc_sheet_helper.process_data_changes(df, existing_snc_data_df)
                
                # Send notification email if there are changes
                if email_files or email_body_parts:
                    # print(f"1) email_body_parts: \n{email_body_parts}\n\n")
                    self.snc_sheet_helper.send_notification_email(email_files, email_body_parts)

                # print(f"2) sheet_name: {sheet_name}")

                # print(f"done populating sheet {sheet_name}: {populated}")
                



                # exit(1)                                                       

        
        self.log_audit_in_db(log_msg=f"Completed: all  excel sections for file ID {self.file_id} from {self.site_url}")


    def data_ingestion(self):
        """
        Performs the data ingestion process.
        """
        try:
            data_date = datetime.datetime.now().strftime("%Y-%m-%d")
            # self.execute_sql_scripts(self.sql_file_path, data_date)
            self.execute_sql_scripts()

        except Exception as err:
            err_msg = str(err).replace("'", "")
            self.log_audit_in_db(log_msg= f"Error encountered function - data_ingestion | {err_msg}", log_type='Error')
             
def main():

    start_time = time.time()
    start_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    


    processor = DataProcessor()

    processor.log_audit_in_db(f"Starting main() at {start_now}")

    processor.data_ingestion()

    # calculate duration
    end_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
    duration = processor.sf.get_duration(start_time)

    processor.log_audit_in_db(f"Finished main():\t[time: {end_now}] [{duration}]")

if __name__ == "__main__":

    # sets timezone to Central
    os.environ['TZ'] = 'America/Chicago'
    time.tzset()

    main()

