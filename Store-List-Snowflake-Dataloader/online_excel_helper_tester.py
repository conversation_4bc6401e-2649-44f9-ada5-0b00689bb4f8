import time
import random
from datetime import datetime

from libs.excel_helper import SharePointExcelOnline
from libs.excel_helper import debug_sharepoint_structure

def process_excel_sheet_cells(site_url:str, file_id: str, worksheet_name: str, range_address: str) -> bool:
    """
    Process a specific range in a worksheet (e.g., clear and then populate)
    
    Args:
        site_url: SharePoint site URL
        file_id: Excel file ID
        worksheet_name: Name of the worksheet
        range_address: Range to process (e.g., "A1:C10")
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    excel_helper = SharePointExcelOnline()

    # Get Excel session using file ID
    excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)

    starting_data_row = 2
    first_data_column = 'A'  # Assuming we want to clear from column A
    last_data_column = 'E'  # Assuming we want to clear up to column E
    clearing_data_row_start = starting_data_row + 1  # Start clearing from row 3 (A3)

    # First, get the used range to find where to append
    existing_row_count = excel_helper.get_excel_sheet_row_count(excel_session, worksheet_name)

    if existing_row_count > starting_data_row: 
        _range_address = f"{first_data_column}{clearing_data_row_start}:{last_data_column}{existing_row_count}" # e.g., "A3:D120"
        print(f"Clearing existing range: {_range_address} in worksheet '{worksheet_name}'")
        # Clear the range first
        cleared = excel_helper.clear_excel_sheet_cells(excel_session, worksheet_name, _range_address)
    
        if not cleared:
            print("Failed to clear the range")
            return False
        sleep_secs = 5
        print(f"Existing range cleared successfully. Sleeeping for {sleep_secs} seconds before populating new data...")
        time.sleep(sleep_secs)
    
    # Now populate with new data
    # new_values = [
    #     ["New Item 1", "Category A", 100],
    #     ["New Item 2", "Category B", 200],
    #     ["New Item 3", "Category C", 300]
    # ]

    new_values = []
    _counter = 100
    for i in range(10):
        start_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')    
        _counter += 1
        id = random.randint(1, 1000)
        new_values.append([f"Python Item {i}", f"Category {i}", _counter, start_now, id])

    
    populated = excel_helper.append_data_to_worksheet(
        excel_session, worksheet_name, new_values
    )
    # populated = populate_excel_sheet_cells(site_url, file_id, worksheet_name, range_address, new_values)
    print("Done populating Excel sheet")
    return populated

def clear_excel_sheet_content(site_url:str, file_id: str, worksheet_name: str):
    
    excel_helper = SharePointExcelOnline()

    # Get Excel session using file ID
    excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)
        
    # Get current data from the worksheet
    print("Getting current data...")
    current_data = excel_helper.get_worksheet_data(excel_session, worksheet_name)
    
    if current_data:
        values = current_data.get('values', [])
        print(f"Current worksheet has {len(values)} rows")
        
        # Print first few rows as sample
        if values:

            print("Sample data (first 3 rows):")
            headers = []
            for i, row in enumerate(values):
                if i == 0: # skip header
                    headers = row
                    continue
                # set every value to nothing to clear
                for v in range(len(row)):
                    values[i][v] = ''

                # print(f"  Row {i+1}: {row}")
                print(f"  Row {i}: {values[i]}")

            start_col = 'A'
            start_row = 1
            end_col = chr(ord('A') + len(headers) - 1) if headers else 'A'
            end_row = len(values)

            
            range_address = f"{start_col}{start_row}:{end_col}{end_row}" # append_data_to_worksheet() - Appending data to worksheet 'Sheet2' at range A74:D83

            excel_helper.update_worksheet_range(excel_session, worksheet_name, range_address, values)
            time.sleep(5)

            print(f"range_address: {range_address}\n\n")

# def debug_sharepoint_structure(site_url:str):
#     """
#     Debug the SharePoint structure
#     """
#     excel_helper = SharePointExcelOnline()
#     excel_helper.debug_sharepoint_structure(site_url)
        

if __name__ == "__main__":
    # Test the function with a sample file

    start_time = time.time()
    start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    

    print(f"\n\nStarting: main()\t[time: {start_now}]")

    potential_id = '01NHAQJOGEO3WCSHXZ3FAYUTOTUPHJNWGT' # test book by Julian: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/dev/_layouts/15/Doc.aspx?sourcedoc=%7B29EC76C4-F91E-41D9-8A4D-D3A3CE96D8D3%7D&file=Book.xlsx&action=default&mobileredirect=true
    # potential_id = '013USHB7VZ6D5NXGNXOBALFMOSTWOVBLHW' # store list    : https://highlandventuresltd442.sharepoint.com/:x:/r/_layouts/15/guestaccess.aspx?e=idNgBP&share=EdGLYvvK0jRHiQB72ffw90QB8qMNvo328bkA0NWjQoVEgw&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1749202116661&web=1

    site_url = 'https://highlandventuresltd442.sharepoint.com/sites/dev'
    # site_url = 'https://highlandventuresltd442.sharepoint.com/sites/hrg'


    #example_excel_online_operations()
    # debug_sharepoint_structure(site_url=site_url)
    # work_with_book_excel()
    # work_with_book_excel_by_id(file_id=potential_id, site_url=site_url)

    # clear_excel_sheet_cells(
    #     site_url=site_url, 
    #     file_id=potential_id, 
    #     worksheet_name='Sheet2', 
    #     range_address='A3:D120'
    # )

    # process_excel_sheet_cells(
    #     site_url=site_url, 
    #     file_id=potential_id, 
    #     worksheet_name='Sheet2', 
    #     range_address='A3:D120'
    # )

    # clear_excel_sheet_content(site_url=site_url, 
    #     file_id=potential_id, 
    #     worksheet_name='Hours')
    

    site_url = 'https://highlandventuresltd442.sharepoint.com/sites/legacypro'
    site_url = 'https://highlandventuresltd442.sharepoint.com/sites/dev'
    site_url = 'https://highlandventuresltd442.sharepoint.com/sites/hrg'
    debug_sharepoint_structure(site_url=site_url)

    start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    

    print(f"\nDone: main()\t[time: {start_now}] {__file__}\n")
