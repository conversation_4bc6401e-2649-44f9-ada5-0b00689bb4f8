"""
Process Name: Marcos OTU Data Loader
R-Script Name: MARCOS_OTU_Codes_to_DB.R
This script is designed to load OTU codes from a SharePoint file, process them, and upload them to a Snowflake database.
Replaces the previous Marcos OTU Loader script that worked with google drive instead of SharePoint.
- <PERSON> - 6/12/2025
"""

from libs.snowflake_helper import SnowflakeHelper
from libs.excel_helper import SharePointExcelOnline, debug_sharepoint_structure
from libs.sharepoint_helper import SharePointClient
import time
import uuid
import os
import pandas as pd
import calendar
import datetime
from libs.jgutils import split_list, save_file, read_file, create_directories
import numpy as np

class MarcosOtuLoader():
    # SCRIPT_NAME = 'Marcos-Otu-Loader'
    PROCESS_TYPE = 'Data Processing'

    ENVM = os.environ['DATABASE_ENVM']
    SCRIPT_NAME = os.path.basename(__file__)
    
    PRINT_STATEMENTS = True
    LOCAL_DEV = False
    AUTO_COMMIT = True
    TEST_MODE = False

    DRY_RUN = False if TEST_MODE else False
    MOVE_FILES = False if TEST_MODE else True
    DOWNLOAD_FILES = True if TEST_MODE else True
    PROCESS_FILES = True if TEST_MODE else True
    LOG_TO_DB = True if TEST_MODE else True
    CLEAN_FILES = True if TEST_MODE else True

    LOG_SCHEMA = 'BATCH_AUDIT'
    LOG_TABLE = 'MOMS_EXECUTION_LOGS'
    CSM_DATABASE = os.environ['DATABASE_CSM_DATABASE']
    OTU_TABLE = 'MP_OTU_CODES'
    OTU_SCHEMA = 'CORPORATE'

    def __init__(self):
        self.site_url = 'https://highlandventuresltd442.sharepoint.com/sites/marketingandanalytics'
        # self.file_path = 'Dormancy_25P06 50% OFF PIZZA Tier Decrease Program.xlsx'

        self.folder_path = 'Marketing/Hoogland Restaurant Group/OTU Loader'
        self.data_directory = os.environ['SCRIPTS_BASE_DATA_DIR']
        self.processed_codes_path = self.folder_path + '/Processed Codes'
        #self.local_data_directory = os.path.join(os.path.dirname(__file__), 'data/')
        self.local_data_directory = self.data_directory

        self.sf = SnowflakeHelper()
        self.sp = SharePointClient()
        self.sp.authenticate()
        self.ex = SharePointExcelOnline(sharepoint_client=self.sp)

        self.cursor = self.sf.cs
        self.conn = self.sf.conn
        self.log_buffer = []
        self.log_audit_in_db = self.sf.log_audit_in_db
        self.continue_processing = True

    def run(self):
        total_start_time = time.time()
        self.log_audit_in_db(log_msg=f'Starting process', process_type=self.PROCESS_TYPE, print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME)
        self.ready_local_data_directory()

        if self.DOWNLOAD_FILES:

            # Retrieve File
            raw_otu_codes_df = self.retrieve_otu_codes()
            if raw_otu_codes_df is None:
                self.log_audit_in_db(log_msg="No data retrieved from SharePoint, skipping process", log_type='Info', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
                self.continue_processing = False

        if self.continue_processing:
            if self.DOWNLOAD_FILES and self.PROCESS_FILES:
                processed_otu_codes_df = self.process_otu_codes(raw_otu_codes_df=raw_otu_codes_df)

                # Upload File into Snowflake
                upload_status = self.upload_otu_codes(processed_otu_codes_df=processed_otu_codes_df)

                if not upload_status:
                    self.log_audit_in_db(log_msg="Upload failed, exiting process", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
                else:
                    self.log_audit_in_db(log_msg="Upload successful", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)

            # Move File from base to processed within Sharepoint
            if self.MOVE_FILES:
                move_status = self.move_processed_otu_codes()

                if not move_status:
                    self.log_audit_in_db(log_msg="Failed to move processed file", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
                else:
                    self.log_audit_in_db(log_msg="Successfully moved processed file", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)

            if self.CLEAN_FILES:
                clean = self.clean_up_files()

                if clean:
                    self.log_audit_in_db(log_msg="Clean up completed successfully", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
                else:
                    self.log_audit_in_db(log_msg="Clean up failed", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)

        total_duration = self.sf.get_duration(start_time=total_start_time)
        self.log_audit_in_db(log_msg=f'Process Ended with a duration of {total_duration} minutes', process_type=self.PROCESS_TYPE, print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME)

    def retrieve_otu_codes(self):
        """
        Find and download the first file in the specified folder
        """
        self.sp.authenticate()
        try:

            # Get the first file in the folder
            first_file = self.sp.get_first_file_from_folder(
                site_url=self.site_url, 
                folder_path=self.folder_path
            )
            
            if not first_file:
                self.log_audit_in_db(log_msg=f"No files found to process", script_file_name=self.SCRIPT_NAME, log_type='Info', process_type=self.PROCESS_TYPE)
                return None
            
            file_name = first_file.get('name')
            local_file_path = os.path.join(self.local_data_directory, file_name)
            
            # Download the file
            success = self.sp.download_file(
                site_url=self.site_url,
                file_path=file_name,
                local_path=local_file_path,
                folder_name=self.folder_path,
                file_info=first_file
            )
            
            if success:
                self.log_audit_in_db(log_msg=f"Successfully downloaded: {local_file_path}", script_file_name=self.SCRIPT_NAME, print_msg=self.PRINT_STATEMENTS, process_type=self.PROCESS_TYPE)
                # Read the file into a DataFrame
                if file_name.endswith('.csv'):
                    raw_otu_codes_df = pd.read_csv(local_file_path, header=None)
                if file_name.endswith('.xlsx') or file_name.endswith('.xls'):
                    raw_otu_codes_df = pd.read_excel(local_file_path, header=None)
                # Remove headers if they exist
                if raw_otu_codes_df.empty:
                    self.log_audit_in_db(log_msg=f"DataFrame is empty after reading the file.")
                    return None
                raw_otu_codes_df = raw_otu_codes_df.values

                if self.PRINT_STATEMENTS:
                    self.log_audit_in_db(log_msg=f"DataFrame loaded with {len(raw_otu_codes_df)} rows", print_msg=True, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)

                return raw_otu_codes_df

            else:
                self.log_audit_in_db(log_msg=f"Download failed during method retrieve_otu_codes", log_type='Info', script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
                return None
        except Exception as e:
            self.log_audit_in_db(log_msg=f"Error retrieving OTU codes during method retrieve_otu_codes: {e}", log_type='Info', script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
            return None

    # Processes file, applying any transformation
    def process_otu_codes(self, raw_otu_codes_df=None):
        """
        Processes the raw OTU codes DataFrame, applying necessary transformations
        """
        try:
            if raw_otu_codes_df is None:
                self.log_audit_in_db(log_msg=f"No raw data provided for processing")
                return None
            
            # Convert numpy array back to DataFrame if needed
            if isinstance(raw_otu_codes_df, np.ndarray):
                # Assuming first column contains the codes
                df = pd.DataFrame(raw_otu_codes_df[:, 0], columns=['CODE_RAW'])
            else:
                df = raw_otu_codes_df.copy()
                # Take only the first column and rename it
                df = pd.DataFrame(df.iloc[:, 0], columns=['CODE_RAW'])
            
            # 2. Data Filtering - Remove header rows and unwanted text
            unwanted_values = ['RECEIVED:', 'Coupon Number', 'RECEIVED: ', 'Coupon Number']
            df = df[~df['CODE_RAW'].isin(unwanted_values)]
            
            # Remove any NaN or empty values
            df = df.dropna(subset=['CODE_RAW'])
            df = df[df['CODE_RAW'].str.strip() != '']
            
            # 1. Channel Extraction - Extract channel from filename using underscore delimiter
            # Get the filename from the downloaded file
            first_file = self.sp.get_first_file_from_folder(
                site_url=self.site_url, 
                folder_path=self.folder_path
            )
            
            if first_file:
                filename = first_file.get('name', '')
                # Extract channel (first word before underscore)
                if '_' in filename:
                    channel = filename.split('_')[0].upper()
                else:
                    # Fallback: use filename without extension as channel
                    channel = filename.split('.')[0].upper()
            else:
                channel = 'UNKNOWN'
            
            df['CHANNEL'] = channel
            
            # 3. Code Standardization - Convert to "MOMS reporting style"
            df['CODE_REPORTING'] = df['CODE_RAW'].apply(
                lambda x: f"suc_{str(x).lower().replace('-', '')}" if pd.notna(x) else None
            )
            
            # 4. Remove duplicates
            df = df.drop_duplicates()
            
            # Log the processing results
            if self.PRINT_STATEMENTS:
                self.log_audit_in_db(
                    log_msg=f"Processed {len(df)} unique codes for channel '{channel}'", 
                    print_msg=True,
                    script_file_name=self.SCRIPT_NAME, 
                    process_type=self.PROCESS_TYPE
                )
                self.log_audit_in_db(
                    log_msg=f"Sample processed data: {df.head().to_dict('records')}", 
                    print_msg=True,
                    script_file_name=self.SCRIPT_NAME,
                    process_type=self.PROCESS_TYPE
                )

            return df
        except Exception as e:
            self.log_audit_in_db(log_msg=f"Error processing OTU codes during method process_otu_codes: {e}", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
            return None
        

    # # Uploads processed file to Snowflake Table
    # def upload_otu_codes(self, processed_otu_codes_df=None):
    #     """
    #     Uploads the processed OTU codes DataFrame to the Snowflake table
    #     """

    #     if processed_otu_codes_df is None or processed_otu_codes_df.empty:
    #         print("No processed data to upload")
    #         self.log_audit_in_db(log_msg=f"No processed data to upload", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
    #         return False
    #     try:
    #         #upload the DataFrame to Snowflake
    #         if self.DRY_RUN:
    #             print("Dry run mode: Not uploading to Snowflake")
    #             return True
    #         if self.LOG_TO_DB:
    #             self.log_audit_in_db(log_msg="Uploading processed OTU codes to Snowflake", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
    #         self.sf.bulk_insert(
    #             columns_list=['CODE_RAW', 'CHANNEL', 'CODE_REPORTING'], 
    #             data_list=processed_otu_codes_df.values.tolist(), 
    #             database=self.CSM_DATABASE, 
    #             schema=self.OTU_SCHEMA, 
    #             table=self.OTU_TABLE
    #         )
            
    #         self.log_audit_in_db(log_msg=f"Successfully uploaded {len(processed_otu_codes_df)} records to Snowflake", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
    #         return True
    #     except Exception as e:
    #         self.log_audit_in_db(log_msg=f"Error uploading OTU codes to Snowflake during method upload_otu_codes: {e}", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
    #         return False

    def upload_otu_codes(self, processed_otu_codes_df=None):
        """
        Uploads the processed OTU codes DataFrame to the Snowflake table using MERGE with VALUES clause
        """
        if processed_otu_codes_df is None:
            self.log_audit_in_db(log_msg=f"No processed data to upload", log_type='Info', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
            return False
        
        try:
            if self.DRY_RUN:
                print("Dry run mode: Not uploading to Snowflake")
                return True
                
            if self.LOG_TO_DB:
                self.log_audit_in_db(log_msg="Uploading processed OTU codes to Snowflake using MERGE", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
            
            # Create VALUES clause from DataFrame
            values_list = []
            for _, row in processed_otu_codes_df.iterrows():
                values_list.append(f"('{row['CODE_RAW']}', '{row['CHANNEL']}', '{row['CODE_REPORTING']}')")
            
            values_clause = ", ".join(values_list)
            
            # Execute MERGE statement with VALUES
            merge_query = f"""
            MERGE INTO {self.CSM_DATABASE}.{self.OTU_SCHEMA}.{self.OTU_TABLE} AS target
            USING (VALUES {values_clause}) AS source(CODE_RAW, CHANNEL, CODE_REPORTING)
            ON target.CODE_RAW = source.CODE_RAW AND target.CHANNEL = source.CHANNEL
            WHEN MATCHED THEN
                UPDATE SET CODE_REPORTING = source.CODE_REPORTING
            WHEN NOT MATCHED THEN
                INSERT (CODE_RAW, CHANNEL, CODE_REPORTING)
                VALUES (source.CODE_RAW, source.CHANNEL, source.CODE_REPORTING)
            """
            
            result = self.sf.execute_snowflake_query(merge_query)
            print(f"Merge result: {result}")
            
            self.log_audit_in_db(log_msg=f"Successfully merged {len(processed_otu_codes_df)} records to Snowflake", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
            return True
            
        except Exception as e:
            self.log_audit_in_db(log_msg=f"Error merging OTU codes to Snowflake during method upload_otu_codes: {e}", log_type='Error', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
            return False

    # Moves file in sharepoint site from unprocessed to processed
    def move_processed_otu_codes(self):
        """
        Move the processed file to a 'Processed Files' folder within SharePoint
        """
        try:
            self.log_audit_in_db(log_msg="Moving processed file to archive folder", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)

            # Get the file that was processed
            first_file = self.sp.get_first_file_from_folder(
                site_url=self.site_url, 
                folder_path=self.folder_path
            )
            if not first_file:
                self.log_audit_in_db(log_msg="No file found to move", log_type='Warning', print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME, process_type=self.PROCESS_TYPE)
                return False

            file_name = first_file.get('name')
            file_id = first_file.get('id')
            
            # Define the processed folder path
            processed_folder_path = self.processed_codes_path
            
            # Check if processed folder exists, create if it doesn't
            processed_folder = self.sp.get_folder(
                site_url=self.site_url, 
                folder_path=processed_folder_path
            )
            
            if not processed_folder:
                self.log_audit_in_db(log_msg=f"Creating processed folder: {processed_folder_path}", print_msg=self.PRINT_STATEMENTS, script_file_name=self.SCRIPT_NAME)
                # You'll need to implement create_folder in your SharePointClient
                # For now, log that manual creation is needed
                self.log_audit_in_db(
                    log_msg=f"Processed folder '{processed_folder_path}' doesn't exist. Please create it manually.",
                    script_file_name=self.SCRIPT_NAME, 
                    log_type='Warning', 
                    print_msg=self.PRINT_STATEMENTS,
                    process_type=self.PROCESS_TYPE
                )
                return False
            
            # Move the file
            move_success = self.sp.move_file(
                site_url=self.site_url,
                file_id=file_id,
                destination_folder_path=processed_folder_path
            )
            
            if move_success:
                self.log_audit_in_db(
                    log_msg=f"Successfully moved file '{file_name}' to '{processed_folder_path}'",
                    script_file_name=self.SCRIPT_NAME, 
                    print_msg=self.PRINT_STATEMENTS,
                    process_type=self.PROCESS_TYPE
                )
                return True
            else:
                self.log_audit_in_db(
                    log_msg=f"Failed to move file '{file_name}' to '{processed_folder_path}' during method move_processed_otu_codes",
                    script_file_name=self.SCRIPT_NAME, 
                    log_type='Error', 
                    print_msg=self.PRINT_STATEMENTS,
                    process_type=self.PROCESS_TYPE
                )
                return False

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"Error moving processed file during method move_processed_otu_codes during method move_processed_otu_codes: {e}",
                script_file_name=self.SCRIPT_NAME,
                log_type='Error', 
                print_msg=self.PRINT_STATEMENTS,
                process_type=self.PROCESS_TYPE
            )
            return False

    def ready_local_data_directory(self):
        # Prepare local data directory for storing processed files
        self.log_audit_in_db(log_msg=f"Preparing local data directory at: {self.local_data_directory}", script_file_name=self.SCRIPT_NAME, print_msg=self.PRINT_STATEMENTS, process_type=self.PROCESS_TYPE)

        try:
            create_directories(self.local_data_directory)
        except Exception as e:
            self.log_audit_in_db(log_msg=f"Error creating local data directory: {e} during method: ready_local_data_directory", log_type='Error', script_file_name=self.SCRIPT_NAME, print_msg=self.PRINT_STATEMENTS, process_type=self.PROCESS_TYPE)
            return False
        self.log_audit_in_db(log_msg=f"Local data directory ready at: {self.local_data_directory}", log_type='Info', script_file_name=self.SCRIPT_NAME, print_msg=self.PRINT_STATEMENTS, process_type=self.PROCESS_TYPE)
        return True


    def clean_up_files(self):
        """
        Clean up only files, leaving directory structure intact
        """
        try:
            if os.path.exists(self.local_data_directory):
                files_removed = 0
                # Walk through all subdirectories
                for root, dirs, files in os.walk(self.local_data_directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            files_removed += 1
                        except Exception as e:
                            self.log_audit_in_db(
                                log_msg=f"Error removing file {file_path}: {e}",
                                script_file_name=self.SCRIPT_NAME,
                                log_type='Warning', 
                                print_msg=self.PRINT_STATEMENTS,
                                process_type=self.PROCESS_TYPE
                            )

                self.log_audit_in_db(
                    log_msg=f"Cleaned up {files_removed} files from directory tree: {self.local_data_directory}",
                    script_file_name=self.SCRIPT_NAME, 
                    print_msg=self.PRINT_STATEMENTS,
                    process_type=self.PROCESS_TYPE
                )
            else:
                self.log_audit_in_db(
                    log_msg=f"Local data directory does not exist: {self.local_data_directory}",
                    script_file_name=self.SCRIPT_NAME,
                    print_msg=self.PRINT_STATEMENTS,
                    process_type=self.PROCESS_TYPE
                )
            return True
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"Error cleaning up files during method clean_up_files during method clean_up_files: {e}",
                script_file_name=self.SCRIPT_NAME,
                log_type='Error', 
                print_msg=self.PRINT_STATEMENTS,
                process_type=self.PROCESS_TYPE
            )
            return False
     

    # def log_audit_in_db(self, log_msg, log_type='Info', print_msg=False, print_data_list=True, start_upload=False):
    #     """
    #     Store log messages in a buffer and upload them in bulk when requested
        
    #     Args:
    #         log_msg (str): Message to log
    #         log_type (str): Type of log (Info, Warning, Error, etc.)
    #         print_msg (bool): Whether to print the log message
    #         print_data_list (bool): Whether to print the data list
    #         start_upload (bool): Whether to upload the collected logs
        
    #     Returns:
    #         bool: Success status of the operation
    #     """
    #     try:            
    #         # Print log message if requested
    #         if print_msg:
    #             print(f"{self.SCRIPT_NAME} - {log_type}: {log_msg}")
                
    #         # Create the log record and add to buffer
    #         uuid_str = str(uuid.uuid4())
    #         script_name = self.SCRIPT_NAME
    #         rec_ins_date = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
    #         record = [uuid_str, script_name, log_type, self.SCRIPT_NAME, log_msg, rec_ins_date]
    #         self.log_buffer.append(record)
            
    #         if print_data_list:
    #             self.log_audit_in_db(log_msg=f"Added to log buffer. Current size: {len(self.log_buffer)}")

    #         if not self.LOG_TO_DB:
    #             return True
            
    #         # Upload logs if requested
    #         if start_upload and self.log_buffer:
    #             columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
                
    #             try:
    #                 self.sf.bulk_insert(
    #                     columns_list=columns_list, 
    #                     data_list=self.log_buffer, 
    #                     database=os.environ['DATABASE_RAW_DATABASE'], 
    #                     schema=self.LOG_SCHEMA, 
    #                     table=self.LOG_TABLE
    #                 )
    #                 self.log_audit_in_db(log_msg=f"Uploaded {len(self.log_buffer)} log entries in bulk")
                    
    #                 # Clear the buffer after successful upload
    #                 self.log_buffer = []
    #                 return True
                    
    #             except Exception as e:
    #                 self.log_audit_in_db(log_msg=f"Error uploading logs during method move_processed_otu_codes: {e}", log_type='Error', print_msg=self.PRINT_STATEMENTS)
    #                 return False
                    
    #         return True
            
    #     except Exception as e:
    #         print(f"Error in log_audit_in_db during method move_processed_otu_codes: {e}", log_type='Error', print_msg=self.PRINT_STATEMENTS)
    #         return False
        

if __name__ == '__main__':
    otu_loader = MarcosOtuLoader()
    otu_loader.run()


    # DEBUG = False

    # if DEBUG:
    #     print('Attempting debug sharepoint')
    #     site_url = 'https://highlandventuresltd442.sharepoint.com/sites/dev'
    #     debug_sharepoint_structure(site_url=site_url)
    #     print(f"Finished Debug")
