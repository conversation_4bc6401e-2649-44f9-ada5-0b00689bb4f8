"""
Process Name: Marcos Weekly Banking Tables to SharePoint Update SF
R Script Name: MARCOS_Weekly_Banking-Tables_to_Google_Update-SF.R
Description: This script extracts banking reference data from Snowflake and updates 
            SharePoint Excel sheets with current information. Replaces Google Sheets 
            functionality with SharePoint Excel operations.
Author: Converted from R by Julian
Date: 2025-06-25
Scheduling: Daily

Updates sheets [MP_BANK_ID_MASTER, MP_BANK_ID_LOCAL, LOCAL join MASTER] from the following workbook:
    
    https://highlandventuresltd442.sharepoint.com/:x:/r/sites/finance/_layouts/15/Doc.aspx?sourcedoc=%7BCA107DF5-6685-4E8F-8E15-EE01796861DF%7D&file=MP_BANK_ID_LOCAL%20and%20MP_BANK_ID_MASTER%20table%20info%20(current%20or%20future).xlsx&action=default&mobileredirect=true&isSPOFile=1&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=****************************************************************************************************************


"""

import os
import pandas as pd
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from libs.snowflake_helper import SnowflakeHelper
from libs.excel_helper import SharePointExcelOnline
from libs.sharepoint_helper import SharePointClient
import libs.email_client as email_client
import time
import uuid

OVERRIDE_EMAIL_RECIPIENTS = True

@dataclass
class SheetUpdateResult:
    """Data class to hold sheet update results"""
    sheet_name: str
    rows_updated: int
    success: bool
    error_message: Optional[str] = None

class WeeklyBankingTablesToSharePointUpdateSF:
    """
    Class to handle updating SharePoint Excel sheets with banking reference data from Snowflake.
    Replaces the R script functionality that updated Google Sheets.
    """

    PROCESS_NAME = 'Marcos Weekly Banking-Tables to SharePoint Update'
    PROCESS_TYPE = 'Update'
    LOG_TO_DB = True

    R_SCRIPT_NAME = 'MARCOS_Weekly_Banking-Tables_to_Google_Update-SF.R'
    
    LOG_SCHEMA = None # 'BATCH_AUDIT'
    LOG_TABLE = None # 'MOMS_EXECUTION_LOGS'
    DRY_RUN = False  # Set to True for testing without actual SharePoint operations
    TEST_MODE = False  # Set to True to override date calculations for testing
    SEND_EMAILS = True  # Set to True to enable email notifications (disabled by default)

    # SharePoint configuration - using same site as transaction loader
    SHAREPOINT_FINANCE_URL = 'https://highlandventuresltd442.sharepoint.com/sites/finance'
    SHAREPOINT_BANKING_FOLDER = 'Documents/Marcos Bank Transactions/Weekly Marcos Bank Transactions'
    EXCEL_FILENAME = 'MP_BANK_ID_LOCAL and MP_BANK_ID_MASTER table info (current or future).xlsx'

    # Database configuration
    ENVM = os.environ['DATABASE_ENVM']
    DATABASE_CSM_DATABASE = os.environ['DATABASE_CSM_DATABASE']
    DATABASE_CORPORATE_SCHEMA = 'CORPORATE' # os.environ['DATABASE_CORPORATE_SCHEMA']
    DATABASE_WAREHOUSE = os.environ['DATABASE_WAREHOUSE']
    DATABASE_ROLE = os.environ['DATABASE_ROLE']

    MP_BANK_ID_MASTER_TABLE = 'MP_BANK_ID_MASTER'
    MP_BANK_ID_LOCAL_TABLE = 'MP_BANK_ID_LOCAL'

    # Sheets to update
    TARGET_SHEETS = ["MP_BANK_ID_MASTER", "MP_BANK_ID_LOCAL", "LOCAL join MASTER"]

    # Columns to exclude from Snowflake results (internal metadata)
    COLUMNS_BLACKLIST = [
        "_DLT_LOAD_ID",
        "_DLT_ID", 
        "_DLT_LOAD_ID.1",
        "_DLT_ID.1"
    ]

    PRINT_STATEMENTS = True

    def __init__(self, testing_emails: bool = False, send_emails: bool = None):
        """
        Initialize the updater with required clients.
        
        Args:
            testing_emails: Whether to send test emails only (if emails enabled)
            send_emails: Override for email sending (None uses class default)
        """
        self.sf = SnowflakeHelper()
        self.LOG_SCHEMA = self.sf.batch_audit_schema
        self.LOG_TABLE = self.sf.moms_log_tbl
        #self.testing_emails = testing_emails
        #self.send_emails = send_emails if send_emails is not None else self.SEND_EMAILS
        self.send_emails = True

        # Initialize SharePoint clients with error suppression
        self.sp = SharePointClient(suppress_false_errors=True)
        self.sp.authenticate()
        self.ex = SharePointExcelOnline(sharepoint_client=self.sp)

        self.cursor = self.sf.cs
        self.conn = self.sf.conn
        self.log_buffer = []
        
        # Email configuration (if enabled)
        # if self.send_emails:
        self.warn_recipients = ["<EMAIL>","<EMAIL>"]
        self.norm_recipients = ["<EMAIL>","<EMAIL>"]
        self.test_recipients = ["<EMAIL>"]
        
        # Date configuration
        self.setup_date_ranges()
        
        # Results storage
        self.update_results: List[SheetUpdateResult] = []

    def setup_date_ranges(self):
        """Setup date ranges for data filtering"""
        if self.TEST_MODE:
            today = datetime(2025, 6, 18).date()  # Fixed date for testing
            self.log_audit_in_db(f"TEST_MODE: Overriding today's date to {today}")
        else:
            today = datetime.now().date()

        # Calculate query date ranges (13 days back from current week)
        days_since_sunday = today.weekday() + 1 if today.weekday() != 6 else 0
        week_start = today - timedelta(days=days_since_sunday)
        
        self.query_start_date = week_start - timedelta(days=11)  # 13 days back from week start - 2
        self.query_end_date = week_start + timedelta(days=2)     # Week start + 2 days

        self.log_audit_in_db(
            log_msg=f'Date ranges calculated - Start: {self.query_start_date}, End: {self.query_end_date}',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )

    def log_audit_in_db(self, log_msg, log_type='Info', print_msg=False, print_data_list=True, start_upload=False,
                            process_type=None, script_file_name=None):
        """
        Store log messages in a buffer and upload them in bulk when requested
        
        Args:
            log_msg (str): Message to log
            log_type (str): Type of log (Info, Warning, Error, etc.)
            print_msg (bool): Whether to print the log message
            print_data_list (bool): Whether to print the data list
            start_upload (bool): Whether to upload the collected logs
        
        Returns:
            bool: Success status of the operation
        """
        try:            
            # Print log message if requested
            if print_msg:
                print(f"{self.PROCESS_NAME} - {log_type}: {log_msg}")
                
            # Create the log record and add to buffer
            uuid_str = str(uuid.uuid4())
            script_name = self.PROCESS_NAME
            rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            record = [uuid_str, script_name, log_type, self.PROCESS_TYPE, log_msg, rec_ins_date]
            self.log_buffer.append(record)

            
            if print_data_list:
                print(f"Added to log buffer. Current size: {len(self.log_buffer)}")

            if not self.LOG_TO_DB:
                return True
            
            # Upload logs if requested
            if start_upload and self.log_buffer:
                columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
                
                try:
                    self.sf.bulk_insert(
                        columns_list=columns_list, 
                        data_list=self.log_buffer, 
                        database=os.environ['DATABASE_RAW_DATABASE'], 
                        schema=self.LOG_SCHEMA, 
                        table=self.LOG_TABLE
                    )
                    print(f"Uploaded {len(self.log_buffer)} log entries in bulk")
                    
                    # Clear the buffer after successful upload
                    self.log_buffer = []
                    return True
                    
                except Exception as e:
                    print(f"Error uploading logs for WeeklyBankingImportTransactionsSFLoader: {e}")
                    return False
                    
            return True
            
        except Exception as e:
            print(f"Error in log_audit_in_db for WeeklyBankingImportTransactionsSFLoader: {e}")
            return False

    def get_sheet_query(self, sheet_name: str) -> str:
        """
        Get the appropriate SQL query for each sheet
        
        Args:
            sheet_name: Name of the sheet to query for
            
        Returns:
            SQL query string
        """
        schema = f"{self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}"
        
        if sheet_name == "MP_BANK_ID_MASTER":
            return f"""
                SELECT M.*
                FROM {schema}.MP_BANK_ID_MASTER M
                WHERE 
                    M.ACTIVE = 1
                ORDER BY M.NAME_LONG, M.B_ID_MASTER;
            """
        
        elif sheet_name == "MP_BANK_ID_LOCAL":
            return f"""
                SELECT L.*
                FROM {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.MP_BANK_ID_LOCAL L
                WHERE 
                    (L.E_DATE IS NULL OR L.E_DATE >= TO_DATE('{self.query_start_date.strftime("%Y-%m-%d")}','YYYY-MM-DD'))
                ORDER BY L.LOC_NUM, L.B_ID_MASTER, L.S_DATE;
            """
        
        elif sheet_name == "LOCAL join MASTER":
            return f"""
                SELECT L.*,
                    M.*
                FROM {schema}.MP_BANK_ID_LOCAL L
                JOIN {schema}.MP_BANK_ID_MASTER M
                    ON L.B_ID_MASTER = M.B_ID_MASTER
                WHERE 
                    (L.E_DATE IS NULL OR L.E_DATE >= TO_DATE('{self.query_start_date.strftime("%Y-%m-%d")}','YYYY-MM-DD'))
                ORDER BY L.LOC_NUM, L.B_ID_MASTER;
            """
        
        else:
            raise ValueError(f"Unknown sheet name: {sheet_name}")

    def verify_sharepoint_access(self) -> bool:
        """
        Verify access to SharePoint file and get sheet information

        Returns:
            True if access successful, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg=f'Verifying access to SharePoint file: {self.EXCEL_FILENAME}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Get folder contents to find the file
            folder_info = self.sp.get_folder(
                site_url=self.SHAREPOINT_FINANCE_URL,
                folder_path=self.SHAREPOINT_BANKING_FOLDER
            )

            if not folder_info or 'items' not in folder_info:
                self.log_audit_in_db(
                    log_msg='Could not access SharePoint folder',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Find the target file
            file_found = False
            file_id = None

            for item in folder_info['items']:
                if 'file' in item and item.get('name', '') == self.EXCEL_FILENAME:
                    file_found = True
                    file_id = item.get('id')
                    break

            if not file_found:
                self.log_audit_in_db(
                    log_msg=f'Excel file not found: {self.EXCEL_FILENAME}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Get Excel session and verify sheets exist
            excel_session = self.ex.get_excel_file_by_id(
                site_url=self.SHAREPOINT_FINANCE_URL,
                file_id=file_id
            )

            if not excel_session:
                self.log_audit_in_db(
                    log_msg='Could not create Excel session',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                return False

            # Get worksheet names
            worksheets = self.ex.list_worksheets(excel_session)
            worksheet_names = [ws.get('name', '') for ws in worksheets]

            # Check if all target sheets exist
            missing_sheets = []
            for sheet_name in self.TARGET_SHEETS:
                if sheet_name not in worksheet_names:
                    missing_sheets.append(sheet_name)

            if missing_sheets:
                self.log_audit_in_db(
                    log_msg=f'Missing expected sheets: {missing_sheets}. Available sheets: {worksheet_names}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                # Continue with available sheets only
                self.available_sheets = [sheet for sheet in self.TARGET_SHEETS if sheet in worksheet_names]
            else:
                self.available_sheets = self.TARGET_SHEETS.copy()

            # Store file info for later use
            self.excel_file_id = file_id
            self.excel_session = excel_session

            self.log_audit_in_db(
                log_msg=f'SharePoint access verified. Available sheets: {self.available_sheets}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error verifying SharePoint access: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def retrieve_latest_snowflake_sheet_data(self, sheet_name: str) -> Optional[pd.DataFrame]:
        """
        Query Snowflake for data to populate a specific sheet

        Args:
            sheet_name: Name of the sheet to query data for

        Returns:
            DataFrame with query results or None if error
        """
        try:
            query = self.get_sheet_query(sheet_name)

            self.log_audit_in_db(
                log_msg=f'Querying Snowflake for sheet: {sheet_name}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Execute query using SnowflakeHelper
            query_results = self.sf.execute_snowflake_query(
                query=query,
                print_query=self.PRINT_STATEMENTS,
                pull_only_one_record=False
            )

            if not query_results:
                self.log_audit_in_db(
                    log_msg=f'No data returned for sheet: {sheet_name}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(query_results)

            # Remove blacklisted columns (Snowflake metadata)
            df = df.drop(columns=[col for col in self.COLUMNS_BLACKLIST if col in df.columns])

            # Convert datetime columns to dates to match original R script behavior
            for col in df.columns:
                if df[col].dtype == 'datetime64[ns]':
                    df[col] = df[col].dt.date

            self.log_audit_in_db(
                log_msg=f'Retrieved {len(df)} rows for sheet: {sheet_name}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return df

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error querying Snowflake for sheet {sheet_name}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return None

    def update_sharepoint_sheet(self, sheet_name: str, data: pd.DataFrame) -> SheetUpdateResult:
        """
        Update a specific SharePoint Excel sheet with new data

        Args:
            sheet_name: Name of the sheet to update
            data: DataFrame containing the data to write

        Returns:
            SheetUpdateResult with update status
        """
        try:
            if self.DRY_RUN:
                self.log_audit_in_db(
                    log_msg=f'DRY_RUN: Would update sheet {sheet_name} with {len(data)} rows',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
                return SheetUpdateResult(
                    sheet_name=sheet_name,
                    rows_updated=len(data),
                    success=True
                )

            self.log_audit_in_db(
                log_msg=f'Updating SharePoint sheet: {sheet_name} with {len(data)} rows',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Clear existing data in the sheet (equivalent to R's range_clear)
            try:
                # Get the used range and clear it
                worksheet_data = self.ex.get_worksheet_data(self.excel_session, sheet_name)
                if worksheet_data and 'values' in worksheet_data and worksheet_data['values']:
                    # Clear all data starting from A1 - use a reasonable range
                    cleared = self.ex.clear_excel_sheet_cells(
                        excel_session=self.excel_session,
                        worksheet_name=sheet_name,
                        range_address="A1:Z1000"  # Clear a large range to ensure all data is removed
                    )

                    if cleared:
                        self.log_audit_in_db(
                            log_msg=f'Cleared existing data in sheet: {sheet_name}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME
                        )
                    else:
                        self.log_audit_in_db(
                            log_msg=f'Warning: Clear operation returned False for sheet: {sheet_name}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Warning'
                        )

            except Exception as clear_error:
                self.log_audit_in_db(
                    log_msg=f'Warning: Could not clear sheet {sheet_name}: {clear_error}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )

            # Prepare data for writing (convert DataFrame to list of lists)
            if not data.empty:
                # Include headers
                headers = list(data.columns)
                data_rows = data.values.tolist()

                # Convert any NaN values to empty strings
                for i, row in enumerate(data_rows):
                    data_rows[i] = ['' if pd.isna(val) else str(val) for val in row]

                # Combine headers and data
                all_data = [headers] + data_rows

                # Calculate the range address for the data
                num_rows = len(all_data)
                num_cols = len(headers)
                # Convert column number to letter (A, B, C, etc.)
                end_col_letter = chr(ord('A') + num_cols - 1) if num_cols <= 26 else f"A{chr(ord('A') + num_cols - 27)}"
                range_address = f"A1:{end_col_letter}{num_rows}"

                # Write data to sheet using update_worksheet_range
                success = self.ex.update_worksheet_range(
                    excel_session=self.excel_session,
                    worksheet_name=sheet_name,
                    range_address=range_address,
                    values=all_data
                )

                if success:
                    self.log_audit_in_db(
                        log_msg=f'Successfully updated sheet {sheet_name} with {len(data)} rows',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )

                    # Add a small delay to avoid overwhelming SharePoint
                    time.sleep(2)

                    return SheetUpdateResult(
                        sheet_name=sheet_name,
                        rows_updated=len(data),
                        success=True
                    )
                else:
                    error_msg = f'Failed to write data to sheet {sheet_name}'
                    self.log_audit_in_db(
                        log_msg=error_msg,
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )
                    return SheetUpdateResult(
                        sheet_name=sheet_name,
                        rows_updated=0,
                        success=False,
                        error_message=error_msg
                    )
            else:
                # Empty data - just clear the sheet
                self.log_audit_in_db(
                    log_msg=f'No data to write to sheet {sheet_name} - sheet cleared only',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return SheetUpdateResult(
                    sheet_name=sheet_name,
                    rows_updated=0,
                    success=True
                )

        except Exception as e:
            error_msg = f'Error updating sheet {sheet_name}: {e}'
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return SheetUpdateResult(
                sheet_name=sheet_name,
                rows_updated=0,
                success=False,
                error_message=error_msg
            )

    def send_notification_email(self, subject: str, body: str, is_error: bool = False):
        """
        Send notification email if email notifications are enabled

        Args:
            subject: Email subject
            body: Email body (HTML)
            is_error: Whether this is an error notification
        """
        if not self.send_emails:
            return

        try:
            # Import email functionality only if needed
            # from libs.email_client import EmailClient

            recipients = self.warn_recipients if is_error else self.norm_recipients
            if self.testing_emails:
                recipients = self.test_recipients
                body = f"<p><b>TEST EMAIL (normal recipients: {', '.join(recipients)})</b></p>{body}"

            # email_client = EmailClient()
            email_client.send_email(
                recipient=recipients,
                subject=subject,
                body=body,
                # is_html=True
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )

            self.log_audit_in_db(
                log_msg=f'Email notification sent: {subject}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Failed to send email notification: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )

    def run(self) -> bool:
        """
        Main execution method to update all SharePoint sheets with Snowflake data

        Returns:
            True if all updates successful, False if any failures
        """
        start_time = datetime.now()

        self.log_audit_in_db(
            log_msg=f'Starting {self.PROCESS_NAME} routine',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )

        try:
            # Step 1: Verify SharePoint access
            if not self.verify_sharepoint_access():
                error_msg = 'Failed to verify SharePoint access - aborting'
                self.log_audit_in_db(
                    log_msg=error_msg,
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )

                if self.send_emails:
                    self.send_notification_email(
                        subject=f"{self.PROCESS_NAME} - SharePoint Access Error",
                        body=f"<p>SharePoint access verification failed.</p><p>The routine is aborting without updates.</p>",
                        is_error=True
                    )

                return False

            # Step 2: Process each available sheet
            all_successful = True

            for sheet_name in self.available_sheets:
                try:
                    self.log_audit_in_db(
                        log_msg=f'Processing sheet: {sheet_name}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )

                    # Query Snowflake for data
                    sheet_data = self.retrieve_latest_snowflake_sheet_data(sheet_name)

                    if sheet_data is None:
                        # Error already logged in query method
                        result = SheetUpdateResult(
                            sheet_name=sheet_name,
                            rows_updated=0,
                            success=False,
                            error_message="Failed to query Snowflake data"
                        )
                        all_successful = False
                    else:
                        # Update SharePoint sheet
                        result = self.update_sharepoint_sheet(sheet_name, sheet_data)
                        if not result.success:
                            all_successful = False

                    self.update_results.append(result)

                except Exception as e:
                    error_msg = f'Error processing sheet {sheet_name}: {e}'
                    self.log_audit_in_db(
                        log_msg=error_msg,
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )

                    result = SheetUpdateResult(
                        sheet_name=sheet_name,
                        rows_updated=0,
                        success=False,
                        error_message=error_msg
                    )
                    self.update_results.append(result)
                    all_successful = False

            # Step 3: Generate summary and send notifications if needed
            self.generate_summary_report(start_time, all_successful)

            return all_successful

        except Exception as e:
            error_msg = f'Unexpected error in main execution: {e}'
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )

            if self.send_emails:
                self.send_notification_email(
                    subject=f"{self.PROCESS_NAME} - Unexpected Error",
                    body=f"<p>An unexpected error occurred during execution:</p><p>{error_msg}</p>",
                    is_error=True
                )

            return False

    def generate_summary_report(self, start_time: datetime, all_successful: bool):
        """
        Generate summary report and send notifications if configured

        Args:
            start_time: When the process started
            all_successful: Whether all updates were successful
        """
        end_time = datetime.now()
        duration = end_time - start_time

        # Generate summary statistics
        successful_updates = sum(1 for result in self.update_results if result.success)
        failed_updates = len(self.update_results) - successful_updates
        total_rows_updated = sum(result.rows_updated for result in self.update_results if result.success)

        summary_msg = (
            f'Process completed in {duration.total_seconds():.1f} seconds. '
            f'Sheets processed: {len(self.update_results)}, '
            f'Successful: {successful_updates}, '
            f'Failed: {failed_updates}, '
            f'Total rows updated: {total_rows_updated}'
        )

        self.log_audit_in_db(
            log_msg=summary_msg,
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME,
            log_type='Info' if all_successful else 'Warning'
        )

        # Send email notification if enabled and there were issues
        if self.send_emails and (not all_successful or failed_updates > 0):
            subject = f"{self.PROCESS_NAME} - {'Completed with Issues' if not all_successful else 'Completed Successfully'}"

            body_parts = [
                f"<p>{summary_msg}</p>",
                "<h3>Sheet Update Results:</h3>",
                "<ul>"
            ]

            for result in self.update_results:
                status = "✓ Success" if result.success else "✗ Failed"
                body_parts.append(
                    f"<li><b>{result.sheet_name}</b>: {status} "
                    f"({result.rows_updated} rows updated)"
                )
                if result.error_message:
                    body_parts.append(f" - {result.error_message}")
                body_parts.append("</li>")

            body_parts.append("</ul>")

            if failed_updates > 0:
                body_parts.append("<p><b>Please review the failed updates and take appropriate action.</b></p>")

            body = "".join(body_parts)

            self.send_notification_email(
                subject=subject,
                body=body,
                is_error=not all_successful
            )


def main():
    """
    Main entry point for the script
    """
    print(f"Starting {WeeklyBankingTablesToSharePointUpdateSF.PROCESS_NAME}")

    try:
        # Create and run the updater
        updater = WeeklyBankingTablesToSharePointUpdateSF(
            testing_emails=False,  # Set to True for testing email functionality
            send_emails=False      # Set to True to enable email notifications
        )

        success = updater.run()

        if success:
            print("Process completed successfully")
            return 0
        else:
            print("Process completed with errors")
            return 1

    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
