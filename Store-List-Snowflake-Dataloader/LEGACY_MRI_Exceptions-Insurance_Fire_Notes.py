"""
LEGACY MRI Exceptions - Insurance Fire Notes
Python conversion of R script for processing MRI insurance fire code exceptions

Version: 20241119
Converted from R to Python
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from snowflake.connector import DictCursor

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo

import traceback
from pathlib import Path


import libs.snowflake_helper as sf
import libs.email_client as email_client


OVERRIDE_EMAIL_RECIPIENTS = True

class MRIInsuranceFireNotesProcessor:
    def __init__(self):
        # Configuration
        self.testing_emails = False  # Set to True for testing
        self.version = "20241119"

        self.sf_obj = sf.SnowflakeHelper()
        
                
        # Parameters
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.okay_to_continue = True
        
        # Report configuration
        self.rpt_folder = "LEGACY_MRI_Exceptions-Insurance"
        self.my_report_name = "MRI INSURANCE Fire Code Exceptions"
        self.rpt_fn = "MRI_Insurance_Fire_Code_Exceptions.xlsx"
        
        # Fire codes configuration
        self.my_fire_codes = [
            'FRAME',
            'JOISTED MASONRY',
            'NONCOMBUSTIBLE',
            'MASONRY NONCOMBUSTIBLE',
            'MODIFIED FIRE RESISTIVE',
            'FIRE RESISTIVE',
            'LAND'
        ]
        self.my_fire_codes_query = "'" + "','".join(self.my_fire_codes) + "'"
        
        # Report criteria
        self.my_report_criteria = self.build_report_criteria()
        
        # Environment setup
        self.setup_environment()
        
        # Snowflake configuration
        # self.setup_snowflake_config()
        
        # Email configuration
        self.setup_email_config()
        
        # Paths
        self.setup_paths()


    def build_report_criteria(self):
        """Build the report criteria text"""
        fire_codes_display = "', '".join([code for code in self.my_fire_codes if code != 'LAND'])
        return f"""<p><b>Criteria for inclusion in the report (only active buildings queried):</b><ul>
                   <li>Missing 'CNSTBLD' note</li>
                   <li>'CNSTBLD' note present, but not expected ISO Fire Code 
                   ('{fire_codes_display}' or 'Land' if no building)</li>
                   </ul></p><p></p>"""

    def setup_environment(self):
        """Setup environment variables and paths"""

        self.testing_pc = False
        # self.main_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
        
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S")


    def setup_email_config(self):
        """Setup email configuration"""
        # Email recipients and signatures
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.norm_sig = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
                          Sr. Analytics Mgr.<br/>
                          <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
                          2500 Lehigh Ave.<br/>
                          Glenview, IL 60026<br/>
                          Ph: 847/904-9043<br/>"""
        
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Gmail auth email
        self.gmail_auth_email = "<EMAIL>"

    def setup_paths(self):
        """Setup file paths"""
        self.log_path = self.main_path / self.rpt_folder
        self.rpt_path = self.log_path
        self.hv_sig_path = self.main_path / "HTML_signatures.csv"


    def check_df_rows(self, df, min_num_rows, report_name=None):
        """Check the number of rows in the DataFrame"""
        if isinstance(df, pd.DataFrame):
            if len(df) >= min_num_rows:
                error_status = f"{report_name}: OKAY"
                temp_nrow = len(df)
                temp_bool = True
            else:
                temp_bool = False
                temp_nrow = len(df)
                error_status = f"{report_name}: INCOMPLETE"
        else:
            temp_bool = False
            temp_nrow = 0
            error_status = f"{report_name}: ERROR"
        
        return [temp_bool, temp_nrow, error_status]

    def write_xlsx(self, dir_path, file_name, sheet_name="Sheet1", rpt_df=None, 
                   col_names=True, col_widths=None, write_over=True):
        """Write DataFrame to Excel file with formatting"""
        try:
            # Create directory if it doesn't exist
            dir_path = Path(dir_path)
            dir_path.mkdir(parents=True, exist_ok=True)
            
            file_path = dir_path / file_name
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data to worksheet
            for r in dataframe_to_rows(rpt_df, index=False, header=col_names):
                ws.append(r)
            
            # Format header row
            if col_names:
                header_font = Font(name='Arial Narrow', size=12, bold=True)
                header_fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                
                for cell in ws[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                
                # Freeze first row
                ws.freeze_panes = 'A2'
                
                # Add autofilter
                ws.auto_filter.ref = ws.dimensions
            
            # Set column widths
            if col_widths is not None and isinstance(col_widths, dict):
                for col_name, width in col_widths.items():
                    col_index = None
                    for idx, col in enumerate(rpt_df.columns):
                        if col == col_name:
                            col_index = idx + 1
                            break
                    if col_index:
                        ws.column_dimensions[openpyxl.utils.get_column_letter(col_index)].width = width
            
            # Save workbook
            wb.save(file_path)
            # self.logger.info(f"Excel file saved: {file_path}")
            self.sf_obj.log_audit_in_db(log_msg=f"Excel file saved: {file_path}", process_type=self.my_report_name, script_file_name=__file__, log_type='Info')
            
        except Exception as e:
            # self.logger.error(f"Failed to write Excel file: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to write Excel file: {str(e)}", process_type=self.my_report_name, script_file_name=__file__, log_type='Error')
            # Send warning email about file save failure
            self.send_warning_email(f"File save error: {str(e)}")

    def send_warning_email(self, message):
        """Send warning email"""
        try:
            body_text = f"""This is an automated email to inform you of an issue during the {self.my_report_name} routine.<br/><br/>
                           {message}<br/><br/>
                           {self.warn_sig}"""
            
            email_client.send_email(
                recipient=self.warn_recip,
                subject=f"{self.my_report_name} : WARNING",
                body=body_text,
                # sender=self.gmail_auth_email,
                # replyto=self.gmail_reply_to,
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
        except Exception as e:
            # self.logger.error(f"Failed to send warning email: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to send warning email: {str(e)}", process_type=self.my_report_name, script_file_name=__file__, log_type='Error')

    
    def create_html_table(self, df):
        """Create HTML table from DataFrame"""
        if len(df) < 31:
            html_table = df.to_html(
                index=False,
                table_id="report_table",
                classes="table table-striped",
                border=2,
                escape=False
            )
            return f"<p>The info below contains MRI data (from yesterday) that appears to be an exception. <b>See attached Excel file for full details.</b></p><p>{html_table}</p>"
        else:
            return f"<p><strong><em>There are {len(df)} results, see attached file for all.</em></strong></p>"

    def process_exceptions(self):
        """Main processing function to find exceptions and email results"""
        if not self.okay_to_continue:
            self.sf_obj.log_audit_in_db(log_msg="Script configuration warning - cannot continue", process_type=self.my_report_name, script_file_name=__file__, log_type='Warning')
            return
        
        try:
            # Connect to Snowflake
            # conn = self.connect_to_snowflake()
            conn = self.sf_obj.conn
            
            # Build and execute query
            query_exceptions = f"""
                SELECT
                    BLDG.BLDGID,
                    MNGR.MNGRNAME AS PROPERTY_MGR,
                    TO_DATE(ENTITY.ACQUIRED) AS ACQUIRED,
                    CASE
                        WHEN NOTB.NOTETEXT IS NULL THEN 'Missing "CNSTBLD" ISO Fire Construction Code building note'
                        ELSE '"CNSTBLD" notetext not recognized as one of the allowable ISO Fire Code types (Frame, Joisted Masonry, Noncombustible, Masonry Noncombustible, Modified Fire Resistive, Fire Resistive, Land)'
                    END AS ISSUE,
                    TO_DATE(NOTB.NOTEDATE) AS NOTEDATE,
                    NOTB.NOTETEXT
                FROM MRI.BLDG
                LEFT JOIN MRI.NOTB
                ON BLDG.BLDGID = NOTB.BLDGID AND (NOTB.REF1 = 'CNSTBLD' OR NOTB.REF2 = 'CNSTBLD')
                LEFT JOIN MRI.ENTITY
                ON BLDG.ENTITYID = ENTITY.ENTITYID
                LEFT JOIN MRI.MNGR
                ON BLDG.MNGRID = MNGR.MNGRID
                WHERE
                    (INACTIVE IS NULL OR INACTIVE <> 'Y')
                    AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
                    AND (
                        NOTB.NOTETEXT IS NULL
                        OR 
                        UPPER(NOTB.NOTETEXT) NOT IN ({self.my_fire_codes_query})
                    )
                ORDER BY BLDG.BLDGID, NOTEDATE
            """
            

            # self.logger.info("Executing query for exceptions...")
            self.sf_obj.log_audit_in_db(log_msg="Executing query for exceptions...", process_type=self.my_report_name, script_file_name=__file__, log_type='Info')
            cursor = conn.cursor(DictCursor)
            cursor.execute(query_exceptions)
            results = cursor.fetchall()
            
            # Convert to DataFrame
            my_data = pd.DataFrame(results)
            
            if not my_data.empty:
                # Clean string columns (remove trailing spaces)
                for col in my_data.select_dtypes(include=['object']).columns:
                    if my_data[col].dtype == 'object':
                        my_data[col] = my_data[col].astype(str).str.rstrip()
                
                # Convert date columns properly
                date_columns = ['ACQUIRED', 'NOTEDATE']
                for col in date_columns:
                    if col in my_data.columns:
                        my_data[col] = pd.to_datetime(my_data[col], errors='coerce')
            

            # Check data status
            my_data_status = self.check_df_rows(my_data, min_num_rows=1, report_name=self.my_report_name)
            
            if my_data_status[0]:  # Exceptions found
                # self.logger.info(f"Found {len(my_data)} exceptions")
                self.sf_obj.log_audit_in_db(log_msg=f"Found {len(my_data)} exceptions", process_type=self.my_report_name, script_file_name=__file__, log_type='Info')
                
                # Define column widths for Excel
                col_widths = {
                    'BLDGID': 8.5,
                    'PROPERTY_MGR': 18,
                    'ACQUIRED': 11,
                    'ISSUE': min(135, max(60, my_data['ISSUE'].astype(str).str.len().max())),
                    'NOTEDATE': 11,
                    'NOTETEXT': min(52, max(24, my_data['NOTETEXT'].fillna('').astype(str).str.len().max()))
                }
                
                # Create Excel file
                self.write_xlsx(
                    dir_path=self.rpt_path,
                    file_name=self.rpt_fn,
                    sheet_name=self.query_date,
                    rpt_df=my_data,
                    col_names=True,
                    col_widths=col_widths,
                    write_over=True
                )
                
                # Prepare email
                my_email_files = self.rpt_path / self.rpt_fn
                
                # Create email body with data summary
                email_body_cols = ['BLDGID', 'PROPERTY_MGR', 'ISSUE', 'NOTETEXT']
                my_data_email_body = my_data[email_body_cols].drop_duplicates()
                
                # Format dates for email display
                for col in my_data_email_body.columns:
                    if my_data_email_body[col].dtype == 'datetime64[ns]':
                        my_data_email_body[col] = my_data_email_body[col].dt.strftime('%m/%d/%y')
                
                # Create HTML table for email
                body_table = self.create_html_table(my_data_email_body)
                
                body_text = f"""<p><h2>REPORT: {self.my_report_name}</h2></p>
                               {self.my_report_criteria}
                               {body_table}
                               <br/>
                               {self.norm_sig}"""
                
                # Send email
                email_client.send_email(
                    recipient=self.norm_recip,
                    subject=self.my_report_name,
                    body=body_text,
                    attachments=[str(my_email_files) if my_email_files.exists() else None],
                    # test=self.testing_emails,
                    # test_recipient=self.test_recip
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
                self.sf_obj.log_audit_in_db(log_msg="Report generated and emailed successfully", process_type=self.my_report_name, script_file_name=__file__, log_type='Info')
                # self.logger.info("Report generated and emailed successfully")
            else:
                self.sf_obj.log_audit_in_db(log_msg="No exceptions found", process_type=self.my_report_name, script_file_name=__file__, log_type='Info')
                # self.logger.info("No exceptions found")
            
            # Close connection
            # cursor.close()
            # conn.close()
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error in process_exceptions: {str(e)}", process_type=self.my_report_name, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error in process_exceptions: {str(e)}")
            # self.logger.error(traceback.format_exc())
            raise

def main():
    """Main execution function"""
    try:
        processor = MRIInsuranceFireNotesProcessor()

        processor.process_exceptions()

        # print("MRI Insurance Fire Notes processing completed successfully")
        processor.sf_obj.log_audit_in_db(log_msg="MRI Insurance Fire Notes processing completed successfully", process_type=processor.my_report_name, script_file_name=__file__, log_type='Info')
        
    except Exception as e:
        # logging.error(f"Failed to process MRI Insurance Fire Notes: {str(e)}")
        # logging.error(traceback.format_exc())
        print(f"Failed to process MRI Insurance Fire Notes: {str(e)}")
        print(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main() 