#!/usr/bin/env python3
"""
GL Summary vs Details Exceptions Report (exclude YTD balance)
Converted from R to Python

Version: 20220614 Python Conversion
"""

import os
import sys

import pandas as pd

from datetime import datetime, timedelta

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo
import warnings

import libs.snowflake_helper as sf
import libs.email_client as email_client

warnings.filterwarnings('ignore')

OVERRIDE_EMAIL_RECIPIENTS = False

class GLSummaryDetailsReport:
    def __init__(self):
        # Configuration
        self.sf_obj = sf.SnowflakeHelper()
        self.db_connection = self.sf_obj.conn

        # Oracle connection
        # self.db_connection = None

        
        self.testing_emails = False  # NORMAL, should normally be disabled in PRODUCTION
        # self.testing_emails = True  # Uncomment for testing
        
        # Version and report info
        self.version = "20220614"
        self.query_date = datetime.now().strftime("%d-%b-%y")
        # For testing, uncomment one of these:
        # self.query_date = "19-MAR-22"
        # self.query_date = (datetime.now() + timedelta(days=7)).strftime("%d-%b-%y")
        
        self.okay_to_continue = True
        
        # Report configuration
        self.rpt_folder = "HV_Exceptions-GL_Summary_vs_Details"
        self.report_name = "Oracle GL Summary vs Details Exceptions (exclude YTD balance)"
        self.rpt_filename = "Oracle_GL_Summary_vs_Details_Exceptions_YTD_excl.xlsx"
        self.report_criteria = """<p><b>Criteria for inclusion in the report:</b> 
                                 (note comparisons are made at the GL Coding level within each period...if 
                                 the YTD sum of the difference eventually nets out to $0, it will drop off 
                                 this report at that point)<ul>
                                 <li>GL Summary NET doesn't equal the sum of the Details view</li>
                                 <li>GL Summary NET has a balance <> 0, but no corresponding Detail invoices present</li>
                                 <li>GL Detail invoices present, but no corresponding GL Summary row present</li>
                                 </ul></p><br/>"""
        
        # Paths
        self.log_path = os.path.join(os.environ["SCRIPTS_BASE_DATA_DIR"], self.rpt_folder)
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S")
        
        # Check if testing PC
        # computer_name = os.environ.get('COMPUTERNAME', '')
        # self.testing_pc = computer_name in ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13"]
        
        # if self.testing_pc:
        #     self.log_path = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles", self.rpt_folder)
        #     self.hv_sig_logo_path = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles", "HV Logo Email Signature.png")
        #     self.rpt_path = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles", self.rpt_folder)
        # else:
        self.rpt_path = self.log_path
        
        # Email configuration
        self.norm_recip = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        self.norm_sig = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
                          Sr. Analytics Mgr.<br/>
                          <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
                          2500 Lehigh Ave.<br/>
                          Glenview, IL 60026<br/>
                          Ph: 847/904-9043<br/></span></font>"""
        
        self.warn_recip = ["<EMAIL>", "<EMAIL>"]
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
    
    
    def check_mydata_rows(self, df, min_num_rows, report_name=None):
        """Check if dataframe has minimum required rows"""
        if report_name is None:
            report_name = self.report_name
            
        if df is not None and len(df) >= min_num_rows:
            error_status = f"{report_name}: COMPLETE"
            temp_nrow = len(df)
            temp_bool = True
        elif df is not None:
            temp_bool = False
            temp_nrow = len(df)
            error_status = f"{report_name}: INCOMPLETE RESULTS"
        else:
            temp_bool = False
            temp_nrow = 0
            error_status = f"{report_name}: NO RESULTS"
        
        return temp_bool, temp_nrow, error_status
    
    def write_xlsx(self, dir_path, fname, sheet_name="Sheet1", rpt_df=None, 
                   col_names=True, col_widths=None, write_over=True):
        """Write DataFrame to Excel file with formatting"""
        my_fn = os.path.join(dir_path, fname)
        
        # Create directory if it doesn't exist
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        
        try:
            # Convert datetime columns to date
            df_copy = rpt_df.copy()
            for col in df_copy.columns:
                if pd.api.types.is_datetime64_any_dtype(df_copy[col]):
                    df_copy[col] = df_copy[col].dt.date
            
            # Create workbook and worksheet
            wb = Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data to worksheet
            for r in dataframe_to_rows(df_copy, index=False, header=col_names):
                ws.append(r)
            
            # Format header row
            if col_names:
                header_font = Font(bold=True, size=12, name='Arial Narrow')
                header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
                header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
                
                for cell in ws[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
            
            # Set column widths
            if col_widths:
                for col_name, width in col_widths.items():
                    col_idx = None
                    for idx, cell in enumerate(ws[1], 1):
                        if cell.value == col_name:
                            col_idx = idx
                            break
                    if col_idx:
                        ws.column_dimensions[ws.cell(row=1, column=col_idx).column_letter].width = width
            
            # Add autofilter
            if len(df_copy) > 0:
                ws.auto_filter.ref = ws.dimensions
            
            # Freeze top row
            ws.freeze_panes = "A2"
            
            # Save workbook
            wb.save(my_fn)
            print(f"Excel file saved: {my_fn}")
            return True
            
        except Exception as e:
            print(f"Error writing Excel file: {e}")
            # Send warning email
            body_text = f"""This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> 
                           during the <b>{self.report_name}</b> routine.<br/><br/>
                           {my_fn}<br/><br/>
                           Error: {str(e)}<br/><br/>
                           The routine should continue without saving this file.<br/><br/>
                           {self.warn_sig}"""
            
            email_client.send_email(
                recipient=self.warn_recip,
                subject=f"{self.report_name} : REPORT FILE SAVING ERROR",
                body=body_text, 
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
            return False
    
    def get_current_period(self):
        """Get current period information"""
        # query = f"""
        #     SELECT I.PERIOD_NUM, I.PERIOD_YEAR
        #     FROM corporate.MP_CALENDAR I
        #     WHERE TRUNC(I.S_DATE) <= TRUNC(to_date('{self.query_date}', 'DD-MON-YY') - 1)
        #         AND TRUNC(I.E_DATE) >= TRUNC(to_date('{self.query_date}', 'DD-MON-YY') - 1)
        # """
        
        query = f"""
            SELECT I.PERIOD_NUM, I.PERIOD_YEAR
                FROM corporate.MP_CALENDAR I
                WHERE DATE_TRUNC('DAY', I.S_DATE) <= DATE_TRUNC('DAY', TO_DATE('{self.query_date}', 'DD-MON-YY') - 1)
                    AND DATE_TRUNC('DAY', I.E_DATE) >= DATE_TRUNC('DAY', TO_DATE('{self.query_date}', 'DD-MON-YY') - 1)
        """

        try:
            df = pd.read_sql(query, self.db_connection)
            if len(df) > 0:
                return int(df.iloc[0]['PERIOD_NUM']), int(df.iloc[0]['PERIOD_YEAR'])
            else:
                return None, None
        except Exception as e:
            print(f"Error getting current period {query}: {e}")
            return None, None
    
    def build_period_list(self, period_num, period_year):
        """Build list of periods to query"""
        periods = []
        
        # Add current year periods
        for p in range(1, period_num + 1):
            periods.append({'PERIOD_NUM': p, 'PERIOD_YEAR': period_year})
        
        # If early in year, add previous year periods
        if period_num < 3:
            for p in range(1, 15):  # 14 periods + 1
                periods.append({'PERIOD_NUM': p, 'PERIOD_YEAR': period_year - 1})
        
        return periods
    
    def get_gl_exceptions_for_period(self, period_num, period_year):
        """Get GL exceptions for a specific period"""
        query = f"""
        SELECT
            RSLT.ISSUE
        ,   RSLT.PERIOD_YEAR
        ,   RSLT.PERIOD_NUM
        ,   RSLT.COMPANY_ID
        ,   RSLT.GL_ACCOUNT
        ,   RSLT.GL_ACCOUNT_DESCRIPTION
        ,   RSLT.COST_CENTER
        ,   RSLT.GL_CODING
        ,   RSLT.NUM_DETAIL_INVOICES
        ,   RSLT.DETAIL_PERIOD_DEBITS
        ,   RSLT.DETAIL_PERIOD_CREDITS
        ,   RSLT.DETAIL_PERIOD_NET
        ,   RSLT.SUMMARY_PERIOD_DEBITS
        ,   RSLT.SUMMARY_PERIOD_CREDITS
        ,   RSLT.SUMMARY_PERIOD_NET
        ,   RSLT.NET_DIFF
        FROM
        (
            SELECT
                CASE WHEN GLD.PERIOD_NET IS NULL THEN 'SUMMARY GL_ACCOUNT not present in DETAILS'
                  ELSE 'DETAILS NET different than SUMMARY'
                  END AS ISSUE
            ,   GLS.FISCAL_YEAR AS PERIOD_YEAR
            ,   GLS.PERIOD AS PERIOD_NUM
            ,   GLS.COMPANY_ID
            ,   GLS.GL_ACCOUNT
            ,   GLS.GL_ACCOUNT_DESCRIPTION AS GL_ACCOUNT_DESCRIPTION
            ,   GLS.COST_CENTER
            ,   GLS.GL_CODING
            ,   NVL(GLD.INVOICE_CNT, 0) AS NUM_DETAIL_INVOICES
            ,   GLD.DEBITS AS DETAIL_PERIOD_DEBITS
            ,   GLD.CREDITS AS DETAIL_PERIOD_CREDITS
            ,   GLD.PERIOD_NET AS DETAIL_PERIOD_NET
            ,   GLS.TOTAL_PERIOD_DEBITS AS SUMMARY_PERIOD_DEBITS
            ,   GLS.TOTAL_PERIOD_CREDITS AS SUMMARY_PERIOD_CREDITS
            ,   GLS.TOTAL_PERIOD_NET AS SUMMARY_PERIOD_NET
            ,   GLS.TOTAL_PERIOD_NET - NVL(GLD.PERIOD_NET,0) AS NET_DIFF
            FROM
            (
                SELECT
                    S.FISCAL_YEAR
                ,   S.PERIOD
                ,   S.COMPANY_ID
                ,   S.GL_ACCOUNT
                ,   S.GL_ACCOUNT_DESCRIPTION
                ,   S.COST_CENTER
                ,   S.GL_CODING
                ,   S.TOTAL_PERIOD_DEBITS
                ,   S.TOTAL_PERIOD_CREDITS
                ,   S.TOTAL_PERIOD_NET
                FROM CORPORATE.RPT_GL_SUMMARY_V S
                WHERE S.COST_CENTER != '0000'
                    AND (S.FISCAL_YEAR = {period_year} AND S.PERIOD = {period_num})
            ) GLS
            LEFT JOIN
            (
                SELECT
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                -- ,   D.COST_CENTER
                ,   '' as COST_CENTER
                ,   D.GL_CODING
                ,   COUNT(D.GL_TRANSACTION_DATE) AS INVOICE_CNT
                ,   SUM(D.DEBIT) AS DEBITS
                ,   SUM(D.CREDIT) AS CREDITS
                ,   SUM(D.NET) AS PERIOD_NET
                FROM CORPORATE.RPT_GL_DETAIL_V D
                WHERE 
                    -- D.COST_CENTER != '0000'
                    -- AND 
                    (D.PERIOD_YEAR = {period_year} AND D.PERIOD_NUM = {period_num})
                GROUP BY
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                -- ,   D.COST_CENTER
                ,   D.GL_CODING
            ) GLD
            ON GLS.FISCAL_YEAR = GLD.PERIOD_YEAR
                AND GLS.PERIOD = GLD.PERIOD_NUM
                AND GLS.COMPANY_ID = GLD.COMPANY_ID
                AND GLS.COST_CENTER = GLD.COST_CENTER
                AND GLS.GL_CODING = GLD.GL_CODING
            WHERE GLS.TOTAL_PERIOD_NET != NVL(GLD.PERIOD_NET,0)
            
            UNION ALL
            
            SELECT
                'DETAILS GL_ACCOUNT not present in SUMMARY' as ISSUE
            ,   GLD.PERIOD_YEAR AS PERIOD_YEAR
            ,   GLD.PERIOD_NUM AS PERIOD_NUM
            ,   GLD.COMPANY_ID
            ,   GLD.GL_ACCOUNT
            ,   GLD.GL_ACCOUNT_DESCRIPTION
            ,   GLD.COST_CENTER
            ,   GLD.GL_CODING
            ,   GLD.INVOICE_CNT AS NUM_DETAIL_INVOICES
            ,   GLD.DEBITS AS DETAIL_PERIOD_DEBITS
            ,   GLD.CREDITS AS DETAIL_PERIOD_CREDITS
            ,   GLD.PERIOD_NET AS DETAIL_PERIOD_NET
            ,   GLS.TOTAL_PERIOD_DEBITS AS SUMMARY_PERIOD_DEBITS
            ,   GLS.TOTAL_PERIOD_CREDITS AS SUMMARY_PERIOD_CREDITS
            ,   GLS.TOTAL_PERIOD_NET AS SUMMARY_PERIOD_NET
            ,   GLD.PERIOD_NET - NVL(GLS.TOTAL_PERIOD_NET, 0) AS NET_DIFF
            FROM
            (
                SELECT
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                -- ,   D.COST_CENTER
                , '' as  COST_CENTER
                ,   D.GL_CODING
                ,   COUNT(D.GL_TRANSACTION_DATE) AS INVOICE_CNT
                ,   SUM(D.DEBIT) AS DEBITS
                ,   SUM(D.CREDIT) AS CREDITS
                ,   SUM(D.NET) AS PERIOD_NET
                FROM CORPORATE.RPT_GL_DETAIL_V D
                WHERE
                    -- D.COST_CENTER != '0000'
                    -- AND 
                    (D.PERIOD_YEAR = {period_year} AND D.PERIOD_NUM = {period_num})
                GROUP BY
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                -- ,   D.COST_CENTER
                ,   D.GL_CODING
            ) GLD
            LEFT JOIN
            (
                SELECT
                    S.FISCAL_YEAR
                ,   S.PERIOD
                ,   S.COMPANY_ID
                ,   S.GL_ACCOUNT
                ,   S.GL_ACCOUNT_DESCRIPTION
                ,   S.COST_CENTER
                ,   S.GL_CODING
                ,   S.TOTAL_PERIOD_DEBITS
                ,   S.TOTAL_PERIOD_CREDITS
                ,   S.TOTAL_PERIOD_NET
                FROM CORPORATE.RPT_GL_SUMMARY_V S
                WHERE S.COST_CENTER != '0000'
                    AND (S.FISCAL_YEAR = {period_year} AND S.PERIOD = {period_num})
            ) GLS
            ON GLD.PERIOD_YEAR = GLS.FISCAL_YEAR
                AND GLD.PERIOD_NUM = GLS.PERIOD
                AND GLD.COMPANY_ID = GLS.COMPANY_ID
                AND GLD.COST_CENTER = GLS.COST_CENTER
                AND GLD.GL_CODING = GLS.GL_CODING
            WHERE (GLD.PERIOD_NET <> 0 AND GLS.TOTAL_PERIOD_NET is NULL)
        ) RSLT
        ORDER BY RSLT.PERIOD_YEAR
        ,   RSLT.PERIOD_NUM
        ,   RSLT.COST_CENTER
        ,   RSLT.GL_ACCOUNT
        """
        
        # print(f"Query: {query}")
        # exit()
        try:
            df = pd.read_sql(query, self.db_connection)
            return df
        except Exception as e:
            print(f"Error getting GL exceptions for period {period_num}-{period_year}: {e}")
            return pd.DataFrame()
    
    def create_html_table(self, df):
        """Create HTML table from DataFrame"""
        if len(df) == 0:
            return "<p>No data to display</p>"
        
        html = "<table border='2' cellspacing='1'>"
        
        # Header
        html += "<tr>"
        for col in df.columns:
            html += f"<th>{col}</th>"
        html += "</tr>"
        
        # Data rows
        for _, row in df.iterrows():
            html += "<tr>"
            for val in row:
                html += f"<td>{val}</td>"
            html += "</tr>"
        
        html += "</table>"
        return html
    
    def run_report(self):
        """Main report execution function"""
        print(f"Starting {self.report_name}")
        print(f"Query date: {self.query_date}")

        self.sf_obj.log_audit_in_db(log_msg=f"Starting {self.report_name}. Query date: {self.query_date}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
        
        # if not self.connect_to_oracle():
        #     return False
        
        try:
            # Get current period
            period_num, period_year = self.get_current_period()
            if period_num is None or period_year is None:
                print("Could not determine current period")
                self.sf_obj.log_audit_in_db(log_msg=f"Could not determine current period", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                self.okay_to_continue = False
                return False
            
            print(f"Current period: {period_num}-{period_year}")
            
            
            # Build period list
            periods = self.build_period_list(period_num, period_year)
            print(f"Processing {len(periods)} periods")
            self.sf_obj.log_audit_in_db(log_msg=f"Current period: {period_num}-{period_year}. Processing {len(periods)} periods", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            
            # Collect exceptions from all periods
            all_exceptions = []
            for period in periods:
                p_num = period['PERIOD_NUM']
                p_year = period['PERIOD_YEAR']
                print(f"Processing period {p_num}-{p_year}")
                self.sf_obj.log_audit_in_db(log_msg=f"Processing period {p_num}-{p_year}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                
                exceptions = self.get_gl_exceptions_for_period(p_num, p_year)
                if len(exceptions) > 0:
                    all_exceptions.append(exceptions)
            
            if not all_exceptions:
                print("No exceptions found")
                self.sf_obj.log_audit_in_db(log_msg=f"No exceptions found", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                return True
            
            # Combine all exceptions
            combined_df = pd.concat(all_exceptions, ignore_index=True)
            
            # Clean column names (replace underscores with spaces)
            combined_df.columns = [col.replace('_', ' ') for col in combined_df.columns]
            
            # Strip trailing whitespace from string columns
            for col in combined_df.select_dtypes(include=['object']).columns:
                combined_df[col] = combined_df[col].astype(str).str.rstrip()
            
            # Remove reconciled instances where YTD activity is equal
            ytd_net = combined_df.groupby(['PERIOD YEAR', 'GL CODING'])['NET DIFF'].sum().reset_index()
            ytd_net.columns = ['PERIOD YEAR', 'GL CODING', 'sum']
            non_zero_diffs = ytd_net[ytd_net['sum'] != 0][['PERIOD YEAR', 'GL CODING']]
            
            # Filter main data to only include non-zero YTD differences
            combined_df = combined_df.merge(non_zero_diffs, on=['PERIOD YEAR', 'GL CODING'], how='inner')
            
            print(f"Found {len(combined_df)} exceptions after YTD filtering")
            self.sf_obj.log_audit_in_db(log_msg=f"Found {len(combined_df)} exceptions after YTD filtering", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            
            # Check if we have exceptions to report
            is_valid, num_rows, status = self.check_mydata_rows(combined_df, 1)
            
            if is_valid:
                # Define column widths
                col_widths = {
                    'ISSUE': max(40, min(56, combined_df['ISSUE'].str.len().max() + 1)) if len(combined_df) > 0 else 40,
                    'PERIOD YEAR': 8.25,
                    'PERIOD NUM': 8.25,
                    'COMPANY ID': 10.5,
                    'GL ACCOUNT': 10.5,
                    'GL ACCOUNT DESCRIPTION': max(27, min(32, combined_df['GL ACCOUNT DESCRIPTION'].str.len().max())) if len(combined_df) > 0 else 27,
                    'COST CENTER': 9,
                    'GL CODING': 15,
                    'NUM DETAIL INVOICES': 10,
                    'DETAIL PERIOD DEBITS': 10.5,
                    'DETAIL PERIOD CREDITS': 10.5,
                    'DETAIL PERIOD NET': 10.5,
                    'SUMMARY PERIOD DEBITS': 10.5,
                    'SUMMARY PERIOD CREDITS': 10.5,
                    'SUMMARY PERIOD NET': 10.5,
                    'NET DIFF': max(9.5, min(30, combined_df['NET DIFF'].astype(str).str.len().max())) if len(combined_df) > 0 else 9.5
                }
                
                # Create sheet name
                first_period = periods[0]
                last_period = periods[-1]
                sheet_name = f"P{first_period['PERIOD_NUM']:02d}-{first_period['PERIOD_YEAR'] % 100:02d} to P{last_period['PERIOD_NUM']:02d}-{last_period['PERIOD_YEAR'] % 100:02d}"
                
                # Write Excel file
                excel_file = os.path.join(self.rpt_path, self.rpt_filename)
                success = self.write_xlsx(
                    dir_path=self.rpt_path,
                    fname=self.rpt_filename,
                    sheet_name=sheet_name,
                    rpt_df=combined_df,
                    col_names=True,
                    col_widths=col_widths,
                    write_over=True
                )
                
                if success:
                    # Create email body
                    email_cols = ['ISSUE', 'PERIOD YEAR', 'PERIOD NUM', 'GL ACCOUNT DESCRIPTION', 'GL CODING', 'NET DIFF']
                    email_df = combined_df[email_cols].drop_duplicates()
                    
                    if len(email_df) < 40:
                        body_table = f"""<p>The info below contains Oracle GL Summary vs Detail view data that 
                                        appears to not match for {sheet_name}. <b>See attached Excel file for full details.</b></p>
                                        <p>{self.create_html_table(email_df)}</p>"""
                    else:
                        body_table = f"<p><strong><em>There are {len(email_df)} results, see attached file for all.</em></strong></p>"
                    
                    body_text = f"""<p><h2>REPORT: {self.report_name} {sheet_name}</h2></p>
                                   {self.report_criteria}
                                   {body_table}
                                   <br/>
                                   {self.norm_sig}"""
                    
                    # Send email
                    email_client.send_email(
                        recipient=self.norm_recip,
                        subject=self.report_name,
                        body=body_text,
                        attachments=[excel_file] ,
                        override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                        # inline=True,
                        # test=self.testing_emails,
                        # test_recipient=self.test_recip
                    )
                    
                    print("Report completed successfully")
                    self.sf_obj.log_audit_in_db(log_msg=f"Report completed successfully", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                    return True
                else:
                    print("Failed to write Excel file")
                    self.sf_obj.log_audit_in_db(log_msg=f"Failed to write Excel file", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    return False
            else:
                print(f"No valid exceptions found: {status}")
                self.sf_obj.log_audit_in_db(log_msg=f"No valid exceptions found: {status}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
        
        except Exception as e:
            print(f"Error running report: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error running report: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return False
        
        # finally:
        #     self.disconnect_oracle()
        
        return True


def main():
    """Main execution function"""
    report = GLSummaryDetailsReport()
    success = report.run_report()
    
    if success:
        print("Report execution completed successfully")
        report.sf_obj.log_audit_in_db(log_msg=f"Report execution completed successfully", process_type=report.report_name, script_file_name=__file__, log_type='Info')
        return 0
    else:
        print("Report execution failed")
        report.sf_obj.log_audit_in_db(log_msg=f"Report execution failed", process_type=report.report_name, script_file_name=__file__, log_type='Error')
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 