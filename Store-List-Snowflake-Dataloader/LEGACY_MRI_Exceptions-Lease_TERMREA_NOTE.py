# Converted from  R to Python: 5/28/2025
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# import base64
# from email.mime.text import MIMEText
# from email.mime.multipart import MIMEMultipart
# from email.mime.application import MIMEApplication
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
import warnings
import libs.snowflake_helper as sf
import libs.email_client as email_client

warnings.filterwarnings('ignore')

# dir_path = os.path.dirname(os.path.realpath(__file__))
dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
csm_db = os.environ["DATABASE_CSM_DATABASE"]

# Configuration
TESTING_EMAILS = False
VERSION = "20240215"
QUERY_DATE = datetime.now().strftime("%d-%b-%y")
OKAY_TO_CONTINUE = True
SCRIPT_FOLDER = "LEGACY_MRI_Exceptions-Leases"
REPORT_NAME = "MRI Lease Exceptions - Non-conforming TERMREA Notes"
REPORT_FILENAME = "MRI_Lease_TERMREA_Notes.xlsx"

# Parameters
VACATE_MONTHS = 12
STOPBILL_MONTHS = 2
TERMREA_MONTHS = 1
ALLOWED_NOTES = [
    'Business Fail',
    'Clerical/New Deal',
    'Early Termination - LL Right (14.6)',
    'Early Termination-TT Right',
    'HRG Decision',
    'LL Decision',
    'Natural Expiration',
    'Never Commenced',
    'Seasonal',
    'Sold',
    'Termination-Buyout',
    'Termination-Contingency'
]

# Email configuration
NORMAL_RECIPIENTS = ["<EMAIL>"]
WARNING_RECIPIENTS = ["<EMAIL>"]
TEST_RECIPIENTS = ["<EMAIL>"]
TEST_CC_RECIPIENTS = ["<EMAIL>"]


REPORT_PATH = f"{dir_path}/reports/LEGACY_MRI_Exceptions-Lease_TERMREA_NOTE"


# Gmail configuration
GMAIL_AUTH_EMAIL = "<EMAIL>"
GMAIL_REPLY_TO = "<EMAIL>"


def write_excel(dirpath, fname, sname, df, colnames=True, colwidths=None, writeover=True):
    """Write DataFrame to Excel file with formatting."""
    my_fn = os.path.join(dirpath, fname)
    
    if not os.path.exists(dirpath):
        os.makedirs(dirpath)
    
    # Create Excel writer
    writer = pd.ExcelWriter(my_fn, engine='openpyxl')
    
    # Write DataFrame to Excel
    df.to_excel(writer, sheet_name=sname, index=False, header=colnames)
    
    # Get workbook and worksheet
    workbook = writer.book
    worksheet = writer.sheets[sname]
    
    # Apply header style
    header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
    header_font = Font(name="Arial Narrow", size=12, bold=True)
    
    for cell in worksheet[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Set column widths
    if colwidths is not None:
        for col_name, width in colwidths.items():
            col_idx = df.columns.get_loc(col_name) + 1
            worksheet.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
    
    # Freeze pane
    worksheet.freeze_panes = 'A2'
    
    # Save workbook
    try:
        writer.close()
    except Exception as e:
        # Handle file saving error
        error_msg = f"Error saving file {my_fn}: {str(e)}"
        email_client.send_email(
            recipient=WARNING_RECIPIENTS,
            subject=f"{REPORT_NAME} : REPORT FILE SAVING ERROR",
            body=f"<p>This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> "
                 f"during the <b>{REPORT_NAME}</b> routine.<br/><br/>{my_fn}<br/><br/>"
                 f"Error: {error_msg}<br/><br/>The routine should continue without saving this file.<br/> <br/></p>"
        )

def check_dataframe_rows(df, min_rows, report_name=None):
    """Check if DataFrame has sufficient rows."""
    if isinstance(df, pd.DataFrame):
        if len(df) >= min_rows:
            error_status = f"{report_name}: OKAY"
            temp_nrow = len(df)
            temp_bool = True
        else:
            temp_bool = False
            temp_nrow = len(df)
            error_status = f"{report_name}: INCOMPLETE"
    else:
        temp_bool = False
        temp_nrow = 0
        error_status = f"{report_name}: ERROR"
    
    return temp_bool, temp_nrow, error_status

def main():
    """Main function to execute the report generation."""
    if not OKAY_TO_CONTINUE:
        return
    

    
    sf_obj = sf.SnowflakeHelper()
    conn = sf_obj.conn

    # Set timezone
    conn.cursor().execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
    
    # Build query
    query_notes = "', '".join(ALLOWED_NOTES)
    query = f"""
        SELECT
            LEAS.BLDGID,
            LEAS.SUITID,
            MNGR.MNGRNAME AS "Property Manager",
            LEAS.LEASID,
            RTRIM(LEAS.OCCPNAME) AS "Occupant Name",
            LEAS.GENCODE,
            CL.CODEDESC AS "Occupant Status",
            TO_DATE(LEAS.STOPBILLDATE) AS "Stop Billing Date",
            TO_DATE(LEAS.VACATE) as "Vacate Date",
            TO_DATE(LEAS.EXPIR) as "Expire Date",
            IFNULL(TO_CHAR(TERMREA_NOTE.NOTEDATE,'MM/dd/yyyy'),'') AS "NOTE DATE",
            IFNULL(TO_CHAR(TERMREA_NOTE.LASTDATE,'MM/dd/yyyy'),'') AS "NOTE UPDATED",
            'TERMREA' AS "Note Ref",
            (SELECT RTYP.DESCRPTN FROM MRI.RTYP WHERE RTYPID = 'TERMREA') AS "Reference",
            IFNULL(RTRIM(TERMREA_NOTE.NOTETEXT),'(No TERMREA note)') as "TERMREA Note Text"
        FROM MRI.LEAS
        LEFT JOIN (
            SELECT *
            FROM MRI.NOTE
            WHERE (NOTE.REF1 = 'TERMREA' OR NOTE.REF2 = 'TERMREA')
        ) TERMREA_NOTE
        ON LEAS.LEASID = TERMREA_NOTE.LEASID
        AND LEAS.BLDGID = TERMREA_NOTE.BLDGID
        LEFT JOIN MRI.CODELIST CL
        ON LEAS.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
        LEFT JOIN MRI.BLDG
        ON LEAS.BLDGID = BLDG.BLDGID
        LEFT JOIN MRI.ENTITY
        ON BLDG.ENTITYID = ENTITY.ENTITYID
        LEFT JOIN MRI.MNGR
        ON BLDG.MNGRID = MNGR.MNGRID
        LEFT JOIN (
            select
            leas.BLDGID,
            leas.MOCCPID,
            leas.OCCPNAME,
            GENCODE,
            GENERATION,
            LEAS.STOPBILLDATE,
            LEAS.VACATE
            from MRI.leas
        ) OTHER_LEASES
        ON LEAS.BLDGID = OTHER_LEASES.BLDGID
        AND LEAS.MOCCPID = OTHER_LEASES.MOCCPID
        AND (
            (LEAS.STOPBILLDATE < OTHER_LEASES.STOPBILLDATE OR OTHER_LEASES.STOPBILLDATE is NULL)
            OR
            (LEAS.VACATE < OTHER_LEASES.VACATE OR OTHER_LEASES.VACATE is NULL)
        )
        AND LEAS.GENERATION < OTHER_LEASES.GENERATION
        WHERE (
            TERMREA_NOTE.NOTETEXT NOT IN ('{query_notes}')
            AND
            TERMREA_NOTE.LASTDATE >= dateadd(month, -{TERMREA_MONTHS}, CURRENT_DATE)
        )
        OR (
            TERMREA_NOTE.NOTETEXT IS NULL
            AND
            (LEAS.STOPBILLDATE >= dateadd(month, -{STOPBILL_MONTHS}, CURRENT_DATE)
            or
            LEAS.VACATE >= dateadd(month, -{VACATE_MONTHS}, CURRENT_DATE)
            )
            AND
            LEAS.OCCPSTAT = 'I'
        )
        AND OTHER_LEASES.MOCCPID IS NULL
    """
    
    # Execute query and get results
    df = pd.read_sql(query, conn)
    
    # Clean up data
    # df = df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)
    
    # Check if we have results
    df_status = check_dataframe_rows(df, min_rows=1, report_name=REPORT_NAME)
    
    if df_status[0]:
        # Define column widths
        col_widths = {
            "BLDGID": 8.5,
            "SUITID": 9.5,
            "Property Manager": 18,
            "LEASID": 10,
            "Occupant Name": min(60, max(df["Occupant Name"].str.len().max(), 30)),
            "GENCODE": 11,
            "Occupant Status": 12,
            "Stop Billing Date": 12,
            "Vacate Date": 12,
            "Expire Date": 12,
            "NOTE DATE": 12,
            "NOTE UPDATED": 12,
            "Note Ref": 11,
            "Reference": 22,
            "TERMREA Note Text": min(52, max(df["TERMREA Note Text"].str.len().max(), 24))
        }
        
        # Write to Excel
        write_excel(
            dirpath=REPORT_PATH,
            fname=REPORT_FILENAME,
            sname=QUERY_DATE,
            df=df,
            colnames=True,
            colwidths=col_widths,
            writeover=True
        )
        
        # Prepare email
        email_cols = [0, 2, 3, 4, 14]  # BLDGID, Property Manager, LEASID, Occupant Name, TERMREA Note Text
        email_df = df.iloc[:, email_cols].drop_duplicates()
        
        # Format dates
        for col in df.select_dtypes(include=['datetime64']).columns:
            email_df[col] = email_df[col].dt.strftime('%m/%d/%y')
        
        # Create email body
        if len(email_df) < 31:
            body_table = email_df.to_html(index=False, border=2)
            body_text = f"""
                <p>The info below contains MRI data (from yesterday) that appears to be an exception. 
                <b>See attached Excel file for full details.</b></p>
                <p>{body_table}</p>
            """
        else:
            body_text = f"""
                <p><strong><em>There are {len(email_df)} results, see attached file for all.</em></strong></p>
            """
        
        # Add report criteria
        report_criteria = f"""
            <p><b>Criteria for inclusion in the report:</b>
            <ul>
                <li>Missing 'TERMREA' note</li>
                <li>'TERMREA' note present, but text is not <strong>EXACTLY</strong> one of the following...
                <ul>{''.join(f'<li>{note}</li>' for note in ALLOWED_NOTES)}</ul>
                </li>
            </ul></p>
            <p><em>(missing notes included when VACATE in last {VACATE_MONTHS} month(s),
            STOP BILLING date in last {STOPBILL_MONTHS} month(s),
            or non-conforming TERMREA notes when edited date in last {TERMREA_MONTHS} month(s))</em></p>
        """
        
        # Send email
        email_client.send_email(
            recipient=NORMAL_RECIPIENTS,
            subject=REPORT_NAME,
            body=f"<h2>{REPORT_NAME}</h2>{report_criteria}{body_text}",
            attachments=[os.path.join(REPORT_PATH, REPORT_FILENAME)],
            test=TESTING_EMAILS,
            test_recipient=TEST_RECIPIENTS,
            replyto=GMAIL_REPLY_TO
        )
    
    # Close Snowflake connection
    conn.close()

if __name__ == "__main__":
    main() 