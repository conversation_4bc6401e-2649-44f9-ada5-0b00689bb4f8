"""
LEGACY MRI Exceptions - Owner-Landlord
Python conversion of R script for finding and reporting MRI ownership exceptions.

Version: 20240110 (converted from R)
"""

import pandas as pd

import os
import sys
from datetime import datetime, date
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo
import re

from typing import List, Optional, Dict, Any, Tuple

import libs.snowflake_helper as sf
import libs.email_client as email_client

OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration
TESTING_EMAILS = False  # NORMAL, should normally be disabled in PRODUCTION
# TESTING_EMAILS = True  # Uncomment for testing

class MRIOwnershipExceptions:
    def __init__(self):
        self.sf_obj = sf.SnowflakeHelper()
        self.sf_connection = self.sf_obj.conn

        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.okay_to_continue = True
        
        # Report configuration
        self.script_folder = "LEGACY_MRI_Exceptions-Owner-Landlord"
        self.report_name = "MRI Owner-Landlord Exceptions"
        self.report_filename = "MRI_Ownership-Landlord_Exceptions.xlsx"
        
        # Report criteria
        self.report_criteria = """<p><b>Criteria for exceptions:</b><ul>
            <li>Owner ID is NULL (missing)</li>
            <li>Landlord ID is NULL</li>
            <li>Landlord ID <> Owner ID</li>
            <li>Owner NAME is NULL</li>
            <li>Landlord NAME is NULL</li>
            <li>Landlord NAME <> Owner NAME</li>
            <li>Rented location where Owner ID <> "RENTED"</li>
            <li>Ownership PCT > 100%</li>
            <li>Building ID is <b>NOT</b> 'ACQUIS'</li>
            </ul></p>"""
        
        # Initialize paths and computer settings
        self._setup_paths()
        
        # Email configuration
        self._setup_email_config()
        
        # Snowflake configuration
        # self._setup_snowflake_config()
        
        # Set up logging
        # self._setup_logging()
        
        # print(f"Routine Starting: {self.report_name}")
        self.sf_obj.log_audit_in_db(log_msg=f"Routine Starting: {self.report_name}", process_type=self.report_name, script_file_name=__file__, log_type='Info')

    def _setup_paths(self):
        """Set up file paths based on computer environment"""

        self.testing_pc = False
        # self.main_path = tableau_path
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])

        self.log_path = self.main_path / self.script_folder
        self.report_path = self.log_path

    def _setup_email_config(self):
        """Set up email configuration"""
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Recipients
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>", "<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Signatures
        self.norm_sig = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
            Sr. Analytics Mgr.<br/>
            <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
            2500 Lehigh Ave.<br/>
            Glenview, IL 60026<br/>
            Ph: 847/904-9043<br/>"""
        
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"

    def get_exceptions_query(self) -> str:
        """Return the SQL query for finding ownership exceptions"""
        return """
        SELECT /* Snowflake version */
        	array_to_string(
        		array_construct_compact(
        			CASE WHEN (rtrim(LLRD.LLRDID) IS NOT NULL AND trim(MYGOWN.OWNERID) IS NULL) THEN 'Owner ID is NULL' END,
	        	CASE WHEN (rtrim(LLRD.LLRDID) IS NULL AND trim(MYGOWN.OWNERID) IS NOT NULL) THEN 'Landlord ID is NULL' END,
	        	CASE WHEN (rtrim(LLRD.LLRDID) <> trim(MYGOWN.OWNERID)) THEN 'Landlord ID <> Owner ID' END,
	        	CASE WHEN (rtrim(LLRD.LLRDNAME) IS NOT NULL AND trim(GNAM.NAME) IS NULL) THEN 'Owner NAME is NULL' END,
	            CASE WHEN (rtrim(LLRD.LLRDNAME) IS NULL AND trim(GNAM.NAME) IS NOT NULL) THEN 'Landlord NAME is NULL' END,
	        	CASE WHEN (rtrim(LLRD.LLRDNAME) <> trim(GNAM.NAME)) THEN 'Landlord NAME <> Owner NAME' END,
	        	CASE WHEN (TRY_CAST(BLDG.BLDGID AS INT) IS NULL AND (Rtrim(MYGOWN.OWNERID) IS NULL OR Rtrim(MYGOWN.OWNERID) <> 'RENTED') AND BLDG.BLDGID <> 'ROVER' )
	        		THEN 'Rented location where Owner ID <> "RENTED"' END,
	        	CASE WHEN (MYGOWN.PCT + IFNULL(ALTGOWN.ALT_PCT,0) > 100) THEN 'Ownership PCT > 100%' END
        		)
        		, '; ') AS ISSUE
        ,	BLDG.BLDGID AS BLDGID
        ,	CASE 
        		WHEN Rtrim(LLRD.LLRDID) IS NOT NULL AND Rtrim(MYGOWN.OWNERID) IS NULL THEN 'Yes'
        		WHEN Rtrim(LLRD.LLRDID) IS NULL AND Rtrim(MYGOWN.OWNERID) IS NOT NULL THEN 'Yes'
        		WHEN Rtrim(LLRD.LLRDID) <> Rtrim(MYGOWN.OWNERID) THEN 'Yes'
        		ELSE 'No' END AS "Landlord ID different than Owner ID"
        ,	CASE 
        		WHEN Rtrim(LLRD.LLRDNAME) IS NOT NULL AND Rtrim(GNAM.NAME) IS NULL THEN 'Yes'
        		WHEN Rtrim(LLRD.LLRDNAME) IS NULL AND Rtrim(GNAM.NAME) IS NOT NULL THEN 'Yes'
        		WHEN Rtrim(LLRD.LLRDNAME) <> Rtrim(GNAM.NAME) THEN 'Yes'
        		ELSE 'No' END AS "Landlord NAME different than Owner NAME"
        ,	LLRD.LLRDID AS "Landlord ID"
        ,	Rtrim(LLRD.LLRDNAME) AS "Landlord NAME"
        ,	Rtrim(LLRD.CMPYID) AS "Landlord CMPYID"
        ,	Rtrim(MYGOWN.OWNERID) AS "Owner ID (Entity)"
        ,	Rtrim(GNAM.NAME) AS "Owner NAME"
        ,	MYGOWN.PCT AS "Owner PCT"
        ,	MYGOWN.PRIMARYOWN AS "Owner PRIMARY"
        ,	MYGOWN.BEGPD AS "Owner BEGPD"
        ,	MYGOWN.ENDPD AS "Owner ENDPD"
        ,	ALTGOWN.MULTI_OWN_PCT AS "Multiple Owners-PCT"
        ,	MYGOWN.PCT + IFNULL(ALTGOWN.ALT_PCT,0) AS "Total Owned PCT"
        FROM MRI.BLDG
        LEFT JOIN MRI.LLRD ON BLDG.LLRDID = LLRD.LLRDID
        LEFT JOIN MRI.ENTITY ON BLDG.ENTITYID = ENTITY.ENTITYID
        
        LEFT JOIN
        (/* PRIMARY OWNER */
        	SELECT ENTITYID
        	,	OWNERID
        	,	BEGPD
        	,	ENDPD
        	,	PRIMARYOWN
        	,	PCT
        	,	LASTDATE
        	FROM
        	(
        	  SELECT O.ENTITYID
        	  ,	O.OWNERID
        	  ,	O.BEGPD
        	  ,	O.ENDPD
        	  ,	O.PRIMARYOWN
        	  ,	O.PERCENT AS PCT
        	  ,	O.LASTDATE
        	  ,	RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.BEGPD, O.LASTDATE DESC) AS RANK
        	  FROM MRI.GOWN O
        	  WHERE (O.BEGPD = 
        		  (
        			  SELECT MAX(I.BEGPD)
        			  FROM MRI.GOWN I
        			  WHERE I.ENTITYID = O.ENTITYID
        				  AND I.BEGPD <= TO_CHAR(CURRENT_DATE, 'yyyyMM')
        
        		  )
        	  OR (
        			  SELECT MAX(I.BEGPD)
        			  FROM MRI.GOWN I
        			  WHERE I.ENTITYID = O.ENTITYID
        		  ) IS NULL
        	  )
        	  AND (O.ENDPD IS NULL OR O.ENDPD >= TO_CHAR(CURRENT_DATE, 'yyyyMM'))
        	) RANKED
        	WHERE RANK = 1
        ) MYGOWN
        ON ENTITY.ENTITYID = MYGOWN.ENTITYID
        LEFT JOIN MRI.GNAM ON MYGOWN.OWNERID = GNAM.OWNERID
        
        LEFT JOIN
        (/* ALTERNATE MULTIPLE OWNERS */
        	SELECT ENTITYID
        	,	LISTAGG( CONCAT(RANKED.OWNERID,' - ',RANKED.PCT,'PCT'),'; ') AS MULTI_OWN_PCT
        	,	SUM(RANKED.PCT) as ALT_PCT
        	FROM
        	(
        	  SELECT O.ENTITYID
        	  ,	O.OWNERID
        	  ,	O.BEGPD
        	  ,	O.ENDPD
        	  ,	O.PRIMARYOWN
        	  ,	O.PERCENT AS PCT
        	  ,	O.LASTDATE
        	  ,	RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.BEGPD, O.LASTDATE DESC) AS RANK
        	  FROM MRI.GOWN O
        	  WHERE (O.BEGPD = 
        		  (
        			  SELECT MAX(I.BEGPD)
        			  FROM MRI.GOWN I
        			  WHERE I.ENTITYID = O.ENTITYID
        				  AND I.BEGPD <= TO_CHAR(CURRENT_DATE, 'yyyyMM')
        
        		  )
        	  OR (
        			  SELECT MAX(I.BEGPD)
        			  FROM MRI.GOWN I
        			  WHERE I.ENTITYID = O.ENTITYID
        		  ) IS NULL
        	  )
        	  AND (O.ENDPD IS NULL OR O.ENDPD >= TO_CHAR(CURRENT_DATE, 'yyyyMM'))
        	) RANKED
        	WHERE RANK >= 1
        	GROUP BY ENTITYID
        	HAVING COUNT(RANK) > 1
        ) ALTGOWN
        ON ENTITY.ENTITYID = ALTGOWN.ENTITYID
        
        WHERE (BLDG.INACTIVE <> 'Y' OR BLDG.INACTIVE IS NULL)
          AND RTRIM(BLDG.BLDGID) not in ('ACQUIS','HV3RD','HVCORP')
        	AND
        	( /* ID or NAME mismatches */
        		(Rtrim(LLRD.LLRDID) IS NOT NULL AND trim(MYGOWN.OWNERID) IS NULL)
        		OR
        		(Rtrim(LLRD.LLRDID) IS NULL AND trim(MYGOWN.OWNERID) IS NOT NULL)
        		OR
        		(Rtrim(LLRD.LLRDID) <> trim(MYGOWN.OWNERID))
        		OR
        		(Rtrim(LLRD.LLRDNAME) IS NOT NULL AND trim(GNAM.NAME) IS NULL)
        		OR
        		(Rtrim(LLRD.LLRDNAME) IS NULL AND trim(GNAM.NAME) IS NOT NULL)
        		OR
        		(Rtrim(LLRD.LLRDNAME) <> trim(GNAM.NAME))
        		OR
        		/* ALPHA BLDGID and Owner <> 'RENTED' */
        		(TRY_CAST(BLDG.BLDGID AS INT) IS NULL AND (Rtrim(MYGOWN.OWNERID) IS NULL OR Rtrim(MYGOWN.OWNERID) <> 'RENTED') AND BLDG.BLDGID <> 'ROVER' )
        		OR
        		(MYGOWN.PCT + IFNULL(ALTGOWN.ALT_PCT,0) > 100)
        	)
        	AND UPPER(BLDG.BLDGID) NOT IN ('ROVER','OHCANN')
        ORDER BY ISSUE, BLDGID
        """

    def check_dataframe_rows(self, df: pd.DataFrame, min_rows: int, report_name: str = None) -> Tuple[bool, int, str]:
        """Check if dataframe meets minimum row requirements"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_rows:
                error_status = f"{report_name}: OKAY"
                return True, len(df), error_status
            else:
                error_status = f"{report_name}: INCOMPLETE"
                return False, len(df), error_status
        else:
            error_status = f"{report_name}: ERROR"
            return False, 0, error_status

    def create_excel_file(self, df: pd.DataFrame, filename: str, sheet_name: str = None) -> str:
        """Create formatted Excel file from dataframe"""
        if sheet_name is None:
            sheet_name = self.query_date
            
        file_path = self.report_path / filename
        
        # Create directory if it doesn't exist
        self.report_path.mkdir(parents=True, exist_ok=True)
        
        # Column widths mapping
        col_widths = {
            "ISSUE": 31,
            "BLDGID": 8.5,
            "Landlord NAME": 34,
            "Landlord CMPYID": 8.5,
            "Owner NAME": 34,
            "Owner PCT": 7.5,
            "Owner PRIMARY": 9.5,
            "Owner BEGPD": 8.5,
            "Owner ENDPD": 8.5,
            "Multiple Owners-PCT": 17.5,
            "Total Owned PCT": 7.5
        }
        
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # Get the workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets[sheet_name]
                
                # Style the headers
                header_font = Font(name='Arial Narrow', size=12, bold=True, color='000000')
                header_fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                
                # Set column widths
                for col_num, column in enumerate(df.columns, 1):
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    if column in col_widths:
                        worksheet.column_dimensions[col_letter].width = col_widths[column]
                
                # Freeze panes
                worksheet.freeze_panes = 'A2'
                
                # Add autofilter
                worksheet.auto_filter.ref = worksheet.dimensions
            
            # self.logger.info(f"Excel file created successfully: {file_path}")
            self.sf_obj.log_audit_in_db(log_msg=f"Excel file created successfully: {file_path}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            return str(file_path)
            
        except Exception as e:
            # self.logger.error(f"Failed to create Excel file: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel file: {str(e)}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            raise


    def create_html_table(self, df: pd.DataFrame, max_rows: int = 30) -> str:
        """Create HTML table from dataframe for email body"""
        if len(df) <= max_rows:
            return df.to_html(table_id="exceptions_table", 
                            classes="table table-striped", 
                            border=2, 
                            index=False,
                            escape=False)
        else:
            return f"<p>There are {len(df)} results, see attached file for all.</p>"

    def run(self):
        """Main execution method"""
        if not self.okay_to_continue:
            # self.logger.error("Script configuration error - cannot continue")
            self.sf_obj.log_audit_in_db(log_msg="Script configuration error - cannot continue", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return
        
        try:
            # Connect to Snowflake
            # conn = self.connect_to_snowflake()
            # conn = self.sf_connection
            
            # Execute query
            # self.logger.info("Executing exceptions query...")
            self.sf_obj.log_audit_in_db(log_msg="Executing exceptions query...", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            query = self.get_exceptions_query()
            # print(f"query: {query}")
            # exit(1)
            df = pd.read_sql(query, self.sf_connection)
            
            # Close connection
            # conn.close()
            
            # Check results
            data_status = self.check_dataframe_rows(df, 1, self.report_name)
            
            if data_status[0]:  # Exceptions found
                # self.logger.info(f"Found {data_status[1]} exceptions")
                self.sf_obj.log_audit_in_db(log_msg=f"Found {data_status[1]} exceptions", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                
                # Create Excel file
                excel_file_path = self.create_excel_file(df, self.report_filename)
                
                # Create email body
                email_df = df[['ISSUE', 'BLDGID']].copy()
                body_table = self.create_html_table(email_df)
                
                body_text = f"""
                <p><b>{self.report_name}</b></p>
                {self.report_criteria}
                <p>The info below contains MRI data from yesterday that may need updating. 
                <b>See attached Excel file for more details.</b></p>
                {body_table}
                <br/>
                {self.norm_sig}
                """
                
                # Send email
                email_client.send_email(
                    recipient=self.norm_recip,
                    subject=self.report_name,
                    body=body_text,
                    attachments=[excel_file_path],
                    # sender=self.gmail_auth_email,
                    replyto=self.gmail_reply_to,
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
                # if success:
                #     # self.logger.info("Report completed successfully")
                self.sf_obj.log_audit_in_db(log_msg="Report completed successfully", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                # else:
                #     # self.logger.error("Failed to send email")
                #     self.sf_obj.log_audit_in_db(log_msg="Failed to send email", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    
            else:
                # self.logger.info("No exceptions found - no report to send")
                self.sf_obj.log_audit_in_db(log_msg="No exceptions found - no report to send", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                
        except Exception as e:
            # self.logger.error(f"Script execution failed: {str(e)}")
            self.sf_obj.log_audit_in_db(log_msg=f"Script execution failed: {str(e)}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            
            # Send error notification
            error_body = f"""
            <p>An error occurred during the {self.report_name} routine:</p>
            <p><b>Error:</b> {str(e)}</p>
            {self.warn_sig}
            """
            
            email_client.send_email(
                recipient=self.warn_recip,
                subject=f"{self.report_name}: ERROR",
                body=error_body,
                # sender=self.gmail_auth_email,
                replyto=self.gmail_reply_to,
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
            
            raise


def main():
    """Main entry point"""
    try:
        processor = MRIOwnershipExceptions()
        processor.run()
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 