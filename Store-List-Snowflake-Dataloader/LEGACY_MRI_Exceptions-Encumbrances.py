#!/usr/bin/env python3
"""
LEGACY MRI Encumbrance Exceptions Report
Converted from R to Python

Version: 20250124
Author: Converted from R script

Purpose: Generate MRI Encumbrance Exception reports and email them
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

from pathlib import Path

from snowflake.connector import DictCursor
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.filters import AutoFilter
from openpyxl.worksheet.views import Pane

import re
import warnings

import libs.snowflake_helper as sf
import libs.email_client as email_client

OVERRIDE_EMAIL_RECIPIENTS = False

warnings.filterwarnings('ignore')

# Configuration
TESTING_EMAILS = False  # Set to True for testing
SCRIPT_VERSION = "20250124"

class MRIEncumbranceExceptions:
    def __init__(self):

        self.sf_obj = sf.SnowflakeHelper()
        self.sf_conn = self.sf_obj.conn

        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.okay_to_continue = True
        self.script_folder = "LEGACY_MRI_Exceptions-Encumbrances"
        self.report_name = "MRI Encumbrance Exceptions"
        self.report_filename = "MRI_Encumbrance_Exception_Details.xlsx"
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S%Z")
        
        # Report criteria
        self.report_criteria = """
        <p><b>Criteria for inclusion in the report:</b> (note any building with $0 owed is EXCLUDED)<ul>
        <li>Current loan has multiple CURRENT OWED AMOUNTS</li>
        <li>Current loan has multiple ORIGINAL OWED AMOUNTS</li>
        <li>Current loan has multiple DUE DATES</li>
        <li>Loan DUE DATE in past, but CURRENT OWED > $0</li>
        <li>Inactive bldg has loan where CURRENT OWED > $0</li>
        <li>Current loan has multiple INTEREST RATES</li>
        <li>Current loan has multiple DEBT TYPES</li>
        </ul></p>
        """
        
        # Initialize paths and computer detection
        self.setup_paths()
        
        # Email configuration
        self.setup_email_config()
        
        # Database configuration
        # self.setup_database_config()
        
        # print(f"Beginning '{self.report_name}' routine")
        self.sf_obj.log_audit_in_db(log_msg=f"Beginning '{self.report_name}' routine", process_type=self.report_name, script_file_name=__file__, log_type='Info')
        # time.sleep(1.5)
    
    def setup_paths(self):
        """Setup file paths based on computer name"""
        # central_path = Path("//*************/public/steveo/R Stuff/ReportFiles")
        # tableau_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        
        # test_computers = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
        # prod_computers = ["DESKTOP-TABLEAU"]
        
        # computer_name = os.environ.get('COMPUTERNAME', socket.gethostname())
        
        # if computer_name in test_computers:
        #     self.testing_pc = True
        #     self.main_path = central_path
        # else:
        self.testing_pc = False
        # self.main_path = tableau_path
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
        
        self.log_path = self.main_path / self.script_folder
        self.report_path = self.log_path
        self.hv_sig_path = self.main_path / "HTML_signatures.csv"
        
        # Create directories if they don't exist
        self.log_path.mkdir(parents=True, exist_ok=True)
        self.report_path.mkdir(parents=True, exist_ok=True)
    
    def setup_email_config(self):
        """Setup email configuration"""
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Default signature
        self.norm_sig = """
        <b><span style='font-weight:bold'>Steve Olson</span></b><br/>
        Sr. Analytics Mgr.<br/>
        <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
        2500 Lehigh Ave.<br/>
        Glenview, IL 60026<br/>
        Ph: 847/904-9043<br/>
        """
        
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        
        # Load HTML signatures if available
        self.load_html_signatures()
    
    def load_html_signatures(self):
        """Load HTML signatures from CSV file"""
        try:
            if self.hv_sig_path.exists():
                signatures_df = pd.read_csv(self.hv_sig_path)
                lcp_sig = signatures_df[signatures_df['Desc'] == 'LCP Reporting']
                if not lcp_sig.empty:
                    self.norm_sig = lcp_sig.iloc[0]['HTML']
        except Exception as e:
            print(f"Warning: Could not load HTML signatures: {e}")
    
    
    
    def check_dataframe_rows(self, df, min_rows=1, report_name=None):
        """Check if dataframe has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_rows:
                return True, len(df), f"{report_name}: OKAY"
            else:
                return False, len(df), f"{report_name}: INCOMPLETE"
        else:
            return False, 0, f"{report_name}: ERROR"
    
    def write_excel_report(self, df, filepath, sheet_name="Sheet1", col_widths=None):
        """Write dataframe to Excel with formatting"""
        try:
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data to worksheet
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Format header row
            header_font = Font(name='Arial Narrow', size=12, bold=True)
            header_fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # Set column widths
            if col_widths:
                for col_name, width in col_widths.items():
                    col_idx = None
                    for idx, cell in enumerate(ws[1], 1):
                        if cell.value == col_name:
                            col_idx = idx
                            break
                    if col_idx:
                        ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
            
            # Add autofilter
            ws.auto_filter = AutoFilter(ref=f"A1:{openpyxl.utils.get_column_letter(len(df.columns))}{len(df)+1}")
            
            # Freeze panes
            ws.freeze_panes = ws['A2']
            
            # Save workbook
            wb.save(filepath)
            return True
            
        except Exception as e:
            # print(f"Error writing Excel file: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error writing Excel file: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return False
    
    
    
    def get_exceptions_query(self):
        """Return the SQL query for finding encumbrance exceptions"""
        return """
        SELECT CMPY_ID AS CMPY_ID
        ,	LOANID AS LOAN_ID
        ,	ISSUE
        ,	ISSUE_COUNT
        ,	ISSUE_VALUES
        ,	BLDGID
        ,	BLDG_VALUES
        FROM
        (
        	SELECT /* Rows where Multiple CURRENTOWED values */
        		ENCUMB.CMPY_ID
        	,	ENCUMB.LOANID
        	,	'Multiple Current Owed Values' AS ISSUE
        	,	MCO.ISSUE_COUNT
        	,	MCO.ISSUE_VALUES
        	,	ENCUMB.TABLEKEY AS BLDGID
        	,	TO_VARCHAR(ENCUMB.CURRENTOWED,'999,999,999,990.00') AS BLDG_VALUES
        	FROM MRI.ENCUMB
        	JOIN
        	(
        		SELECT /* Multiple CURRENTOWED values */
        			CMPY_ID
        		,	LOANID
        		,	'Multiple Current Owed Values' AS ISSUE
        		,	COUNT(*) as ISSUE_COUNT
        		,	LISTAGG(TO_VARCHAR(CURRENTOWED,'999,999,999,990.00'), '; ') WITHIN GROUP (ORDER BY CURRENTOWED ASC) AS ISSUE_VALUES
        		FROM
        		(
        			SELECT DISTINCT
        				CMPY_ID
        			,	LOANID
        			,	CURRENTOWED
        			FROM MRI.ENCUMB ie
        			WHERE ie.TABLEID = 'BLDG'
        				 and ie.CURRENTOWED > 0
        			GROUP BY 
        				CMPY_ID
        			,	LOANID
        			,	CURRENTOWED
        		) CNT
        		GROUP BY
        			CNT.CMPY_ID
        		,	CNT.LOANID
        		HAVING COUNT(*) > 1
        	) MCO
        	ON ENCUMB.CMPY_ID = MCO.CMPY_ID
        		AND ENCUMB.LOANID = MCO.LOANID

        	UNION ALL

        	SELECT /* Rows where Multiple Original AMOUNT values */
        		ENCUMB.CMPY_ID
        	,	ENCUMB.LOANID
        	,	'Multiple Original Amount Values' AS ISSUE
        	,	MOO.ISSUE_COUNT
        	,	MOO.ISSUE_VALUES
        	,	ENCUMB.TABLEKEY AS BLDGID
        	,	TO_VARCHAR(ENCUMB.AMOUNT,'999,999,999,990.00') AS BLDG_VALUES
        	FROM MRI.ENCUMB
        	JOIN
        	(
        		SELECT /* Multiple Original Amount of Debt values */
        			CMPY_ID
        		,	LOANID
        		,	'Multiple Original Amount Values' AS ISSUE
        		,	COUNT(*) as ISSUE_COUNT
        		,	LISTAGG(TO_VARCHAR(AMOUNT,'999,999,999,990.00'), '; ') WITHIN GROUP (ORDER BY AMOUNT ASC) AS ISSUE_VALUES
        		FROM
        		(
        			SELECT DISTINCT
        				CMPY_ID
        			,	LOANID
        			,	AMOUNT
        			FROM MRI.ENCUMB ie
        			WHERE ie.TABLEID = 'BLDG'
        				and ie.CURRENTOWED > 0
        			GROUP BY 
        				CMPY_ID
        			,	LOANID
        			,	AMOUNT
        		) CNT
        		GROUP BY
        			CNT.CMPY_ID
        		,	CNT.LOANID
        		HAVING COUNT(*) > 1
        	) MOO
        	ON ENCUMB.CMPY_ID = MOO.CMPY_ID
        		AND ENCUMB.LOANID = MOO.LOANID

        	UNION ALL

        	SELECT /* Rows where Multiple DUE DATE values */
        		ENCUMB.CMPY_ID
        	,	ENCUMB.LOANID
        	,	'Multiple Due Date Values' AS ISSUE
        	,	MDD.ISSUE_COUNT
        	,	MDD.ISSUE_VALUES
        	,	ENCUMB.TABLEKEY AS BLDGID
        	,	TO_VARCHAR(ENCUMB.DUEDATE, 'MM/dd/yyyy')  AS BLDG_VALUES
        	FROM MRI.ENCUMB
        	JOIN
        	(
        		SELECT /* Multiple Due Date of Debt values */
        			CMPY_ID
        		,	LOANID
        		,	'Multiple Due Date Values' AS ISSUE
        		,	COUNT(*) as ISSUE_COUNT
        		,	LISTAGG(TO_VARCHAR(DUEDATE, 'MM/dd/yyyy'), '; ') WITHIN GROUP (ORDER BY DUEDATE ASC) AS ISSUE_VALUES
        		FROM
        		(
        			SELECT DISTINCT
        				CMPY_ID
        			,	LOANID
        			,	DUEDATE
        			FROM MRI.ENCUMB ie
        			WHERE ie.TABLEID = 'BLDG'
        				and ie.CURRENTOWED > 0
        			GROUP BY 
        				CMPY_ID
        			,	LOANID
        			,	DUEDATE
        		) CNT
        		GROUP BY
        			CNT.CMPY_ID
        		,	CNT.LOANID
        		HAVING COUNT(*) > 1
        	) MDD
        	ON ENCUMB.CMPY_ID = MDD.CMPY_ID
        		AND ENCUMB.LOANID = MDD.LOANID  
        		
        	UNION ALL

        	SELECT /* Due Date has passed but CURRENTOWED > 0 and BLDGID is active */
        		CMPY_ID
        	,	LOANID
        	,	'Due Date in past but $ OWED > 0' AS ISSUE
        	,	COUNT(*) AS ISSUE_COUNT
        	,	TO_VARCHAR(DUEDATE, 'MM/dd/yyyy')||' $'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS ISSUE_VALUES
        	,	IE.TABLEKEY AS BLDGID
        	,   TO_VARCHAR(DUEDATE, 'MM/dd/yyyy')||' $'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS BLDG_VALUES
        	FROM MRI.ENCUMB ie
        	LEFT JOIN MRI.BLDG B
        	ON IE.TABLEKEY = B.BLDGID
        	WHERE ie.TABLEID = 'BLDG'
        		and ie.CURRENTOWED > 0
        		AND TO_DATE(ie.DUEDATE) < CURRENT_DATE
        		AND IFNULL(B.INACTIVE, 'N') = 'N'
        	GROUP BY 
        		CMPY_ID
        	,	LOANID
        	,	DUEDATE
        	,	CURRENTOWED 
        	,	IE.TABLEKEY

        	UNION ALL

        	SELECT /* CURRENTOWED > 0 and BLDGID is INACTIVE */
        		CMPY_ID
        	,	LOANID
        	,	'Inactive BLDGID, but Current Owed > 0' AS ISSUE
        	,	COUNT(*) AS ISSUE_COUNT
        	,	'$'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS ISSUE_VALUES
        	,	IE.TABLEKEY AS BLDGID
        	,   '$'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS BLDG_VALUES
        	FROM MRI.ENCUMB ie
        	LEFT JOIN MRI.BLDG B
        	ON IE.TABLEKEY = B.BLDGID
        	WHERE ie.TABLEID = 'BLDG'
        		and ie.CURRENTOWED > 0
        		AND IFNULL(B.INACTIVE, 'N') = 'Y'
        	GROUP BY 
        		CMPY_ID
        	,	LOANID
        	,	DUEDATE
        	,	CURRENTOWED 
        	,	IE.TABLEKEY

        	UNION ALL

        	SELECT /* Rows where Multiple Interest Rate values */
        		ENCUMB.CMPY_ID
        	,	ENCUMB.LOANID
        	,	'Multiple Interest Rate Values' AS ISSUE
        	,	MIR.ISSUE_COUNT
        	,	MIR.ISSUE_VALUES
        	,	ENCUMB.TABLEKEY AS BLDGID
        	,	TO_VARCHAR(ENCUMB.RATECUR) AS BLDG_VALUES
        	FROM MRI.ENCUMB
        	JOIN
        	(
        		SELECT /* Multiple Interest Rate values */
        			CMPY_ID
        		,	LOANID
        		,	'Multiple Interest Rate Values' AS ISSUE
        		,	COUNT(*) as ISSUE_COUNT
        		,	LISTAGG(RATECUR, '; ') WITHIN GROUP (ORDER BY RATECUR ASC) AS ISSUE_VALUES
        		FROM
        		(
        			SELECT DISTINCT
        				CMPY_ID
        			,	LOANID
        			,	RATECUR
        			FROM MRI.ENCUMB ie
        			WHERE ie.TABLEID = 'BLDG'
        				and ie.CURRENTOWED > 0
        			GROUP BY 
        				CMPY_ID
        			,	LOANID
        			,	RATECUR
        		) CNT
        		GROUP BY
        			CNT.CMPY_ID
        		,	CNT.LOANID
        		HAVING COUNT(*) > 1
        	) MIR
        	ON ENCUMB.CMPY_ID = MIR.CMPY_ID
        		AND ENCUMB.LOANID = MIR.LOANID
        	
        	UNION ALL
        	
        	SELECT /* Rows where Multiple DEBTTYPE values - added 20230418 */
        		ENCUMB.CMPY_ID
        	,	ENCUMB.LOANID
        	,	'Multiple Debt Type Values' AS ISSUE
        	,	MDEBT.ISSUE_COUNT
        	,	MDEBT.ISSUE_VALUES
        	,	ENCUMB.TABLEKEY AS BLDGID
        	,	RTRIM(DEBTTYPEID) AS BLDG_VALUES
        	FROM MRI.ENCUMB
        	JOIN
        	(
        		SELECT /* Multiple DEBT TYPE values */
        			CMPY_ID
        		,	LOANID
        		,	'Multiple Debt Type Values' AS ISSUE
        		,	COUNT(*) as ISSUE_COUNT
        		,	LISTAGG(RTRIM(DEBTTYPEID), '; ') WITHIN GROUP (ORDER BY DEBTTYPEID ASC) AS ISSUE_VALUES
        		FROM
        		(
        			SELECT DISTINCT
        				CMPY_ID
        			,	LOANID
        			,	DEBTTYPEID
        			FROM MRI.ENCUMB ie
        			WHERE ie.TABLEID = 'BLDG'
        				and ie.CURRENTOWED > 0
        			GROUP BY 
        				CMPY_ID
        			,	LOANID
        			,	DEBTTYPEID
        		) CNT
        		GROUP BY
        			CNT.CMPY_ID
        		,	CNT.LOANID
        		HAVING COUNT(*) > 1
        	) MDEBT
        	ON ENCUMB.CMPY_ID = MDEBT.CMPY_ID
        		AND ENCUMB.LOANID = MDEBT.LOANID
        	
        ) DETAILS
        ORDER BY DETAILS.CMPY_ID
        ,	DETAILS.LOANID
        ,	DETAILS.ISSUE
        ,	DETAILS.BLDG_VALUES
        ,	DETAILS.BLDGID
        """
    
    def format_data_for_email_table(self, df, max_rows=20):
        """Format dataframe for email table display"""
        if len(df) > max_rows:
            return f"<p><strong><em>There are {len(df)} results, see attached file for all.</em></strong></p>"
        
        # Convert dataframe to HTML table
        html_table = df.to_html(index=False, table_id="results_table", 
                               classes="table table-striped table-bordered",
                               escape=False)
        
        # Add some styling
        styled_table = f"""
        <style>
        #results_table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        #results_table th, #results_table td {{
            border: 2px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        #results_table th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        </style>
        {html_table}
        """
        
        return styled_table
    
    def run_report(self):
        """Main method to run the encumbrance exceptions report"""
        if not self.okay_to_continue:
            print("Cannot continue - setup failed")
            return False
        
        try:
            # Connect to Snowflake
            print("Connecting to Snowflake...")
            conn = self.sf_obj.conn # self.get_snowflake_connection()
            if not conn:
                print("Failed to connect to Snowflake")
                return False
            
            # Execute query
            print("Executing exceptions query...")
            query = self.get_exceptions_query()

            # print(f"query: {query}")
            # exit(1)
            
            cursor = conn.cursor(DictCursor)
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Convert to DataFrame
            if results:
                df = pd.DataFrame(results)
                
                # Strip whitespace from string columns
                for col in df.select_dtypes(include=['object']).columns:
                    df[col] = df[col].astype(str).str.strip()
                
                # Replace underscores with spaces in column names
                df.columns = [col.replace('_', ' ') for col in df.columns]
                
                # print(f"Found {len(df)} exceptions")
                self.sf_obj.log_audit_in_db(log_msg=f"Found {len(df)} exceptions", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                
                # Check if we have valid data
                status_ok, num_rows, status_msg = self.check_dataframe_rows(df, min_rows=1, report_name=self.report_name)
                
                if status_ok:
                    # Define column widths for Excel
                    col_widths = {
                        'CMPY ID': 10,
                        'LOAN ID': max(12, min(30, df['LOAN ID'].astype(str).str.len().max() if 'LOAN ID' in df.columns else 12)),
                        'ISSUE': max(25, min(38, df['ISSUE'].astype(str).str.len().max() if 'ISSUE' in df.columns else 25)),
                        'ISSUE COUNT': 8,
                        'ISSUE VALUES': max(24, min(36, df['ISSUE VALUES'].astype(str).str.len().max() if 'ISSUE VALUES' in df.columns else 24)),
                        'BLDGID': 8,
                        'BLDG VALUES': max(11, min(38, df['BLDG VALUES'].astype(str).str.len().max() if 'BLDG VALUES' in df.columns else 11))
                    }
                    
                    # Create Excel file
                    excel_path = self.report_path / self.report_filename
                    sheet_name = self.query_date
                    
                    # print(f"Creating Excel report: {excel_path}")
                    self.sf_obj.log_audit_in_db(log_msg=f"Creating Excel report: {excel_path}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                    if self.write_excel_report(df, excel_path, sheet_name, col_widths):
                        # print("Excel report created successfully")
                        self.sf_obj.log_audit_in_db(log_msg=f"Excel report created successfully: {excel_path}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                        
                        # Prepare email
                        email_df = df[['CMPY ID', 'LOAN ID', 'ISSUE', 'ISSUE COUNT', 'ISSUE VALUES']].drop_duplicates()
                        table_html = self.format_data_for_email_table(email_df)
                        
                        email_body = f"""
                        <p><h2>REPORT: {self.report_name}</h2></p>
                        {self.report_criteria}
                        <p>The info below contains MRI data (from yesterday) that appears to be an exception. 
                        <b>See attached Excel file for full details.</b></p>
                        {table_html}
                        <br/>
                        {self.norm_sig}
                        """
                        
                        # Send email
                        print("Sending email report...")
                        email_client.send_email(
                            recipient=self.norm_recip,
                            subject=self.report_name,
                            body=email_body,
                            attachments=[excel_path],
                            replyto=self.gmail_reply_to,
                            override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                            # test=TESTING_EMAILS
                        )
                        
                        # if success:
                        #     print("Email sent successfully")
                        # else:
                        #     print("Failed to send email")
                        
                        return True
                    else:
                        # print("Failed to create Excel report")
                        self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel report: {excel_path}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                        return False
                else:
                    # print(f"No exceptions found or data error: {status_msg}")
                    self.sf_obj.log_audit_in_db(log_msg=f"No exceptions found or data error: {status_msg}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    return False
            else:
                # print("No data returned from query")
                self.sf_obj.log_audit_in_db(log_msg="No data returned from query", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                return False
                
        except Exception as e:
            # print(f"Error running report: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error running report: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return False
        finally:
            if 'conn' in locals():
                conn.close()
                print("Database connection closed")

def main():
    """Main function to run the MRI Encumbrance Exceptions report"""
    try:
        print(f"Starting MRI Encumbrance Exceptions Report - Version {SCRIPT_VERSION}")
        print(f"Testing mode: {TESTING_EMAILS}")
        
        # Create and run report
        report = MRIEncumbranceExceptions()
        success = report.run_report()
        
        if success:
            print("Report completed successfully")
            return 0
        else:
            print("Report failed")
            return 1
            
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 