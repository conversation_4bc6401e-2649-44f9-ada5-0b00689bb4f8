"""
LEGACY MRI Building VISITS NOTB Exceptions
Converted from R to Python
Version 20250519

This script checks for MRI buildings missing 'VISITS' (expected visits a year) notes
and emails Excel reports to Property Managers.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.worksheet.filters import AutoFilter
from openpyxl.utils.dataframe import dataframe_to_rows

# import re
# import csv
from pathlib import Path
import libs.snowflake_helper as sf
import libs.email_client as email_client


OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration

SCRIPT_FOLDER = "LEGACY_MRI_Exceptions-Building-Entity"
REPORT_NAME = "MRI Building VISITS Note Exceptions"


class EmailSender:
    """Handle email sending functionality"""
    
    def __init__(self):
        self.auth_email = "<EMAIL>"
        self.reply_to = "<EMAIL>"
        
        # Email recipients
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>", "<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Signatures
        self.norm_sig = self._get_normal_signature()
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
    
    def _get_normal_signature(self):
        """Get normal email signature"""
        return (
            "<b><span style='font-weight:bold'>Steve Olson</span></b><br/>"
            "Sr. Analytics Mgr.<br/>"
            "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>"
            "2500 Lehigh Ave.<br/>"
            "Glenview, IL 60026<br/>"
            "Ph: 847/904-9043<br/></span></font>"
        )
    
    

class ExcelReportGenerator:
    """Handle Excel report generation"""
    
    def __init__(self, report_path, sf_obj):
        self.report_path = Path(report_path)
        self.sf_obj = sf_obj
        self.report_path.mkdir(parents=True, exist_ok=True)
    
    def create_excel_report(self, data, filename, sheet_name, column_widths=None):
        """Create Excel report with formatting"""
        try:
            filepath = self.report_path / filename
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data
            for r in dataframe_to_rows(data, index=False, header=True):
                ws.append(r)
            
            # Format header row
            header_font = Font(name='Arial Narrow', size=12, bold=True, color='000000')
            header_fill = PatternFill(start_color='D6D6D6', end_color='D6D6D6', fill_type='solid')
            header_alignment = Alignment(wrap_text=True, vertical='center')
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # Set column widths
            if column_widths:
                for col_name, width in column_widths.items():
                    col_idx = None
                    for idx, cell in enumerate(ws[1], 1):
                        if cell.value == col_name:
                            col_idx = idx
                            break
                    if col_idx:
                        ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
            
            # Add filters
            ws.auto_filter = AutoFilter(ref=f"A1:{openpyxl.utils.get_column_letter(len(data.columns))}{len(data) + 1}")
            
            # Freeze top row
            ws.freeze_panes = 'A2'
            
            # Save workbook
            wb.save(filepath)
            return str(filepath)
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel report: {e}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            # logging.error(f"Failed to create Excel report: {e}")
            return None

class MRIBuildingVisitsChecker:
    """Main class for MRI Building VISITS Note Exceptions checking"""
    
    def __init__(self):
        # self.setup_logging()

        self.sf_obj = sf.SnowflakeHelper()
        # self.connection = self.sf_obj.conn

        self.setup_paths()
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.report_time = datetime.now().strftime("%H:%M:%S %Z")
        
        # Initialize components
        # self.sf_conn = SnowflakeConnection("PROD")  # Change to "STAGE" for staging
        self.email_sender = EmailSender()
        self.excel_generator = ExcelReportGenerator(self.report_path, self.sf_obj)
        
    
    def setup_paths(self):
        """Setup file paths based on computer"""
        # computer_name = os.environ.get('COMPUTERNAME', '')
        # test_computers = ['STEVEO-PLEX7010', 'LAPTOPTOSHIBA13', 'STEVEANDJENYOGA']
        # prod_computers = ['DESKTOP-TABLEAU']
        
        # if computer_name in test_computers:
        #     self.testing_pc = True
        #     # main_path = Path("//*************/public/steveo/R Stuff/ReportFiles")
        # else:
        self.testing_pc = False
        # main_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
        
        self.report_path = main_path / SCRIPT_FOLDER
    
    def get_exceptions_data(self):
        """Query database for building VISITS note exceptions"""
        query = """
        SELECT
            BLDG.BLDGID,
            BLDG.ADDRESS1 AS ADDRESS,
            BLDG.CITY,
            BLDG.STATE,
            PTYP.DESCRPN AS PROPERTY_TYPE,
            MNGRNAME AS PM_NAME,
            lower(MNGR.EMAIL) AS PM_EMAIL 
        FROM MRI.BLDG
        INNER JOIN 
            MRI.ENTITY
            ON BLDG.ENTITYID = ENTITY.ENTITYID
        LEFT JOIN 
            MRI.PTYP
            ON ENTITY.PROPTYPE = PTYP.PROPTYPE
        LEFT JOIN
            MRI.MNGR
            ON BLDG.MNGRID = MNGR.MNGRID 
        LEFT JOIN
            (
                SELECT /* NOTEB Expected Yearly Visits notes */
                    O.BLDGID,
                    TRY_CAST(REGEXP_SUBSTR(O.NOTETEXT, '([0-9]+)', 1, 1, 'e', 1) AS INT) AS NOTE_INT
                FROM MRI.NOTB O
                WHERE (O.REF1 = 'VISITS' OR O.REF2 = 'VISITS')
                AND REGEXP_LIKE(O.NOTETEXT, '.[0-9].*') IS NOT NULL  /* NOTE MUST CONTAIN NUMBER */
                AND O.NOTEDATE = (
                    SELECT MAX(I.NOTEDATE)
                    FROM MRI.NOTB I
                    WHERE (I.REF1 = 'VISITS' OR I.REF2 = 'VISITS')
                        AND REGEXP_LIKE(I.NOTETEXT, '.[0-9].*') IS NOT NULL  /* NOTE MUST CONTAIN NUMBER */
                        AND I.NOTEDATE <= CURRENT_DATE()
                        AND I.BLDGID = O.BLDGID
                )
            ) V_NOTES
            ON BLDG.BLDGID = V_NOTES.BLDGID
        WHERE 
            UPPER(IFNULL(BLDG.INACTIVE,'N')) = 'N'			--active building IDs
            AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL	--exclude alpha building IDs like 'RENTMP'
            AND PTYP.PROPTYPE != 'RES'						--exclude residential bldgs (Siesta, Tomahawk)
            AND V_NOTES.NOTE_INT IS NULL					--missing current 'VISITS' note
            /* may need additional logic to exclude new buildings for a certain period after acquisition */
        """
        
        try:
            data = self.sf_obj.execute_query(query,return_df=True)
            self.sf_obj.log_audit_in_db(log_msg=f"Retrieved {len(data)} exception records", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            # logging.info(f"Retrieved {len(data)} exception records")
            return data
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to retrieve exceptions data: {e}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            # logging.error(f"Failed to retrieve exceptions data: {e}")
            return pd.DataFrame()
    
    def create_html_table(self, data, caption):
        """Create HTML table from DataFrame"""
        html = f"<table border='2' cellspacing='1'>\n"
        html += f"<caption style='font-weight: bold; margin-bottom: 10px;'>{caption}</caption>\n"
        
        # Header
        html += "<tr style='background-color: #D6D6D6; font-weight: bold;'>\n"
        for col in data.columns:
            html += f"<th style='padding: 5px; text-align: left;'>{col}</th>\n"
        html += "</tr>\n"
        
        # Data rows
        for _, row in data.iterrows():
            html += "<tr>\n"
            for col in data.columns:
                value = row[col] if pd.notna(row[col]) else ""
                html += f"<td style='padding: 5px;'>{value}</td>\n"
            html += "</tr>\n"
        
        html += "</table>\n"
        return html
    
    def process_exceptions(self, data):
        """Process exceptions data and send emails to Property Managers"""
        if data.empty or len(data) < 1:
            self.sf_obj.log_audit_in_db(log_msg="No exceptions found", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            # logging.info("No exceptions found")
            return
        
        self.sf_obj.log_audit_in_db(log_msg=f"Processing {len(data)} exceptions", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
        # logging.info(f"Processing {len(data)} exceptions")
        
        # Define column widths for Excel
        column_widths = {
            "BLDGID": 8.5,
            "ADDRESS": 27,
            "CITY": 14,
            "STATE": 7,
            "PROPERTY_TYPE": 24,
            "PM_NAME": 24,
            "PM_EMAIL": 20
        }
        
        # Get unique PM email addresses
        pm_emails = data['PM_EMAIL'].dropna().unique()
        
        for pm_email in pm_emails:
            pm_data = data[data['PM_EMAIL'] == pm_email].copy()
            pm_name = pm_data['PM_NAME'].iloc[0]
            
            # Create Excel file
            filename = f"MRI_Building_VISITS_Note_Exceptions-{pm_name}.xlsx"
            excel_path = self.excel_generator.create_excel_report(
                pm_data, filename, self.query_date, column_widths
            )
            
            if excel_path:
                # Create email body
                html_table = self.create_html_table(pm_data, f"{REPORT_NAME} ({self.query_date})")
                
                body = f"""
                <p><b>{REPORT_NAME}</b></p>
                <p>The info below (also included in attached Excel file) indicates buildings missing a current 'VISITS' (expected visits a year) note.</p>
                <br/>
                {html_table}
                <br/><br/>
                {self.email_sender.norm_sig}
                """
                
                # Send email
                email_client.send_email(
                    recipient=pm_email,
                    subject=REPORT_NAME,
                    body=body,
                    attachments=[excel_path],
                    # test_mail=self.gmail_auth_email,
                    replyto=self.email_sender.reply_to,
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
                self.sf_obj.log_audit_in_db(log_msg=f"Email sent successfully to {pm_email}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
                # if success:
                #     logging.info(f"Email sent successfully to {pm_email}")
                # else:
                #     logging.error(f"Failed to send email to {pm_email}")
    
    def run(self):
        """Main execution method"""
        try:
            self.sf_obj.log_audit_in_db(log_msg=f"Beginning '{REPORT_NAME}' routine", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            # logging.info(f"Beginning '{REPORT_NAME}' routine")
            # time.sleep(1.5)
            
            # Connect to database
            # self.sf_conn.connect()
            # logging.info("Connected to Snowflake")
            
            # Get exceptions data
            exceptions_data = self.get_exceptions_data()
            
            # Process exceptions
            self.process_exceptions(exceptions_data)
            
            self.sf_obj.log_audit_in_db(log_msg=f"'{REPORT_NAME}' routine completed", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            # logging.info(f"'{REPORT_NAME}' routine completed")
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error in main execution: {e}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            # logging.error(f"Error in main execution: {e}")
            # Send error notification email
            error_body = f"""
            This is an automated email to inform you that an error occurred during the <b>{REPORT_NAME}</b> routine.<br/><br/>
            Error: {str(e)}<br/><br/>
            {self.email_sender.warn_sig}
            """
            
            email_client.send_email(
                recipient=self.email_sender.warn_recip,
                subject=f"{REPORT_NAME} : ERROR",
                body=error_body,
                # test_mail=self.gmail_auth_email,
                replyto=self.email_sender.reply_to,
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
            
        # finally:
        #     # Clean up
        #     self.sf_conn.close()
        #     logging.info("Database connection closed")

def main():
    """Main function"""
    checker = MRIBuildingVisitsChecker()
    checker.run()

if __name__ == "__main__":
    main() 