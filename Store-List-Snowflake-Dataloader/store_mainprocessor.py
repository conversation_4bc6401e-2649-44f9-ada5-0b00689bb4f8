#--------------------------------------------------------------------------------------
# Rev. No     Date      Author   Description
#---------------------------------------------------------------------------------------
#  1.0    2024/03/01   boopathi    Initial Version
#  1.1    2024/03/15   boopathi    Added data flattening logic
#  1.2    2024/04/23   boopathi    Implemented the feedbacks from HV team 
#  1.3    2024/05/07   boopathi    Added role in snowflake connection 
#  1.4    2025/06/06   <PERSON>   Converted to MS 365
#---------------------------------------------------------------------------------------

import os
import pandas as pd
import datetime
import time
import uuid

from libs.snowflake_helper import SnowflakeHelper
from libs.excel_helper import SharePointExcelOnline

class DataProcessor:

    LOG_TO_DB=True
    DEFAULT_SLEEP_SECS = 5

    def __init__(self):
        
        self.sf = SnowflakeHelper()
        self.excel_helper = SharePointExcelOnline()

        self.uuid_str = uuid.uuid4()
        self.dir_path = os.path.dirname(os.path.realpath(__file__))

        
        self.sql_file_path = os.path.join(self.dir_path, "Data_transformation", "sql_files")

        self.conn = self.sf.conn
        self.cs = self.conn.cursor()
        self.envm = os.environ["DATABASE_ENVM"]
        self.raw_database = os.environ["DATABASE_RAW_DATABASE"]

        self.script_name = __file__
        self.process_type = 'Store List'        
   
    
    def log_audit_in_db(self, log_msg, log_type='Info', print_msg=True):
        
        # only log to db if indicated
        if not self.LOG_TO_DB:
            if print_msg:
                print(log_msg)

            return 
        
        uuid_str = str(uuid.uuid4())
        script_name = os.path.basename(self.script_name)
        rec_ins_date = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        data_list = []
        record = [uuid_str, script_name, log_type, self.process_type, log_msg, rec_ins_date]
        columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
        data_list.append(record)
        # print(f"data_list: {data_list}")

        self.sf.bulk_insert(columns_list=columns_list, data_list=data_list,database=self.raw_database, schema=self.sf.batch_audit_schema, table=self.sf.moms_log_tbl)    

        if print_msg:
            print(log_msg)

    def execute_sql_scripts(self, sql_file_path,data_date):

        # file_id = '01NHAQJOGEO3WCSHXZ3FAYUTOTUPHJNWGT' # test book by Julian: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/dev/_layouts/15/Doc.aspx?sourcedoc=%7B29EC76C4-F91E-41D9-8A4D-D3A3CE96D8D3%7D&file=Book.xlsx&action=default&mobileredirect=true
        # file_id = '013USHB7VZ6D5NXGNXOBALFMOSTWOVBLHW' # store list    : https://highlandventuresltd442.sharepoint.com/:x:/r/_layouts/15/guestaccess.aspx?e=idNgBP&share=EdGLYvvK0jRHiQB72ffw90QB8qMNvo328bkA0NWjQoVEgw&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1749202116661&web=1
        # file_id='01NHAQJOACIRVQWY65CVG2KDPJDXWISBY3'  # store list copy: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/dev/_layouts/15/Doc.aspx?sourcedoc=%7B0B6B4402-DD63-4D15-A50D-E91DEC89071B%7D&file=Store%20List%20-%20Copy.xlsx&action=default&mobileredirect=true&DefaultItemOpen=1
        # file_id='01KJJTCLV6XNTPR4JQJVEJYGUAGFBCU7JU'  # store list prod test: https://highlandventuresltd442.sharepoint.com/:x:/r/_layouts/15/Doc.aspx?sourcedoc=%7BF866BBBE-30F1-484D-9C1A-8031422A7D34%7D&file=Store%20List%20-%20prod%20test.xlsx&action=default&mobileredirect=true
        file_id='01KJJTCLWRRNRPXSWSGRDYSAD33H37B52E'    # store list prod: https://highlandventuresltd442.sharepoint.com/:x:/r/_layouts/15/Doc.aspx?sourcedoc=%7BFB628BD1-D2CA-4734-8900-7BD9F7F0F744%7D&file=Store%20List.xlsx&action=default&mobileredirect=true

        # site_url = 'https://highlandventuresltd442.sharepoint.com/sites/dev'

        site_url = 'https://highlandventuresltd442.sharepoint.com'
        # site_url = 'https://highlandventuresltd442.sharepoint.com/sites/hrg'
        
        self.log_audit_in_db(log_msg=f"Start: Getting excel session for file ID {file_id} from {site_url}. Users URL: https://highlandventuresltd442.sharepoint.com/:x:/r/_layouts/15/Doc.aspx?sourcedoc=%7BFB628BD1-D2CA-4734-8900-7BD9F7F0F744%7D&file=Store%20List.xlsx&action=default&mobileredirect=true")

        # Get Excel session using file ID
        excel_session = self.excel_helper.get_excel_file_by_id(site_url, file_id)

        self.log_audit_in_db(log_msg=f"Obtained excel session for {file_id} from {site_url}: {excel_session}")

        for file in ["StoreList","StoreMatches","Personnel","Closed","UpdateHours","Hours","Property Managers"]:
            filepath = os.path.join(sql_file_path, f"{file}.sql")
            try:
                fp = open(filepath)
            except:
                self.log_audit_in_db(log_msg=f"File Not Found: {filepath}", log_type='Error')
                raise Exception(f"File Not Found: {filepath}")

            script = fp.read()
            script = script.replace("$envm_bus_date", data_date).replace("$envm", self.envm)
            self.cs.execute(script)
            result = self.cs.fetchall()
            fp.close()     
            # gc = gspread.service_account(filename=self.credential_file)
            # sh = gc.open_by_key(self.spreadsheet_id)
            print(f"\n\n")
            self.log_audit_in_db(log_msg=f"Processing file: {file}")
            if file == "StoreList":
                sheet_name = "StoreList"
                df = pd.DataFrame(data=result, columns=['ST' , 'Store Name' , 'Address' , 'City' , 'State' , 'Zip' , 'Phone' ,'Store Email' ,'Street Corners' ,'Open Date' ,'Company' ,'Rent' ,'Lease' ,'RM' ,'RM Email' ,'RM Report Name' ,'DM' ,'DM Email' ,'DM Report Name' ,'Manager' ,'MIT'], dtype=str)
                # print(f"df: {df}")
                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'U'  # Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_cells_custom(excel_session=excel_session, worksheet_name=sheet_name, starting_data_row=starting_data_row, first_data_column=first_data_column, last_data_column=last_data_column, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                populated = self.excel_helper.append_data_to_worksheet(
                    excel_session, sheet_name, df.values.tolist()
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                # exit(1)
            elif file == "StoreMatches":
                sheet_name = "StoreMatches"
                df = pd.DataFrame(data=result, columns=['FV', 'HF', 'SF', 'HPWI', 'DD', 'FAKEFV'])
                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'F'  # Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_cells_custom(excel_session=excel_session, worksheet_name=sheet_name, starting_data_row=starting_data_row, first_data_column=first_data_column, last_data_column=last_data_column, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                populated = self.excel_helper.append_data_to_worksheet(
                    excel_session, sheet_name, df.values.tolist()
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                # exit(1)                
            elif file == "Personnel":
                sheet_name = "Personnel"
                df = pd.DataFrame(data=result, columns=['Name', 'Title', 'Ext', 'Speed', 'Phone', 'Email','Sort_Order'])
                df = df.drop(columns=['Sort_Order'])

                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'F'  # Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_cells_custom(excel_session=excel_session, worksheet_name=sheet_name, starting_data_row=starting_data_row, first_data_column=first_data_column, last_data_column=last_data_column, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                populated = self.excel_helper.append_data_to_worksheet(
                    excel_session, sheet_name, df.values.tolist()
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                # exit(1)                      
            elif file == "Closed":
                sheet_name = "Closed"
                df = pd.DataFrame(data=result, columns=['ST', 'Store Name', 'Address', 'City', 'State', 'Zip', 'Phone', 'Open Date', 'Close Date', 'RM', 'DM'])

                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'K'  # Assuming we want to clear up to column E

                self.excel_helper.clear_excel_sheet_cells_custom(excel_session=excel_session, worksheet_name=sheet_name, starting_data_row=starting_data_row, first_data_column=first_data_column, last_data_column=last_data_column, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                populated = self.excel_helper.append_data_to_worksheet(
                    excel_session, sheet_name, df.values.tolist()
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                # exit(1)                                      
            elif file == "Hours":
                sheet_name = "Hours"
                df = pd.DataFrame(data=result, columns=['ST', 'Sun O', 'Sun C', 'Mon O', 'Mon C', 'Tue O', 'Tue C', 'Wed O', 'Wed C', 'Thu O', 'Thu C', 'Fri O', 'Fri C', 'Sat O', 'Sat C', 'Tot Open Hrs'])

                starting_data_row = 1
                first_data_column = 'A'  # Assuming we want to clear from column A
                last_data_column = 'P'  # Assuming we want to clear up to column E

                # self.excel_helper.clear_excel_sheet_cells_custom(excel_session=excel_session, worksheet_name=sheet_name, starting_data_row=starting_data_row, first_data_column=first_data_column, last_data_column=last_data_column, sleep_time_secs=self.DEFAULT_SLEEP_SECS)
                self.excel_helper.clear_excel_sheet_content(excel_session=excel_session, worksheet_name=sheet_name, sleep_time_secs=self.DEFAULT_SLEEP_SECS)

                populated = self.excel_helper.append_data_to_worksheet(
                    excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                )

                # print(f"done populating sheet {sheet_name}: {populated}")
                self.log_audit_in_db(log_msg=f"done populating sheet {sheet_name}: {populated}")
                # exit(1)                     
            elif file == "Property Managers":
                # sheet_name = "Property Managers"
                # worksheet = sh.worksheet(sheet_name)
                # worksheet.clear()
                # worksheet.update_cell(1, 1, str(result[0][0]))
                # worksheet.format('A1', {'horizontalAlignment': 'LEFT'})
                pass
        
        self.log_audit_in_db(log_msg=f"Completed: all  excel sections for file ID {file_id} from {site_url}")


    def data_ingestion(self):
        """
        Performs the data ingestion process.
        """
        try:
            script_name = self.script_name
            uuid_str = self.uuid_str
            data_date = datetime.datetime.now().strftime("%Y-%m-%d")
            self.execute_sql_scripts(self.sql_file_path, data_date)

        except Exception as err:
            err_msg = str(err).replace("'", "")
            self.log_audit_in_db(log_msg= f"Error encountered function - data_ingestion | {err_msg}", log_type='Error')
             
def main():

    start_time = time.time()
    start_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    

    

    processor = DataProcessor()

    processor.log_audit_in_db(f"Starting main() at {start_now}")

    processor.data_ingestion()

    # calculate duration
    end_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
    duration = processor.sf.get_duration(start_time)

    processor.log_audit_in_db(f"Finished main():\t[time: {end_now}] [{duration}]")

if __name__ == "__main__":

    # sets timezone to Central
    os.environ['TZ'] = 'America/Chicago'
    time.tzset()

    main()
