#!/usr/bin/env python3
"""
MARCOS Hourly Man-Hours Oracle Load
Converted from R to Python
Written by <PERSON> March 2023
Converted to Python January 2025
"""

import cx_Oracle
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import smtplib
import keyring
import os
import sys
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from email.mime.image import MimeImage
import socket
import logging
from pathlib import Path

# Version ********
# ******** change: new file, based on MARCOS_Weekly_Banking-Import_Transactions.R script

# Global configuration
testing_emails = False  # NORMAL, should normally be False in PRODUCTION
# testing_emails = True  # Uncomment for testing

class MarcosHourlyManHours:
    def __init__(self):
        self.setup_configuration()
        self.setup_logging()
        self.okay_to_continue = True
        
    def setup_configuration(self):
        """Setup configuration parameters"""
        self.my_report_name = "<PERSON>'s Hourly Man-Hours-Oracle Load"
        self.script_folder = "MARCOS_Man_Hours"
        self.rpt_folder = "reports"
        
        # Email parameters
        self.warn_recip = ["<EMAIL>", "<EMAIL>"]
        self.warn_sig = "<br/><b> <PERSON> </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        self.norm_recip = ["<EMAIL>"]
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Check if testing PC
        computer_name = os.environ.get('COMPUTERNAME', '')
        self.testing_pc = computer_name in ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13"]
        
        # Setup paths
        if self.testing_pc:
            self.log_path = Path("//*************/public/steveo/R Stuff/ReportFiles") / self.script_folder
            self.hv_sig_logo_path = Path("//*************/public/steveo/R Stuff/ReportFiles/HV Logo Email Signature.png")
        else:
            self.log_path = Path("C:/Users/<USER>/Documents/ReportFiles") / self.script_folder
            self.hv_sig_logo_path = Path("C:/Users/<USER>/Documents/ReportFiles/HV Logo Email Signature.png")
            
        self.my_report_path = self.log_path / self.rpt_folder
        
        # Add signature logo if exists
        if self.hv_sig_logo_path.exists():
            self.warn_sig += f'<br/><img src="{self.hv_sig_logo_path}" width="420">'
        
        # Date parameters
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.query_days = 14
        self.rpt_date = datetime.strptime(self.query_date, "%d-%b-%y").date()
        
        # Oracle parameters
        self.my_schema = "STEVE"
        self.my_table = "MP_HOURLY_MAN_HOURS"
        self.my_table_name = f"{self.my_schema}.{self.my_table}"
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_file = self.log_path / "python_conversion.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_oracle_connection(self):
        """Establish Oracle database connection"""
        try:
            # Set timezone
            os.environ['TZ'] = 'GMT'  
            os.environ['ORA_SDTZ'] = 'GMT'
            
            # Connection string
            dsn = cx_Oracle.makedsn("172.28.31.11", 1531, sid="fvpa")
            
            # Get password from keyring
            password = keyring.get_password("Oracle", "steve")
            if not password:
                raise Exception("Could not retrieve Oracle password from keyring")
                
            connection = cx_Oracle.connect("steve", password, dsn)
            self.logger.info("Oracle connection established successfully")
            return connection
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Oracle: {str(e)}")
            raise
            
    def send_email(self, recipients, subject, body, attachment=None, test=False, 
                   test_recipients=None, report_name=None):
        """Send email notification"""
        try:
            if report_name is None:
                report_name = self.my_report_name
                
            sender_email = "<EMAIL>"
            sender_name = f"{report_name} <{sender_email}>"
            
            if test and test_recipients:
                actual_recipients = test_recipients
                body = f"<p><b>TEST SEND (normal recipient: {'; '.join(recipients)})</b></p>{body}"
            else:
                actual_recipients = recipients
                
            # Create message
            msg = MimeMultipart()
            msg['From'] = sender_name
            msg['To'] = ', '.join(actual_recipients)
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MimeText(body, 'html'))
            
            # Add attachment if provided
            if attachment and Path(attachment).exists():
                with open(attachment, 'rb') as f:
                    img = MimeImage(f.read())
                    msg.attach(img)
            
            # Send email via Gmail SMTP
            gmail_password = keyring.get_password("GMail", "steve")
            if not gmail_password:
                raise Exception("Could not retrieve Gmail password from keyring")
                
            with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
                server.login(sender_email, gmail_password)
                server.send_message(msg)
                
            self.logger.info(f"Email sent successfully to {actual_recipients}")
            
        except Exception as e:
            self.logger.error(f"Failed to send email: {str(e)}")
            raise
            
    def check_dataframe_rows(self, df, min_num_rows, report_name=None):
        """Check if dataframe has minimum required rows"""
        if report_name is None:
            report_name = self.my_report_name
            
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_num_rows:
                error_status = f"{report_name}: OKAY"
                temp_nrow = len(df)
                temp_bool = True
            else:
                temp_bool = False
                temp_nrow = len(df)
                error_status = f"INCOMPLETE DATA: {report_name}"
        else:
            temp_bool = False
            temp_nrow = 0
            error_status = f"LOAD ERROR: {report_name}"
            
        return [temp_bool, temp_nrow, error_status]
        
    def query_oracle_data(self, connection):
        """Query Oracle for man-hours data"""
        try:
            # First query to get date range
            date_query = f"""
                select /* current and last pay period */
                  trunc(bpp) as s_date,
                  trunc(to_date('{self.query_date}')) as e_date,
                  to_number(trunc(to_date('{self.query_date}')) - trunc(bpp) + 1) as num_days
                from steve.famv_begin_payperiods
                where bpp <= to_date('{self.query_date}') - {self.query_days}
                  and epp >= trunc(to_date('{self.query_date}')) - {self.query_days}
            """
            
            my_dates = pd.read_sql(date_query, connection)
            self.logger.info(f"Date range query returned {len(my_dates)} rows")
            
            # Main complex query for man-hours data
            main_query = f"""
                WITH cte_dates as
                (
                    select /* current and last pay period */
                      trunc(bpp) as s_date,
                      trunc(to_date('{self.query_date}')) as e_date,
                      to_number(trunc(to_date('{self.query_date}')) - trunc(bpp) + 1) as num_days
                    from steve.famv_begin_payperiods
                    where bpp <= to_date('{self.query_date}') - {self.query_days}
                      and epp >= trunc(to_date('{self.query_date}')) - {self.query_days}
                ),
                cte_hours as
                (
                    SELECT
                        CAST(a.s_date + (rownum-1)/24 AS timestamp) AS HOUR_START,
                        CAST(a.s_date + (rownum-1)/24 + (1/24) AS timestamp) AS HOUR_NEXT
                    from cte_dates a
                    connect by level <= (24*a.num_days)
                ),
                cte_stores as
                (
                    select distinct i.store
                    from ab_employees_punch i
                    inner join HR.hr_locations_all hrloc
                        on lpad(i.store,4,0) = hrloc.location_code
                        and UPPER(substr(hrloc.LOC_INFORMATION15,0,2)) = 'HF'
                    where i.e_date >= (select s_date from cte_dates)
                      and i.s_date <= (select e_date + 1.2 from cte_dates)
                ),
                cte_store_hours as
                (
                    SELECT * FROM CTE_STORES CROSS JOIN CTE_HOURS
                ),
                cte_punches as
                (
                    select b.store,
                           b.s_date,
                           nvl(b.e_date, b.s_date + (hours/24)) as e_date
                    from ab_employees_punch b
                    inner join cte_stores d on b.store = d.store
                    where b.s_date >= (select s_date - 0.8 from cte_dates)
                      and b.s_date < (select e_date + 1.2 from cte_dates)
                      and b.p_type != 'S' /* exclude 'special' time (not worked) */
                      and b.p_type != 'P' /* exclude 'personal' time (not worked) */
                )
                /* main query using CTE above*/
                select
                    a.store,
                    cast(a.hour_start as date) as hour_start,
                    cast(a.hour_next as date) as hour_next,
                    round(
                        nvl(
                            SUM(/* punch minutes within hour*/
                                CASE WHEN b.s_date >= a.hour_start
                                    THEN 0 - EXTRACT(MINUTE FROM CAST(b.s_date AS TIMESTAMP))
                                ELSE 0
                                END
                                +
                                CASE WHEN b.e_date >= a.hour_next
                                    THEN 60
                                WHEN b.e_date < a.hour_next
                                    THEN EXTRACT(MINUTE FROM CAST(b.e_date AS TIMESTAMP))
                                ELSE 0
                                END
                            )
                        ,0) / 60 
                    ,3) as MAN_HOURS
                from CTE_STORE_HOURS a
                left join cte_punches b
                on a.store = b.store
                and a.hour_start <= b.e_date
                and a.hour_next > b.s_date
                group by
                    a.store,
                    cast(a.hour_start as date),
                    cast(a.hour_next as date)
                order by store, hour_start
            """
            
            my_data = pd.read_sql(main_query, connection)
            self.logger.info(f"Main query returned {len(my_data)} rows")
            
            return my_dates, my_data
            
        except Exception as e:
            self.logger.error(f"Error querying Oracle data: {str(e)}")
            raise
            
    def load_oracle_data(self, connection, my_dates, my_data):
        """Load data into Oracle table"""
        try:
            cursor = connection.cursor()
            
            # Format dates for Oracle queries
            query_start_date = my_dates.iloc[0]['S_DATE'].strftime("%d-%b-%y")
            query_end_date = (my_dates.iloc[0]['E_DATE'] + timedelta(days=1)).strftime("%d-%b-%y")
            
            self.logger.info(f"Loading data for date range: {query_start_date} to {query_end_date}")
            
            # Check current row count
            select_query = f"""
                select count(*)
                from {self.my_table_name}
                where hour_start >= to_date('{query_start_date}')
                and hour_start < to_date('{query_end_date}')
            """
            
            cursor.execute(select_query)
            select_cnt = cursor.fetchone()[0]
            
            # Delete existing data
            delete_query = f"""
                delete from {self.my_table_name}
                where hour_start >= to_date('{query_start_date}')
                and hour_start < to_date('{query_end_date}')
            """
            
            cursor.execute(delete_query)
            rows_deleted = cursor.rowcount
            
            if rows_deleted != select_cnt:
                self.logger.warning("Dubious deletion - rolling back transaction")
                connection.rollback()
                
                error_text = f"""
                    <p>There was an unexpected issue deleting previous data that might 
                    have been present for dates between {query_start_date} 
                    and {my_dates.iloc[0]['E_DATE'].strftime('%d-%b-%y')}. 
                    <b>The routine has ABORTED without attempting to load the current results!</b></p>
                """
                
                body_text = f"""
                    <p>This is an automated email to inform you that it appears there has 
                    been an error querying the man-hours for the {self.my_report_name} routine! 
                    The query failed or produced less than 24 rows of results.</p>
                    <br>
                    <p>The routine is aborting without an update</p> 
                    {self.warn_sig}
                """
                
                # Send warning email
                self.send_email(
                    self.warn_recip,
                    f"{self.my_report_name} Issue: Oracle deletion error",
                    body_text,
                    test=testing_emails,
                    test_recipients=self.test_recip
                )
                
                return False
                
            else:
                # Deletion successful, commit and proceed
                connection.commit()
                self.logger.info(f"Successfully deleted {rows_deleted} existing rows")
                
                # Get pre-load count
                cursor.execute(select_query)
                select_cnt_pre = cursor.fetchone()[0]
                
                # Prepare data for insertion
                # Convert DataFrame to list of tuples for bulk insert
                data_tuples = []
                for _, row in my_data.iterrows():
                    data_tuples.append((
                        int(row['STORE']),
                        row['HOUR_START'],
                        row['HOUR_NEXT'], 
                        float(row['MAN_HOURS'])
                    ))
                
                # Insert new data
                insert_query = f"""
                    INSERT INTO {self.my_table_name} 
                    (STORE, HOUR_START, HOUR_NEXT, MAN_HOURS) 
                    VALUES (?, ?, ?, ?)
                """
                
                cursor.executemany(insert_query, data_tuples)
                connection.commit()
                
                # Get post-load count
                cursor.execute(select_query)
                select_cnt_post = cursor.fetchone()[0]
                
                my_load_numrows = select_cnt_post - select_cnt_pre
                my_data_numrows = len(my_data)
                
                self.logger.info(f"Loaded {my_load_numrows} rows, expected {my_data_numrows}")
                
                if my_load_numrows != my_data_numrows:
                    # Mismatch in rows loaded
                    load_check_query = f"""
                        select store, count(hour_start) as COUNT_LOADED
                        from {self.my_table_name}
                        where hour_start >= to_date('{query_start_date}')
                          and hour_start < to_date('{query_end_date}')
                        group by store
                        order by store
                    """
                    
                    load_stores_curr = pd.read_sql(load_check_query, connection)
                    
                    # Get expected counts by store
                    load_stores_results_cnt = my_data.groupby('STORE').size().reset_index(name='QUERY_RESULTS')
                    
                    # Compare expected vs actual
                    load_comparison = load_stores_results_cnt.merge(
                        load_stores_curr, 
                        left_on='STORE', 
                        right_on='STORE', 
                        how='left'
                    ).fillna(0)
                    
                    load_failed = load_comparison[load_comparison['QUERY_RESULTS'] != load_comparison['COUNT_LOADED']]
                    
                    if len(load_failed) > 0:
                        self.logger.warning(f"Load failed for {len(load_failed)} stores")
                        
                        # Create HTML table for failed stores
                        failed_table_html = load_failed.to_html(index=False, escape=False, 
                                                                table_id="failed_stores",
                                                                classes="table table-striped")
                        
                        load_error_text = f"""
                            <p>One or more stores failed to load the expected number of rows 
                            into Oracle for the dates between {query_start_date} 
                            and {my_dates.iloc[0]['E_DATE'].strftime('%d-%b-%y')}. 
                            <b>The Oracle table ({self.my_table_name}) will have incomplete results for these dates!</b> 
                            Investigate the stores shown below and re-run this routine when issue 
                            has been addressed.<br>
                            {failed_table_html}
                            </p>
                        """
                        
                        body_text = f"""
                            <p>This is an automated email to inform you that it appears there has 
                            been an error populating Oracle in the {self.my_report_name} routine.</p>
                            {load_error_text}
                            <br>
                            {self.warn_sig}
                        """
                        
                        # Send warning email
                        self.send_email(
                            self.warn_recip,
                            f"{self.my_report_name} Issue: Oracle load error",
                            body_text,
                            test=testing_emails,
                            test_recipients=self.test_recip
                        )
                        
                        return False
                        
                self.logger.info("Data loaded successfully to Oracle")
                return True
                
        except Exception as e:
            self.logger.error(f"Error loading data to Oracle: {str(e)}")
            connection.rollback()
            raise
        finally:
            cursor.close()
            
    def run(self):
        """Main execution method"""
        try:
            self.logger.info(f"Starting {self.my_report_name}")
            
            if not self.okay_to_continue:
                self.logger.error("Initial checks failed, aborting")
                return
                
            # Get Oracle connection
            connection = self.get_oracle_connection()
            
            try:
                # Query data
                my_dates, my_data = self.query_oracle_data(connection)
                
                # Check data quality
                data_status = self.check_dataframe_rows(my_data, 24, self.my_report_name)
                
                if not data_status[0]:
                    # Data quality check failed
                    self.okay_to_continue = False
                    
                    body_text = f"""
                        <p>This is an automated email to inform you that it appears there has 
                        been an error querying the man-hours for the {self.my_report_name} routine! 
                        The query failed or produced less than 24 rows of results.</p>
                        <br>
                        <p>The routine is aborting without an update</p> 
                        {self.warn_sig}
                    """
                    
                    # Send warning email
                    self.send_email(
                        self.warn_recip,
                        f"{self.my_report_name} Issue: Query error, missing data",
                        body_text,
                        test=testing_emails,
                        test_recipients=self.test_recip
                    )
                    
                    self.logger.error(f"Data quality check failed: {data_status[2]}")
                    return
                    
                self.logger.info(f"Data quality check passed: {data_status[1]} rows")
                
                # Load data to Oracle
                if self.okay_to_continue:
                    success = self.load_oracle_data(connection, my_dates, my_data)
                    
                    if success:
                        self.logger.info(f"{self.my_report_name} completed successfully")
                    else:
                        self.logger.error(f"{self.my_report_name} completed with errors")
                        
            finally:
                connection.close()
                self.logger.info("Oracle connection closed")
                
        except Exception as e:
            self.logger.error(f"Fatal error in {self.my_report_name}: {str(e)}")
            
            # Send critical error email
            body_text = f"""
                <p>This is an automated email to inform you that there has been a critical error 
                in the {self.my_report_name} routine!</p>
                <p><b>Error Details:</b><br>{str(e)}</p>
                <br>
                <p>The routine has been aborted. Please investigate and resolve the issue.</p>
                {self.warn_sig}
            """
            
            try:
                self.send_email(
                    self.warn_recip,
                    f"{self.my_report_name} Issue: Critical Error",
                    body_text,
                    test=testing_emails,
                    test_recipients=self.test_recip
                )
            except:
                self.logger.error("Failed to send critical error email")
                
            raise

def main():
    """Main entry point"""
    processor = MarcosHourlyManHours()
    processor.run()

if __name__ == "__main__":
    main() 