#!/usr/bin/env python3
"""
Office 365 Email Reader Example <PERSON><PERSON><PERSON>
Demonstrates how to read emails from Office 365 using both IMAP and Microsoft Graph API
"""

import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import List

# Add the libs directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'libs'))

from libs.office365_email_reader import (
    Office365EmailReader, 
    EmailReaderMethod, 
    EmailMessage,
    print_email_summary,
    save_emails_to_json
)


def setup_environment_variables():
    """Set up environment variables for Office 365 authentication"""
    print("Setting up environment variables...")
    
    # IMAP Configuration
    os.environ["OFFICE365_IMAP_SERVER"] = "outlook.office365.com"
    os.environ["OFFICE365_IMAP_PORT"] = "993"
    os.environ["OFFICE365_EMAIL_ADDRESS"] = "<EMAIL>"  # CHANGE THIS
    os.environ["OFFICE365_EMAIL_PASSWORD"] = "your_app_password"     # CHANGE THIS
    
    # Microsoft Graph API Configuration (optional)
    os.environ["OFFICE365_CLIENT_ID"] = "your_client_id"            # CHANGE THIS
    os.environ["OFFICE365_CLIENT_SECRET"] = "your_client_secret"    # CHANGE THIS
    os.environ["OFFICE365_TENANT_ID"] = "your_tenant_id"           # CHANGE THIS
    
    print("Environment variables set (you need to update them with your actual values)")


def example_imap_basic():
    """Example 1: Basic IMAP email reading"""
    print("\n" + "="*80)
    print("EXAMPLE 1: Basic IMAP Email Reading")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        # Get last 5 emails
        emails = reader.get_emails(limit=5)
        print_email_summary(emails)
        
        reader.close()
        
    except Exception as e:
        print(f"IMAP example failed: {str(e)}")
        print("Make sure you have set the correct email address and app password")


def example_imap_filters():
    """Example 2: IMAP with filters"""
    print("\n" + "="*80)
    print("EXAMPLE 2: IMAP with Filters")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        # Get only unread emails from last week
        since_date = datetime.now() - timedelta(days=7)
        emails = reader.get_emails(
            limit=10,
            since=since_date,
            unread_only=True
        )
        
        print(f"Found {len(emails)} unread emails from the last week")
        print_email_summary(emails)
        
        reader.close()
        
    except Exception as e:
        print(f"IMAP filters example failed: {str(e)}")


def example_graph_api():
    """Example 3: Microsoft Graph API"""
    print("\n" + "="*80)
    print("EXAMPLE 3: Microsoft Graph API Email Reading")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.GRAPH_API)
        
        # Get last 5 emails
        emails = reader.get_emails(limit=5)
        print_email_summary(emails)
        
        # Save to JSON file
        if emails:
            save_emails_to_json(emails, "output/office365_emails.json")
        
        reader.close()
        
    except Exception as e:
        print(f"Graph API example failed: {str(e)}")
        print("Make sure you have configured the Azure app registration correctly")


def example_search_emails():
    """Example 4: Search for specific emails"""
    print("\n" + "="*80)
    print("EXAMPLE 4: Search for Specific Emails")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        # Search for emails with specific subject
        emails = reader.get_emails(
            limit=10,
            search_criteria="report",  # Search for emails with "report" in subject
            since=datetime.now() - timedelta(days=30)
        )
        
        print(f"Found {len(emails)} emails with 'report' in subject from last 30 days")
        print_email_summary(emails)
        
        reader.close()
        
    except Exception as e:
        print(f"Search example failed: {str(e)}")


def example_get_folders():
    """Example 5: Get available folders"""
    print("\n" + "="*80)
    print("EXAMPLE 5: Get Available Folders")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        folders = reader.get_folders()
        print(f"Available folders: {folders}")
        
        # Get emails from a specific folder (if available)
        if "Sent" in folders:
            sent_emails = reader.get_emails(folder="Sent", limit=3)
            print(f"Found {len(sent_emails)} emails in Sent folder")
            print_email_summary(sent_emails)
        
        reader.close()
        
    except Exception as e:
        print(f"Folders example failed: {str(e)}")


def example_process_emails():
    """Example 6: Process emails for business logic"""
    print("\n" + "="*80)
    print("EXAMPLE 6: Process Emails for Business Logic")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        # Get recent emails
        emails = reader.get_emails(
            limit=20,
            since=datetime.now() - timedelta(days=7)
        )
        
        # Process emails
        report_emails = []
        urgent_emails = []
        
        for email in emails:
            # Check for report emails
            if any(keyword in email.subject.lower() for keyword in ['report', 'summary', 'data']):
                report_emails.append(email)
            
            # Check for urgent emails
            if any(keyword in email.subject.lower() for keyword in ['urgent', 'asap', 'critical']):
                urgent_emails.append(email)
        
        print(f"Found {len(report_emails)} report-related emails")
        print(f"Found {len(urgent_emails)} urgent emails")
        
        # Save different categories
        if report_emails:
            save_emails_to_json(report_emails, "output/report_emails.json")
        if urgent_emails:
            save_emails_to_json(urgent_emails, "output/urgent_emails.json")
        
        reader.close()
        
    except Exception as e:
        print(f"Process emails example failed: {str(e)}")


def example_integration_with_existing_system():
    """Example 7: Integration with existing email system"""
    print("\n" + "="*80)
    print("EXAMPLE 7: Integration with Existing Email System")
    print("="*80)
    
    try:
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        # Get emails that might need processing
        emails = reader.get_emails(
            limit=10,
            unread_only=True
        )
        
        # Process emails similar to existing patterns in the codebase
        for email in emails:
            print(f"Processing email: {email.subject}")
            
            # Example: Check if email is from a specific sender
            if "reporting" in email.sender.lower():
                print(f"  -> This is a reporting email from {email.sender}")
                
                # You could trigger existing reporting processes here
                # For example, save attachment to data folder
                if email.attachments:
                    print(f"  -> Has {len(email.attachments)} attachments")
                    # Could save attachments to data/reports/ folder
            
            # Example: Check for specific keywords
            if any(keyword in email.body.lower() for keyword in ['exception', 'error', 'failed']):
                print(f"  -> This email contains error/exception keywords")
                # Could trigger error notification system
        
        reader.close()
        
    except Exception as e:
        print(f"Integration example failed: {str(e)}")


def main():
    """Main function to run all examples"""
    print("Office 365 Email Reader Examples")
    print("="*80)
    
    # Setup environment (you need to modify these with actual values)
    setup_environment_variables()
    
    print("\nNOTE: Before running these examples, you need to:")
    print("1. Set your actual Office 365 email address and app password")
    print("2. For Graph API examples, set up Azure app registration")
    print("3. Create 'output' directory if it doesn't exist")
    
    # Create output directory if it doesn't exist
    os.makedirs("output", exist_ok=True)
    
    # Run examples (comment out the ones you don't want to run)
    try:
        example_imap_basic()
        example_imap_filters()
        example_search_emails()
        example_get_folders()
        example_process_emails()
        example_integration_with_existing_system()
        
        # Uncomment if you have Graph API configured
        # example_graph_api()
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"Error running examples: {str(e)}")
    
    print("\n" + "="*80)
    print("Examples completed!")


if __name__ == "__main__":
    main() 