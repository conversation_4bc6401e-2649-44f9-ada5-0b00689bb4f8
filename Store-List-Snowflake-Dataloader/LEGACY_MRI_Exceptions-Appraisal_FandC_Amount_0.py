"""
LEGACY MRI Exceptions - Appraisal Free and Clear Amount is $0
Python conversion of R script
Version 20241209
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timezone
# import keyring
# import snowflake.connector
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo

from typing import List, Optional, Dict, Any, Tuple

import libs.snowflake_helper as sf
import libs.email_client as email_client

OVERRIDE_EMAIL_RECIPIENTS = False

# Configuration
TESTING_EMAILS = False  # Set to True for testing
SCRIPT_FOLDER = "LEGACY_MRI_Exceptions-Appraisals"
REPORT_NAME = "MRI Appraisal Exceptions-Free and Clear Amount is $0"
REPORT_FILENAME = "MRI_Appraisal_Exceptions-FandC_is_0.xlsx"
REPORT_CRITERIA = """<p><b>Criteria for exceptions:</b><ul>
<li>Appraisal has missing or $0 Free and Clear Amount</li>
</ul></p><br/>"""

# Gmail API scopes
# SCOPES = ['https://www.googleapis.com/auth/gmail.send']

class LegacyMRIAppraisalExceptions:
    def __init__(self):

        self.sf_obj = sf.SnowflakeHelper()
        self.sf_connection = self.sf_obj.conn

        # self.setup_logging()
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.report_time = datetime.now().strftime("%Y%m%d-%H%M%S%Z")
        self.okay_to_continue = True
        
        # Email configuration
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>","<EMAIL>"]
        
        # Gmail authentication
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Computer and path configuration
        # self.central_path = Path("//*************/public/steveo/R Stuff/ReportFiles")
        # self.tableau_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        # self.test_computers = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
        # self.prod_computers = ["DESKTOP-TABLEAU"]
        
        self.setup_paths()
        self.setup_signatures()
        
        
        
    def setup_paths(self):
        """Setup file paths based on computer type"""
        # computer_name = os.environ.get("COMPUTERNAME", "")
        
        # if computer_name in self.test_computers:
        #     self.testing_pc = True
        #     self.main_path = self.central_path
        # else:
        self.testing_pc = False
        # self.main_path = self.tableau_path
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
            
        self.log_path = self.main_path / SCRIPT_FOLDER
        self.report_path = self.log_path
        self.hv_sig_path = self.main_path / "HTML_signatures.csv"
        
    def setup_signatures(self):
        """Load email signatures"""
        self.norm_sig = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
Sr. Analytics Mgr.<br/>
<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
2500 Lehigh Ave.<br/>
Glenview, IL 60026<br/>
Ph: 847/904-9043<br/>"""

        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        
        # Try to load signature template
        if self.hv_sig_path.exists():
            try:
                signatures_df = pd.read_csv(self.hv_sig_path)
                lcp_reporting_sig = signatures_df[signatures_df['Desc'] == 'LCP Reporting']
                if not lcp_reporting_sig.empty:
                    self.norm_sig = lcp_reporting_sig.iloc[0]['HTML']
            except Exception as e:
                pass
                # self.logger.warning(f"Could not load signatures: {e}")
                
    
            
    def check_dataframe_rows(self, df: pd.DataFrame, min_rows: int, 
                           report_name: str) -> Tuple[bool, int, str]:
        """Check if dataframe has minimum required rows"""
        if df is not None and isinstance(df, pd.DataFrame):
            if len(df) >= min_rows:
                return True, len(df), f"{report_name}: OKAY"
            else:
                return False, len(df), f"{report_name}: INCOMPLETE"
        else:
            return False, 0, f"{report_name}: ERROR"
            
    def write_excel_file(self, df: pd.DataFrame, filepath: str, sheet_name: str = "Sheet1",
                        column_widths: Optional[Dict[str, float]] = None):
        """Write DataFrame to Excel file with formatting"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
                
            # Format header row
            header_font = Font(bold=True, name="Arial Narrow", size=12)
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                
            # Set column widths
            if column_widths:
                for col_name, width in column_widths.items():
                    col_idx = None
                    for idx, cell in enumerate(ws[1], 1):
                        if cell.value == col_name:
                            col_idx = idx
                            break
                    if col_idx:
                        col_letter = openpyxl.utils.get_column_letter(col_idx)
                        ws.column_dimensions[col_letter].width = width
                        
            # Freeze panes and add filter
            ws.freeze_panes = 'A2'
            ws.auto_filter.ref = ws.dimensions
            
            # Save workbook
            wb.save(filepath)
            # self.logger.info(f"Excel file saved: {filepath}")
            self.sf_obj.log_audit_in_db(log_msg=f"Excel file saved: {filepath}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            
        except Exception as e:
            # self.logger.error(f"Failed to save Excel file: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to save Excel file, {filepath}: {e}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            # Send warning email
            email_client.send_email(
                recipient=self.warn_recip,
                subject=f"{REPORT_NAME} : REPORT FILE SAVING ERROR",
                body=f"""This is an automated email to inform you that the following file WAS NOT SAVED 
                during the <b>{REPORT_NAME}</b> routine.<br/><br/>
                {filepath}<br/><br/>
                Either the path wasn't accessible or the file was open in another process.
                <br/><br/>The routine should continue without saving this file.<br/><br/>
                {self.warn_sig}""",
                replyto=self.gmail_reply_to,
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
            
    def get_appraisal_exceptions(self) -> pd.DataFrame:
        """Query Snowflake for appraisal exceptions"""
        query = """
        SELECT
            'Free & Clear Appraisal is $0' as ISSUE
        ,   BLDG.BLDGID
        ,   TO_DATE(APPR.APPRDATE) AS "Appraisal Date"
        ,   CODELIST.CODEDESC AS "Internal / External"
        ,   APPR.FC_AMOUNT AS "Free & Clear Appraisal Amount"
        ,   APPR.REVCAP AS "Reversion Year Cap Rate"
        ,   APPR.DISCRATE AS "Discount Rate"
        ,   APPR.LEV_AMOUNT AS "Leveraged Amount"
        ,   APPR.HOLDPER AS "Hold Period"
        ,   APPR.RECNUM AS "Record Number"
        ,   APPR.APPRAISER AS "ID of Appraisal Firm"
        ,   COMPANY.CMPY_NAME AS "Appraisal Firm Name"
        ,   APPR.YEAR1CAP AS "First Year Cap Rate"
        ,   APPR.INC_RATE AS "Income Growth Rate"
        ,   APPR.EXP_RATE AS "Expense Growth Rate"
        ,   CASE WHEN APPR.FP_TYPE = 'P' THEN 'Preliminary'
              WHEN APPR.FP_TYPE = 'F' THEN 'Final'
              ELSE APPR.FP_TYPE
              END AS "Final / Preliminary"
        ,   APPR.APPR_NOTE AS "Appraisal Notes"
        ,   CASE WHEN APPR.APPRDATE = NEWEST.APPRDATE THEN 'Y' ELSE 'N' END AS "Newest Appraisal"
        from MRI.APPR
        left join MRI.CODELIST
        on APPR.IE_TYPE = CODELIST.CODEVAL
            and CODELIST.CODETYPE = 'APPRLOCATION'
        LEFT JOIN MRI.COMPANY
        ON APPR.APPRAISER = COMPANY.CMPY_ID
        LEFT JOIN
        (
            SELECT I.TABLEKEY,
                MAX(I.APPRDATE) AS APPRDATE
            FROM MRI.APPR I
            WHERE I.TABLEID = 'BLDG'
            GROUP BY I.TABLEKEY
        ) NEWEST
        ON APPR.TABLEKEY = NEWEST.TABLEKEY
            AND APPR.APPRDATE = NEWEST.APPRDATE
        LEFT JOIN MRI.BLDG
        ON  APPR.TABLEID = 'BLDG'
            AND APPR.TABLEKEY = BLDG.BLDGID
        WHERE APPR.TABLEID = 'BLDG'
            AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
            AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
            AND (APPR.FC_AMOUNT IS NULL OR APPR.FC_AMOUNT = 0)
        ORDER BY BLDG.BLDGID
        ,   APPR.RECNUM
        """
        # print(f"query: {query}")
        # exit(1)
        try:
            cursor = self.sf_connection.cursor()
            cursor.execute(query)
            
            # Fetch results
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            
            # Create DataFrame
            df = pd.DataFrame(data, columns=columns)
            
            # Clean string columns (remove trailing spaces)
            for col in df.select_dtypes(include=['object']).columns:
                df[col] = df[col].astype(str).str.rstrip()
                
            cursor.close()
            return df
            
        except Exception as e:
            # self.logger.error(f"Failed to execute query: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to execute query: {e}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            return pd.DataFrame()
            
    def create_email_body_table(self, df: pd.DataFrame, max_rows: int = 20) -> str:
        """Create HTML table for email body"""
        # Select key columns for email display
        email_cols = ['ISSUE', 'BLDGID', 'Appraisal Date', 'Free & Clear Appraisal Amount', 'Appraisal Firm Name']
        display_df = df[email_cols].copy()
        
        # Format date columns
        for col in display_df.columns:
            if 'Date' in col and display_df[col].dtype == 'datetime64[ns]':
                display_df[col] = display_df[col].dt.strftime('%m/%d/%y')
                
        if len(display_df) <= max_rows:
            # Create HTML table
            table_html = display_df.to_html(
                index=False, 
                classes='table table-striped',
                table_id='exceptions-table',
                border=2
            )
            return f"<p>{table_html}</p>"
        else:
            return f"<p>There are {len(display_df)} results, see attached file for all.</p>"
            
    def process_exceptions(self):
        """Main processing function for exceptions"""
        if not self.okay_to_continue:
            return
            
        # self.logger.info(f"Starting: {REPORT_NAME}")
        self.sf_obj.log_audit_in_db(log_msg=f"Starting: {REPORT_NAME}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
        
        # Connect to Snowflake
        # self.connect_to_snowflake()
        # if not self.okay_to_continue:
        #     return
            
        # Get exceptions data
        exceptions_df = self.get_appraisal_exceptions()
        
        # Check if we have data
        has_data, row_count, status = self.check_dataframe_rows(
            exceptions_df, 1, REPORT_NAME
        )
        
        if has_data:
            # self.logger.info(f"Found {row_count} exceptions")
            self.sf_obj.log_audit_in_db(log_msg=f"Found {row_count} exceptions", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            
            # Replace underscores in column names with spaces
            exceptions_df.columns = exceptions_df.columns.str.replace('_', ' ')
            
            # Define column widths for Excel
            column_widths = {
                "ISSUE": 29,
                "BLDGID": 8,
                "Appraisal Date": 10.5,
                "Internal / External": 10.5,
                "Free & Clear Appraisal Amount": 10.5,
                "Reversion Year Cap Rate": 9,
                "Discount Rate": 9,
                "Leveraged Amount": 9,
                "Hold Period": 9,
                "Record Number": 9.5,
                "ID of Appraisal Firm": 10.5,
                "Appraisal Firm Name": 18,
                "First Year Cap Rate": 9.5,
                "Income Growth Rate": 9.5,
                "Expense Growth Rate": 9.5,
                "Final / Preliminary": 10,
                "Appraisal Notes": 25,
                "Newest Appraisal": 8
            }
            
            # Create Excel file
            excel_filepath = self.report_path / REPORT_FILENAME
            self.write_excel_file(
                exceptions_df, 
                str(excel_filepath), 
                sheet_name=self.query_date,
                column_widths=column_widths
            )
            
            # Create email body
            body_table = self.create_email_body_table(exceptions_df)
            
            email_body = f"""<p><b>REPORT: {REPORT_NAME}</b></p>
            {REPORT_CRITERIA}
            <p>The info below contains MRI data (from yesterday) that appears to be an exception. 
            <b>See attached Excel file for more details.</b></p>
            {body_table}
            <br/>
            {self.norm_sig}"""
            
            # Send email
            email_client.send_email(
                recipient=self.norm_recip,
                subject=REPORT_NAME,
                body=email_body,
                attachments=str(excel_filepath) if excel_filepath.exists() else [],
                replyto=self.gmail_reply_to,
                # test=TESTING_EMAILS
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
            
            # self.logger.info("Report completed successfully")
            self.sf_obj.log_audit_in_db(log_msg="Report completed successfully", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
        else:
            # self.logger.info(f"No exceptions found - {status}")
            self.sf_obj.log_audit_in_db(log_msg=f"No exceptions found - {status}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Info')
            
    def cleanup(self):
        """Clean up resources"""
        if self.sf_connection:
            self.sf_connection.close()
            # self.logger.info("Snowflake connection closed")
            
    def run(self):
        """Main execution function"""
        try:
            self.process_exceptions()
        except Exception as e:
            # self.logger.error(f"Error in main execution: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error in main execution: {e}", process_type=REPORT_NAME, script_file_name=__file__, log_type='Error')
            # Send error notification
            self.send_email(
                recipients=self.warn_recip,
                subject=f"{REPORT_NAME} - ERROR",
                body=f"""An error occurred during the {REPORT_NAME} routine:<br/><br/>
                {str(e)}<br/><br/>
                Please check the logs for more details.<br/><br/>
                {self.warn_sig}"""
            )
        finally:
            self.cleanup()


def main():
    """Main entry point"""
    processor = LegacyMRIAppraisalExceptions()
    processor.run()


if __name__ == "__main__":
    main() 