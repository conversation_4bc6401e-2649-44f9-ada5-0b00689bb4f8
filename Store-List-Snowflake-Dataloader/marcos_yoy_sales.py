"""
Probably not needed depending on what happens with main Marketing Plans routines
--------------------------------------------------------------------------------
Marcos Year-over-Year Sales Report Generator
Converted from R to Python

This script builds a .csv file of Marcos Sales for last year
and this year, used by a Marcos advertising plan macro that builds
an advertising calendar for each store which includes their previous
year and current year sales.

Written by <PERSON> July 2019
Converted to Python - Version 20240530
"""

import cx_Oracle
import pandas as pd
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import datetime
import os
import sys
import csv
import keyring
from pathlib import Path
import logging
from typing import Optional, List, Dict, Any
import time


class MarcosYoYSalesProcessor:
    """Main class for processing Marco's Year-over-Year sales data"""
    
    def __init__(self):
        # Configuration
        self.logname = "Marcos-YoYSales-Log.csv"
        self.logpath = "C:/Users/<USER>/Documents/ReportFiles/MARCOS_YoY/"
        self.rptpath = "C:/Users/<USER>/Documents/ReportFiles/MARCOS_YoY/"
        # Steve PC testing paths (commented out for normal use)
        # self.logpath = "C:/Users/<USER>/Documents/R Scripts/TestScripts/Marcos/YoY Sales/"
        # self.rptpath = "C:/Users/<USER>/Documents/R Scripts/TestScripts/Marcos/YoY Sales/"
        
        self.rptpath_central = "//*************/public/steveo/monday_reports/"
        
        # Database connection
        self.connection = None
        
        # Date variables
        self.query_date = datetime.datetime.now().strftime("%d-%b-%y").upper()
        # For testing: self.query_date = "05-MAY-19"
        
        self.report_date = datetime.datetime.strptime(self.query_date, "%d-%b-%y").strftime("%Y%m%d")
        self.report_time = datetime.datetime.now().strftime("%H%M%S")
        self.report_time_txt = datetime.datetime.now().strftime("%H:%M:%S %Z")
        
        # Email parameters
        self.warn_recip = [
            "Steve Olson<<EMAIL>>",
            "Sean Coyle<<EMAIL>>"
        ]
        self.warn_sig = ("<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> "
                        "(847)904-9043 Office<br/> (715)379-8525 Cell")
        self.test_recip = ["<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>"]
        
        # Initialize error log
        self.MyErrorLog = None
        
    def connect_database(self):
        """Connect to Oracle database"""
        try:
            username = "deanna"
            password = keyring.get_password("Oracle", "deanna")
            dsn = "FVPA64"  # You may need to configure this as a TNS name or connection string
            
            self.connection = cx_Oracle.connect(username, password, dsn)
            print("Database connection successful")
            return True
        except Exception as e:
            print(f"Database connection failed: {e}")
            return False
    
    def disconnect_database(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            print("Database connection closed")
    
    def mailsend(self, recipients: List[str], subject: str, body: str, attachment: Optional[str] = None):
        """Send email using Gmail SMTP"""
        try:
            sender = "Marcos YoY Sales Report <<EMAIL>>"
            sender_email = "<EMAIL>"
            sender_password = keyring.get_password("GMail", "steve")
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = sender
            msg['To'] = ", ".join(recipients)
            msg['Reply-To'] = "<EMAIL>"
            msg['Subject'] = subject
            
            # Add body to email
            msg.attach(MIMEText(body, 'html'))
            
            # Add attachment if provided
            if attachment and os.path.exists(attachment):
                with open(attachment, "rb") as attachment_file:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment_file.read())
                
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {os.path.basename(attachment)}'
                )
                msg.attach(part)
            
            # Gmail SMTP configuration
            server = smtplib.SMTP('smtp.gmail.com', 587)
            server.starttls()
            server.login(sender_email, sender_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(sender_email, recipients, text)
            server.quit()
            
            print(f"Email sent successfully to {recipients}")
            return True
            
        except Exception as e:
            print(f"Failed to send email: {e}")
            return False
    
    def writelog(self, log_table: pd.DataFrame):
        """Write log table to CSV file"""
        try:
            log_file = os.path.join(self.logpath, self.logname)
            log_table.to_csv(log_file, index=False)
            print(f"Log written to {log_file}")
        except Exception as e:
            print(f"Failed to write log: {e}")
    
    def file_opened(self, path: str) -> bool:
        """Check if file is currently opened/locked"""
        try:
            with open(path, 'a'):
                return False
        except IOError:
            return True
    
    def day_ord(self, date_obj: datetime.datetime) -> str:
        """Return day with ordinal suffix (1st, 2nd, 3rd, etc.)"""
        day = date_obj.day
        if 10 <= day % 100 <= 20:
            suffix = 'th'
        else:
            suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
        return f"{day}{suffix}"
    
    def create_html_table(self, df: pd.DataFrame, caption: str = "") -> str:
        """Convert pandas DataFrame to HTML table"""
        html = df.to_html(
            classes='table table-bordered',
            table_id='data-table',
            escape=False,
            index=False,
            border=2
        )
        if caption:
            html = html.replace('<table', f'<table caption="{caption}"')
        return html
    
    def check_initialize_log(self):
        """Check if log exists and is current, initialize if needed"""
        new_error_log = False
        log_file = os.path.join(self.logpath, self.logname)
        
        if os.path.exists(log_file):
            try:
                self.MyErrorLog = pd.read_csv(log_file)
                # Check if log is from prior date
                if self.MyErrorLog.iloc[0]['QUERY_DATE'] != self.query_date:
                    new_error_log = True
            except Exception as e:
                print(f"Error reading existing log: {e}")
                new_error_log = True
        else:
            new_error_log = True
        
        if new_error_log:
            self.MyErrorLog = pd.DataFrame({
                'QUERY_DATE': [self.query_date],
                'SALES_STATUS': ['NO LOG FILE'],
                'QUERY_STATUS': ['NO LOG FILE'],
                'PROGRESS': ['NO LOG FILE']
            })
    
    def check_data_readiness(self) -> Dict[str, str]:
        """Check if FT_ORDER table and weekly summary are ready"""
        myquery = f"""
        SELECT 
        DISTINCT TRUNC(o.DAY) AS DAY,
        COUNT(DISTINCT o.STORE_NUMBER) AS ST_COUNT,
        COUNT(DISTINCT(w.e_date)) as WEEKLY_COUNT
        FROM ft_order o
        left join steve.ft_order_summary_weekly w on o.day = w.e_date
        WHERE o.DAY >= trunc(to_date('{self.query_date}') - 7, 'IW') 
        and o.DAY < trunc(to_date('{self.query_date}') - 0, 'IW') 
        GROUP BY TRUNC(o.DAY)
        ORDER BY TRUNC(o.DAY)
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(myquery)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            ftorder_stcnt = pd.DataFrame(results, columns=columns)
            cursor.close()
            
            # Convert Oracle dates to pandas datetime
            ftorder_stcnt['DAY'] = pd.to_datetime(ftorder_stcnt['DAY'])
            
            # Determine status
            if len(ftorder_stcnt) < 7:
                ftorder_status = 'NOT READY'
            elif ftorder_stcnt['ST_COUNT'].min() <= (ftorder_stcnt['ST_COUNT'].max() * 0.03 + 6):
                ftorder_status = 'NOT READY'
            else:
                ftorder_status = 'READY'
            
            if ftorder_stcnt['WEEKLY_COUNT'].max() == 1:
                ftweekly_status = 'READY'
            else:
                ftweekly_status = 'NOT READY'
            
            return {
                'SALES_STATUS': ftorder_status,
                'WEEKLY_SUMMARY_STATUS': ftweekly_status,
                'ftorder_stcnt': ftorder_stcnt
            }
            
        except Exception as e:
            print(f"Error checking data readiness: {e}")
            return {
                'SALES_STATUS': 'ERROR',
                'WEEKLY_SUMMARY_STATUS': 'ERROR',
                'ftorder_stcnt': pd.DataFrame()
            }
    
    def get_date_ranges(self) -> Dict[str, str]:
        """Get date ranges for the sales query"""
        mydatesquery = f"""
        select 
            min(cy.period_year) as period_year, 
            to_char(max(cy.e_date) + 28, 'DD-MON-RR') as max_e_date,
            to_char(min(py.e_date), 'DD-MON-RR') as min_py_e_date
        from mp_calendar_weekly cy
        left join mp_calendar_weekly py on (cy.period_year - 1) = py.period_year
        where cy.period_year = (select period_year from mp_calendar where to_date('{self.query_date}') between s_date and e_date)
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(mydatesquery)
            result = cursor.fetchone()
            cursor.close()
            
            return {
                'PERIOD_YEAR': str(result[0]),
                'MAX_E_DATE': result[1],
                'MIN_PY_E_DATE': result[2]
            }
        except Exception as e:
            print(f"Error getting date ranges: {e}")
            return {}
    
    def get_sales_data(self, dates: Dict[str, str]) -> pd.DataFrame:
        """Execute main sales query and return DataFrame"""
        mysalesquery = f"""
        select
            STORE_NUMBER,
            S_DATE,
            E_DATE,
            PERIOD_NUM,
            WEEK_NUM,
            SUM(LASTYEAR_SALES) AS LASTYEAR_SALES,
            SUM(THISYEAR_SALES) AS THISYEAR_SALES,
            SUM(LASTYEAR_ORDERS) AS LASTYEAR_ORDERS,
            SUM(THISYEAR_ORDERS) AS THISYEAR_ORDERS
        from
        (
              select
                  CySales.store_number
              ,   cal.s_date
              ,   cal.e_date
              ,   cal.period_num
              ,   cal.week_num
              ,   CySales.LASTYEAR_SALES
              ,   CySales.THISYEAR_SALES
              ,   CySales.LASTYEAR_ORDERS
              ,   CySales.THISYEAR_ORDERS
              from mp_calendar_weekly cal
              left join
              (
                  select
                      store_number
                  ,   e_date
                  ,   to_number(NULL) as LASTYEAR_SALES
                  ,   netsales as THISYEAR_SALES
                  ,   to_number(NULL) as LASTYEAR_ORDERS
                  ,   totalorders as THISYEAR_ORDERS
                  from steve.ft_order_summary_weekly
                  where e_date >= to_date('{dates['MIN_PY_E_DATE']}')
              ) CySales
              on cal.e_date = CySales.e_date
              where cal.period_year >= {dates['PERIOD_YEAR']}
              and cal.e_date <= to_date('{dates['MAX_E_DATE']}')
             
             union
        
              select
                  PySales.store_number
              ,   cal.s_date
              ,   cal.e_date
              ,   cal.period_num
              ,   cal.week_num
              ,   PySales.LASTYEAR_SALES
              ,   PySales.THISYEAR_SALES
              ,   PySales.LASTYEAR_ORDERS
              ,   PySales.THISYEAR_ORDERS
              from mp_calendar_weekly cal
              left join
              (
                  select
                      store_number
                  ,   e_date
                  ,   netsales as LASTYEAR_SALES
                  ,   to_number(NULL) as THISYEAR_SALES
                  ,   totalorders as LASTYEAR_ORDERS
                  ,   to_number(NULL) as THISYEAR_ORDERS
                  from steve.ft_order_summary_weekly
                  where e_date >= to_date('{dates['MIN_PY_E_DATE']}')
              ) PySales
              on (cal.e_date - 364) = PySales.e_date
              where cal.period_year >= {dates['PERIOD_YEAR']}
              and cal.e_date <= to_date('{dates['MAX_E_DATE']}')
        )combined
        group by
            STORE_NUMBER,
            S_DATE,
            E_DATE,
            PERIOD_NUM,
            WEEK_NUM
        ORDER BY S_DATE, STORE_NUMBER
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(mysalesquery)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            yoy_sales = pd.DataFrame(results, columns=columns)
            cursor.close()
            
            # Add query date as final column (empty)
            yoy_sales[self.query_date] = None
            
            return yoy_sales
            
        except Exception as e:
            print(f"Error getting sales data: {e}")
            return pd.DataFrame()
    
    def save_report(self, data: pd.DataFrame, filename: str, path: str) -> bool:
        """Save report to specified path"""
        try:
            full_path = os.path.join(path, filename)
            
            if not os.path.exists(path):
                print(f"Directory does not exist: {path}")
                return False
            
            if self.file_opened(full_path):
                print(f"File is currently open/locked: {full_path}")
                return False
            
            data.to_csv(full_path, index=False, na_rep="")
            print(f"Report saved successfully: {full_path}")
            return True
            
        except Exception as e:
            print(f"Error saving report: {e}")
            return False
    
    def check_create_prior_year_report(self, dates: Dict[str, str]):
        """Check if prior year report exists, create if needed"""
        # Get prior year
        myquery = f"""
        select PERIOD_YEAR - 1 
         from MP_CALENDAR 
         where next_day(to_date('{self.query_date}') - 7, 'SUN') - 1 >= S_DATE 
          and next_day(to_date('{self.query_date}') - 7, 'SUN') - 1 < E_DATE + 1
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(myquery)
            py_salesyear = cursor.fetchone()[0]
            cursor.close()
            
            myFNpy = os.path.join(self.rptpath_central, f"MARCOS_YoY_Sales_{py_salesyear}.csv")
            current_date = datetime.datetime.strptime(self.query_date, "%d-%b-%y")
            
            if not os.path.exists(myFNpy) or current_date.timetuple().tm_yday <= 36:
                # Create prior year report
                print("Creating prior year report...")
                py_dates = self.get_prior_year_dates()
                if py_dates:
                    py_sales_data = self.get_prior_year_sales_data(py_dates)
                    if not py_sales_data.empty:
                        # Save local copy
                        local_file = f"MARCOS_YoY_Sales_{py_salesyear}.csv"
                        if not self.save_report(py_sales_data, local_file, self.rptpath):
                            self.send_file_error_email(os.path.join(self.rptpath, local_file))
                        
                        # Save central copy
                        central_file = f"MARCOS_YoY_Sales_{py_salesyear}.csv"
                        if not self.save_report(py_sales_data, central_file, self.rptpath_central):
                            self.send_file_error_email(os.path.join(self.rptpath_central, central_file))
                            
        except Exception as e:
            print(f"Error checking/creating prior year report: {e}")
    
    def get_prior_year_dates(self) -> Dict[str, str]:
        """Get date ranges for prior year sales query"""
        mypydatesquery = f"""
        select 
            min(cy.period_year) as period_year, 
            to_char(max(cy.e_date) + 28, 'DD-MON-RR') as max_e_date,
            to_char(min(py.e_date), 'DD-MON-RR') as min_py_e_date
        from mp_calendar_weekly cy
        left join mp_calendar_weekly py on (cy.period_year - 1) = py.period_year
        where cy.period_year = (select period_year - 1 from mp_calendar where to_date('{self.query_date}') between s_date and e_date)
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(mypydatesquery)
            result = cursor.fetchone()
            cursor.close()
            
            return {
                'PERIOD_YEAR': str(result[0]),
                'MAX_E_DATE': result[1],
                'MIN_PY_E_DATE': result[2]
            }
        except Exception as e:
            print(f"Error getting prior year date ranges: {e}")
            return {}
    
    def get_prior_year_sales_data(self, dates: Dict[str, str]) -> pd.DataFrame:
        """Get prior year sales data"""
        # Same query structure as current year but with prior year dates
        return self.get_sales_data(dates)
    
    def send_file_error_email(self, filename: str):
        """Send email notification for file save errors"""
        bodytext = (f"This is an automated email to inform you that it appears "
                   f"<b>the following file WAS NOT SAVED</b> during the "
                   f"<b>Marco's YoY Sales Report</b> routine.<br/><br/>"
                   f"{filename}<br/><br/>"
                   f"Either the path wasn't accessible or the file was open in another process."
                   f"<br/><br/>The routine should continue without saving this file.<br/><br/>"
                   f"{self.warn_sig}")
        
        self.mailsend(self.warn_recip, "Marco's YoY Sales Issue: REPORT FILE NOT SAVED", bodytext)
    
    def send_data_not_ready_email(self, sales_status: Dict[str, Any]):
        """Send email when data is not ready"""
        # Create error log
        query_status = pd.DataFrame({'QUERY_STATUS': ['NOT STARTED']})
        
        progress = []
        if sales_status['SALES_STATUS'] != 'READY':
            progress.append('SALES STATUS')
        if sales_status['WEEKLY_SUMMARY_STATUS'] != 'READY':
            progress.append('FT_ORDER_SUMMARY_WEEKLY STATUS')
        
        progress_status = pd.DataFrame({'PROGRESS': ['; '.join(progress)]})
        
        # Update error log
        sales_df = pd.DataFrame({
            'QUERY_DATE': [self.query_date],
            'SALES_STATUS': [sales_status['SALES_STATUS']],
            'WEEKLY_SUMMARY_STATUS': [sales_status['WEEKLY_SUMMARY_STATUS']]
        })
        
        self.MyErrorLog = pd.concat([sales_df, query_status, progress_status], axis=1)
        self.writelog(self.MyErrorLog)
        
        print('TABLES NOT READY')
        
        # Create email body
        ftorder_stcnt = sales_status['ftorder_stcnt'].copy()
        ftorder_stcnt['DAY'] = ftorder_stcnt['DAY'].dt.strftime('%Y-%m-%d')
        
        bodytext = (f"This is an automated email to inform you that it appears that the SALES table "
                   f"needed for the <b>Marco's YoY Sales Report</b> routine is not ready yet.<br/><br/>"
                   f"{self.create_html_table(self.MyErrorLog, f'Marcos YoY Sales Report ({self.report_time_txt})')}"
                   f"<br><br>Here are the store counts per day per the <b>ft_orders</b> table:<br>"
                   f"{self.create_html_table(ftorder_stcnt)}"
                   f"<br/>The routine will attempt to run again in 40 minutes.<br/><br/>"
                   f"{self.warn_sig}")
        
        self.mailsend(self.warn_recip, "Marco's YoY Sales Issue: TABLES NOT READY", bodytext)
    
    def run(self):
        """Main execution method"""
        print(f"Starting Marco's YoY Sales Report for {self.query_date}")
        
        # Connect to database
        if not self.connect_database():
            print("Failed to connect to database. Exiting.")
            return False
        
        try:
            # Initialize log
            self.check_initialize_log()
            
            # Check data readiness
            sales_status = self.check_data_readiness()
            
            if (sales_status['SALES_STATUS'] != 'READY' or 
                sales_status['WEEKLY_SUMMARY_STATUS'] != 'READY'):
                
                self.send_data_not_ready_email(sales_status)
                return False
            
            # Data is ready, proceed with processing
            if (sales_status['SALES_STATUS'] == 'READY' and 
                sales_status['WEEKLY_SUMMARY_STATUS'] == 'READY' and 
                self.MyErrorLog.iloc[0]['PROGRESS'] != 'COMPLETE'):
                
                # Update log - started
                query_status = pd.DataFrame({'QUERY_STATUS': ['STARTED']})
                progress_status = pd.DataFrame({'PROGRESS': ['QUERY STATUS']})
                sales_df = pd.DataFrame({
                    'QUERY_DATE': [self.query_date],
                    'SALES_STATUS': [sales_status['SALES_STATUS']],
                    'WEEKLY_SUMMARY_STATUS': [sales_status['WEEKLY_SUMMARY_STATUS']]
                })
                
                self.MyErrorLog = pd.concat([sales_df, query_status, progress_status], axis=1)
                self.writelog(self.MyErrorLog)
                
                # Get date ranges
                dates = self.get_date_ranges()
                if not dates:
                    print("Failed to get date ranges")
                    return False
                
                # Get sales data
                yoy_sales = self.get_sales_data(dates)
                if yoy_sales.empty:
                    print("Failed to get sales data")
                    return False
                
                # Update log - query complete
                self.MyErrorLog.iloc[0, self.MyErrorLog.columns.get_loc('QUERY_STATUS')] = 'COMPLETE'
                self.MyErrorLog.iloc[0, self.MyErrorLog.columns.get_loc('PROGRESS')] = 'SAVING LOCAL REPORT'
                self.writelog(self.MyErrorLog)
                
                # Save local report
                local_filename = "MARCOS_YoY_Sales.csv"
                if not self.save_report(yoy_sales, local_filename, self.rptpath):
                    self.send_file_error_email(os.path.join(self.rptpath, local_filename))
                
                # Update log - saving central report
                self.MyErrorLog.iloc[0, self.MyErrorLog.columns.get_loc('PROGRESS')] = 'SAVING CENTRAL REPORT'
                self.writelog(self.MyErrorLog)
                
                # Save central report
                central_filename = "MARCOS_YoY_Sales.csv"
                if self.save_report(yoy_sales, central_filename, self.rptpath_central):
                    # Only mark complete if central file saved successfully
                    self.MyErrorLog.iloc[0, self.MyErrorLog.columns.get_loc('PROGRESS')] = 'COMPLETE'
                    self.writelog(self.MyErrorLog)
                else:
                    self.send_file_error_email(os.path.join(self.rptpath_central, central_filename))
                
                # Check/create prior year report
                self.check_create_prior_year_report(dates)
                
                print("Marco's YoY Sales Report completed successfully")
                return True
            
            else:
                print("Sales not ready or previously completed")
                return False
                
        except Exception as e:
            print(f"Error in main execution: {e}")
            return False
            
        finally:
            self.disconnect_database()


def main():
    """Main entry point"""
    processor = MarcosYoYSalesProcessor()
    success = processor.run()
    
    if success:
        print("Process completed successfully")
        sys.exit(0)
    else:
        print("Process failed or skipped")
        sys.exit(1)


if __name__ == "__main__":
    main() 