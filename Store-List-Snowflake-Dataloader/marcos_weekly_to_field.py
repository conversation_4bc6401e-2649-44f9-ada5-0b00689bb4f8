"""
Marcos Weekly To Field - Python Conversion
Converted from R script: Marcos_Weekly_To_Field - SF.R
Version: 20250312 (Python conversion)

This script downloads the latest weekly Marcos Weekly results and
emails the following:
1) Restaurants get the stores in their District (if a DM name is present, otherwise just their store)
2) DMs get the stores in their Region
"""

import pandas as pd
import numpy as np
import os
import sys
import time
import datetime as dt
import re
import logging
from pathlib import Path
# import keyring
# import smtplib
# import base64
# import json
# from email.mime.multipart import MIMEMultipart
# from email.mime.text import MimeText
# from email.mime.base import MimeBase
# from email import encoders
# import snowflake.connector
# import gspread
# from google.oauth2.service_account import Credentials
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, NamedStyle
from openpyxl.utils.dataframe import dataframe_to_rows
# from googleapiclient.discovery import build
# from google.auth.transport.requests import Request
# from google.oauth2.credentials import Credentials as OAuthCredentials
# from google_auth_oauthlib.flow import InstalledAppFlow

import libs.snowflake_helper as sf
import libs.email_client as email_client
from libs.excel_helper import SharePointExcelOnline

OVERRIDE_EMAIL_RECIPIENTS = True

# Global configuration
class Config:
    # Version and testing parameters
    TESTING_EMAILS = False  # NORMAL, set to True for testing
    
    # Dates
    QUERY_DATE = dt.datetime.now().strftime("%d-%b-%y")
    REPORT_NAME = "Marcos Weekly"
    
    # Email notes configuration
    EMAILNOTE_STORE_DATE = dt.datetime.strptime("19-DEC-22", "%d-%b-%y").date()
    EMAILNOTE_STORE = '<p><span style="color: #0000ff;">This is a re-send and replaces the one from this morning. OT has been corrected in this version. Sorry for any confusion!</span></p>'
    
    EMAILNOTE_DM_DATE = dt.datetime.strptime("19-DEC-22", "%d-%b-%y").date()
    EMAILNOTE_DM = '<p><span style="color: #0000ff;">This is a re-send and replaces the one from this morning. OT has been corrected in this version. Sorry for any confusion!</span></p>'
    
    # Report configuration
    REPORT_START_DATE = dt.datetime.strptime(QUERY_DATE, "%d-%b-%y").strftime("%Y%m%d")
    REPORT_TIME = dt.datetime.now().strftime("%H%M%S")
    REPORT_TIME_TXT = dt.datetime.now().strftime("%H:%M:%S %Z")
    PLOT_START_DATE = dt.datetime.strptime(QUERY_DATE, "%d-%b-%y").strftime("%m/%d/%Y")
    
    # Google Sheet WE date to most recent Sunday as m/d/yy
    GSHT_WE_SN = f"WE {dt.datetime.strptime(QUERY_DATE, '%d-%b-%y').strftime('%m/%d/%y')}"
    
    # Paths
    LOG_NAME = "MyMarcosWeekly-Log.csv"
    RPT_FOLDER = "Marcos_Weekly_To_Field"
    
    # Computer detection
    # THIS_COMPUTER = os.environ.get("COMPUTERNAME", "")
    # PROD_COMPUTERS = ["DESKTOP-TABLEAU"]
    # TEST_COMPUTERS = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
    # TESTING_PC = False #THIS_COMPUTER not in PROD_COMPUTERS
    BASE_DIR = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
    # # Paths based on computer type
    # if TESTING_PC:
    #     LOG_PATH = Path("//*************/public/steveo/R Stuff/ReportFiles") / RPT_FOLDER
    #     RPT_PATH = Path("//*************/public/steveo/R Stuff/ReportFiles") / RPT_FOLDER
    #     PLOT_PATH = Path("//*************/public/steveo/R Stuff/ReportFiles") / RPT_FOLDER / "Plots"
    #     RM_DM_PATH = Path("//*************/public/steveo/R Stuff/ReportFiles") / RPT_FOLDER / "RM-DM Files"
    # else:
    LOG_PATH = BASE_DIR / RPT_FOLDER
    RPT_PATH = BASE_DIR / RPT_FOLDER
    PLOT_PATH = BASE_DIR / RPT_FOLDER / "Plots"
    RM_DM_PATH = BASE_DIR / RPT_FOLDER / "RM-DM Files"
    
    # Excel file naming
    RM_DM_XL_FN = "Combined Weekly-"
    RPT_PATH_CENTRAL = Path("//*************/public/steveo/monday_reports")
    
    FILE_ID= "01FJIKBT4XE3YGZG7JFNHZGD2LFVP7OFIZ"
    SITE_URL = "https://highlandventuresltd442-my.sharepoint.com/personal/dflynn_hv_ltd"

    # Google Sheets configuration
    GSHEET_TOKEN_PATH = Path("C:/Users/<USER>/.R/gargle/gargle-oauth")
    GSHT_KEY = '1iNA_sdIJ6etKbC40fqG-DHPOs7-rvdHhesuT7xFjBqQ' # old
    GSHT_ST_COL_NAME = "STORE_NUMBER"
    GSHT_MGR_COL_NAME = "MGR"
    GSHT_DM_COL_NAME = "DM"
    GSHT_RM_COL_NAME = "RD"
    
    # Snowflake configuration
    # SF_ENVIRON = "PROD"  # "DEV", "STAGE", or "PROD"
    
    # if SF_ENVIRON == "STAGE":
    #     SF_DB = "STAGE_CSM_DB"
    #     SF_SCHEMA = "CORPORATE"
    #     SF_WH = "STAGE_DATA_ANA_WH"
    #     SF_ROLE = "AR_STAGE_CONSUMPTION_RW"
    #     SF_USER_KEY = "tableau_ID_stage"
    #     SF_PW_KEY = "tableau_PW_stage"
    # else:
    #     SF_DB = "PROD_CSM_DB"
    #     SF_SCHEMA = "CORPORATE"
    #     SF_WH = "PROD_DATA_ANA_WH"
    #     SF_ROLE = "AR_PROD_CONSUMPTION_RW"
    #     SF_USER_KEY = "tableau_ID_prod"
    #     SF_PW_KEY = "tableau_PW_prod"
    
    # Email configuration
    WARN_RECIP = ["<EMAIL>", "<EMAIL>","<EMAIL>"]
    TEST_WARN_RECIP = ["<EMAIL>"]
    WARN_SIG = "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
    
    HV_SIG_PATH = Path("//*************/public/steveo/R Stuff/ReportFiles/HTML_signatures.csv")
    TEST_RECIP = ["<EMAIL>"]
    TEST_CC_RECIP = ["<EMAIL>", "<EMAIL>"]
    
    GMAIL_AUTH_EMAIL = "<EMAIL>"
    
    # Known columns for Google Sheet validation
    GSHT_RANK_COLS = [
        "WAUS Rank", "8wk Avg Sales Rank", "SSS % Rank", "Coup % Rank", "Non Coup % Rank",
        "Avg Check Rank", "SSO % Rank", "# of Voids Rank", "Cash Over/Short Rank",
        "Cash Over/Short Rank (ABS)", "Delivery <30 % Rank", "Delivery>40 % Rank",
        "ADT Rank", "DPR Rank", "ACWT Rank", "Hang Up Calls Rank", "Hang Up % Rank",
        "Del Order % Rank", "Total Online % Rank", "Labor % Rank", "Ideal Hour Var Rank",
        "OT Rank", "SPLH Rank", "Food Var Rank", "ALT Rank", "PPLH Rank"
    ]

# class DatabaseManager:
#     """Handle Snowflake database connections and queries"""
    
#     def __init__(self):
#         self.connection = None
        
#     def connect(self):
#         """Connect to Snowflake database"""
#         try:
#             sf_user = keyring.get_password("SfHV", Config.SF_USER_KEY)
#             sf_pw = keyring.get_password("SfHV", Config.SF_PW_KEY)
            
#             self.connection = snowflake.connector.connect(
#                 user=sf_user,
#                 password=sf_pw,
#                 account='your_account',  # Replace with actual account
#                 warehouse=Config.SF_WH,
#                 database=Config.SF_DB,
#                 schema=Config.SF_SCHEMA,
#                 role=Config.SF_ROLE
#             )
            
#             # Set timezone
#             cursor = self.connection.cursor()
#             cursor.execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
#             cursor.close()
            
#             return True
#         except Exception as e:
#             logging.error(f"Database connection failed: {e}")
#             return False
    
#     def execute_query(self, query):
#         """Execute a query and return results as DataFrame"""
#         if not self.connection:
#             if not self.connect():
#                 return None
        
#         try:
#             return pd.read_sql(query, self.connection)
#         except Exception as e:
#             logging.error(f"Query execution failed: {e}")
#             return None
    
#     def close(self):
#         """Close database connection"""
#         if self.connection:
#             self.connection.close()

class SheetsManager:
    """Handle Google Sheets operations"""
    
    def __init__(self, sf_obj, excel_helper):
        self.sf_obj = sf_obj
        self.excel_helper = excel_helper
        self.gc = None
        # self._authenticate()
    
    # def _authenticate(self):
    #     """Authenticate with Google Sheets API"""
    #     try:
    #         # Use service account credentials or OAuth flow
    #         scope = ['https://spreadsheets.google.com/feeds',
    #                 'https://www.googleapis.com/auth/drive']
            
    #         # Try service account first, fall back to OAuth
    #         try:
    #             creds = Credentials.from_service_account_file('path/to/service_account.json', scopes=scope)
    #             self.gc = gspread.authorize(creds)
    #         except:
    #             # OAuth flow for user authentication
    #             flow = InstalledAppFlow.from_client_secrets_file('path/to/credentials.json', scope)
    #             creds = flow.run_local_server(port=0)
    #             self.gc = gspread.authorize(creds)
                
    #     except Exception as e:
    #         logging.error(f"Google Sheets authentication failed: {e}")
    
    def read_sheet(self, site_url, file_id, sheet_name):
        """Read data from Google Sheet"""
        try:
            # sheet = self.gc.open_by_key(sheet_key)
            # worksheet = sheet.worksheet(sheet_name)
            # data = worksheet.get_all_records()
            # return pd.DataFrame(data)

            excel_session = self.excel_helper.get_excel_file_by_id(site_url, file_id)
            existing_data_from_sheet_dict = self.excel_helper.get_worksheet_data(excel_session, sheet_name)
            values_existing_data_from_sheet_list = existing_data_from_sheet_dict.get('values')
            existing_data_from_sheet_df = pd.DataFrame(data=values_existing_data_from_sheet_list[1:], columns=values_existing_data_from_sheet_list[0])
            return existing_data_from_sheet_df

        except Exception as e:
            # logging.error(f"Failed to read Google Sheet: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Failed to read Google Sheet: {e}", process_type=Config.REPORT_NAME, script_file_name=__file__, log_type='Error')
            return None

class ExcelManager:
    """Handle Excel file creation and formatting"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
    
    def create_workbook(self, title="Combined Weekly"):
        """Create a new Excel workbook"""
        self.workbook = Workbook()
        self.workbook.properties.title = title
        self.workbook.properties.creator = "Python Conversion"
        return self.workbook
    
    def create_worksheet(self, name, data):
        """Create a worksheet with data and formatting"""
        if self.workbook is None:
            self.create_workbook()
        
        # Remove default sheet if it exists
        if "Sheet" in [ws.title for ws in self.workbook.worksheets]:
            self.workbook.remove(self.workbook["Sheet"])
        
        self.worksheet = self.workbook.create_sheet(name[:31])  # Excel sheet name limit
        
        # Add data
        for r in dataframe_to_rows(data, index=False, header=True):
            self.worksheet.append(r)
        
        # Apply formatting
        self._apply_formatting(data)
        
        return self.worksheet
    
    def _apply_formatting(self, data):
        """Apply formatting to the worksheet"""
        if not self.worksheet:
            return
        
        # Header formatting
        header_font = Font(bold=True, color="000000")
        header_fill = PatternFill(start_color="FFCC00", end_color="FFCC00", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center", text_rotation=90, wrap_text=True)
        
        # Apply header formatting
        for cell in self.worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # Column width adjustments
        column_widths = {
            Config.GSHT_ST_COL_NAME: 4.75,
            "CITY": 16.5,
            "LOC_NAME": 16.5,
            Config.GSHT_RM_COL_NAME: 12,
            Config.GSHT_DM_COL_NAME: 12,
            Config.GSHT_MGR_COL_NAME: 12,
            "Final Tier": 6.5,
            "MOMs Installation Week": 10.5
        }
        
        for col_idx, column in enumerate(data.columns, 1):
            col_letter = self.worksheet.cell(row=1, column=col_idx).column_letter
            width = column_widths.get(column, 7.75)
            self.worksheet.column_dimensions[col_letter].width = width
        
        # Row height
        for row in range(1, len(data) + 2):
            self.worksheet.row_dimensions[row].height = 30 if row > 1 else 57
    
    def save_workbook(self, filepath):
        """Save the workbook to file"""
        if self.workbook:
            self.workbook.save(filepath)

class EmailManager:
    """Handle email operations"""
    
    def __init__(self):
        self.smtp_server = None
        self.auth_email = Config.GMAIL_AUTH_EMAIL
    
    def authenticate(self):
        """Set up email authentication"""
        # This would implement Gmail API authentication
        # For now, using placeholder
        pass
    
    def send_email(self, recipients, subject, body, attachments=None, test=False):
        """Send email with optional attachments"""
        try:
            if test:
                recipients = Config.TEST_RECIP
                body = f"<p><b>TEST SEND (normal recipient: {', '.join(recipients)})</b></p>{body}"
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{Config.REPORT_NAME} <{self.auth_email}>"
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MimeText(body, 'html'))
            
            # Add attachments
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # Send email (implement Gmail API or SMTP)
            # This is a placeholder - implement actual email sending
            logging.info(f"Email sent to: {recipients}")
            
            # Add delay to prevent rate limiting
            time.sleep(6)
            
        except Exception as e:
            logging.error(f"Failed to send email: {e}")
    
    def _add_attachment(self, msg, filepath):
        """Add file attachment to email"""
        try:
            with open(filepath, "rb") as attachment:
                part = MimeBase('application', 'octet-stream')
                part.set_payload(attachment.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {os.path.basename(filepath)}'
                )
                msg.attach(part)
        except Exception as e:
            logging.error(f"Failed to add attachment {filepath}: {e}")

class LogManager:
    """Handle logging operations"""
    
    def __init__(self):
        self.log_path = Config.LOG_PATH / Config.LOG_NAME
        self.error_log = None
        self._initialize_log()
    
    def _initialize_log(self):
        """Initialize or load existing log"""
        if self.log_path.exists():
            try:
                self.error_log = pd.read_csv(self.log_path)
                # Check if log is from prior week
                if self.error_log.iloc[0]['QUERY_DATE'] != Config.QUERY_DATE:
                    self._create_new_log()
            except Exception as e:
                logging.error(f"Failed to load existing log: {e}")
                self._create_new_log()
        else:
            self._create_new_log()
    
    def _create_new_log(self):
        """Create new log entry"""
        self.error_log = pd.DataFrame({
            'QUERY_DATE': [Config.QUERY_DATE],
            'QUERY_STATUS': ['NO LOG FILE'],
            'GS_STATUS': ['NO LOG FILE'],
            'SUMMARY_STATUS': ['NO LOG FILE'],
            'ST_EMAIL_STATUS': ['NO LOG FILE'],
            'DM_EMAIL_STATUS': ['NO LOG FILE'],
            'RM_EMAIL_STATUS': ['NO LOG FILE'],
            'PROGRESS': ['NO LOG FILE']
        })
    
    def update_log(self, **kwargs):
        """Update log with new status"""
        for key, value in kwargs.items():
            if key.upper() in self.error_log.columns:
                self.error_log.iloc[0][key.upper()] = value
        self.write_log()
    
    def write_log(self):
        """Write log to file"""
        try:
            self.error_log.to_csv(self.log_path, index=False)
        except Exception as e:
            logging.error(f"Failed to write log: {e}")

class MarcosWeeklyProcessor:
    """Main processor for Marcos Weekly reports"""
    
    def __init__(self):

        # self.db_manager = DatabaseManager()
        self.sf_obj = sf.SnowflakeHelper()
        self.excel_helper = SharePointExcelOnline()

        self.sheets_manager = SheetsManager(self.sf_obj, self.excel_helper)
        self.excel_manager = ExcelManager()
        self.email_manager = EmailManager()
        self.log_manager = LogManager()
        
        self.store_list = None
        self.gsht_stores = None
        self.okay_to_continue = True
    
    def run(self):
        """Main execution flow"""
        try:
            logging.info("Starting Marcos Weekly processing...")
            
            # Query store list
            if self.okay_to_continue:
                self._query_store_list()
            
            # Get Google Sheet data
            if self.okay_to_continue:
                self._get_google_sheet_data()
            
            # Create Excel files
            if self.okay_to_continue:
                self._create_excel_files()
            
            # Send emails
            if self.okay_to_continue:
                self._send_emails()
            
            # Mark as complete
            self.log_manager.update_log(progress="COMPLETE")
            logging.info("Marcos Weekly processing completed successfully")
            
        except Exception as e:
            logging.error(f"Processing failed: {e}")
            self.log_manager.update_log(progress=f"FAILED: {str(e)}")
        # finally:
        #     self.db_manager.close()
    
    def _query_store_list(self):
        """Query store list from database"""
        self.log_manager.update_log(query_status="STARTING ST LIST")
        
        query = """
        select
            ab_store.st as STORE,
            ab_store.CITY as CITY,
            (case when Mgr.fname is not null
            then initcap(Mgr.fname)
            else 'No' end) as MANAGER_FNAME,
            (case when Mgr.lname is not null
            then initcap(Mgr.lname)
            else 'Mgr' end) as MANAGER_LNAME,
            Mgr.EMAIL as MANAGER_EMAIL,
            (case when district.fname is not null
            then initcap(district.fname)
            else 'No' end) as DM_FNAME,
            (case when district.lname is not null
            then initcap(district.lname)
            else 'DM' end) as DM_LNAME,
            (case when district.fname is not null
            then initcap(district.fname)||' '||initcap(district.lname)
            else 'No DM' end) as DM_FULLNAME,
            district.EMAIL AS DM_EMAIL,
            (case when regional.fname is not null
            then initcap(regional.fname)
            else 'No' end) as RM_FNAME,
            (case when regional.lname is not null
            then initcap(regional.lname)
            else 'RM' end) as RM_LNAME,
            regional.EMAIL as RM_EMAIL,
            ab_store.o_date as OPEN_DATE,
            (case 
                when UPPER(hrloc.LOC_INFORMATION15) LIKE 'FVMC%'
                    then 'fv0'||lpad(ab_store.st,3,0)||'@fvmc.com'
                when UPPER(hrloc.LOC_INFORMATION15) LIKE 'VET%'
                    then 'vt'||lpad(ab_store.st,4,0)||'@familyvetgroup.com'
                when UPPER(hrloc.LOC_INFORMATION15) LIKE 'HF%'
                    then 'mp'||lpad(ab_store.st,4,0)||'@fvmc.com'
                when UPPER(hrloc.LOC_INFORMATION15) LIKE 'STFIT%'
                    then 'sf'||lpad(ab_store.st,4,0)||'@stayfit24.com'
                end
            ) as STORE_EMAIL
        from ab_store
        left join
        (select ab_employees.paynum,
        ab_employees.fname,
        ab_employees.lname,
        ab_store_employees.store,
        ab_employees.EMAIL
        from ab_store_employees
        inner join ab_employees
        on ab_store_employees.paynum = ab_employees.paynum
        inner join famv_payroll_position pos
        on ab_employees.position = pos.position
        and sub_categories like '%,DIST_RPT_INC,%'
        where status = 'A') district
        on ab_store.st = district.store
        left join
        (select ab_employees.paynum,
        ab_employees.fname,
        ab_employees.lname,
        ab_store_employees.store,
        ab_employees.EMAIL
        from ab_store_employees
        inner join ab_employees
        on ab_store_employees.paynum = ab_employees.paynum
        inner join famv_payroll_position pos
        on ab_employees.position = pos.position
        and sub_categories like '%,REG_RPT_INC,%'
        where status = 'A') regional
        on ab_store.st = regional.store
        left join
        (select ab_employees.paynum,
        ab_employees.fname,
        ab_employees.lname,
        ab_store_employees.store,
        ab_employees.EMAIL
        from ab_store_employees
        inner join ab_employees
        on ab_store_employees.paynum = ab_employees.paynum
        inner join famv_payroll_position pos
        on ab_employees.position = pos.position
        and sub_categories like '%,MGR_HISTORY,%'
        where status = 'A') Mgr
        on ab_store.st = Mgr.store
        left join hr_locations_all hrloc
        on lpad(ab_store.st,4,0) = hrloc.location_code
        where ab_store.st >= 2
        and ab_store.st < 7000
        order by ab_store.st
        """
        
        self.store_list = self.sf_obj.execute_query(query, return_df=True)

        
        if self.store_list is not None and len(self.store_list) > 1:
            self.log_manager.update_log(query_status="COMPLETE")
        else:
            self.log_manager.update_log(query_status="FAILED")
            self.okay_to_continue = False
    
    def _get_google_sheet_data(self):
        """Get data from Google Sheets"""
        self.log_manager.update_log(gs_status="LOADING GOOGLE SHEET")
        
        # Read the Google Sheet
        # self.gsht_stores = self.sheets_manager.read_sheet(Config.GSHT_KEY, Config.GSHT_WE_SN)
        self.gsht_stores = self.sheets_manager.read_sheet(Config.SITE_URL, Config.FILE_ID, Config.GSHT_WE_SN)
        
        if self.gsht_stores is not None and len(self.gsht_stores) > 0:
            # Find Grand Total row and filter data
            grand_total_row = 0
            for col in self.gsht_stores.columns:
                gt_rows = self.gsht_stores[self.gsht_stores[col] == "Grand Total"].index
                if len(gt_rows) > 0:
                    grand_total_row = max(grand_total_row, gt_rows[0])
            
            if grand_total_row > 0:
                self.gsht_stores = self.gsht_stores.iloc[:grand_total_row]
            
            # Validate data
            self._validate_google_sheet_data()
            
            if self.okay_to_continue:
                self.log_manager.update_log(gs_status="COMPLETE")
        else:
            self.log_manager.update_log(gs_status="No Data or Error")
            self.okay_to_continue = False
    
    def _validate_google_sheet_data(self):
        """Validate Google Sheet data"""
        # Check for unknown columns
        pct_cols = [col for col in self.gsht_stores.columns if '%' in col]
        pct_cols = [col for col in pct_cols if col not in Config.GSHT_RANK_COLS]
        
        gsht_known_cols = [
            "CITY", "Final Tier", "Avg Check", "SPLH", "Avg Hourly Wage", "Min Wage",
            "Net Sales", "4 Wk Avg (NS)", "Total Order", "LY Net Sales", "WAUS",
            "8wk WAUS", "8 Wk WAUS", "Worked Hours", "Ideal Hours", "LY WAUS",
            "Average Delivery Time", "Hang Up Calls", "Hang Up Calls (Net)",
            "Avg Call Wait Time", "Ideal Hour Variance", "Avg. Ideal Hour Variance",
            "Deliverys per Run", "Deliveries per Run", "Net Hires", "# of Voids",
            "Cash Over/Short", "OT (CW)", "# of Voids (Combined)", "Combined Avg Worked Hours",
            "MOMs Installation Week", "Avg. Average Delivery Time", "Worked Hours Combined",
            "Avg. # of Voids", "Average Load Time", "PPLH", "ALT Rank", "PPLH Rank",
            "Pricing Tier (4/8/24)", "Pricing Tier (7/8/24)", "STORE_NUMBER", "LOC_NAME",
            "MGR", "PRICING TIER", "Avg Ticket", "Hangup Calls", "Total Worked Hours",
            "Var Total Hours vs. Ideal", "Total OT", "Cash Over/Short Rank (ABS)"
        ] + pct_cols + [Config.GSHT_ST_COL_NAME, Config.GSHT_RM_COL_NAME, 
                        Config.GSHT_DM_COL_NAME, Config.GSHT_MGR_COL_NAME] + Config.GSHT_RANK_COLS
        
        unknown_cols = set(self.gsht_stores.columns) - set(gsht_known_cols)
        
        if unknown_cols:
            self.log_manager.update_log(gs_status="UNKNOWN COL HDRS")
            # Send warning email about unknown columns
            self._send_warning_email("UNKNOWN COLUMNS", list(unknown_cols))
        
        # Check for unassigned rows
        assignment_cols = [Config.GSHT_RM_COL_NAME, Config.GSHT_DM_COL_NAME, Config.GSHT_ST_COL_NAME]
        assignment_na_counts = self.gsht_stores[assignment_cols].isna().sum(axis=1)
        unassigned_rows = len(assignment_na_counts[assignment_na_counts == 3])
        
        if unassigned_rows > 0:
            self.log_manager.update_log(gs_status="UNASSIGNED ROWS (missing RD, DM AND Store#)")
            self._send_warning_email("UNASSIGNED ROWS", f"{unassigned_rows} rows missing all assignments")
            self.okay_to_continue = False
    
    def _send_warning_email(self, issue_type, details):
        """Send warning email"""
        subject = f"Marco's Combined Weekly Issue: {issue_type}"
        body = f"""
        <p>This is an automated email to inform you of an issue with the Combined Weekly processing.</p>
        <p><b>{issue_type}:</b> {details}</p>
        <p>Please review and take appropriate action.</p>
        {Config.WARN_SIG}
        """
        
        recipients = Config.TEST_WARN_RECIP if Config.TESTING_EMAILS else Config.WARN_RECIP
        self.email_manager.send_email(recipients, subject, body, test=Config.TESTING_EMAILS)
    
    def _create_excel_files(self):
        """Create Excel files for DMs and RMs"""
        self.log_manager.update_log(summary_status="CREATING FILES")
        
        # Create RM-DM directory
        Config.RM_DM_PATH.mkdir(parents=True, exist_ok=True)
        
        # Clear old files
        for old_file in Config.RM_DM_PATH.glob("*"):
            if old_file.is_file():
                old_file.unlink()
        
        # Create DM files
        self._create_rm_dm_files("DM")
        
        # Create RM files  
        self._create_rm_dm_files("RM")
        
        self.log_manager.update_log(summary_status="COMPLETE")
    
    def _create_rm_dm_files(self, rm_or_dm):
        """Create Excel files for RMs or DMs"""
        col_name = Config.GSHT_DM_COL_NAME if rm_or_dm == "DM" else Config.GSHT_RM_COL_NAME
        
        # Get unique names
        unique_names = self.gsht_stores[col_name].dropna().unique()
        
        # Add individual stores without DM (for DM processing)
        if rm_or_dm == "DM":
            no_dm_stores = self.gsht_stores[self.gsht_stores[col_name].isna()][Config.GSHT_ST_COL_NAME].unique()
            unique_names = np.concatenate([unique_names, no_dm_stores.astype(str)])
        
        for name in unique_names:
            if name.isdigit():  # Store number
                data = self.gsht_stores[self.gsht_stores[Config.GSHT_ST_COL_NAME] == int(name)]
                filename = f"{Config.RM_DM_XL_FN}{Config.REPORT_START_DATE}-Store {name}.xlsx"
            else:  # RM/DM name
                data = self.gsht_stores[self.gsht_stores[col_name] == name]
                filename = f"{Config.RM_DM_XL_FN}{Config.REPORT_START_DATE}-{rm_or_dm} {name.upper()}.xlsx"
            
            if len(data) > 0:
                # Create Excel file
                excel_manager = ExcelManager()
                excel_manager.create_workbook()
                sheet_name = f"{Config.REPORT_START_DATE}-{name}"[:31]
                excel_manager.create_worksheet(sheet_name, data)
                
                filepath = Config.RM_DM_PATH / filename
                excel_manager.save_workbook(filepath)
    
    def _send_emails(self):
        """Send emails to stores and DMs"""
        # Send store emails
        self._send_store_emails()
        
        # Send DM emails  
        self._send_dm_emails()
    
    def _send_store_emails(self):
        """Send emails to stores"""
        if not Config.TESTING_EMAILS:  # Skip in testing mode for now
            return
            
        self.log_manager.update_log(st_email_status="EMAILING STARTED")
        
        # Get unique DMs and their stores
        unique_dms = self.gsht_stores[Config.GSHT_DM_COL_NAME].dropna().unique()
        
        for i, dm_name in enumerate(unique_dms):
            dm_stores = self.gsht_stores[self.gsht_stores[Config.GSHT_DM_COL_NAME] == dm_name]
            
            # Get store emails
            store_emails = []
            for _, store_row in dm_stores.iterrows():
                store_num = store_row[Config.GSHT_ST_COL_NAME]
                store_email = self._get_store_email(store_num)
                if store_email:
                    store_emails.append(store_email)
            
            if store_emails:
                # Create email content
                subject = f"{dm_name} - District Combined Weekly"
                body = f"""
                <h1>{dm_name} - District Combined Weekly</h1>
                <p>The Marco's Combined Weekly for your District is attached. 
                This Weekly is for the week ending {Config.QUERY_DATE}.</p>
                <p>Stores receive the file for all the stores in their DISTRICT.</p>
                {self._get_email_signature()}
                """
                
                # Find attachment file
                attachment_path = Config.RM_DM_PATH / f"{Config.RM_DM_XL_FN}{Config.REPORT_START_DATE}-DM {dm_name.upper()}.xlsx"
                
                if attachment_path.exists():
                    self.email_manager.send_email(
                        store_emails, subject, body, 
                        attachments=[str(attachment_path)], 
                        test=Config.TESTING_EMAILS
                    )
            
            self.log_manager.update_log(progress=f"{i+1} of {len(unique_dms)}")
        
        self.log_manager.update_log(st_email_status="COMPLETE")
    
    def _send_dm_emails(self):
        """Send emails to DMs"""
        if not Config.TESTING_EMAILS:  # Skip in testing mode for now
            return
            
        self.log_manager.update_log(dm_email_status="EMAILING STARTED")
        
        # Get unique RMs and their DMs
        unique_rms = self.gsht_stores[Config.GSHT_RM_COL_NAME].dropna().unique()
        
        for i, rm_name in enumerate(unique_rms):
            rm_dms = self.gsht_stores[self.gsht_stores[Config.GSHT_RM_COL_NAME] == rm_name]
            
            # Get DM emails
            dm_emails = []
            for dm_name in rm_dms[Config.GSHT_DM_COL_NAME].dropna().unique():
                dm_email = self._get_dm_email(dm_name)
                if dm_email:
                    dm_emails.append(dm_email)
            
            if dm_emails:
                # Create email content
                subject = f"{rm_name} - Region Combined Weekly"
                body = f"""
                <h1>{rm_name} - Region Combined Weekly</h1>
                <p>The Marco's Combined Weekly for your Region is attached. 
                This Weekly is for the week ending {Config.QUERY_DATE}.</p>
                <p>DMs receive the file for all the stores in their REGION.</p>
                {self._get_email_signature()}
                """
                
                # Find attachment file
                attachment_path = Config.RM_DM_PATH / f"{Config.RM_DM_XL_FN}{Config.REPORT_START_DATE}-RM {rm_name.upper()}.xlsx"
                
                if attachment_path.exists():
                    self.email_manager.send_email(
                        dm_emails, subject, body, 
                        attachments=[str(attachment_path)], 
                        test=Config.TESTING_EMAILS
                    )
            
            self.log_manager.update_log(progress=f"{i+1} of {len(unique_rms)}")
        
        self.log_manager.update_log(dm_email_status="COMPLETE")
    
    def _get_store_email(self, store_number):
        """Get email address for a store"""
        if self.store_list is not None:
            store_row = self.store_list[self.store_list['STORE'] == store_number]
            if len(store_row) > 0:
                manager_email = store_row.iloc[0]['MANAGER_EMAIL']
                if pd.notna(manager_email) and manager_email:
                    return manager_email
                return store_row.iloc[0]['STORE_EMAIL']
        
        # Fallback to constructed email
        if 2 <= store_number <= 888:
            return f"fv{store_number:04d}@fvmc.com"
        elif 3500 <= store_number <= 3999:
            return f"mp{store_number:04d}@fvmc.com"
        elif 6500 <= store_number <= 6999:
            return f"sf{store_number:04d}@stayfit24.com"
        
        return Config.WARN_RECIP[0]  # Fallback
    
    def _get_dm_email(self, dm_name):
        """Get email address for a DM"""
        if self.store_list is not None:
            dm_row = self.store_list[self.store_list['DM_FULLNAME'].str.upper() == dm_name.upper()]
            if len(dm_row) > 0:
                return dm_row.iloc[0]['DM_EMAIL']
        return None
    
    def _get_email_signature(self):
        """Get email signature HTML"""
        if Config.HV_SIG_PATH.exists():
            try:
                sig_df = pd.read_csv(Config.HV_SIG_PATH)
                template = sig_df[sig_df['Desc'] == 'HRG Normal']['HTML'].iloc[0]
                
                signature = template.replace('[NAME]', 'Steve Olson')
                signature = signature.replace('[TITLE]', 'Sr. Analytics Mgr.')
                signature = signature.replace('[EMAIL_FULL]', '<EMAIL>')
                signature = signature.replace('[TEL (*************]', '(*************')
                
                return signature
            except:
                pass
        
        return Config.WARN_SIG

def main():
    """Main execution function"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(Config.LOG_PATH / 'marcos_weekly.log'),
            logging.StreamHandler()
        ]
    )
    
    # Create directories if they don't exist
    Config.LOG_PATH.mkdir(parents=True, exist_ok=True)
    Config.RPT_PATH.mkdir(parents=True, exist_ok=True)
    Config.PLOT_PATH.mkdir(parents=True, exist_ok=True)
    Config.RM_DM_PATH.mkdir(parents=True, exist_ok=True)
    
    # Run the processor
    processor = MarcosWeeklyProcessor()
    processor.run()

if __name__ == "__main__":
    main() 