"""
LEGACY MRI Building-Entity Exceptions Report
Python conversion of R script for automated reporting

Version: 20250109
- Updated to reference Snowflake DB
- Uses Gmail API for OAuth email sending
- Creates Excel reports with formatting
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.worksheet.filters import AutoFilter
from openpyxl.utils.dataframe import dataframe_to_rows

import libs.snowflake_helper as sf
import libs.email_client as email_client

OVERRIDE_EMAIL_RECIPIENTS = False


class LegacyMRIBuildingEntityExceptions:
    def __init__(self):

        self.sf_obj = sf.SnowflakeHelper()
        self.sf_conn = self.sf_obj.conn

        # Configuration
        self.testing_emails = False  # Set to True for testing
        self.version = "20250109"
        
        # Script configuration
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.script_folder = "LEGACY_MRI_Exceptions-Building-Entity"
        self.report_name = "MRI BLDG-ENTITY Exceptions"
        self.report_time_txt = datetime.now().strftime("%H:%M:%S %Z")
        
        # Computer and path configuration
        # self.test_computers = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
        # self.prod_computers = ["DESKTOP-TABLEAU"]
        # self.this_computer = socket.gethostname()
        
        # Path configuration
        # self.central_path = Path("//*************/public/steveo/R Stuff/ReportFiles")
        # self.tableau_path = Path("C:/Users/<USER>/Documents/ReportFiles")
        
        # if self.this_computer in self.test_computers:
        #     self.testing_pc = True
        #     self.main_path = self.central_path
        # else:
        self.testing_pc = False
        # self.main_path = self.tableau_path
        self.main_path = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])
            
        self.log_path = self.main_path / self.script_folder
        self.rpt_path = self.log_path
        self.hv_sig_path = self.main_path / "HTML_signatures.csv"
        
        # Email configuration
        self.gmail_auth_email = "<EMAIL>"
        self.gmail_reply_to = "<EMAIL>"
        
        # Recipients
        self.norm_recip = ["<EMAIL>","<EMAIL>"]
        self.warn_recip = ["<EMAIL>","<EMAIL>"]
        self.test_recip = ["<EMAIL>","<EMAIL>"]
        self.test_cc_recip = ["<EMAIL>","<EMAIL>"]
        
        # Email signatures
        self.norm_sig = self._get_email_signature()
        self.warn_sig = "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
        
        # Snowflake configuration
        self.sf_environ = "PROD"  # or "STAGE"
        # self._setup_snowflake_config()
        
        # Gmail API configuration
        self.SCOPES = ['https://www.googleapis.com/auth/gmail.send']
        self.gmail_service = None
        
        # Logging setup
        # self._setup_logging()
        
    
            
    def _get_email_signature(self):
        """Get email signature from HTML signatures file"""
        default_sig = (
            "<b><span style='font-weight:bold'>Steve Olson</span></b><br/>"
            "Sr. Analytics Mgr.<br/>"
            "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>"
            "2500 Lehigh Ave.<br/>"
            "Glenview, IL 60026<br/>"
            "Ph: 847/904-9043<br/>"
        )
        
        try:
            if self.hv_sig_path.exists():
                sig_df = pd.read_csv(self.hv_sig_path)
                lcp_sig = sig_df[sig_df['Desc'] == 'LCP Reporting']['HTML'].iloc[0]
                return lcp_sig
        except Exception as e:
            # logging.warning(f"Could not load signature file: {e}")
            pass
            
        return default_sig
        
    
            
    def disconnect_from_snowflake(self):
        """Close Snowflake connection"""
        if hasattr(self, 'sf_conn'):
            self.sf_conn.close()
            # logging.info("Disconnected from Snowflake")
            
    
            
    def check_data_rows(self, data, min_rows=1, report_name=None):
        """Check if dataframe has minimum required rows"""
        if data is None or data.empty:
            return False, 0, f"{report_name}: NO RESULTS"
        elif len(data) >= min_rows:
            return True, len(data), f"{report_name}: COMPLETE"
        else:
            return False, len(data), f"{report_name}: INCOMPLETE RESULTS"
            
    def create_excel_file(self, data, file_path, sheet_name=None, column_widths=None):
        """Create Excel file with formatting"""
        if sheet_name is None:
            sheet_name = self.query_date
            
        # Ensure directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # Add data to worksheet
            for r in dataframe_to_rows(data, index=False, header=True):
                ws.append(r)
                
            # Format header row
            header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
            header_font = Font(name="Arial Narrow", size=12, bold=True)
            header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            
            for cell in ws[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                
            # Set column widths
            if column_widths:
                for col_name, width in column_widths.items():
                    col_idx = None
                    for idx, col in enumerate(data.columns, 1):
                        if col == col_name:
                            col_idx = idx
                            break
                    if col_idx:
                        ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
                        
            # Add autofilter
            ws.auto_filter = AutoFilter(ref=f"A1:{openpyxl.utils.get_column_letter(len(data.columns))}{len(data)+1}")
            
            # Freeze first row
            ws.freeze_panes = "A2"
            
            # Save workbook
            wb.save(file_path)
            # logging.info(f"Excel file created: {file_path}")
            self.sf_obj.log_audit_in_db(log_msg=f"Excel file created: {file_path}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
            return True
            
        except Exception as e:
            # logging.error(f"Error creating Excel file: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error creating Excel file, {file_path}: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return False
            
    def get_building_entity_exceptions(self):
        """Query Snowflake for building-entity exceptions"""
        query = """
        SELECT 
            CASE
            WHEN (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL) AND E.DISPOSED IS NOT NULL
                THEN 'BLDG ACTIVE, but DISPOSED date present'
            WHEN (BLDG.INACTIVE = 'Y' AND E.DISPOSED IS NULL)
                THEN 'BLDG INACTIVE, but no DISPOSED date present'
            WHEN BLDG.BLDGID <> BLDG.ENTITYID  
                and (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
                and UPPER(BLDG.BLDGID) NOT LIKE 'ROVER%'
                THEN 'BLDGID is different than ENTITYID'
            END AS ISSUE
        ,   E.ENTITYID
        ,   BLDG.BLDGID
        ,   BLDG.INACTIVE AS "INACTIVE (BLDG)"
        ,   TO_DATE(E.ACQUIRED) AS "ACQUIRED (ENTITY)"
        ,   TO_DATE(E.DISPOSED) AS "DISPOSED (ENTITY)"
        ,   BLDG.ADDRESS1
        ,   BLDG.CITY
        ,   BLDG.STATE
        FROM MRI.BLDG
        LEFT JOIN MRI.ENTITY E
        ON BLDG.ENTITYID = E.ENTITYID
        WHERE 
            (
                (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
                AND E.DISPOSED IS NOT NULL
            )
            OR /* BLDG INACTIVE but no DISPOSED date */
            (BLDG.INACTIVE = 'Y' AND E.DISPOSED IS NULL)
            OR /* BLDGID <> ENTITYID (owned buildings only) */
            ( 
                BLDG.BLDGID <> BLDG.ENTITYID 
                and (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
                and UPPER(BLDG.BLDGID) NOT LIKE 'ROVER%' /* Exclude ROVER locations, Family Vet */
            )
        ORDER BY BLDG.BLDGID
        """
        
        try:
            cursor = self.sf_conn.cursor()
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            cursor.close()
            
            if data:
                df = pd.DataFrame(data, columns=columns)
                # logging.info(f"Retrieved {len(df)} exception records")
                self.sf_obj.log_audit_in_db(log_msg=f"Retrieved {len(df)} exception records", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                return df
            else:
                # logging.info("No exception records found")
                self.sf_obj.log_audit_in_db(log_msg="No exception records found", process_type=self.report_name, script_file_name=__file__, log_type='Info')
                return pd.DataFrame()
                
        except Exception as e:
            # logging.error(f"Error executing query: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error executing query: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return None
            
    def run_report(self):
        """Main method to run the building-entity exceptions report"""
        # logging.info(f"Starting routine: {self.report_name}")
        self.sf_obj.log_audit_in_db(log_msg=f"Starting routine: {self.report_name}", process_type=self.report_name, script_file_name=__file__, log_type='Info')
        
        try:
            # Connect to Snowflake
            # if not self.connect_to_snowflake():
            #     return False
                
            # # Authenticate Gmail
            # if not self.authenticate_gmail():
            #     return False
                
            # Get exceptions data
            data = self.get_building_entity_exceptions()
            
            # Check data quality
            has_data, row_count, status = self.check_data_rows(data, 1, self.report_name)
            
            if has_data:
                # Create Excel file
                excel_filename = "MRI_BLDG-ENTITY_Exceptions.xlsx"
                excel_file_path = self.rpt_path / excel_filename
                
                # Define column widths
                column_widths = {
                    "ISSUE": 47,
                    "ENTITYID": 11,
                    "BLDGID": 8.5,
                    "INACTIVE (BLDG)": 11,
                    "ACQUIRED (ENTITY)": 13,
                    "DISPOSED (ENTITY)": 13,
                    "ADDRESS1": 25,
                    "CITY": 15,
                    "STATE": 8
                }
                
                if self.create_excel_file(data, excel_file_path, column_widths=column_widths):
                    # Create email content
                    criteria_text = """
                    <p><b>Criteria for inclusion in the report:</b><ul>
                    <li>BLDG ACTIVE, but DISPOSED date present</li>
                    <li>BLDG INACTIVE, but no DISPOSED date present</li>
                    <li>BLDGID is different than ENTITYID</li>
                    </ul></p>
                    """
                    
                    body_text = f"""
                    <p><h2>REPORT: {self.report_name}</h2></p>
                    {criteria_text}
                    <p>The attached file contains {self.report_name} from yesterday's MRI data.</p>
                    {self.norm_sig}
                    """
                    
                    # Send email
                    attachments = [str(excel_file_path)] if excel_file_path.exists() else []
                    email_client.send_email(
                        recipient=self.norm_recip,
                        subject=self.report_name,
                        body=body_text,
                        attachments=attachments,
                        override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS,
                        replyto=self.gmail_reply_to
                    )
                    
                    return True
                else:
                    # logging.error("Failed to create Excel file")
                    self.sf_obj.log_audit_in_db(log_msg=f"Failed to create Excel file: {str(excel_file_path)}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
                    return False
            else:
                # logging.info(f"No exceptions found. Status: {status}")
                self.sf_obj.log_audit_in_db(log_msg=f"No exceptions found. Status: {status}", process_type=self.report_name, script_file_name=__file__, log_type='Warning')
                return True
                
        except Exception as e:
            # logging.error(f"Error in run_report: {e}")
            self.sf_obj.log_audit_in_db(log_msg=f"Error in run_report: {e}", process_type=self.report_name, script_file_name=__file__, log_type='Error')
            return False
        finally:
            self.disconnect_from_snowflake()
            
def main():
    """Main execution function"""
    try:
        reporter = LegacyMRIBuildingEntityExceptions()
        success = reporter.run_report()
        
        if success:
            print("Report completed successfully")
            sys.exit(0)
        else:
            print("Report failed")
            sys.exit(1)
            
    except Exception as e:
        # logging.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)
        
if __name__ == "__main__":
    main() 