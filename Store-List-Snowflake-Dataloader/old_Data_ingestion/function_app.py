import azure.functions as func
import datetime
import json
import logging
import os
import uuid
from store_mainprocessor import main
import time


app = func.FunctionApp()

@app.route(route="http_trigger", auth_level=func.AuthLevel.FUNCTION)
def http_trigger(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    try:
        main()
        return func.HttpResponse("This HTTP triggered function executed successfully.")
    
    except Exception as e:
        logging.info(e)
        return func.HttpResponse("This HTTP triggered function executed with exception. " + str(e))