#--------------------------------------------------------------------------------------
# Rev. No     Date      Author   Description
#---------------------------------------------------------------------------------------
#  1.0    2024/03/01   boopathi    Initial Version
#  1.1    2024/03/15   boopathi    Added data flattening logic
#  1.2    2024/04/23   boopathi    Implemented the feedbacks from HV team 
#  1.3    2024/05/07   boopathi    Added role in snowflake connection 
#---------------------------------------------------------------------------------------

import sys
import os
import pandas as pd
import datetime
from snowflake.connector.pandas_tools import write_pandas
import time
import uuid
import snowflake.connector
import gspread


class DataProcessor:
    def __init__(self):
        
        self.uuid_str = uuid.uuid4()
        self.dir_path = os.path.dirname(os.path.realpath(__file__))
        self.env = self.get_environment()
        self.env = os.environ["DATABASE__envm"]
        self.log_path = os.path.join(self.dir_path ,"logs")
        self.output_path = os.path.join(self.dir_path ,"output")
        self.sql_file_path = os.path.join(self.dir_path, "Data_transformation", "sql_files")
        self.create_directories(self.log_path)
        self.create_directories(self.sql_file_path)
        self.create_directories(self.output_path)
        self.log_file_name = os.path.join(self.log_path, str(self.uuid_str) + "_store_list_process.log")
        self.csm_sql_file = os.path.join(self.sql_file_path, "..","csm_files", "csm_file.sql")
        self.conn = self.connect_to_snowflake()
        self.cs = self.conn.cursor()
        self.envm = os.environ["DATABASE__envm"]
        self.raw_database = os.environ["DATABASE__raw_database"]
        self.trf_database = os.environ["DATABASE__trf_database"]
        self.raw_temp_schema = os.environ["DATABASE__raw_temp_schema"]
        self.trf_schema = os.environ["DATABASE__trf_schema"]
        self.csm_database = os.environ["DATABASE__csm_database"]
        self.csm_schema = os.environ["DATABASE__csm_schema"]
        self.credential_file = os.environ["GSHEET__credential_file"]
        self.spreadsheet_id  = os.environ["GSHEET__spreadsheet_id"]
        self.log_tbl_db = self.conn.database
        self.log_tbl_schema = 'BATCH_AUDIT'
        self.log_tbl = 'MOMS_EXECUTION_LOGS'
        self.config_tbl_schema = 'METADATA_CONFIG'
        self.script_name = 'store_mainprocessor'
        self.data_flatting = "Data Flattening"
        self.data_ingest = "Data Ingestion"
        self.start_time = time.time()

    def get_environment(self):
        """
        Determines the environment based on the directory path.
        """
        if self.dir_path.find('_dev') != -1:
            return 'dev'
        elif self.dir_path.find('_stage') != -1:
            return 'stage'
        elif self.dir_path.find('_prod') != -1:
            return 'prod'
        else:
            raise Exception("Could not determine environment. Must run from directory containing\n\n\teither *_dev for dev environment\n\tor *_stage for stage environment\n\tor *_prod for prod environment.\n\nQuitting.")

   
    def create_directories(self,path):
        """
        Creates directories if they don't exist.
        """
        os.makedirs(path, exist_ok=True)
        os.chmod(path, 0o777)
    
    def connect_to_snowflake(self):
        """
        Connects to Snowflake using the configuration settings.
        """
        return snowflake.connector.connect(
            user=os.environ["DATABASE__user"],
            password=os.environ["DATABASE__password"],
            account=os.environ["DATABASE__account"],
            database=os.environ["DATABASE__raw_database"],
            schema=os.environ["DATABASE__raw_temp_schema"],
            warehouse=os.environ["DATABASE__warehouse"],
            role=os.environ["DATABASE__role"]
        )

    
    def log_data(self, uuid_str, script_name, process_type, message, log_type='Info'):
        """
        Logs the data to a log file.
        """
        self.fp = open(self.log_file_name, "a")
        self.fp.write(f"{uuid_str},{script_name},{log_type},{process_type},{message},{datetime.datetime.now()}\n")
        self.fp.close()

    def log_data_sf(self):
        """
        Logs the data to Snowflake.
        """
        print("Logs the data to Snowflake.")
        df_log = pd.read_csv(self.log_file_name, names=['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE', 'PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT'])
        write_pandas(conn=self.conn, df=df_log, database=self.raw_database, schema=self.log_tbl_schema,
                     table_name=self.log_tbl, quote_identifiers=False)
    

    def execute_sql_scripts(self, sql_file_path,data_date):
        for file in ["StoreList","StoreMatches","Personnel","Closed","UpdateHours","Hours","Property Managers"]:
            try:
                fp = open(os.path.join(sql_file_path, f"{file}.sql"))
            except:
                self.log_data(self.uuid_str, self.script_name, self.data_flatting,
                            f"File Not Found")
                raise Exception("File Not Found")

            script = fp.read()
            script = script.replace("$envm_bus_date", data_date).replace("$envm", self.envm)
            self.cs.execute(script)
            result = self.cs.fetchall()
            fp.close()     
            gc = gspread.service_account(filename=self.credential_file)
            sh = gc.open_by_key(self.spreadsheet_id)

            if file == "StoreList":
                sheet_name = "StoreList"
                df = pd.DataFrame(data=result, columns=['ST' , 'Store Name' , 'Address' , 'City' , 'State' , 'Zip' , 'Phone' ,'Store Email' ,'Street Corners' ,'Open Date' ,'Company' ,'Rent' ,'Lease' ,'RM' ,'RM Email' ,'RM Report Name' ,'DM' ,'DM Email' ,'DM Report Name' ,'Manager' ,'MIT'], dtype=str)
                worksheet = sh.worksheet(sheet_name)
                worksheet.clear()
                worksheet.update([df.columns.values.tolist()] + df.values.tolist(), value_input_option='USER_ENTERED')
                worksheet.format('A', { "numberFormat": { "type": "NUMBER" }})
                worksheet.format('F', { "numberFormat": { "type": "NUMBER" }})
                worksheet.format('A1:Z1', {'textFormat': {'bold': True}})
                worksheet.freeze(rows=1, cols=1)
            elif file == "StoreMatches":
                sheet_name = "StoreMatches"
                df = pd.DataFrame(data=result, columns=['FV', 'HF', 'SF', 'HPWI', 'DD', 'FAKEFV'])
                worksheet = sh.worksheet(sheet_name)
                worksheet.clear()
                worksheet.update([df.columns.values.tolist()] + df.values.tolist(), value_input_option='USER_ENTERED')
                worksheet.format('A', { "numberFormat": { "type": "NUMBER" }})
                worksheet.format('F', { "numberFormat": { "type": "NUMBER" }})
                worksheet.format('A1:Z1', {'textFormat': {'bold': True}})
                worksheet.freeze(rows=1)
            elif file == "Personnel":
                sheet_name = "Personnel"
                df = pd.DataFrame(data=result, columns=['Name', 'Title', 'Ext', 'Speed', 'Phone', 'Email','Sort_Order'])
                df = df.drop(columns=['Sort_Order'])
                worksheet = sh.worksheet(sheet_name)
                worksheet.clear()
                worksheet.update([df.columns.values.tolist()] + df.values.tolist(), value_input_option='USER_ENTERED')
                worksheet.format('C', { "numberFormat": { "type": "NUMBER" ,"pattern": "[>9999](*************;[<=9999]####" }})
                worksheet.format('D', { "numberFormat": { "type": "NUMBER" ,"pattern": "\*0" }})
                worksheet.format('E', { "numberFormat": { "type": "NUMBER" ,"pattern": "(*************" }})
                worksheet.format('A1:Z1', {'textFormat': {'bold': True}})
                worksheet.freeze(rows=1)
            elif file == "Closed":
                sheet_name = "Closed"
                df = pd.DataFrame(data=result, columns=['ST', 'Store Name', 'Address', 'City', 'State', 'Zip', 'Phone', 'Open Date', 'Close Date', 'RM', 'DM'])
                worksheet = sh.worksheet(sheet_name)
                worksheet.clear()
                worksheet.update([df.columns.values.tolist()] + df.values.tolist(), value_input_option='USER_ENTERED')
                worksheet.format('F', { "numberFormat": { "type": "NUMBER" }})
                worksheet.format('A1:Z1', {'textFormat': {'bold': True}})
                worksheet.freeze(rows=1)
            elif file == "Hours":
                sheet_name = "Hours"
                df = pd.DataFrame(data=result, columns=['ST', 'Sun O', 'Sun C', 'Mon O', 'Mon C', 'Tue O', 'Tue C', 'Wed O', 'Wed C', 'Thu O', 'Thu C', 'Fri O', 'Fri C', 'Sat O', 'Sat C', 'Tot Open Hrs'])
                worksheet = sh.worksheet(sheet_name)
                worksheet.clear()
                worksheet.update([df.columns.values.tolist()] + df.values.tolist(), value_input_option='USER_ENTERED')
                worksheet.format('B:O', { "numberFormat": { "type": "TIME", "pattern": "hh:mm" }})
                worksheet.freeze(rows=1)   
            elif file == "Property Managers":
                sheet_name = "Property Managers"
                worksheet = sh.worksheet(sheet_name)
                worksheet.clear()
                worksheet.update_cell(1, 1, str(result[0][0]))
                worksheet.format('A1', {'horizontalAlignment': 'LEFT'})
        
        self.log_data(self.uuid_str, self.script_name, 'Data Processing',
                          f"Data_date: {data_date} | Data Processing for Store_List completed")


    def data_ingestion(self):
        """
        Performs the data ingestion process.
        """
        try:
            script_name = self.script_name
            uuid_str = self.uuid_str
            data_date = datetime.datetime.now().strftime("%Y-%m-%d")
            self.execute_sql_scripts(self.sql_file_path, data_date)
            end_time = time.time()
            total_time = round((end_time - self.start_time) / 60, 2)
            self.log_data(uuid_str, script_name, 'Data Processing',
                          f"Data_date: {data_date} | Total Data processing  time | {total_time} in mins")

            self.log_data(uuid_str, script_name, 'Data Processing',
                          f"Data_date: {data_date} | Data Processing for Store_List completed")

        except Exception as err:
            err_msg = str(err).replace("'", "")
            self.log_data(uuid_str, script_name, self.data_ingest,
                          f"Error encountered function - data_ingestion | {err_msg}", 'Error')
            print(f"Error encountered function - data_ingestion | {err_msg}")
             
def main():
    processor = DataProcessor()
    processor.data_ingestion()
    processor.fp.close()
    processor.log_data_sf()

if __name__ == "__main__":
    main()
