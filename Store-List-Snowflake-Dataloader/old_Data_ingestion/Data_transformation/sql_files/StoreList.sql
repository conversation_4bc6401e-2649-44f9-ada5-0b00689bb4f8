with dm as (    SELECT
        s.store,
        INITCAP(e.fname) || ' ' || INITCAP(e.lname)  AS dm_name,
        e.email as dm_email,
        substr(upper(e.fname),1,1) ||
           substr(lower(e.fname),2) ||
           ' ' ||
           substr(upper(e.lname),1,1) dm_rpt_name
    FROM
        $envm_csm_db.corporate.ab_employees e,
        $envm_csm_db.corporate.ab_store_employees s,
        $envm_csm_db.corporate.famv_payroll_position p
    WHERE
        e.position = p.position
        AND s.paynum = e.paynum
        AND p.sub_categories LIKE '%DIST_RPT_INC%'
        AND status = 'A' 
        ),


 rm as (   
SELECT
        s.store,
        INITCAP(e.fname) || ' ' || INITCAP(e.lname)   AS rm_name,
        e.email as rm_email,
        substr(upper(e.fname),1,1) ||
           substr(lower(e.fname),2) ||
           ' ' ||
           substr(upper(e.lname),1,1) rm_rpt_name
    FROM
        $envm_csm_db.corporate.ab_employees e,
        $envm_csm_db.corporate.ab_store_employees s,
        $envm_csm_db.corporate.famv_payroll_position p
    WHERE
        e.position = p.position
        AND s.paynum = e.paynum
        AND p.sub_categories LIKE '%REG_RPT_INC%'
        AND status = 'A' ),
mgr as (
    SELECT
        s.store,
        INITCAP(e.fname) || ' ' || INITCAP(e.lname) as mgr_name,
    FROM
        $envm_csm_db.corporate.ab_employees e,
        $envm_csm_db.corporate.ab_store_employees s,
        $envm_csm_db.corporate.famv_payroll_position p
    WHERE
        e.position = p.position
        AND s.paynum = e.paynum
        AND p.sub_categories LIKE '%MGR_HISTORY%'
        AND status = 'A' ),

mit as (
        SELECT s.store,
         LISTAGG(DISTINCT INITCAP(e.fname) || ' ' || INITCAP(e.lname), '/') as mit_name
    FROM
        $envm_csm_db.corporate.ab_employees e,
        $envm_csm_db.corporate.ab_store_employees s,
        $envm_csm_db.corporate.famv_payroll_position p
    WHERE
        e.position = p.position
        AND s.paynum = e.paynum
        AND p.sub_categories LIKE '%MIT_HISTORY%'
        AND status = 'A'
        group by s.store
        ),
        
 store_desc as (

SELECT
            to_number(location_code) as location_code ,
            description as name,
            address_line_1 as address,
            town_or_city as city,
            postal_code as zip,
            region_2 as state,
            telephone_number_1 as phone,
            decode(attribute6,'RENT','R','') as rent,
            attribute9 as lease,
            DATE_TRUNC('day', TO_DATE(attribute5, 'DD-MON-YY')) as open_date,
            attribute19 as street_corner,
            loc_information15 as location_type,
       CASE WHEN substr(loc_information15, 0, 4) = 'FVMC' 
            THEN 'fv' || LPAD(location_code, 4, '0') || '@fvmc.com'
        WHEN substr(loc_information15, 0, 2) = 'HF'
            THEN 'mp' || LPAD(location_code, 4, '0') || '@fvmc.com'
        WHEN substr(loc_information15, 0, 5) = 'STFIT'
            THEN 'sf' || LPAD(location_code, 4, '0') || '@stayfit24.com'
        WHEN substr(loc_information15, 0, 3) = 'DDC'
            THEN 'dd' || LPAD(location_code, 4, '0') || '@digitaldoc.com'
        WHEN substr(loc_information15, 0, 4) = 'HPWI'
            THEN ''
        ELSE ''
    END AS store_email,
    CASE 
        WHEN substr(loc_information15, 0, 4) = 'FVMC' 
            THEN 'FV'
        WHEN substr(loc_information15, 0, 2) = 'HF'
            THEN 'HF'
        WHEN substr(loc_information15, 0, 5) = 'STFIT'
            THEN 'SF'
        WHEN substr(loc_information15, 0, 3) = 'DDC'
            THEN 'DD'
        WHEN substr(loc_information15, 0, 4) = 'HPWI'
            THEN 'HPWI'
        ELSE ''
    END AS company
        FROM
            $envm_csm_db.corporate.hr_locations_all
        WHERE
            attribute14 = 'Y'
            AND inactive_date IS NULL
            AND (
                    date(attribute5 ,'DD-MON-YY') <= current_date
                    OR (
                        date(attribute10 ,'DD-MON-YY') <= current_date + 14
                        AND date(attribute10 ,'DD-MON-YY') > current_date
                        )
                    )
            AND substr(loc_information15, 1, 3) != 'DDF' ) 
SELECT
s.location_code as st,
s.name as store_name,
s.address,
s.city,
s.state,
s.zip,
s.phone,
s.store_email,
s.street_corner,
s.open_date,
s.company,
s.rent,
s.lease,
rm.rm_name,
rm.rm_email,
rm.rm_rpt_name,
dm.dm_name,
dm.dm_email,
dm.dm_rpt_name,
coalesce(mgr.mgr_name,'No Mgr') as mgr_name,
coalesce(mit.mit_name,'No MIT') as mit_name
FROM 
    store_desc s
LEFT OUTER JOIN 
    rm
ON 
    s.location_code = rm.store
LEFT OUTER JOIN 
    mgr
ON 
    s.location_code = mgr.store
LEFT OUTER JOIN 
   mit
ON 
    s.location_code = mit.store
LEFT OUTER JOIN 
    dm
ON 
    s.location_code = dm.store
order by location_code