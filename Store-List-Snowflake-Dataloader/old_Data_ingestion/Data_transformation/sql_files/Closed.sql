SELECT     
            to_number(location_code) as st,
            description as store_name,
            address_line_1 as address,
            town_or_city as city,
            region_2 as state,
            postal_code as zip,
            telephone_number_1 as phone,
             to_char(date(attribute5 ,'DD-MON-YY'),'YYYY-MM-DD') as open_date,
             to_char(date(inactive_date),'YYYY-MM-DD') closed_date,
            initcap(reg.fname || ' ' || reg.lname) as rm,
            initcap(dist.fname || ' ' || dist.lname) as dm
        FROM
            $envm_csm_db.corporate.hr_locations_all
        LEFT JOIN $envm_csm_db.corporate.mp_calendar_weekly cal ON (
            cal.s_date <= hr_locations_all.inactive_date AND
            cal.e_date >= hr_locations_all.inactive_date
        )
        LEFT JOIN $envm_csm_db.corporate.ac_employee_history hist ON (
            hr_locations_all.location_code = lpad(hist.store_number, 4, '0') AND
            hist.period_num = cal.period_num AND
            hist.period_year = cal.period_year AND
            hist.day_offset = (cal.week_num - 1) * 7
        )
        LEFT JOIN $envm_csm_db.corporate.ab_employees reg ON (
            reg.paynum = hist.reg
        )
        LEFT JOIN $envm_csm_db.corporate.ab_employees dist ON (
            dist.paynum = hist.dist
        )
        WHERE


        
            attribute14 = 'Y'
            AND date(inactive_date) < current_date
            AND (
                    date(attribute5 ,'DD-MON-YY') <= current_date
                    OR (
                        date(attribute10 ,'DD-MON-YY') <= current_date + 14
                        AND date(attribute10 ,'DD-MON-YY') > current_date
                        )
                    )
            AND substr(loc_information15, 1, 3) != 'DDF'
        ORDER BY
            inactive_date DESC;