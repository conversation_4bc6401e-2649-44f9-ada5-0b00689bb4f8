with pay_ext as (select paynum,extension
--'(' || SUBSTR(extension, 1, 3) || ') ' || SUBSTR(extension, 4, 3) || '-' || SUBSTR(extension, 7, 4) AS extension
from $envm_csm_db.corporate.paynum_extn)

select 
name,
title,                         
case when p.ext is null then cast(e.extension as varchar) else p.ext end  as ext,
--case when speed is not null then concat('*',speed) else speed end as speed,
speed,
--case when speed is null then null else '(' || SUBSTR(phone, 1, 3) || ') ' || SUBSTR(phone, 4, 3) || '-' || SUBSTR(phone, 7, 4) end as phone,
case when speed is null then null else phone end as phone,
email,
sort_order
 from (
select  
1 sort_order,
a.paynum,
               substr(upper(a.fname),1,1) ||
                  substr(lower(a.fname),2) ||
                  ' ' ||
                  substr(upper(a.lname),1,1) ||
                  substr(lower(a.lname),2) name,
                                 t.title title,
                           cast(a.extension as varchar) ext,

               cast(a.speeddial as varchar) speed,
               a.phone phone,
             lower(a.email) email
        from $envm_csm_db.corporate.ab_employees a,
             $envm_csm_db.corporate.ab_employee_title t
        where a.paynum = t.paynum
          and a.status = 'A'
          and a.position in ('O','V')
union all
		
select 
2 sort_order,
 a.paynum,
               substr(upper(a.fname),1,1) ||
                  substr(lower(a.fname),2) ||
                  ' ' ||
                  substr(upper(a.lname),1,1) ||
                  substr(lower(a.lname),2) name,
                  'Regional' as title,
                                 cast(a.extension as varchar) ext,
               cast(a.speeddial as varchar) speed,
               
               a.phone phone,
               lower(a.email) email
        from $envm_csm_db.corporate.ab_employees a
        where a.status = 'A'
          and a.speeddial is not null
          and a.position in (select position
                             from $envm_csm_db.corporate.famv_payroll_position
                             where sub_categories like '%REG_RPT_INC%')
  
union all
select 
3 sort_order,
a.paynum,
               substr(upper(a.fname),1,1) ||
                  substr(lower(a.fname),2) ||
                  ' ' ||
                  substr(upper(a.lname),1,1) ||
                  substr(lower(a.lname),2) name,
                  'District' as title,
                                 cast(a.extension as varchar) ext,
               cast(a.speeddial as varchar) speed,
               a.phone phone,
               lower(a.email) email
        from $envm_csm_db.corporate.ab_employees a
        where a.status = 'A'
          and a.speeddial is not null
          and a.position in (select position
                             from $envm_csm_db.corporate.famv_payroll_position
                             where sub_categories like '%DIST_RPT_INC%')                  
                             ) as p left join pay_ext e on 
                             p.paynum = e.paynum 
union all                           
select
cast(extension as varchar),
null,
null,
null,
null,
null,
4 as sort_order
from $envm_csm_db.corporate.paynum_extn order by sort_order,name ;
