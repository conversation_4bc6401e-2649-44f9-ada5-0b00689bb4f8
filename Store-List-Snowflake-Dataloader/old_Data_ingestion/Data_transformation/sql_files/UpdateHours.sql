BEGIN

update $envm_csm_db.corporate.ab_store_hours
set sun_o = to_timestamp('20130101' || to_char(sun_o,'HH24MI'),'YYYYMMDDHH24MI'),
    sun_c = to_timestamp('20130101' || to_char(sun_c,'HH24MI'),'YYYYMMDDHH24MI'),
    mon_o = to_timestamp('20130101' || to_char(mon_o,'HH24MI'),'YYYYMMDDHH24MI'),
    mon_c = to_timestamp('20130101' || to_char(mon_c,'HH24MI'),'YYYYMMDDHH24MI'),
    tue_o = to_timestamp('20130101' || to_char(tue_o,'HH24MI'),'YYYYMMDDHH24MI'),
    tue_c = to_timestamp('20130101' || to_char(tue_c,'HH24MI'),'YYYYMMDDHH24MI'),
    wed_o = to_timestamp('20130101' || to_char(wed_o,'HH24MI'),'YYYYMMDDHH24MI'),
    wed_c = to_timestamp('20130101' || to_char(wed_c,'HH24MI'),'YYYYMMDDHH24MI'),
    thu_o = to_timestamp('20130101' || to_char(thu_o,'HH24MI'),'YYYYMMDDHH24MI'),
    thu_c = to_timestamp('20130101' || to_char(thu_c,'HH24MI'),'YYYYMMDDHH24MI'),
    fri_o = to_timestamp('20130101' || to_char(fri_o,'HH24MI'),'YYYYMMDDHH24MI'),
    fri_c = to_timestamp('20130101' || to_char(fri_c,'HH24MI'),'YYYYMMDDHH24MI'),
    sat_o = to_timestamp('20130101' || to_char(sat_o,'HH24MI'),'YYYYMMDDHH24MI'),
    sat_c = to_timestamp('20130101' || to_char(sat_c,'HH24MI'),'YYYYMMDDHH24MI');

update $envm_csm_db.corporate.ab_store_hours set sun_c =  DATEADD('DAY', 1,sun_c) where timediff ('hour',cast(sun_c as date),sun_c)/24 < .25;
update $envm_csm_db.corporate.ab_store_hours set mon_c =  DATEADD('DAY', 1,mon_c) where timediff ('hour',cast(mon_c as date),mon_c)/24 < .25;
update $envm_csm_db.corporate.ab_store_hours set tue_c =  DATEADD('DAY', 1,tue_c) where timediff ('hour',cast(tue_c as date),tue_c)/24 < .25;
update $envm_csm_db.corporate.ab_store_hours set wed_c =  DATEADD('DAY', 1,wed_c) where timediff ('hour',cast(wed_c as date),wed_c)/24 < .25;
update $envm_csm_db.corporate.ab_store_hours set thu_c =  DATEADD('DAY', 1,thu_c) where timediff ('hour',cast(thu_c as date),thu_c)/24 < .25;
update $envm_csm_db.corporate.ab_store_hours set fri_c =  DATEADD('DAY', 1,fri_c) where timediff ('hour',cast(fri_c as date),fri_c)/24 < .25;
update $envm_csm_db.corporate.ab_store_hours set sat_c =  DATEADD('DAY', 1,sat_c) where timediff ('hour',cast(sat_c as date),sat_c)/24 < .25;


END;