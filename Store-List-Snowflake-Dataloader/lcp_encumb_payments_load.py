
"""
LCP Encumbrance Principal Payments Load - Python Version
Converted from R script: LCP_ENCUMB_PAYMENTS_Load.R
Version: ********

### ******** change:
### added Monthly Interest and Annual Interest columns to Gsht and DB table

### 20250127 change:
### added SQL logic to prevent multiple loan rows in Google sheet when 
### multiple CURRENTOWED values are present
### added email notification when a new load is added to the Google sheet

### 20250108 change:
### bug fixes in email signature creation

### 20241209 change
### completed conversion to Snowflake, changed email send from to generic Legacy Reporting email

### 20241016 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### updated email signature to use latest format provided by <PERSON> earlier in 2024
### replaced check_mydata_rows() function with more universal check_mydf_rows()

### 20230807 change:
### new file

### Google source workbook: https://docs.google.com/spreadsheets/d/1S4Z-VcLT-5bpYqXkhq1MpKH-DcLuCduPAjTaPlXdwaE/edit?gid=0#gid=0
### MS 365 source workbook: https://highlandventuresltd442-my.sharepoint.com/:x:/r/personal/solson_hv_ltd/_layouts/15/Doc.aspx?CID=b3b68b44-5dc0-46c7-0139-bde47958c6fa&sourcedoc=%7BFE44DBD0-16A9-5656-90EB-0B32FDE63D28%7D&file=Annual%20Principal%20Payments%20-%20Property%20Summary.xlsx&action=default&mobileredirect=true
"""

import pandas as pd
import numpy as np
import os
import sys
import re
import smtplib
# import keyring
# import logging
from datetime import datetime, timezone

from snowflake.connector.pandas_tools import write_pandas
# import gspread
# from google.oauth2.service_account import Credentials
# from googleapiclient.discovery import build
import time
import json
from typing import List, Dict, Any, Optional, Tuple
# import socket
from libs.snowflake_helper import SnowflakeHelper
from libs.excel_helper import SharePointExcelOnline
import libs.email_client as email_client

# Global Configuration
testing_emails = False  # NORMAL, should normally be disabled in PRODUCTION instance
# testing_emails = True

# Version and basic configuration
VERSION = "********"
okay_to_continue = True
my_report_name = "LCP Encumbrance Principal Payments Load"
print(f"Beginning '{my_report_name}' routine")

script_folder = "LEGACY_ENCUMB_Payments"
rpt_folder = "reports"
gsht_auth_email = "<EMAIL>"
gsht_key = "1S4Z-VcLT-5bpYqXkhq1MpKH-DcLuCduPAjTaPlXdwaE"
my_sheets = ["Sheet1","Dev"]

# Column configurations
gsht_needed_cols = [ # missing: CURRENTOWED	BLDG_CNT
    "CMPY_ID", "LOANID", "Principal Payment", "Annual Principal Payment",
    "Monthly Interest", "Annual Interest", "Notes"
]

old_names = ["Principal Payment", "Annual Principal Payment", "Monthly Interest", "Annual Interest"]
new_names = ["PRINCIPLE_MONTHLY_PYMT", "PRINCIPLE_ANNUAL_PYMT", "INTEREST_MONTHLY_PYMT", "INTEREST_ANNUAL_PYMT"]

db_cols = [
    "CMPY_ID", "LOANID", "BLDG_CNT", "PRINCIPLE_MONTHLY_PYMT", 
    "PRINCIPLE_ANNUAL_PYMT", "INTEREST_MONTHLY_PYMT", "INTEREST_ANNUAL_PYMT"
]

# Email recipients
warn_recip = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
norm_recip = ["<EMAIL>", "<EMAIL>"]
test_recip = ["<EMAIL>"]
test_cc_recip = ["<EMAIL>"]
sig_logo = False

# Path configuration
central_path = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles")
tableau_path = os.path.join("C:", "Users", "table", "Documents", "ReportFiles")
test_computers = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
prod_computers = ["DESKTOP-TABLEAU"]
# this_computer = os.environ.get("COMPUTERNAME", socket.gethostname())

testing_pc = False
main_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
csm_database = os.environ["DATABASE_CSM_DATABASE"]

log_path = os.path.join(main_path, script_folder)
my_report_path = os.path.join(log_path, rpt_folder)

# Database configuration
my_schema = "CORPORATE"
my_table = "LCP_ENCUMB_PAYMENTS"
# my_table = "LCP_ENCUMB_PAYMENTS_test" # remove this line after testing
my_table_name = f"{my_schema}.{my_table}"

# Snowflake environment configuration
sf_environ = "PROD"  # or "STAGE"

# Email configuration
gmail_auth_email = "<EMAIL>"
gmail_reply_to = "<EMAIL>"

workbook_name = "Annual Principal Payments - Property Summary"
workbook_url = "https://highlandventuresltd442-my.sharepoint.com/:x:/r/personal/solson_hv_ltd/_layouts/15/Doc.aspx?CID=b3b68b44-5dc0-46c7-0139-bde47958c6fa&sourcedoc=%7BFE44DBD0-16A9-5656-90EB-0B32FDE63D28%7D&file=Annual%20Principal%20Payments%20-%20Property%20Summary.xlsx&action=default&mobileredirect=true"

site_url = "https://highlandventuresltd442.sharepoint.com/sites/dev"
file_id = "01NHAQJODXGWFMB5MZAZAZQVDGX5JLOGHS" # dev

site_url = "https://highlandventuresltd442-my.sharepoint.com/personal/solson_hv_ltd"
file_id = "01IZI3JMOQ3NCP5KIWKZLJB2YLGL66MPJI" # prod: https://highlandventuresltd442.sharepoint.com/:x:/r/sites/dev/_layouts/15/Doc.aspx?sourcedoc=%7BFE44DBD0-16A9-5656-90EB-0B32FDE63D28%7D&file=Annual%20Principal%20Payments%20-%20Property%20Summary.xlsx&action=default&mobileredirect=true

class SnowflakeConnection:
    """Handle Snowflake database connections and operations"""
    
    def __init__(self, sf):
        self.sf = sf
        self.connection = self.sf.conn
        # self.connect()
    

    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute query and return DataFrame"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            cursor.close()
            return pd.DataFrame(data, columns=columns)
        except Exception as e:
            print(f"Error executing query: {e}")
            raise
    
    def execute_command(self, command: str):
        """Execute command without return"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(command)
            cursor.close()
        except Exception as e:
            print(f"Error executing command: {e}")
            raise
    
    def append_dataframe(self, df: pd.DataFrame, table_name: str, schema: str):
        """Append DataFrame to Snowflake table"""
        try:
            success, nchunks, nrows, _ = write_pandas(
                conn=self.connection, 
                df=df, 
                database=csm_database,
                table_name=table_name, 
                schema=schema, #  or sf_schema
                auto_create_table=False,
                overwrite=False,
                quote_identifiers = False
            )
            return success, nrows
        except Exception as e:
            print(f"Error writing DataFrame: {e}")
            raise
    
    def close(self):
        """Close connection"""
        if self.connection:
            self.connection.close()



def get_signature(template_html: str, name: str = '', title: str = '', 
                 email: str = '', phone: str = '') -> str:
    """Get email signature with substitutions"""
    sig = template_html
    sig = sig.replace('[NAME]', name)
    sig = sig.replace('[TITLE]', title) 
    sig = sig.replace('[EMAIL_FULL]', email)
    sig = sig.replace('[TEL (*************]', phone)
    return sig


def check_dataframe_rows(df: pd.DataFrame, min_num_rows: int, 
                        report_name: str = None) -> Tuple[bool, int, str]:
    """Check if DataFrame has minimum required rows - equivalent to R's check_mydf_rows"""
    if isinstance(df, pd.DataFrame):
        if len(df) >= min_num_rows:
            error_status = f"{report_name}: OKAY"
            temp_nrow = len(df)
            temp_bool = True
        else:
            temp_bool = False
            temp_nrow = len(df)
            error_status = f"{report_name}: INCOMPLETE"
    else:
        temp_bool = False
        temp_nrow = 0
        error_status = f"{report_name}: ERROR"
    
    return temp_bool, temp_nrow, error_status


def null_to_na(x):
    """Convert null values to NaN - equivalent to R's nullToNA function"""
    if x is None or str(x).upper() == 'NULL':
        return np.nan
    return x


def dataframe_to_html_table(df: pd.DataFrame) -> str:
    """Convert DataFrame to HTML table for email"""
    return df.to_html(
        index=False, 
        border=2, 
        table_id="data-table",
        classes="table table-striped"
    )


def main():
    """Main execution function"""
    global okay_to_continue
    
    # Initialize handlers
    db_conn = None
    # email_handler = EmailHandler()
    # sheets_manager = GoogleSheetsManager()
    
    sf = SnowflakeHelper()

    
    try:


        # sf.moms_log_tbl = 'MOMS_EXECUTION_LOGS_test' # remove or comment out after testing
        excel_helper = SharePointExcelOnline()

        # Initialize database connection
        db_conn = SnowflakeConnection(sf)

        start_time = time.time()
        start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    

        sf.log_audit_in_db(f"Starting main() at {start_now}for workbook: {workbook_url}", process_type=my_report_name, script_file_name=__file__)
        # exit(1)
        
        # Load email signatures
        sig_name = 'NA'
        sig_title = 'NA'
        sig_email = 'NA'
        sig_template = 'LCP Reporting'
        sig_phone = 'NA'
        
        hv_sig_path = os.path.join(main_path, "HTML_signatures.csv")
        norm_sig = ""
        warn_sig = ""
        
        if os.path.exists(hv_sig_path):
            try:
                html_signatures = pd.read_csv(hv_sig_path)
                template_row = html_signatures[html_signatures['Desc'] == sig_template]
                if not template_row.empty:
                    template_html = template_row.iloc[0]['HTML']
                    norm_sig = get_signature(template_html, sig_name, sig_title, sig_email, sig_phone)
                    warn_sig = norm_sig
            except Exception as e:
                print(f"Error loading signatures: {e}")
        
        # Check Google Sheet status
        sheet_info = None
        if okay_to_continue:
            try:
                # sheet_info = sheets_manager.get_sheet_info(gsht_key)
                excel_session = excel_helper.get_excel_file_by_id(site_url, file_id)
                
                if not excel_session.get('site_id') or len(excel_session['site_id']) == 0:
                    body_text = (
                        f"<p>This is an automated email to inform you that it appears there is "
                        f"an error in the {my_report_name} routine!</p>"
                        f"<p>There weren't any sheets with the expected names ({'; '.join(my_sheets)}) "
                        f"found in the '{excel_session.get('file_name', 'Unknown')}' workbook.</p>"
                        f"<p>The routine is aborting without an update</p> {warn_sig}"
                    )
                    
                    email_client.send_email(
                        recipient=warn_recip,
                        subject=f"{my_report_name} Issue: No sheets with expected names",
                        body=body_text,
                        test=testing_emails,
                        test_recipient=test_recip
                    )
                    okay_to_continue = False
                    
            except Exception as e:
                body_text = (
                    f"<p>This is an automated email to inform you that it appears there may "
                    f"have been an error reading the Google Sheet for the {my_report_name} routine! "
                    f"The file may be missing or there was an issue accessing it.</p>"
                    f"<p>The routine is aborting without an update</p> {warn_sig}"
                )
                
                email_client.send_email(
                    recipient=warn_recip,
                    subject=f"{my_report_name} Issue: Google Sheet Access Issue",
                    body=body_text,
                    test=testing_emails,
                    test_recipient=test_recip
                )
                okay_to_continue = False
        
        sheet_name=my_sheets[0]
        # sheet_name=my_sheets[1] # remove this line after testing


        # Read existing Google Sheet data
        prev_data = pd.DataFrame()
        if okay_to_continue:
            try:
                # gsht_curr = sheets_manager.read_sheet(gsht_key, my_sheets[0])
                # if len(gsht_curr) < 1:
                #     okay_to_continue = False
                # else:
                #     # Select columns that exist in the DataFrame
                #     available_cols = [col for col in gsht_needed_cols if col in gsht_curr.columns]
                #     prev_data = gsht_curr[available_cols].copy() if available_cols else gsht_curr.copy()
                
                
                
                # get existing availability data from sheet
                existing_availability_data_from_sheet = excel_helper.get_worksheet_data(excel_session, sheet_name)
                values_existing_availability_data_from_sheet_list = existing_availability_data_from_sheet.get('values')
                existing_availability_headers = values_existing_availability_data_from_sheet_list[0] 
                existing_availability_data = values_existing_availability_data_from_sheet_list[1:]
                existing_availability_data_df = pd.DataFrame(data=existing_availability_data, columns=existing_availability_headers)
                # prev_data = pd.DataFrame(data=existing_availability_data, columns=existing_availability_headers)
                # print(f"existing_availability_data_df: {existing_availability_data_df}\n\n")
                # exit(1)

                available_cols = [col for col in gsht_needed_cols if col in existing_availability_data_df.columns]
                prev_data = existing_availability_data_df[available_cols].copy() if available_cols else existing_availability_data_df.copy()

            except Exception as e:
                print(f"Error reading  Sheet: {e}")
                okay_to_continue = False
                sf.log_audit_in_db(f"Error reading Sheet for workbook: {workbook_url}: {e}", process_type=my_report_name, script_file_name=__file__)
        # print(f"prev_data: {prev_data}\nokay_to_continue: {okay_to_continue}")
        # prev_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/lcp_encumb_payments_load_prev_data.csv', index=False)
        # exit(1)
        # Query database and process data
        if okay_to_continue:
            # SQL query from original R script

            query = """
                SELECT distinct
                UNQ.CMPY_ID
                , UNQ.LOANID
                , LISTAGG(trim(TO_VARCHAR(UNQ.CURRENTOWED,'$999,999,999,990.00')), '; ') WITHIN GROUP (ORDER BY UNQ.CURRENTOWED ASC) AS CURRENTOWED
                , SUM(CNT.BLDGS) as BLDG_CNT
                from 
                (
                        SELECT DISTINCT
                            CMPY_ID
                        ,	LOANID
                        ,	CURRENTOWED
                        FROM MRI.ENCUMB ie
                        WHERE ie.TABLEID = 'BLDG'
                            and ie.CURRENTOWED > 1
                            AND ie.DEBTTYPEID != 'LOC'
                ) UNQ
                LEFT JOIN 
                (
                        SELECT
                            ce.CMPY_ID
                        ,	ce.LOANID
                        ,	COUNT(*) AS BLDGS
                        FROM MRI.ENCUMB ce
                        WHERE ce.TABLEID = 'BLDG'
                            and ce.CURRENTOWED > 1
                            AND ce.DEBTTYPEID != 'LOC'
                        GROUP BY 
                        ce.CMPY_ID
                        ,	ce.LOANID
                        ,	ce.CURRENTOWED
                ) CNT
                ON UNQ.CMPY_ID = CNT.CMPY_ID
                AND UNQ.LOANID = CNT.LOANID
                where UNQ.CURRENTOWED > 1
                group by UNQ.CMPY_ID, UNQ.LOANID
                order by UNQ.CMPY_ID, UNQ.LOANID            
            """
            
            try:
                my_updated = db_conn.execute_query(query)
                data_status = check_dataframe_rows(my_updated, 1, my_report_name)
                
                if data_status[0]:  # If data is okay

                    sf.log_audit_in_db(f"{len(my_updated)} records returned from query: {query}", process_type=my_report_name, script_file_name=__file__)
                    # print(f"0) my_updated: {my_updated}\n\n")
                    # my_updated.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/lcp_encumb_payments_load_my_updated.csv', index=False)
                    # exit(1)
                    # Clean string columns (equivalent to R's trimws)
                    for col in my_updated.select_dtypes(include=['object']).columns:
                        my_updated[col] = my_updated[col].astype(str).str.strip()
                    
                    # Join with previous data (equivalent to R's left_join)
                    if not prev_data.empty and all(col in my_updated.columns for col in ['CMPY_ID', 'LOANID']):
                        curr_data = my_updated.merge(prev_data, on=['CMPY_ID', 'LOANID'], how='left')
                        sf.log_audit_in_db(f"Merged with previous data: {len(curr_data)} records", process_type=my_report_name, script_file_name=__file__)
                    else:
                        curr_data = my_updated.copy()
                        sf.log_audit_in_db(f"No previous data found, using current data: {len(curr_data)} records", process_type=my_report_name, script_file_name=__file__)
                    
                    # curr_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/lcp_encumb_payments_load_curr_data_0.csv', index=False)
                    # Add updated timestamp
                    updated_col = f"Updated: {datetime.now().strftime('%a, %b %d, %Y at %I:%M%p %Z')}"
                    curr_data[updated_col] = '' # np.nan

                    # Prepare data for database write
                    my_data = curr_data.copy()

                    # convert columns to text
                    all_text_columns = ['LOANID', 'CURRENTOWED'] # 
                    # prefix with ' for all text columns, so excel wont drop leading zeros
                    for col in all_text_columns:
                        curr_data[col] = "'" + curr_data[col].fillna('').astype(str)            


                    # curr_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/lcp_encumb_payments_load_curr_data_1.csv', index=False)
                    
                    # print(f"1) curr_data: {curr_data}\n\n")
                    # exit(1)

                    # Clear and update Google Sheet
                    # sheets_manager.clear_sheet(gsht_key, my_sheets[0])
                    # time.sleep(2)
                    excel_helper.clear_excel_sheet_content(excel_session=excel_session, worksheet_name=sheet_name)
                    # sheets_manager.write_sheet(gsht_key, my_sheets[0], curr_data)
                    # excel_helper.append_data_to_worksheet(excel_session=excel_session, worksheet_name=sheet_name, data=curr_data)
                    # replace none with empty string
                    clean_values = excel_helper.clean_values_from_none_to_empty_string(curr_data.values.tolist())
                    # print(f"clean_values: {clean_values}\n\n") # df: {df}
                    # exit(1)
                    populated = excel_helper.append_data_to_worksheet(
                        # self.excel_session, sheet_name, [df.columns.values.tolist()] + df.values.tolist(), reset_from_first_row=True
                        excel_session, sheet_name, [curr_data.columns.values.tolist()] + clean_values, reset_from_first_row=True
                    )
                    # print(f"2) populated: {populated}\n\n")
                    # exit(1)
                    sf.log_audit_in_db(f"Done adding new data to worksheet(result: {populated}): {sheet_name}. {len(clean_values)} rows added", process_type=my_report_name, script_file_name=__file__)
                    # Truncate database table
                    db_conn.execute_command(f"TRUNCATE TABLE {my_table_name}")
                    sf.log_audit_in_db(f"Truncated table: {my_table_name}", process_type=my_report_name, script_file_name=__file__,log_type='Info')
                    
                    # # Prepare data for database write
                    # my_data = curr_data.copy()
                    
                    # Rename columns (equivalent to R's setnames)
                    column_mapping = dict(zip(old_names, new_names))
                    my_data = my_data.rename(columns=column_mapping)
                    
                    # Select only needed columns
                    available_db_cols = [col for col in db_cols if col in my_data.columns]
                    my_data = my_data[available_db_cols]
                    
                    # Remove rows without principal payment
                    if 'PRINCIPLE_MONTHLY_PYMT' in my_data.columns:
                        # my_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/lcp_encumb_payments_load_my_data_0.csv', index=False)                        
                        my_data = my_data.dropna(subset=['PRINCIPLE_MONTHLY_PYMT'])
                        my_data = my_data.replace('', None)
                    
                    # Write to database if there's data to write
                    if not my_data.empty:
                        # my_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/lcp_encumb_payments_load_my_data_1.csv', index=False)
                        sf.log_audit_in_db(f"Appending {len(my_data)} rows to {my_table}", process_type=my_report_name, script_file_name=__file__,log_type='Info')
                        success, rows_written = db_conn.append_dataframe(my_data, my_table, my_schema)

                        sf.log_audit_in_db(f"Appended {rows_written} rows to {my_table_name} (result: {success})", process_type=my_report_name, script_file_name=__file__)
                        
                        # Verify row count
                        count_query = f"SELECT COUNT(*) FROM {my_table_name}"
                        select_cnt_post = db_conn.execute_query(count_query).iloc[0, 0]
                        
                        if len(my_data) != select_cnt_post:
                            sf.log_audit_in_db(f"Data load mismatch: {len(my_data)} rows to write vs {select_cnt_post} rows in table, {my_table_name}. Notifying {warn_recip}", process_type=my_report_name, script_file_name=__file__,log_type='Warning')

                            body_text = (
                                f"<p>This is an automated email to inform you that the data load "
                                f"for the '{my_report_name}' routine resulted in an unexpected number of rows!</p>"
                                f"<p>The data to write to the '{my_table_name}' table had {len(my_data)} "
                                f"rows of data and the load resulted in a total of {select_cnt_post} "
                                f"rows in the table.</p>"
                                f"<p><em>Check the routine and re-load once the error has been identified.</em></p> "
                                f"{warn_sig}"
                            )
                            
                            email_client.send_email(
                                recipient=warn_recip,
                                subject=f"{my_report_name} Issue: Data Load Mismatch",
                                body=body_text,
                                test=testing_emails,
                                test_recipient=test_recip
                            )
                    
                    # Check for new loans and notify
                    if not prev_data.empty and all(col in my_updated.columns for col in ['CMPY_ID', 'LOANID']) and \
                       all(col in prev_data.columns for col in ['CMPY_ID', 'LOANID']):
                        
                        # Find new loans (equivalent to R's setdiff)
                        new_loans_merge = my_updated[['CMPY_ID', 'LOANID']].merge(
                            prev_data[['CMPY_ID', 'LOANID']], 
                            on=['CMPY_ID', 'LOANID'], 
                            how='left', 
                            indicator=True
                        )
                        new_loans = new_loans_merge[new_loans_merge['_merge'] == 'left_only'][['CMPY_ID', 'LOANID']]
                        # new_loans = my_updated # remove this line after testing
                        if len(new_loans) > 0:
                            sf.log_audit_in_db(f"New loan(s) found: {len(new_loans)}. Notifying {norm_recip}", process_type=my_report_name, script_file_name=__file__,log_type='Warning')
                            body_table = dataframe_to_html_table(new_loans)
                            body_text = (
                                f"<p>This is an automated email to let you know that the following loan(s) "
                                f"appear to be new additions in the '{my_report_name}' routine. Please "
                                f"enter any appropriate principal payments in the "
                                f"<a href=\"{workbook_url}\">{workbook_name}</a> "
                                f" sheet so they can be accessed by the needed reporting.</p>"
                                f"{body_table}<br>{norm_sig}"
                            )
                            
                            email_client.send_email(
                                recipient=norm_recip,
                                subject=f"{my_report_name} Update: New Loan(s)",
                                body=body_text,
                                test=testing_emails,
                                test_recipient=test_recip
                            )
                
                else:
                    # print(f"1) my_updated: {my_updated}\n\n")
                    # exit(1)
                    sf.log_audit_in_db(f"No data returned from query. Notifying {warn_recip} - query: {query}", process_type=my_report_name, script_file_name=__file__,log_type='Warning')
                    # No data returned from query
                    okay_to_continue = False
                    body_text = (
                        f"<p>This is an automated email to inform you that the MRI query "
                        f"for the '{my_report_name}' routine didn't return any data!</p>"
                        f"<p><em>The routine is aborting without an update!</em></p> {warn_sig}"
                    )
                    
                    email_client.send_email(
                        recipient=warn_recip,
                        subject=f"{my_report_name} Issue: No MRI Data",
                        body=body_text,
                        test=testing_emails,
                        test_recipient=test_recip
                    )
                    
            except Exception as e:
                print(f"Error in main processing: {e}")
                okay_to_continue = False
                sf.log_audit_in_db(f"Error in main processing for workbook: {workbook_url}: {e}", process_type=my_report_name, script_file_name=__file__,log_type='Error')
    
    except Exception as e:
        print(f"Critical error in main execution: {e}")
        sf.log_audit_in_db(f"Critical error in main execution for workbook: {workbook_url}: {e}", process_type=my_report_name, script_file_name=__file__,log_type='Error')
        
    finally:

        # calculate duration
        end_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
        duration = sf.get_duration(start_time)

        sf.log_audit_in_db(f"Finished main():\t[time: {end_now}] [{duration}] for workbook: {workbook_url}", process_type=my_report_name, script_file_name=__file__)
        # Clean up connections
        if db_conn:
            db_conn.close()
        
        print(f"'{my_report_name}' routine completed.")


if __name__ == "__main__":

    # sets timezone to Central
    os.environ['TZ'] = 'America/Chicago'
    time.tzset()

    main() 