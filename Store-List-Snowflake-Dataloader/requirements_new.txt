Babel==2.15.0
ConfigUpdater==3.2
Deprecated==1.2.14
Django==5.1.6
Flask==2.2.5
Flask-AppBuilder==4.5.0
Flask-Babel==2.0.0
Flask-Caching==2.3.0
Flask-JWT-Extended==4.6.0
Flask-Limiter==3.7.0
Flask-Login==0.6.3
Flask-SQLAlchemy==2.5.1
Flask-Session==0.5.0
Flask-WTF==1.2.1
GitPython==3.1.43
Jinja2==3.1.5
Mako==1.3.5
MarkupSafe==2.1.5
Office365-REST-Python-Client==2.6.2
PyJWT==2.8.0
PyMySQL==1.1.0
PyNaCl==1.5.0
PyYAML==6.0.1
Pygments==2.18.0
SQLAlchemy==1.4.52
SQLAlchemy-JSONField==1.0.2
SQLAlchemy-Utils==0.41.2
WTForms==3.1.2
Werkzeug==2.2.3
aiohttp==3.9.5
aiosignal==1.3.1
airflow-oracle-snowflake-plugin==0.1.2
alembic==1.13.1
annotated-types==0.6.0
anyio==4.4.0
apache-airflow==2.9.2
apache-airflow-providers-common-io==1.3.2
apache-airflow-providers-common-sql==1.14.1
apache-airflow-providers-fab==1.2.0
apache-airflow-providers-ftp==3.10.0
apache-airflow-providers-http==4.12.0
apache-airflow-providers-imap==3.6.1
apache-airflow-providers-oracle==3.10.1
apache-airflow-providers-smtp==1.7.1
apache-airflow-providers-snowflake==3.3.0
apache-airflow-providers-sqlite==3.8.1
apispec==6.6.1
appscript==1.3.0
argcomplete==3.4.0
asgiref==3.8.1
asn1crypto==1.5.1
astunparse==1.6.3
async-timeout==4.0.3
atpublic==4.1.0
attrs==23.2.0
backoff==2.2.1
bcrypt==4.2.0
beautifulsoup4==4.12.3
blinker==1.8.2
boto3==1.34.109
botocore==1.34.109
cached-property==2.0.1
cachelib==0.9.0
cachetools==5.3.3
certifi==2024.2.2
cffi==1.16.0
charset-normalizer==3.3.2
click==8.1.7
clickclick==20.10.2
cloudpickle==2.2.1
colorama==0.4.6
colorlog==4.8.0
connexion==2.14.2
contourpy==1.3.1
cron-descriptor==1.4.3
croniter==2.0.5
cryptography==42.0.5
cx_Oracle==8.3.0
cycler==0.12.1
databricks-sql-connector==2.9.3
defusedxml==0.7.1
deprecation==2.1.0
dill==0.3.8
django-admin-adminlte==1.0.8
django-adminlte3==0.1.6
django-snowflake==5.1
dlt==0.4.8
dnspython==2.6.1
docutils==0.21.2
docx2pdf==0.1.8
duckdb==0.10.2
duckdb_engine==0.11.5
email_validator==2.2.0
et-xmlfile==1.1.0
exceptiongroup==1.2.1
exchangelib==5.5.1
fastapi==0.115.8
fastapi-cli==0.0.7
filelock==3.13.4
flask-cors==5.0.1
fonttools==4.56.0
frozenlist==1.4.1
fsspec==2024.5.0
gitdb==4.0.11
giturlparse==0.12.0
google-api-core==2.19.0
google-api-python-client==2.154.0
google-auth==2.29.0
google-auth-httplib2==0.2.0
google-cloud-bigquery==3.23.0
google-cloud-bigquery-storage==2.24.0
google-cloud-core==2.4.1
google-crc32c==1.5.0
google-re2==1.1.20240702
google-resumable-media==2.7.0
googleapis-common-protos==1.63.0
greenlet==3.2.3
grpcio==1.64.0
grpcio-status==1.62.2
gunicorn==22.0.0
h11==0.14.0
hdbcli==2.20.22
hexbytes==1.2.0
httpcore==1.0.5
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.0
humanize==4.9.0
idna==3.7
importlib_metadata==7.1.0
importlib_resources==6.4.0
inflection==0.5.1
ingestr==0.4.0
isodate==0.7.2
itsdangerous==2.2.0
jmespath==1.0.1
jsonpath-ng==1.6.1
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
kiwisolver==1.4.8
lazy-object-proxy==1.10.0
limits==3.13.0
linkify-it-py==2.0.3
lockfile==0.12.2
lxml==5.2.2
lz4==4.3.3
makefun==1.15.2
markdown-it-py==3.0.0
marshmallow==3.21.3
marshmallow-oneofschema==3.1.1
marshmallow-sqlalchemy==0.28.2
matplotlib==3.10.1
mdit-py-plugins==0.4.1
mdurl==0.1.2
methodtools==0.4.7
monotonic==1.6
more-itertools==10.3.0
msal==1.32.0
multidict==6.0.5
mysql-connector-python==9.1.0
numpy==1.26.4
oauthlib==3.2.2
openpyxl==3.1.2
opentelemetry-api==1.25.0
opentelemetry-exporter-otlp==1.25.0
opentelemetry-exporter-otlp-proto-common==1.25.0
opentelemetry-exporter-otlp-proto-grpc==1.25.0
opentelemetry-exporter-otlp-proto-http==1.25.0
opentelemetry-proto==1.25.0
opentelemetry-sdk==1.25.0
opentelemetry-semantic-conventions==0.46b0
oracledb==1.4.2
ordered-set==4.1.0
orjson==3.9.10
packaging==24.0
pandas==2.2.1
paramiko==3.4.0
pathspec==0.12.1
pathvalidate==3.2.0
pendulum==3.0.0
pillow==11.0.0
platformdirs==4.2.0
pluggy==1.5.0
ply==3.11
polars==0.20.19
prison==0.2.1
proto-plus==1.23.0
protobuf==4.25.3
psutil==6.0.0
psycopg2-binary==2.9.9
py-machineid==0.5.1
pyOpenSSL==24.1.0
pyarrow==15.0.2
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycparser==2.22
pydantic==2.7.0
pydantic_core==2.18.1
pymongo==4.6.3
pyodbc==5.1.0
pyparsing==3.2.0
pysftp==0.2.9
pyspnego==0.11.2
python-daemon==3.0.1
python-dateutil==2.9.0.post0
python-dotenv==0.21.1
python-http-client==3.3.7
python-multipart==0.0.20
python-nvd3==0.16.0
python-slugify==8.0.4
pytz==2024.1
redshift-connector==2.1.0
referencing==0.35.1
requests==2.31.0
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
requests_ntlm==1.3.0
requirements-parser==0.9.0
rfc3339-validator==0.1.4
rich==13.7.1
rich-argparse==1.5.2
rich-toolkit==0.13.2
rpds-py==0.19.0
rsa==4.9
rudder-sdk-python==2.1.0
s3transfer==0.10.1
scramp==1.4.5
semver==3.0.2
sendgrid==6.11.0
setproctitle==1.3.3
shellingham==1.5.4
simple-ddl-parser==1.5.3
simplejson==3.19.2
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
snowflake==0.7.0
snowflake-connector-python==3.8.1
snowflake-snowpark-python==1.14.0
snowflake-sqlalchemy==1.5.3
snowflake._legacy==0.7.0
snowflake.core==0.7.0
sortedcontainers==2.4.0
soupsieve==2.5
sqlalchemy-bigquery==1.11.0
sqlalchemy-hana==2.0.0
sqlalchemy-redshift==0.8.14
sqlalchemy2-stubs==0.0.2a38
sqlglot==25.9.0
sqlglotrs==0.2.8
sqlparse==0.5.0
starkbank-ecdsa==2.2.0
starlette==0.45.3
tabulate==0.9.0
tenacity==8.3.0
termcolor==2.4.0
text-unidecode==1.3
thrift==0.16.0
time-machine==2.14.1
toml==0.10.2
tomlkit==0.12.4
tqdm==4.66.2
typer==0.12.3
types-setuptools==69.5.0.********
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.2
ua-parser==1.0.1
ua-parser-builtins==0.18.0.post1
uc-micro-py==1.0.3
unicodecsv==0.14.1
universal_pathlib==0.2.2
uritemplate==4.1.1
urllib3==2.2.1
user-agents==2.2.0
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
websockets==14.2
whitenoise==6.9.0
wirerope==0.4.7
wrapt==1.16.0
xlsxwriter==3.2.5
yarl==1.9.4
zipp==3.19.2