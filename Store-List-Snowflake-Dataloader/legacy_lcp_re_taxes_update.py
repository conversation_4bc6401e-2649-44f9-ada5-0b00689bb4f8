#!/usr/bin/env python3
"""
LEGACY LCP RE TAXES Update Script
Converted from R to Python
Written by <PERSON> February 2022
Python conversion: 2024

This script reads Real Estate tax data from a Excel Sheet and loads it into Snowflake.
Version 20250207 - moved to gmailr for emails, moved to Snowflake CORPORATE.LCP_RE_TAXES table
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from snowflake.connector.pandas_tools import write_pandas

import re
import time
from pathlib import Path
import libs.snowflake_helper as sf
import libs.email_client as email_client
from libs.excel_helper import SharePointExcelOnline

OVERRIDE_EMAIL_RECIPIENTS = False


# Configuration
TESTING_EMAILS = True  # Set to False for production
SCRIPT_FOLDER = "LEGACY_RE_TAXES_DATA"
MY_REPORT_NAME = "Real Estate Taxes Update"
MY_SCRIPT_NAME = "legacy_lcp_re_taxes_update.py"

# Snowflake Configuration
SF_ENVIRON = "PROD"  # or "STAGE"
MY_SCHEMA = "CORPORATE"
MY_TABLE = "LCP_RE_TAXES"
MY_TABLE_NAME = f"{MY_SCHEMA}.{MY_TABLE}"

# Excel Sheets Configuration
GSHT_AUTH_EMAIL = "<EMAIL>"
GSHT_URL = "https://docs.google.com/spreadsheets/d/1VsMKcmoRpkiid8MsHM2tb1OX4CS0UuVDXY_Jc77BhA0/edit#gid=0" # old
GSHT_KEY = GSHT_URL.split('/d/')[1].split('/')[0] # old

# MS 365 Configuration
FILE_ID = '01B3UYKC3DZ4NILFZXZZB3W5JZZ3GDWI42' # prod  : https://highlandventuresltd442-my.sharepoint.com/:x:/g/personal/jbogdan_hv_ltd/ES2YtGEvLvZS1_GfF0CD6D8B2WgljKKO85yOAxc4-WY_Ig?e=prYQcO&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDYwMjA2NjE2IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9
SITE_URL = 'https://highlandventuresltd442-my.sharepoint.com/personal/mgodart_hv_ltd'
PUBLIC_SITE_URL = 'https://highlandventuresltd442.sharepoint.com/:x:/r/sites/finance/_layouts/15/doc2.aspx?sourcedoc=%7BDE5E3A55-E54F-47E1-AA03-2B567E7524E7%7D&file=Real%20Estate%20Taxes.xlsx&action=default&mobileredirect=true&ovuser=704b8542-ab1c-45fb-a7d6-90964ea9c759%2Cjgarifuna%40hv.ltd&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI1MDYwMjA2NjE2IiwiSGFzRmVkZXJhdGVkVXNlciI6ZmFsc2V9'

# Email Configuration
GMAIL_AUTH_EMAIL = "<EMAIL>"
GMAIL_REPLY_TO = "<EMAIL>"
WARN_RECIP = ["<EMAIL>","<EMAIL>"]
NORM_RECIP = ["<EMAIL>","<EMAIL>"]
TEST_RECIP = ["<EMAIL>","<EMAIL>"]
TEST_CC_RECIP = ["<EMAIL>"]

# Path Configuration

TESTING_PC = False
MAIN_PATH = Path(os.environ["SCRIPTS_BASE_DATA_DIR"])

LOG_PATH = MAIN_PATH / SCRIPT_FOLDER
MY_REPORT_PATH = LOG_PATH

# Email signature template
WARN_SIG = """<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> 
(847)904-9043 Office<br/> (715)379-8525 Cell"""
NORM_SIG = WARN_SIG

class DataProcessor:
    def __init__(self):
        self.sf_obj = sf.SnowflakeHelper()
        self.sf_obj.LOG_TO_DB = False
        # self.email_obj = email_client.EmailClient()
        self.excel_helper = SharePointExcelOnline()

        self.okay_to_continue = True
        self.sf_conn = self.sf_obj.conn
        # self.gc = None
        self.query_date = datetime.now().strftime("%d-%b-%y")
        

            
    def check_df_rows(self, df, min_num_rows, report_name=None):
        """Check if dataframe has minimum required rows"""
        if isinstance(df, pd.DataFrame):
            if len(df) >= min_num_rows:
                error_status = f"{report_name}: OKAY"
                temp_nrow = len(df)
                temp_bool = True
            else:
                temp_bool = False
                temp_nrow = len(df)
                error_status = f"{report_name}: INCOMPLETE"
        else:
            temp_bool = False
            temp_nrow = 0
            error_status = f"{report_name}: ERROR"
            
        return temp_bool, temp_nrow, error_status
        
    
            
    def get_prior_data(self):
        """Query existing table data from Snowflake"""
        try:
            query = f"SELECT * FROM {MY_TABLE_NAME}"
            cursor = self.sf_conn.cursor()
            cursor.execute(query)
            prior_data = cursor.fetch_pandas_all()
            cursor.close()
            self.sf_obj.log_audit_in_db(log_msg=f"Retrieved {len(prior_data)} rows from existing table", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
            # self.logger.info(f"Retrieved {len(prior_data)} rows from existing table")

            return prior_data
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error querying prior data: {str(e)}", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error querying prior data: {str(e)}")
            self.okay_to_continue = False
            return None
            
    def get_google_sheet_data(self):
        """Read data from Excel Sheet"""
        try:
            # Open the Excel Sheet
            # sheet = self.gc.open_by_key(GSHT_KEY)
            # worksheet = sheet.worksheet("RE Taxes Data")
            
            # # Get all values and convert to DataFrame
            # data = worksheet.get_all_records()
            # df = pd.DataFrame(data)

            excel_session = self.excel_helper.get_excel_file_by_id(SITE_URL, FILE_ID)
            existing_data_from_sheet_dict = self.excel_helper.get_worksheet_data(excel_session, "RE Taxes Data")
            values_existing_data_from_sheet_list = existing_data_from_sheet_dict.get('values')
            existing_data_from_sheet_df = pd.DataFrame(data=values_existing_data_from_sheet_list[1:], columns=values_existing_data_from_sheet_list[0])
            # existing_data_from_sheet_df = existing_data_from_sheet_df[existing_data_from_sheet_df['ST'] != '']  # # remove rows where all values are empty strings

            
            if len(existing_data_from_sheet_df) == 0:
                self.sf_obj.log_audit_in_db(log_msg="No data found in Excel Sheet", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
                # self.logger.error("No data found in Excel Sheet")
                email_client.send_email(
                    recipient=WARN_RECIP,
                    subject=f"{MY_REPORT_NAME} Issue: No Excel Sheet Tax Rows",
                    body=f"<p>This is an automated email to inform you that it appears there may "
                    f"have been an error reading Excel Sheet in the {MY_REPORT_NAME}!</p>"
                    f"<p>The routine is aborting without an update</p> {WARN_SIG}",
                    # test=False
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                self.okay_to_continue = False
                return None
                
            self.sf_obj.log_audit_in_db(log_msg=f"Retrieved {len(existing_data_from_sheet_df)} rows from  Sheet", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
            # self.logger.info(f"Retrieved {len(df)} rows from Excel Sheet")
            return existing_data_from_sheet_df
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error reading Excel Sheet: {str(e)}", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error reading Excel Sheet: {str(e)}")
            self.okay_to_continue = False
            return None
            
    def process_data(self, gsheet_data):
        """Process and clean the data from Excel Sheet"""
        try:
            # Column names mapping
            gsht_bldg_colname = 'Building #'
            gsht_bldg_colname_new = 'BLDG'
            # gsht_amount_colname = 'Parcel Amount Entered in Oracle during this fiscal Year - Need to update Formula on annual basis'
            gsht_amount_colname = 'Parcel Amount Entered in Sage during this fiscal Year - Need to update Formula on annual basis'
            gsht_amount_colname_new = 'AMOUNT'
            
            # Filter and select needed columns
            # df_filtered = gsheet_data[
            #     (gsheet_data[gsht_bldg_colname] < 10000)
            # ][[gsht_bldg_colname, gsht_amount_colname]].copy()
            df_filtered = gsheet_data[[gsht_bldg_colname, gsht_amount_colname]].copy()

            # remove non-numeric values buildings
            df_filtered[gsht_bldg_colname] = pd.to_numeric(
                df_filtered[gsht_bldg_colname], errors='coerce'
            )
            # df_filtered = df_filtered.dropna()

            # Rename columns
            df_filtered.rename(columns={
                gsht_bldg_colname: gsht_bldg_colname_new,
                gsht_amount_colname: gsht_amount_colname_new
            }, inplace=True)
            
            # Convert amount column to numeric, filtering out non-numeric values
            df_filtered[gsht_amount_colname_new] = pd.to_numeric(
                df_filtered[gsht_amount_colname_new], errors='coerce'
            )
            df_filtered = df_filtered.dropna(subset=[gsht_amount_colname_new])
            
            # Convert building column to numeric
            df_filtered[gsht_bldg_colname_new] = pd.to_numeric(
                df_filtered[gsht_bldg_colname_new], errors='coerce'
            )
            df_filtered = df_filtered.dropna()

            df_filtered[gsht_bldg_colname_new] = df_filtered[gsht_bldg_colname_new].astype(int) # cast building as int
            
            # print(f"df_filtered: {df_filtered}")
            # exit()

            # Group by building and sum amounts
            df_processed = df_filtered.groupby(gsht_bldg_colname_new)[gsht_amount_colname_new].sum().reset_index()

            # print(f"df_processed: {df_processed}")
            # exit()

            
            if len(df_processed) == 0:
                self.sf_obj.log_audit_in_db(log_msg="No valid data rows after processing", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Warning')
                # self.logger.warning("No valid data rows after processing")
                email_client.send_email(
                    recipient=WARN_RECIP,
                    subject=f"{MY_REPORT_NAME} Issue: No Excel Sheet Tax Rows",
                    body=f"<p>This is an automated email to inform you that it appears there may "
                    f"have been an error populating the {MY_TABLE_NAME} "
                    f"table. The routine returned 0 buildings with tax data and will truncate "
                    f"the table to 0 rows.</p>"
                    f"<p>If this is expected result due to yearly turnover then no action needed.</p>"
                    f"<p>Otherwise, check the Excel sheet. Alternately, check the {MY_REPORT_NAME} routine.</p>"
                    f"{WARN_SIG}",
                    # test=False
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
            self.sf_obj.log_audit_in_db(log_msg=f"Processed data: {len(df_processed)} buildings", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
            # self.logger.info(f"Processed data: {len(df_processed)} buildings")
            return df_processed
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error processing data: {str(e)}", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error processing data: {str(e)}")
            self.okay_to_continue = False
            return None
            
    def load_data_to_snowflake(self, processed_data):
        """Load processed data to Snowflake table"""
        try:
            cursor = self.sf_conn.cursor()
            
            # Truncate table
            truncate_query = f"TRUNCATE TABLE {MY_TABLE_NAME}"
            cursor.execute(truncate_query)
            
            # Insert new data
            if len(processed_data) > 0:
                success, nchunks, nrows, _ = write_pandas(
                    self.sf_conn, 
                    processed_data, 
                    MY_TABLE,
                    schema=MY_SCHEMA,
                    auto_create_table=False,
                    overwrite=False
                )
                
                if success:
                    self.sf_obj.log_audit_in_db(log_msg=f"Successfully loaded {nrows} rows to Snowflake", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
                    # self.logger.info(f"Successfully loaded {nrows} rows to Snowflake")
                else:
                    self.sf_obj.log_audit_in_db(log_msg=f"Failed to write data to Snowflake", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
                    raise Exception("Failed to write data to Snowflake")
            else:
                self.sf_obj.log_audit_in_db(log_msg="No data to load - table truncated to 0 rows", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
                # self.logger.info("No data to load - table truncated to 0 rows")
                
            cursor.close()
            
            # Verify data load
            self.verify_data_load(processed_data)
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error loading data to Snowflake: {str(e)}", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error loading data to Snowflake: {str(e)}")
            self.okay_to_continue = False
            
    def verify_data_load(self, original_data):
        """Verify that data was loaded correctly by comparing checksums"""
        try:
            # Get sum from database
            cursor = self.sf_conn.cursor()
            cursor.execute(f"SELECT SUM(AMOUNT) FROM {MY_TABLE_NAME}")
            db_checksum = cursor.fetchone()[0]
            cursor.close()
            
            # Calculate sum from original data
            data_checksum = original_data['AMOUNT'].sum() if len(original_data) > 0 else 0
            
            if abs(float(db_checksum or 0) - float(data_checksum)) > 0.01:  # Allow for small rounding differences
                # Send warning email
                email_client.send_email(
                    recipient=WARN_RECIP,
                    subject=f"{MY_REPORT_NAME} Issue: Mis-match of Excel Sheet sum vs. Database Load",
                    body=f"This is an automated email to inform you that it appears there may "
                    f"have been an error in the {MY_TABLE_NAME} "
                    f"database load. The sum of the tax column in the Excel sheet was: <br/>"
                    f"{data_checksum:.2f}<br/><br/>"
                    f"The sum of the {MY_TABLE_NAME} table is: <br/>"
                    f"{db_checksum:.2f}<br/><br/>"
                    f"The query is contained in the {MY_SCRIPT_NAME} script.<br/><br/>"
                    f"{WARN_SIG}",
                    # test=False
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
            else:
                # Send completion email
                email_client.send_email(
                    recipient=NORM_RECIP,
                    subject=f"{MY_REPORT_NAME} Status: COMPLETE",
                    body=f"This is an automated email to inform you that the "
                    f"<b>{MY_REPORT_NAME}</b> routine has "
                    f"completed and results should now be available in the "
                    f"{MY_TABLE_NAME} table.<br/><br/>"
                    f"{NORM_SIG}",
                    # test=False
                    override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
                )
                
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Error verifying data load: {str(e)}", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Error verifying data load: {str(e)}")
            
    def cleanup(self):
        """Clean up connections"""
        if self.sf_conn:
            self.sf_conn.close()
            # self.sf_obj.log_audit_in_db(log_msg="Snowflake connection closed", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
            # self.logger.info("Snowflake connection closed")
            
    def run(self):
        """Main execution function"""
        try:
            self.sf_obj.log_audit_in_db(log_msg="Starting Real Estate Taxes Update process", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
            # self.logger.info("Starting Real Estate Taxes Update process")
            
            # Setup connections
            # self.setup_snowflake_connection()
            # if not self.okay_to_continue:
            #     return
                
            # self.setup_google_sheets_connection()
            # if not self.okay_to_continue:
            #     return
                
            # Get existing data (for reference)
            prior_data = self.get_prior_data()
            # print(f"prior_data: {prior_data}")
            # exit()
            if not self.okay_to_continue:
                return
                
            # Get new data from Excel Sheet
            gsheet_data = self.get_google_sheet_data()
            # gsheet_data.to_csv('/Users/<USER>/jg/web/customers/highland_ventures/projects/snowflake/store_list/output/legacy_lcp_re_taxes_update.csv', index=False)
            # print(f"gsheet_data: {gsheet_data}")
            # exit()
            if not self.okay_to_continue:
                return
                
            # Process the data
            processed_data = self.process_data(gsheet_data)
            # print(f"processed_data: {processed_data}")
            # exit()
            if not self.okay_to_continue:
                return
                
            # Load data to Snowflake
            self.load_data_to_snowflake(processed_data)
            
            self.sf_obj.log_audit_in_db(log_msg="Real Estate Taxes Update process completed successfully", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Info')
            # self.logger.info("Real Estate Taxes Update process completed successfully")
            
        except Exception as e:
            self.sf_obj.log_audit_in_db(log_msg=f"Unexpected error in main process: {str(e)}", process_type=MY_REPORT_NAME, script_file_name=__file__, log_type='Error')
            # self.logger.error(f"Unexpected error in main process: {str(e)}")
            email_client.send_email(
                recipient=WARN_RECIP,
                subject=f"{MY_REPORT_NAME} Error: Unexpected Error",
                body=f"<p>An unexpected error occurred during the {MY_REPORT_NAME} process:</p>"
                f"<p><b>Error:</b> {str(e)}</p>"
                f"<p>Please check the logs and script.</p>"
                f"{WARN_SIG}",
                # test=False
                override_email_recipients=OVERRIDE_EMAIL_RECIPIENTS
            )
        finally:
            self.cleanup()

def main():
    """Main entry point"""
    processor = DataProcessor()
    processor.run()

if __name__ == "__main__":
    main() 