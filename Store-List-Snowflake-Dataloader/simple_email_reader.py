#!/usr/bin/env python3
"""
Simple Office 365 Email Reader
A minimal example to get started with reading emails from Office 365
"""

import os
import sys
from datetime import datetime, timed<PERSON><PERSON>

# Add the libs directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'libs'))

from libs.office365_email_reader import Office365EmailReader, EmailReaderMethod


def main():
    """Simple example to read emails from Office 365"""
    print("Simple Office 365 Email Reader")
    print("=" * 50)
    
    # TODO: Replace these with your actual credentials
    EMAIL_ADDRESS = "<EMAIL>"
    APP_PASSWORD = "your_app_password"
    
    # Set up environment variables
    os.environ["OFFICE365_EMAIL_ADDRESS"] = EMAIL_ADDRESS
    os.environ["OFFICE365_EMAIL_PASSWORD"] = APP_PASSWORD
    
    print(f"Connecting to {EMAIL_ADDRESS}...")
    
    try:
        # Create email reader using IMAP (simplest method)
        reader = Office365EmailReader(method=EmailReaderMethod.IMAP)
        
        # Get the last 5 emails
        print("Fetching last 5 emails...")
        emails = reader.get_emails(limit=5)
        
        if not emails:
            print("No emails found!")
            return
        
        print(f"Found {len(emails)} emails:")
        print("-" * 50)
        
        # Display email information
        for i, email in enumerate(emails, 1):
            print(f"{i}. From: {email.sender}")
            print(f"   Subject: {email.subject}")
            print(f"   Date: {email.received_date}")
            print(f"   Read: {'Yes' if email.is_read else 'No'}")
            print(f"   Preview: {email.body[:100]}...")
            
            if email.attachments:
                print(f"   Attachments: {len(email.attachments)} files")
            
            print("-" * 50)
        
        # Close connection
        reader.close()
        print("Done!")
        
    except ValueError as e:
        print(f"Configuration error: {e}")
        print("\nPlease check your credentials and try again.")
        print("Make sure to:")
        print("1. Replace EMAIL_ADDRESS with your actual email")
        print("2. Replace APP_PASSWORD with your app password")
        print("3. Enable IMAP in your Office 365 account")
        
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("- Check your internet connection")
        print("- Verify your email address and app password")
        print("- Make sure IMAP is enabled in Office 365")


if __name__ == "__main__":
    main() 