# Converted from  R to Python: 5/28/2025
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz

# import smtplib
# from email.mime.text import MIMEText
# from email.mime.multipart import MIMEMultipart
# from email.mime.application import MIMEApplication
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
# import snowflake.connector
# from snowflake.connector.pandas_tools import write_pandas
import warnings
import libs.snowflake_helper as sf
import libs.email_client as email_client


warnings.filterwarnings('ignore')

# dir_path = os.path.dirname(os.path.realpath(__file__))
dir_path = os.environ["SCRIPTS_BASE_DATA_DIR"]
envm = os.environ["DATABASE_ENVM"]
csm_db = os.environ["DATABASE_CSM_DATABASE"]

# Configuration
TESTING_EMAILS = False
VERSION = "20250321"

# Email parameters
NORMAL_RECIPIENTS = ["<EMAIL>"]
WARNING_RECIPIENTS = ["<EMAIL>", "<EMAIL>"]
TEST_RECIPIENTS = ["<EMAIL>"]
TEST_CC_RECIPIENTS = ["<EMAIL>"]


# REPORT_PATH = LOG_PATH
REPORT_PATH = f"{dir_path}/LEGACY_MRI_Exceptions-Insurance"
MAIN_PATH = REPORT_PATH

def get_signature(name='', title='', email='', phone=''):
    """Get email signature template"""
    sig_path = os.path.join(MAIN_PATH, "HTML_signatures.csv")
    if os.path.exists(sig_path):
        sig_df = pd.read_csv(sig_path)
        template = sig_df[sig_df['Desc'] == 'HV Normal']['HTML'].iloc[0]
        return template.replace('[NAME]', name).replace('[TITLE]', title).replace('[EMAIL_FULL]', email).replace('[TEL (*************]', phone)
    return ""

def check_dataframe_rows(df, min_rows, report_name=None):
    """Check if dataframe has sufficient rows"""
    if isinstance(df, pd.DataFrame):
        if len(df) >= min_rows:
            return True, len(df), f"{report_name}: OKAY"
        return False, len(df), f"INCOMPLETE DATA: {report_name}"
    return False, 0, f"LOAD ERROR: {report_name}"

def write_excel(dirpath, fname, sheet_name, df, colnames=True, colwidths=None, writeover=True):
    """Write dataframe to Excel file with formatting"""
    my_fn = os.path.join(dirpath, fname)
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sheet_name
    
    # Write data
    for r_idx, row in enumerate(df.itertuples(index=False), 1):
        for c_idx, value in enumerate(row, 1):
            ws.cell(row=r_idx, column=c_idx, value=value)
    
    # Add headers
    if colnames:
        for c_idx, col in enumerate(df.columns, 1):
            ws.cell(row=1, column=c_idx, value=col)
    
    # Format headers
    header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
    header_font = Font(name="Arial Narrow", size=12, bold=True)
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Set column widths
    if colwidths is not None:
        for col_name, width in colwidths.items():
            col_idx = df.columns.get_loc(col_name) + 1
            ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = width
    
    # Create directory if it doesn't exist
    os.makedirs(dirpath, exist_ok=True)
    
    # Save file
    try:
        wb.save(my_fn)
        return True, my_fn
    except Exception as e:
        return False, str(e)



def main():
    # Set timezone
    tz = pytz.timezone('America/Chicago')
    query_date = datetime.now(tz).strftime("%d-%b-%y")
    
    
    sf_obj = sf.SnowflakeHelper()
    conn = sf_obj.conn

    # Set timezone in Snowflake
    conn.cursor().execute("ALTER SESSION SET TIMEZONE = 'America/Chicago'")
    
    # Get insurance exceptions
    query = f"""
    SELECT /* Combined Insurance Exceptions v20240110...revised 20241031 for SNOWFLAKE */
    CASE
        WHEN TO_DATE(ENTITY.DISPOSED) >= DATEADD('YEAR',-2,DATE_TRUNC('YEAR',CURRENT_DATE())) AND INS.CARRIER IS NOT NULL
            THEN 'BLDG DISPOSED, currently has CARRIER'
        WHEN (BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL) AND (POWNER.OWNED_RENTED = 'Owned' AND (INS.INSLCODE IS NULL or INS.INSLCODE NOT IN ('SLF','INS','BAN')) )
            THEN 'BLDG OWNED, missing current INS info'
        WHEN (INS.CARRIER IS NOT NULL or INS.INSLLIMI != 0 or INS.DEDUCT != 0)
            AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
            AND INS.INSLCODE = 'SLF'
            THEN 'SLF INSURED, but has Carrier, Limit or Deductable Info'
    END AS ISSUE,
    CASE
        WHEN TO_DATE(ENTITY.DISPOSED) >= DATEADD('YEAR',-2,DATE_TRUNC('YEAR',CURRENT_DATE())) AND INS.CARRIER IS NOT NULL
            THEN TO_DATE(ENTITY.DISPOSED)
        WHEN (BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL) AND (POWNER.OWNED_RENTED = 'Owned' AND (INS.INSLCODE IS NULL or INS.INSLCODE NOT IN ('SLF','INS','BAN')) )
            THEN IFNULL(TO_DATE(INS.INS_LAST_MOD_DATE), TO_DATE(ENTITY.ACQUIRED))
        WHEN (INS.CARRIER IS NOT NULL or INS.INSLLIMI != 0 or INS.DEDUCT != 0)
            AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
            AND INS.INSLCODE = 'SLF'
            THEN TO_DATE(INS.INS_LAST_MOD_DATE)
    END AS "CHG DATE",
    TO_CHAR(BLDG.BLDGID) as BLDGID,
    INS.INSLCODE AS "INS CODE",
    INS.INSCTYPE AS "INS TYPE",
    INS.PERTAIN,
    INS.CARRIER,
    INS.LIMIT,
    INS.DEDUCT AS DEDUCTABLE,
    INS.INS_START_DATE AS "INS START DATE",
    INS.INS_END_DATE AS "INS END DATE",
    INS.INS_LAST_MOD_DATE AS "INS LAST MODIFIED"
    FROM {csm_db}.MRI.BLDG
    LEFT JOIN {csm_db}.MRI.ENTITY
    ON BLDG.ENTITYID = ENTITY.ENTITYID
    LEFT JOIN
    ( /* Insurance current info */
        SELECT
            INSL.TABLEKEY AS BLDGID,
            INSL.RECNUM,
            INSL.INSLCODE,
            INSC.INSCTYPE,
            INSL.INSLLIMI,
            INSL.CARRIERID,
            TB_AM_INSCARRIER.INSCARRIER as CARRIER,
            TB_AM_INSCARRIER.PHONE AS PHONE,
            INSL.DEDUCT,
            INSL.INSLLIMI AS LIMIT,
            INSL.REPLACE,
            INSL.PERTAIN,
            INSL.LASTDATE AS INS_LAST_MOD_DATE,
            TO_DATE(INSL.INSLSTAR) AS INS_START_DATE,
            TO_DATE(INSL.INSLEND) AS INS_END_DATE
        FROM {csm_db}.MRI.INSL
        LEFT JOIN {csm_db}.MRI.INSC ON INSL.INSLCODE = INSC.INSCCODE
        LEFT JOIN {csm_db}.MRI.TB_AM_INSCARRIER ON INSL.CARRIERID = TB_AM_INSCARRIER.INSCARRIERID
        WHERE INSL.RECNUM = (SELECT MAX(I.RECNUM) FROM MRI.INSL I WHERE I.TABLEID = 'BLDG' AND I.TABLEKEY = INSL.TABLEKEY AND I.INSLSTAR <= CURRENT_DATE AND I.INSLEND >= CURRENT_DATE )
    ) INS
    ON BLDG.BLDGID = INS.BLDGID
    LEFT JOIN
    (/* PRIMARY OWNER */
        SELECT ENTITYID,
        OWNERID,
        OWNED_RENTED    
        FROM
        (
          SELECT O.ENTITYID,
          O.OWNERID,
          CASE WHEN UPPER(O.OWNERID) = 'RENTED' THEN 'Rented' ELSE 'Owned' END AS OWNED_RENTED,
          O.BEGPD,
          O.PRIMARYOWN,
          O.PERCENT,
          O.LASTDATE,
          RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.BEGPD, O.LASTDATE DESC) AS RANK
          FROM MRI.GOWN O
          WHERE (O.BEGPD = 
              (
                  SELECT MAX(I.BEGPD)
                  FROM {csm_db}.MRI.GOWN I
                  WHERE I.ENTITYID = O.ENTITYID
                      AND I.BEGPD <= TO_CHAR(GETDATE(), 'yyyyMM')
              )
          OR (
                  SELECT MAX(I.BEGPD)
                  FROM {csm_db}.MRI.GOWN I
                  WHERE I.ENTITYID = O.ENTITYID
              ) IS NULL
          )
          AND (O.ENDPD IS NULL OR O.ENDPD >= TO_CHAR(GETDATE(), 'yyyyMM'))
        ) RANKED
        WHERE RANK = 1
    ) POWNER
    ON ENTITY.ENTITYID = POWNER.ENTITYID
    LEFT JOIN {csm_db}.MRI.GNAM 
    ON POWNER.OWNERID = GNAM.OWNERID
    WHERE trim(bldg.BLDGID) not in ('HV3RD','HVCORP')
    AND
    (
            ( /* BLDG disposed, but INS.CARRIER is not NULL (active insurance?) */
                TO_DATE(ENTITY.DISPOSED) >= DATEADD('YEAR',-2,DATE_TRUNC('YEAR',CURRENT_DATE()))
                AND INS.CARRIER IS NOT NULL
            )
            OR
            ( /* Owned BLDG (normally self insure) missing info */
                (BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL)
                AND (POWNER.OWNED_RENTED = 'Owned' AND (INS.INSLCODE IS NULL or INS.INSLCODE NOT IN ('SLF','INS','BAN')) )
                AND UPPER(BLDG.BLDGID) NOT LIKE 'ROVER%'
            )
            OR
            ( /* Owned BLDG (self insure) but has limit, deductable or carrier present) */
                (INS.CARRIER IS NOT NULL or INS.INSLLIMI != 0 or INS.DEDUCT != 0)
                AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
                AND INS.INSLCODE = 'SLF'
            )
    )
    ORDER BY ISSUE, BLDGID
    """
    
    try:
        df = pd.read_sql(query, conn)
        
        # Check if we have data
        status, row_count, message = check_dataframe_rows(df, 1, "MRI INSURANCE Exceptions")
        
        if status:
            # Define column widths
            col_widths = {
                "ISSUE": 47,
                "CHG DATE": 11,
                "BLDGID": 8.5,
                "INS CODE": 7,
                "INS TYPE": 18,
                "LIMIT": 9,
                "INS START DATE": 12,
                "INS END DATE": 12,
                "INS LAST MODIFIED": 12
            }
            
            # Write to Excel
            success, result = write_excel(
                dirpath=REPORT_PATH,
                fname="MRI_Insurance_Exceptions.xlsx",
                sheet_name=query_date,
                df=df,
                colnames=True,
                colwidths=col_widths,
                writeover=True
            )
            
            if success:
                # Send email
                body_text = f"""
                The <b>MRI INSURANCE Exceptions</b> data is attached. 
                The attached file contains MRI INSURANCE Exceptions from yesterday's MRI data.
                <br/><br/>
                {get_signature(
                    name='Steve Olson',
                    title='Sr. Analytics Mgr.',
                    email='<EMAIL>',
                    phone='(*************'
                )}
                """
                
                email_client.send_email(
                    recipient=NORMAL_RECIPIENTS,
                    subject="MRI INSURANCE Exceptions",
                    body=body_text,
                    attachments=[result],
                    test=TESTING_EMAILS,
                    test_recipient=TEST_RECIPIENTS
                )
            else:
                # Send warning email about file save failure
                warning_body = f"""
                This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> 
                during the <b>MRI INSURANCE Exceptions</b> routine.<br/><br/>
                {result}
                <br/><br/>Either the path wasn't accessible or the file was open in another process.
                <br/><br/>The routine should continue without saving this file.<br/> <br/>
                <br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell
                """
                
                email_client.send_email(
                    recipient=WARNING_RECIPIENTS,
                    subject="MRI INSURANCE Exceptions : REPORT FILE SAVING ERROR",
                    body=warning_body
                )
    
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    main() 