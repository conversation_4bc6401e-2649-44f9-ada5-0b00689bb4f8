"""
Office 365 Email Reader Configuration
This file contains configuration settings and setup instructions for reading emails from Office 365
"""

import os
from typing import Dict, Any


class Office365Config:
    """Configuration class for Office 365 email reading"""
    
    # IMAP Configuration
    IMAP_SERVER = "outlook.office365.com"
    IMAP_PORT = 993
    
    # Graph API Configuration
    GRAPH_API_BASE_URL = "https://graph.microsoft.com/v1.0"
    GRAPH_TOKEN_URL = "https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    GRAPH_SCOPE = "https://graph.microsoft.com/.default"
    
    @classmethod
    def get_imap_config(cls) -> Dict[str, Any]:
        """Get IMAP configuration from environment variables"""
        config = {
            'server': os.environ.get('OFFICE365_IMAP_SERVER', cls.IMAP_SERVER),
            'port': int(os.environ.get('OFFICE365_IMAP_PORT', cls.IMAP_PORT)),
            'email': os.environ.get('OFFICE365_EMAIL_ADDRESS', ''),
            'password': os.environ.get('OFFICE365_EMAIL_PASSWORD', ''),
        }
        
        # Validate required fields
        missing_fields = [key for key, value in config.items() if not value and key in ['email', 'password']]
        if missing_fields:
            raise ValueError(f"Missing required IMAP configuration: {', '.join(missing_fields)}")
        
        return config
    
    @classmethod
    def get_graph_api_config(cls) -> Dict[str, Any]:
        """Get Graph API configuration from environment variables"""
        config = {
            'client_id': os.environ.get('OFFICE365_CLIENT_ID', ''),
            'client_secret': os.environ.get('OFFICE365_CLIENT_SECRET', ''),
            'tenant_id': os.environ.get('OFFICE365_TENANT_ID', ''),
            'email': os.environ.get('OFFICE365_EMAIL_ADDRESS', ''),
        }
        
        # Validate required fields
        missing_fields = [key for key, value in config.items() if not value]
        if missing_fields:
            raise ValueError(f"Missing required Graph API configuration: {', '.join(missing_fields)}")
        
        return config
    
    @classmethod
    def setup_environment_variables(cls, 
                                   email_address: str,
                                   app_password: str = None,
                                   client_id: str = None,
                                   client_secret: str = None,
                                   tenant_id: str = None):
        """
        Set up environment variables for Office 365 authentication
        
        Args:
            email_address: Your Office 365 email address
            app_password: App password for IMAP (if using IMAP)
            client_id: Azure app client ID (if using Graph API)
            client_secret: Azure app client secret (if using Graph API)
            tenant_id: Azure tenant ID (if using Graph API)
        """
        # Set common variables
        os.environ['OFFICE365_EMAIL_ADDRESS'] = email_address
        os.environ['OFFICE365_IMAP_SERVER'] = cls.IMAP_SERVER
        os.environ['OFFICE365_IMAP_PORT'] = str(cls.IMAP_PORT)
        
        # Set IMAP variables
        if app_password:
            os.environ['OFFICE365_EMAIL_PASSWORD'] = app_password
        
        # Set Graph API variables
        if client_id:
            os.environ['OFFICE365_CLIENT_ID'] = client_id
        if client_secret:
            os.environ['OFFICE365_CLIENT_SECRET'] = client_secret
        if tenant_id:
            os.environ['OFFICE365_TENANT_ID'] = tenant_id
    
    @classmethod
    def print_setup_instructions(cls):
        """Print setup instructions for Office 365 email reading"""
        print("""
=============================================================================
OFFICE 365 EMAIL READER SETUP INSTRUCTIONS
=============================================================================

There are two ways to connect to Office 365 for reading emails:

1. IMAP (Simpler setup)
2. Microsoft Graph API (More powerful, recommended for production)

-----------------------------------------------------------------------------
OPTION 1: IMAP SETUP
-----------------------------------------------------------------------------

1. Enable IMAP in your Office 365 account:
   - Go to Outlook.com or Office 365 web interface
   - Click Settings (gear icon) > View all Outlook settings
   - Go to Mail > Sync email
   - Enable IMAP access

2. Create an App Password:
   - Go to Microsoft Account Security settings
   - Select "Advanced security options"
   - Under "App passwords", select "Create a new app password"
   - Use this app password (not your regular password)

3. Set environment variables:
   export OFFICE365_EMAIL_ADDRESS="<EMAIL>"
   export OFFICE365_EMAIL_PASSWORD="your_app_password"
   export OFFICE365_IMAP_SERVER="outlook.office365.com"
   export OFFICE365_IMAP_PORT="993"

-----------------------------------------------------------------------------
OPTION 2: MICROSOFT GRAPH API SETUP (Recommended)
-----------------------------------------------------------------------------

1. Register an application in Azure Portal:
   - Go to https://portal.azure.com
   - Navigate to Azure Active Directory > App registrations
   - Click "New registration"
   - Enter a name for your app
   - Select "Accounts in this organizational directory only"
   - Click "Register"

2. Configure API permissions:
   - In your app registration, go to "API permissions"
   - Click "Add a permission"
   - Select "Microsoft Graph"
   - Choose "Application permissions"
   - Add these permissions:
     * Mail.Read (Read mail in all mailboxes)
     * Mail.ReadWrite (Read and write mail in all mailboxes)
   - Click "Grant admin consent for [your organization]"

3. Create a client secret:
   - In your app registration, go to "Certificates & secrets"
   - Click "New client secret"
   - Enter a description and expiration period
   - Copy the secret value (you won't see it again!)

4. Get your tenant ID:
   - In your app registration, go to "Overview"
   - Copy the "Directory (tenant) ID"

5. Set environment variables:
   export OFFICE365_EMAIL_ADDRESS="<EMAIL>"
   export OFFICE365_CLIENT_ID="your_client_id"
   export OFFICE365_CLIENT_SECRET="your_client_secret"
   export OFFICE365_TENANT_ID="your_tenant_id"

-----------------------------------------------------------------------------
TESTING THE SETUP
-----------------------------------------------------------------------------

Run the example script to test your configuration:
   python office365_email_example.py

-----------------------------------------------------------------------------
TROUBLESHOOTING
-----------------------------------------------------------------------------

IMAP Issues:
- Make sure IMAP is enabled in your Office 365 account
- Use app password, not regular password
- Check if your organization allows IMAP access

Graph API Issues:
- Make sure you've granted admin consent for API permissions
- Check that your client secret hasn't expired
- Verify your tenant ID is correct
- Make sure your app has the correct permissions

Common Error Messages:
- "Authentication failed": Check your credentials
- "Connection refused": Check server settings and ports
- "Permission denied": Check API permissions in Azure Portal

=============================================================================
""")


def quick_setup_imap(email_address: str, app_password: str):
    """Quick setup for IMAP authentication"""
    Office365Config.setup_environment_variables(
        email_address=email_address,
        app_password=app_password
    )
    print(f"IMAP configuration set for {email_address}")


def quick_setup_graph_api(email_address: str, client_id: str, client_secret: str, tenant_id: str):
    """Quick setup for Microsoft Graph API authentication"""
    Office365Config.setup_environment_variables(
        email_address=email_address,
        client_id=client_id,
        client_secret=client_secret,
        tenant_id=tenant_id
    )
    print(f"Graph API configuration set for {email_address}")


if __name__ == "__main__":
    # Print setup instructions
    Office365Config.print_setup_instructions()
    
    # Example usage (uncomment and modify as needed)
    
    # For IMAP setup:
    # quick_setup_imap("<EMAIL>", "your_app_password")
    
    # For Graph API setup:
    # quick_setup_graph_api(
    #     "<EMAIL>",
    #     "your_client_id", 
    #     "your_client_secret",
    #     "your_tenant_id"
    # ) 