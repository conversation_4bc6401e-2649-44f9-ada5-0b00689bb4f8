"""
Process Name: Marcos Weekly Banking Import Transactions SF Loader
R Script Name: Weekly_Banking-Import_Transactions-SF LOAD.R
Description: This script is designed to load weekly banking transactions into Salesforce. 
            It processes the data from Google Sheets, transforms it, and uploads to Snowflake.
Author: <PERSON>    
Date: 2025-6-13
"""
import os
import pandas as pd
from typing import List, Dict, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from libs.snowflake_helper import SnowflakeHelper
#from libs.excel_helper import GoogleSheetsHelper
from libs.excel_helper import SharePointExcelOnline
from libs.sharepoint_helper import SharePointClient
import uuid
import time

@dataclass
class ProcessingResult:
    """Data class to hold processing results for each location"""
    location: str
    bank: str
    b_id_local: str
    acct_start_date: str
    acct_end_date: str
    identifier: str
    filename: str
    sheetname: str
    gsht_link: str
    data_issue: Optional[str] = None
    trans_rejected: int = 0
    trans_processed: int = 0
    delete_failed: bool = False
    trans_loaded: Optional[int] = None

class WeeklyBankingImportTransactionsSFLoader:
    """
    Class to handle the loading of weekly banking transactions into Snowflake.
    """

    PROCESS_NAME = 'Marcos Weekly-Banking Import Transactions Loader'
    PROCESS_TYPE = 'Insert'
    LOG_TO_DB = True

    R_SCRIPT_NAME = 'Weekly_Banking-Import_Transactions-SF LOAD.R'
    
    LOG_SCHEMA = 'BATCH_AUDIT'
    LOG_TABLE = 'MOMS_EXECUTION_LOGS'
    DRY_RUN = False  # Set to True for testing without actual database operations
    TEST_MODE = True  # Set to True to override date to 6/18/2025 for testing with existing SharePoint files

    SHAREPOINT_ACCOUNTING_URL = 'https://highlandventuresltd442.sharepoint.com/sites/finance'
    SHAREPOINT_ACCOUNTING_WEEKLY_BANK_FOLDER = 'Documents/Marcos Bank Transactions/Weekly Marcos Bank Transactions'

    ENVM = os.environ['DATABASE_ENVM']
    DATABASE_CSM_DATABASE = os.environ['DATABASE_CSM_DATABASE']
    DATABASE_CORPORATE_SCHEMA = os.environ['DATABASE_CORPORATE_SCHEMA']
    DATABASE_WAREHOUSE = os.environ['DATABASE_WAREHOUSE']
    DATABASE_ROLE = os.environ['DATABASE_ROLE']
    DATABASE_USER = os.environ['DATABASE_USER']
    DATABASE_PASSWORD = os.environ['DATABASE_PASSWORD']
    
    TRANSACTIONS_TABLE = 'MP_BANK_TRANS_CLONE' if TEST_MODE else 'MP_BANK_TRANS'
    BANK_TRANS_ID_SEQ = 'MP_BANK_TRANS_ID_SEQ'

    PRINT_STATEMENTS = True

    def __init__(self, testing_emails: bool = False):
        """
        Initialize the loader with required clients.
        
        Args:
            snowflake_client: SnowflakeHelper instance
            google_client: GoogleSheetsHelper instance
            testing_emails: Whether to send test emails only
        """
        self.sf = SnowflakeHelper()
        self.testing_emails = testing_emails

        self.sp = SharePointClient(suppress_false_errors=True)
        self.sp.authenticate()
        self.ex = SharePointExcelOnline(sharepoint_client=self.sp)


        self.cursor = self.sf.cs
        self.conn = self.sf.conn
        self.log_buffer = []
        # self.log_audit_in_db = self.sf.log_audit_in_db
        
        # Configuration
        self.warn_recipients = ["<EMAIL>", "<EMAIL>"]
        self.norm_recipients = ["<EMAIL>"]
        self.test_recipients = ["<EMAIL>"]
        
        # Date configuration
        self.setup_date_ranges()
        
        # Processing results storage
        self.processing_results: List[ProcessingResult] = []
        self.delete_failed_locations: List[Dict] = []
        self.load_failed_locations: List[Dict] = []
        
        # Setup logging
        # self.setup_logging()

    def setup_date_ranges(self):
        """Setup date ranges for processing"""
        # Override date for testing purposes
        if hasattr(self, 'TEST_MODE') and self.TEST_MODE:
            today = datetime(2025, 6, 18).date()  # June 18, 2025 for testing
            print(f"TEST_MODE: Overriding today's date to {today}")
        else:
            today = datetime.now().date()
            self.log_audit_in_db(log_msg=f"Using today's date: {today}")

        # Calculate week boundaries (Sunday to Saturday)
        days_since_sunday = today.weekday() + 1 if today.weekday() != 6 else 0
        week_start = today - timedelta(days=days_since_sunday)

        self.query_start_date = week_start - timedelta(days=11)  # 14 days back from week start - 3
        self.query_end_date = week_start + timedelta(days=2)     # Week start + 2 days

        # DEBUG: Log the calculated date ranges
        # print(f"DEBUG - Date calculation:")
        # print(f"  Today: {today} {'(TEST_MODE override)' if hasattr(self, 'TEST_MODE') and self.TEST_MODE else '(actual date)'}")
        # print(f"  Today weekday: {today.weekday()} (0=Monday, 6=Sunday)")
        # print(f"  Days since Sunday: {days_since_sunday}")
        # print(f"  Week start (Sunday): {week_start}")
        # print(f"  Query start date: {self.query_start_date}")
        # print(f"  Query end date: {self.query_end_date}")
        # print(f"  Expected filename format: {self.query_start_date.strftime('%Y%m%d')}-{self.query_end_date.strftime('%Y%m%d')} [BANK]")

        self.email_start = self.query_start_date.strftime("%m/%d/%Y")
        self.email_end = self.query_end_date.strftime("%m/%d/%Y")

        # Determine if this is a test run or actual load
        self.load_db = today.weekday() >= 3  # Thursday or later


    def get_expected_files_sheets(self) -> List[ProcessingResult]:
        """
        Query Snowflake to get expected files and sheets for processing
        
        Returns:
            List of ProcessingResult objects representing expected processing targets
        """
        query = f"""
            SELECT 
                L.LOC_NUM AS LOCATION,
                M.NAME_SHORT AS BANK,
                CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' 
                    THEN M.NAME_SHORT||'_'||L.LOC_NUM 
                    ELSE M.NAME_SHORT END AS SHEETNAME,
                NULL as GSHT_LINK,
                NULL as DATA_ISSUE,
                NULL as TRANS_PROCESSED,
                NULL as TRANS_REJECTED,
                CONCAT(
                    TO_CHAR(TO_DATE('{self.query_start_date.strftime("%Y-%m-%d")}','YYYY-MM-DD'), 'YYYYMMDD'),
                    '-',
                    TO_CHAR(TO_DATE('{self.query_end_date.strftime("%Y-%m-%d")}','YYYY-MM-DD'), 'YYYYMMDD'),
                    ' ',
                    M.NAME_SHORT
                ) AS FILENAME,
                L.B_ID_LOCAL,
                CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' 
                    THEN NULL 
                    ELSE COALESCE(L.IMPORT_ALT_ID, L.ACCOUNT) END AS IDENTIFIER,
                L.S_DATE AS ACCT_START_DATE,
                L.E_DATE AS ACCT_END_DATE,
                COALESCE(M.IMPORT_FUN,'NA') AS IMPORT_FUNCTION_CALL
            FROM {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.MP_BANK_ID_LOCAL L
            JOIN {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.MP_BANK_ID_MASTER M
            ON L.B_ID_MASTER = M.B_ID_MASTER
            WHERE 
                L.S_DATE <= TO_DATE('{self.query_end_date.strftime("%Y-%m-%d")}','YYYY-MM-DD')
                AND (L.E_DATE IS NULL OR L.E_DATE >= TO_DATE('{self.query_start_date.strftime("%Y-%m-%d")}','YYYY-MM-DD'))
            ORDER BY M.NAME_SHORT, L.LOC_NUM, L.B_ID_LOCAL;
        """

        try:
            # This returns List[Dict[str, Any]] from execute_snowflake_query
            query_results_df = self.sf.execute_snowflake_query(query=query, print_query=True, pull_only_one_record=False)
            #print(f"Query results: {query_results_df}")
            

            # Convert query results to ProcessingResult objects
            processing_results = []
            
            if query_results_df:  # Check if we have results
                for row in query_results_df:  # row is a dictionary
                    result = ProcessingResult(
                        location=str(row['LOCATION']),
                        bank=row['BANK'],
                        b_id_local=str(row['B_ID_LOCAL']),
                        acct_start_date=row['ACCT_START_DATE'],
                        acct_end_date=row['ACCT_END_DATE'],
                        identifier=row['IDENTIFIER'],
                        filename=row['FILENAME'],
                        sheetname=row['SHEETNAME'],
                        gsht_link=""
                    )
                    processing_results.append(result)
            
                print(f"Retrieved {len(query_results_df)} records from database")
                # Convert list of dictionaries to DataFrame for CSV export
                df_for_csv = pd.DataFrame(query_results_df)
                #df_for_csv.to_csv('expected_files_sheets.csv', index=True) # Save to CSV for debugging
                return df_for_csv
            else:
                print(f"No results returned")
                return pd.DataFrame()  # Return empty DataFrame if no results
        
        except Exception as e:
            self.log_audit_in_db(f"Error querying expected files: {e}")
            return pd.DataFrame()
    
    def process_bank_transactions(self, processing_results: pd.DataFrame) -> bool:
        """
        Process bank transactions from SharePoint Excel files efficiently using bulk operations
        
        Args:
            processing_results: DataFrame containing processing targets
            
        Returns:
            bool: True if processing succeeded, False otherwise
        """
        self.log_audit_in_db(
            log_msg='Starting efficient SharePoint Excel file processing',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        
        if processing_results.empty:
            self.log_audit_in_db(
                log_msg='No processing results provided - DataFrame is empty',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )
            return False
        
        # Initialize tracking columns
        processing_results['GSHT_LINK'] = ''
        processing_results['DATA_ISSUE'] = None
        processing_results['TRANS_PROCESSED'] = 0
        processing_results['TRANS_REJECTED'] = 0
        
        # Get unique filenames to minimize file operations
        unique_files = processing_results['FILENAME'].unique()
        all_transactions = []
        rejected_transactions = []
        
        self.log_audit_in_db(
            log_msg=f'Processing {len(unique_files)} unique files for {len(processing_results)} locations',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        
        # STEP 1: Batch check file existence (instead of individual checks)
        existing_files = self._batch_check_file_existence(unique_files)
        
        # STEP 2: Process files in bulk
        for filename in unique_files:
            if filename not in existing_files:
                # Mark all rows for this file as failed
                file_mask = processing_results['FILENAME'] == filename
                processing_results.loc[file_mask, 'GSHT_LINK'] = "File not found in SharePoint"
                processing_results.loc[file_mask, 'DATA_ISSUE'] = "File not found in SharePoint"
                continue
            
            try:
                # Get all locations for this file
                file_locations = processing_results[processing_results['FILENAME'] == filename]
                
                self.log_audit_in_db(
                    log_msg=f'Processing file: {filename} ({len(file_locations)} locations)',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
                
                # STEP 3: Single file operation to get worksheets and file info
                file_info, worksheet_names = self._get_file_info_and_worksheets(filename)
                sharepoint_url = file_info.get('web_url', 'SharePoint file')
                
                # STEP 4: Group by worksheet to minimize sheet reads
                worksheet_groups = file_locations.groupby('SHEETNAME')
                
                for worksheet_name, sheet_locations in worksheet_groups:
                    if worksheet_name not in worksheet_names:
                        # Mark all locations for this sheet as failed
                        for idx in sheet_locations.index:
                            processing_results.loc[idx, 'GSHT_LINK'] = sharepoint_url
                            processing_results.loc[idx, 'DATA_ISSUE'] = f"Sheet '{worksheet_name}' not found"
                        continue
                    
                    # STEP 5: Single read operation per worksheet
                    sheet_data = self._read_worksheet_once(filename, worksheet_name)
                    
                    if sheet_data is None or sheet_data.empty:
                        for idx in sheet_locations.index:
                            processing_results.loc[idx, 'GSHT_LINK'] = sharepoint_url
                            processing_results.loc[idx, 'DATA_ISSUE'] = "Sheet is empty or could not be read"
                        continue
                    
                    # STEP 6: Process all locations for this sheet in batch
                    sheet_transactions, sheet_rejected = self._process_worksheet_batch(
                        sheet_data, sheet_locations, sharepoint_url, processing_results
                    )
                    
                    all_transactions.extend(sheet_transactions)
                    rejected_transactions.extend(sheet_rejected)
            
            except Exception as e:
                self.log_audit_in_db(
                    log_msg=f'Error processing file {filename}: {e}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Error'
                )
                file_mask = processing_results['FILENAME'] == filename
                processing_results.loc[file_mask, 'DATA_ISSUE'] = f"File processing error: {str(e)}"
        
        # Store results DataFrame
        self.processing_results_df = processing_results
        
        # Summary logging
        total_transactions = len(all_transactions)
        total_rejected = len(rejected_transactions)
        
        self.log_audit_in_db(
            log_msg=f'Transaction processing completed: {total_transactions} valid, {total_rejected} rejected',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        # print(f"All transactions object: {all_transactions}")
        if not all_transactions:
            self.log_audit_in_db(
                log_msg='No transactions found for processing',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )
            return False
        
        #! Remove #TODO testing
        if self.TEST_MODE:
            self.log_audit_in_db(
                log_msg='Starting database load process',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            return self.load_transactions_to_database(all_transactions)
        
        if not self.TEST_MODE:
            self.log_audit_in_db(
                log_msg='Starting database load process',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            return self.load_transactions_to_database(all_transactions)

        if self.DRY_RUN:
            self.log_audit_in_db(
                log_msg='Dry Run - skipping database load',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
        
        return True

    def _batch_check_file_existence(self, filenames: List[str]) -> Set[str]:
        """
        Check existence of multiple files at once using SharePoint folder listing

        Args:
            filenames: List of filenames to check

        Returns:
            Set of existing filenames
        """
        try:
            self.log_audit_in_db(
                log_msg=f'Batch checking existence of {len(filenames)} files',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            existing_files = set()

            # Get folder contents using existing SharePoint helper method
            try:
                folder_info = self.sp.get_folder(
                    site_url=self.SHAREPOINT_ACCOUNTING_URL,
                    folder_path=self.SHAREPOINT_ACCOUNTING_WEEKLY_BANK_FOLDER
                )

                if folder_info and 'items' in folder_info:
                    # Extract file names from folder items
                    folder_files = folder_info['items']
                    all_file_names = {item.get('name', '') for item in folder_files if 'file' in item}

                    # DEBUG: Log actual files found vs expected
                    self.log_audit_in_db(
                        log_msg=f'DEBUG - Files in SharePoint folder: {sorted(list(all_file_names))}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )

                    self.log_audit_in_db(
                        log_msg=f'DEBUG - Expected files from database: {sorted(list(filenames))}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )

                    # Check which of our target files exist
                    for filename in filenames:
                        # First try exact match
                        if filename in all_file_names:
                            existing_files.add(filename)
                        else:
                            # Try matching with .xlsx extension added
                            filename_with_ext = f"{filename}.xlsx"
                            if filename_with_ext in all_file_names:
                                existing_files.add(filename)
                                self.log_audit_in_db(
                                    log_msg=f'Found file with extension: "{filename}" -> "{filename_with_ext}"',
                                    process_type=self.PROCESS_TYPE,
                                    print_msg=self.PRINT_STATEMENTS,
                                    script_file_name=self.PROCESS_NAME
                                )
                            else:
                                # Try matching SharePoint files without extension to database filename
                                for actual_file in all_file_names:
                                    # Remove extension from SharePoint filename for comparison
                                    actual_file_no_ext = actual_file.rsplit('.', 1)[0] if '.' in actual_file else actual_file
                                    if filename.lower() == actual_file_no_ext.lower():
                                        existing_files.add(filename)
                                        self.log_audit_in_db(
                                            log_msg=f'Found file by removing extension: "{filename}" matches "{actual_file}"',
                                            process_type=self.PROCESS_TYPE,
                                            print_msg=self.PRINT_STATEMENTS,
                                            script_file_name=self.PROCESS_NAME
                                        )
                                        break

                    self.log_audit_in_db(
                        log_msg=f'Found {len(all_file_names)} total files in folder, {len(existing_files)} match our targets',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )
                else:
                    self.log_audit_in_db(
                        log_msg='Could not access SharePoint folder for batch file check',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Warning'
                    )

            except Exception as folder_error:
                self.log_audit_in_db(
                    log_msg=f'Folder access failed, falling back to individual file checks: {folder_error}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )

                # Fallback to individual file checks
                for filename in filenames:
                    try:
                        file_info = self.sp.get_file(
                            site_url=self.SHAREPOINT_ACCOUNTING_URL,
                            file_path=filename,
                            folder_name=self.SHAREPOINT_ACCOUNTING_WEEKLY_BANK_FOLDER
                        )
                        if file_info:
                            existing_files.add(filename)
                    except Exception:
                        # File doesn't exist or can't be accessed
                        continue

            self.log_audit_in_db(
                log_msg=f'Found {len(existing_files)} existing files out of {len(filenames)}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return existing_files

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error in batch file existence check: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return set()

    def _get_file_info_and_worksheets(self, filename: str) -> Tuple[Dict, List[str]]:
        """
        Get file info and worksheet names in single operation using existing SharePoint methods

        Args:
            filename: Name of the file

        Returns:
            Tuple of (file_info dict, worksheet_names list)
        """
        try:
            # Use the same folder listing approach that worked in batch check
            # Get folder contents and find the file
            folder_info = self.sp.get_folder(
                site_url=self.SHAREPOINT_ACCOUNTING_URL,
                folder_path=self.SHAREPOINT_ACCOUNTING_WEEKLY_BANK_FOLDER
            )

            file_info = None
            actual_filename = filename

            if folder_info and 'items' in folder_info:
                folder_files = folder_info['items']

                # Look for exact match first
                for item in folder_files:
                    if 'file' in item and item.get('name', '') == filename:
                        file_info = item
                        actual_filename = filename
                        break

                # If not found, try with .xlsx extension
                if not file_info and not filename.endswith('.xlsx'):
                    filename_with_ext = f"{filename}.xlsx"
                    for item in folder_files:
                        if 'file' in item and item.get('name', '') == filename_with_ext:
                            file_info = item
                            actual_filename = filename_with_ext
                            self.log_audit_in_db(
                                log_msg=f'Found file with extension: {filename} -> {filename_with_ext}',
                                process_type=self.PROCESS_TYPE,
                                print_msg=self.PRINT_STATEMENTS,
                                script_file_name=self.PROCESS_NAME
                            )
                            break

                # If still not found, try removing extension from SharePoint files
                if not file_info:
                    for item in folder_files:
                        if 'file' in item:
                            sp_filename = item.get('name', '')
                            sp_filename_no_ext = sp_filename.rsplit('.', 1)[0] if '.' in sp_filename else sp_filename
                            if filename.lower() == sp_filename_no_ext.lower():
                                file_info = item
                                actual_filename = sp_filename
                                self.log_audit_in_db(
                                    log_msg=f'Found file by name matching: {filename} -> {sp_filename}',
                                    process_type=self.PROCESS_TYPE,
                                    print_msg=self.PRINT_STATEMENTS,
                                    script_file_name=self.PROCESS_NAME
                                )
                                break

            if not file_info:
                self.log_audit_in_db(
                    log_msg=f'File {filename} not found in SharePoint',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return {}, []

            # Get worksheet names using Excel helper
            try:
                if file_info and 'id' in file_info:
                    # Use file ID from folder listing to create Excel session directly
                    file_id = file_info['id']

                    # Create Excel session using file ID
                    excel_session = self.ex.get_excel_file_by_id(
                        site_url=self.SHAREPOINT_ACCOUNTING_URL,
                        file_id=file_id
                    )

                    if excel_session:
                        worksheets = self.ex.list_worksheets(excel_session)
                        worksheet_names = [ws.get('name', '') for ws in worksheets]

                        self.log_audit_in_db(
                            log_msg=f'Successfully accessed Excel file {actual_filename} using file ID',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME
                        )
                    else:
                        worksheet_names = []
                        self.log_audit_in_db(
                            log_msg=f'Could not create Excel session for {actual_filename} using file ID',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Warning'
                        )
                else:
                    worksheet_names = []
                    self.log_audit_in_db(
                        log_msg=f'No file ID available for {filename}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Warning'
                    )

            except Exception as ws_error:
                self.log_audit_in_db(
                    log_msg=f'Error getting worksheets for {filename}: {ws_error}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                worksheet_names = []

            # Add web URL to file info for easy access
            if file_info and 'webUrl' in file_info:
                file_info['web_url'] = file_info['webUrl']
            elif file_info:
                file_info['web_url'] = f"{self.SHAREPOINT_ACCOUNTING_URL}/_layouts/15/Doc.aspx?sourcedoc={file_info.get('id', '')}"

            self.log_audit_in_db(
                log_msg=f'File {filename}: {len(worksheet_names)} worksheets found',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return file_info, worksheet_names

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error getting file info for {filename}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return {}, []

    def _read_worksheet_once(self, filename: str, worksheet_name: str) -> Optional[pd.DataFrame]:
        """
        Read worksheet data once and cache it using existing SharePoint Excel methods

        Args:
            filename: Name of the file
            worksheet_name: Name of the worksheet

        Returns:
            DataFrame with worksheet data or None
        """
        try:
            # Check if we have a cache for this session
            if not hasattr(self, '_worksheet_cache'):
                self._worksheet_cache = {}

            cache_key = f"{filename}::{worksheet_name}"

            if cache_key not in self._worksheet_cache:
                self.log_audit_in_db(
                    log_msg=f'Reading worksheet: {worksheet_name} from {filename}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Use existing SharePoint Excel helper methods
                try:
                    # Use the same folder listing approach to find the file and get its ID
                    folder_info = self.sp.get_folder(
                        site_url=self.SHAREPOINT_ACCOUNTING_URL,
                        folder_path=self.SHAREPOINT_ACCOUNTING_WEEKLY_BANK_FOLDER
                    )

                    file_item = None
                    actual_filename = filename

                    if folder_info and 'items' in folder_info:
                        folder_files = folder_info['items']

                        # Look for exact match first
                        for item in folder_files:
                            if 'file' in item and item.get('name', '') == filename:
                                file_item = item
                                actual_filename = filename
                                break

                        # If not found, try with .xlsx extension
                        if not file_item and not filename.endswith('.xlsx'):
                            filename_with_ext = f"{filename}.xlsx"
                            for item in folder_files:
                                if 'file' in item and item.get('name', '') == filename_with_ext:
                                    file_item = item
                                    actual_filename = filename_with_ext
                                    break

                        # If still not found, try name matching without extension
                        if not file_item:
                            for item in folder_files:
                                if 'file' in item:
                                    sp_filename = item.get('name', '')
                                    sp_filename_no_ext = sp_filename.rsplit('.', 1)[0] if '.' in sp_filename else sp_filename
                                    if filename.lower() == sp_filename_no_ext.lower():
                                        file_item = item
                                        actual_filename = sp_filename
                                        break

                    # Get Excel session using file ID if we found the file
                    excel_session = None
                    if file_item and 'id' in file_item:
                        file_id = file_item['id']
                        excel_session = self.ex.get_excel_file_by_id(
                            site_url=self.SHAREPOINT_ACCOUNTING_URL,
                            file_id=file_id
                        )
                    else:
                        self.log_audit_in_db(
                            log_msg=f'Could not find file {filename} in folder listing for worksheet reading',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Warning'
                        )

                    if not excel_session:
                        self.log_audit_in_db(
                            log_msg=f'Could not create Excel session for {filename}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Warning'
                        )
                        self._worksheet_cache[cache_key] = None
                        return None

                    # Get worksheet data
                    worksheet_data = self.ex.get_worksheet_data(excel_session, worksheet_name)

                    if worksheet_data and 'values' in worksheet_data:
                        values = worksheet_data['values']

                        # Convert to DataFrame with smart header detection
                        if len(values) > 1:
                            headers = values[0]
                            data_rows = values[1:]

                            # Check if first row looks like headers (strings) or data (numbers)
                            if self._looks_like_headers(headers):
                                sheet_df = pd.DataFrame(data_rows, columns=headers)
                                print(f"Headers supposedly found: {headers}")
                            else:
                                # First row is data, create generic column names
                                num_cols = len(headers) if headers else 0
                                if num_cols > 0:
                                    generic_headers = [f'Column_{i+1}' for i in range(num_cols)]
                                    sheet_df = pd.DataFrame(values, columns=generic_headers)
                                    print(f"Headers not found, Generic headers created: {generic_headers}")
                                    self.log_audit_in_db(
                                        log_msg=f'No headers detected, using generic column names: {generic_headers}',
                                        process_type=self.PROCESS_TYPE,
                                        print_msg=self.PRINT_STATEMENTS,
                                        script_file_name=self.PROCESS_NAME
                                    )
                                else:
                                    sheet_df = pd.DataFrame()
                            #sheet_df.to_csv(f'worksheet_{worksheet_name}.csv', index=True)
                        elif len(values) == 1:
                            # Only one row - check if it's headers or data
                            if self._looks_like_headers(values[0]):
                                sheet_df = pd.DataFrame(columns=values[0])
                            else:
                                # Single row of data, create generic columns
                                num_cols = len(values[0]) if values[0] else 0
                                generic_headers = [f'Column_{i+1}' for i in range(num_cols)]
                                sheet_df = pd.DataFrame([values[0]], columns=generic_headers)
                        else:
                            # No data at all
                            sheet_df = pd.DataFrame()

                        self._worksheet_cache[cache_key] = sheet_df

                        # Log column information for debugging
                        self.log_audit_in_db(
                            log_msg=f'Successfully read {len(sheet_df)} rows from worksheet {worksheet_name}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME
                        )

                        self.log_audit_in_db(
                            log_msg=f'Worksheet columns: {list(sheet_df.columns)}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME
                        )
                    else:
                        self.log_audit_in_db(
                            log_msg=f'No data found in worksheet {worksheet_name}',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME,
                            log_type='Warning'
                        )
                        self._worksheet_cache[cache_key] = None

                except Exception as read_error:
                    self.log_audit_in_db(
                        log_msg=f'Error reading worksheet data: {read_error}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )
                    self._worksheet_cache[cache_key] = None

            return self._worksheet_cache[cache_key]

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error reading worksheet {worksheet_name} from {filename}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return None

    def _looks_like_headers(self, row: List) -> bool:
        """
        Determine if a row looks like column headers or data

        Args:
            row: List of values from the first row

        Returns:
            True if row looks like headers, False if it looks like data
        """
        if not row:
            return False

        # Count how many values look like strings vs numbers
        string_count = 0
        number_count = 0

        for value in row:
            if value is None or value == '':
                continue

            # Try to convert to number
            try:
                float(value)
                number_count += 1
            except (ValueError, TypeError):
                # Not a number, likely a string
                string_count += 1

        # If more than half are strings, likely headers
        # Also check for common header patterns
        if string_count > number_count:
            return True

        # Check for common header keywords
        header_keywords = ['date', 'description', 'amount', 'balance', 'memo', 'transaction', 'debit', 'credit']
        for value in row:
            if isinstance(value, str):
                value_lower = value.lower()
                if any(keyword in value_lower for keyword in header_keywords):
                    return True

        # If mostly numbers, likely data
        return False

    def _infer_columns_from_data(self, sheet_data: pd.DataFrame) -> Dict[str, str]:
        """
        Infer column types based on data patterns when no standard headers are found

        Args:
            sheet_data: DataFrame with data to analyze

        Returns:
            Dictionary mapping standard names to actual column names
        """
        found_columns = {}

        if sheet_data.empty:
            return found_columns

        # Analyze each column to infer its type
        for col_idx, col_name in enumerate(sheet_data.columns):
            col_data = sheet_data[col_name].dropna()

            if col_data.empty:
                continue

            # Check for date patterns
            date_count = 0
            number_count = 0
            string_count = 0

            for value in col_data.head(10):  # Check first 10 non-null values
                if pd.isna(value):
                    continue

                # Try to parse as date
                try:
                    pd.to_datetime(value)
                    date_count += 1
                    continue
                except:
                    pass

                # Try to parse as number
                try:
                    float(value)
                    number_count += 1
                    continue
                except:
                    pass

                # Must be string
                string_count += 1

            # Assign column type based on patterns
            total_values = date_count + number_count + string_count
            if total_values == 0:
                continue

            # If mostly dates, this is likely the date column
            if date_count / total_values > 0.7 and 'DATE' not in found_columns:
                found_columns['DATE'] = col_name
            # If mostly numbers and we don't have amount yet, this could be amount
            elif number_count / total_values > 0.5 and 'AMOUNT' not in found_columns:  # Lowered threshold
                # Check if this looks like transaction amounts (has positive/negative values)
                numeric_values = []
                for value in col_data.head(10):
                    try:
                        numeric_values.append(float(value))
                    except:
                        continue

                if numeric_values:
                    # Check for mix of positive/negative (typical for transaction amounts)
                    has_positive = any(v > 0 for v in numeric_values)
                    has_negative = any(v < 0 for v in numeric_values)

                    # If we have both positive and negative, or if no amount column found yet
                    if (has_positive and has_negative) or 'AMOUNT' not in found_columns:
                        found_columns['AMOUNT'] = col_name
            # If mostly strings and we don't have description yet, this could be description
            elif string_count / total_values > 0.5 and 'DESCRIPTION' not in found_columns:  # Lowered threshold
                found_columns['DESCRIPTION'] = col_name

        # If we have multiple numeric columns, try to distinguish amount vs balance
        numeric_cols = []
        for col in sheet_data.columns:
            if col not in found_columns.values():
                # Check if this column is mostly numeric
                try:
                    numeric_values = pd.to_numeric(sheet_data[col], errors='coerce').dropna()
                    if len(numeric_values) / len(sheet_data) > 0.5:  # At least 50% numeric
                        numeric_cols.append(col)
                except:
                    continue

        # If we don't have an AMOUNT column yet, try to find one from numeric columns
        if 'AMOUNT' not in found_columns and numeric_cols:
            for col in numeric_cols:
                try:
                    col_values = pd.to_numeric(sheet_data[col], errors='coerce').dropna()
                    if len(col_values) > 0:
                        # Check for transaction-like patterns (mix of positive/negative)
                        has_positive = any(v > 0 for v in col_values.head(10))
                        has_negative = any(v < 0 for v in col_values.head(10))

                        # Prefer columns with both positive and negative values
                        if has_positive and has_negative:
                            found_columns['AMOUNT'] = col
                            break
                        # If no mixed column found, take the first numeric column
                        elif 'AMOUNT' not in found_columns:
                            found_columns['AMOUNT'] = col
                except:
                    continue

        # Look for balance column among remaining numeric columns
        if 'BALANCE' not in found_columns:
            remaining_numeric = [col for col in numeric_cols if col not in found_columns.values()]
            if remaining_numeric:
                # Take the first remaining numeric column as balance
                found_columns['BALANCE'] = remaining_numeric[0]

        return found_columns

    def _process_worksheet_batch(self, sheet_data: pd.DataFrame, sheet_locations: pd.DataFrame, 
                            sharepoint_url: str, processing_results: pd.DataFrame) -> Tuple[List[Dict], List[Dict]]:
        """
        Process all locations for a single worksheet in batch
        
        Args:
            sheet_data: DataFrame with worksheet data
            sheet_locations: DataFrame with locations for this sheet
            sharepoint_url: SharePoint URL for the file
            processing_results: Main results DataFrame to update
            
        Returns:
            Tuple of (all_transactions, rejected_transactions)
        """
        try:
            all_transactions = []
            rejected_transactions = []
            
            self.log_audit_in_db(
                log_msg=f'Batch processing {len(sheet_locations)} locations for worksheet',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            
            # Group locations by bank type for efficient parsing
            bank_groups = sheet_locations.groupby('BANK')
            
            for bank_name, bank_locations in bank_groups:
                try:
                    # Process all locations for this bank type together
                    bank_transactions = self._process_bank_locations_batch(
                        sheet_data, bank_locations, bank_name
                    )
                    
                    if bank_transactions is not None and not bank_transactions.empty:
                        # Filter by date range
                        valid_transactions = self.filter_transactions_by_date(bank_transactions)
                        
                        # Update processing results for all locations in this bank group
                        for idx in bank_locations.index:
                            location = bank_locations.loc[idx, 'LOCATION']
                            location_transactions = valid_transactions[
                                valid_transactions['LOCATION'] == location
                            ]
                            
                            processing_results.loc[idx, 'GSHT_LINK'] = sharepoint_url
                            processing_results.loc[idx, 'TRANS_PROCESSED'] = len(location_transactions)
                            processing_results.loc[idx, 'TRANS_REJECTED'] = 0  # Calculate if needed
                            
                            # Convert to dict format for database loading
                            location_dicts = location_transactions.to_dict('records')
                            all_transactions.extend(location_dicts)
                        
                        self.log_audit_in_db(
                            log_msg=f'Bank {bank_name}: processed {len(valid_transactions)} transactions for {len(bank_locations)} locations',
                            process_type=self.PROCESS_TYPE,
                            print_msg=self.PRINT_STATEMENTS,
                            script_file_name=self.PROCESS_NAME
                        )
                    else:
                        # Mark all locations in this bank group as having no transactions
                        for idx in bank_locations.index:
                            processing_results.loc[idx, 'GSHT_LINK'] = sharepoint_url
                            processing_results.loc[idx, 'TRANS_PROCESSED'] = 0
                            processing_results.loc[idx, 'TRANS_REJECTED'] = 0
                            
                except Exception as e:
                    self.log_audit_in_db(
                        log_msg=f'Error processing bank group {bank_name}: {e}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME,
                        log_type='Error'
                    )
                    # Mark all locations in this bank group as failed
                    for idx in bank_locations.index:
                        processing_results.loc[idx, 'GSHT_LINK'] = sharepoint_url
                        processing_results.loc[idx, 'DATA_ISSUE'] = f"Bank processing error: {str(e)}"
            
            return all_transactions, rejected_transactions
            
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error in worksheet batch processing: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return [], []

    def _process_bank_locations_batch(self, sheet_data: pd.DataFrame, bank_locations: pd.DataFrame, 
                                    bank_name: str) -> Optional[pd.DataFrame]:
        """
        Process multiple locations for the same bank type in batch
        
        Args:
            sheet_data: Raw worksheet data
            bank_locations: Locations for this bank
            bank_name: Name of the bank
            
        Returns:
            DataFrame with processed transactions for all locations
        """
        try:
            # Create a copy of sheet data for processing
            processed_data = sheet_data.copy()
            
            # Add metadata for all locations at once
            all_locations = []
            all_b_id_locals = []
            
            for _, location_row in bank_locations.iterrows():
                location_count = len(processed_data)
                all_locations.extend([location_row['LOCATION']] * location_count)
                all_b_id_locals.extend([location_row['B_ID_LOCAL']] * location_count)
            
            # Create a combined dataset for all locations
            bank_data_list = []
            for _, location_row in bank_locations.iterrows():
                location_data = processed_data.copy()
                location_data['LOCATION'] = location_row['LOCATION']
                location_data['B_ID_LOCAL'] = location_row['B_ID_LOCAL']
                location_data['BANK'] = bank_name
                bank_data_list.append(location_data)
            
            # Combine all location data
            combined_data = pd.concat(bank_data_list, ignore_index=True)
            
            # Use bank-specific parser
            bank_name_upper = bank_name.upper()
            
            if bank_name_upper == 'ASSOCIATED':
                return self._parse_associated_bank_batch(combined_data)
            elif bank_name_upper == 'FIRST_NATIONAL':
                return self._parse_first_national_batch(combined_data)
            elif bank_name_upper == 'GREAT_WESTERN':
                return self._parse_great_western_batch(combined_data)
            elif bank_name_upper == 'PINNACLE':
                return self._parse_pinnacle_batch(combined_data)
            else:
                return self._parse_generic_bank_batch(combined_data)
                
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error in bank batch processing for {bank_name}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return None

    def _parse_generic_bank_batch(self, combined_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Generic batch parser for bank transactions - uses flexible column mapping

        Args:
            combined_data: Combined data for all locations

        Returns:
            Processed transactions DataFrame
        """
        try:
            # Log available columns for debugging
            self.log_audit_in_db(
                log_msg=f'Batch processing columns: {list(combined_data.columns)}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Use the same flexible column mapping as individual processing
            column_patterns = {
                'DATE': ['DATE', 'TRANSACTION_DATE', 'TRANS_DATE', 'POST_DATE', 'POSTING_DATE', 'EFFECTIVE_DATE'],
                'DESCRIPTION': ['DESCRIPTION', 'DESC', 'MEMO', 'TRANSACTION_DESCRIPTION', 'DETAILS', 'REFERENCE'],
                'AMOUNT': ['AMOUNT', 'TRANSACTION_AMOUNT', 'DEBIT', 'CREDIT', 'NET_AMOUNT'],
                'BALANCE': ['BALANCE', 'RUNNING_BALANCE', 'ACCOUNT_BALANCE', 'ENDING_BALANCE']
            }

            # Find actual column mappings (case-insensitive)
            sheet_columns_upper = [str(col).upper() for col in combined_data.columns]
            found_columns = {}

            for standard_name, possible_names in column_patterns.items():
                for possible_name in possible_names:
                    for i, sheet_col_upper in enumerate(sheet_columns_upper):
                        if possible_name in sheet_col_upper or sheet_col_upper in possible_name:
                            found_columns[standard_name] = combined_data.columns[i]
                            break
                    if standard_name in found_columns:
                        break

            # If no standard columns found, try to infer from data patterns
            if not found_columns:
                self.log_audit_in_db(
                    log_msg=f'No standard column names found in batch, attempting data pattern analysis',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Try to infer columns based on data patterns
                # Only analyze original worksheet columns, not the added metadata columns
                original_columns = [col for col in combined_data.columns
                                  if col not in ['LOCATION', 'B_ID_LOCAL', 'BANK']]
                if original_columns:
                    original_data = combined_data[original_columns]
                    found_columns = self._infer_columns_from_data(original_data)
                else:
                    found_columns = {}

            # Log what we found
            self.log_audit_in_db(
                log_msg=f'Batch column mapping found: {found_columns}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Check if we have at least date and amount columns
            if 'DATE' not in found_columns or 'AMOUNT' not in found_columns:
                self.log_audit_in_db(
                    log_msg=f'Could not find required DATE and AMOUNT columns in batch processing',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return None

            # Create mapping from found columns to standard names
            actual_mapping = {}
            for standard_name, actual_col in found_columns.items():
                if standard_name == 'DATE':
                    actual_mapping[actual_col] = 'BANK_DATE'
                elif standard_name == 'DESCRIPTION':
                    actual_mapping[actual_col] = 'BANK_DESCRIPTION'
                elif standard_name == 'AMOUNT':
                    actual_mapping[actual_col] = 'BANK_AMOUNT'
                elif standard_name == 'BALANCE':
                    actual_mapping[actual_col] = 'BANK_BALANCE'

            # Rename columns to standard names
            transactions = combined_data.rename(columns=actual_mapping)

            self.log_audit_in_db(
                log_msg=f'Applied batch column mapping: {actual_mapping}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            
            # Batch convert data types
            if 'BANK_DATE' in transactions.columns:
                # Handle Excel date serial numbers and various date formats
                def convert_excel_date(date_value):
                    if pd.isna(date_value):
                        return pd.NaT

                    # If it's already a datetime, return as-is
                    if isinstance(date_value, (pd.Timestamp, datetime)):
                        return date_value

                    # If it's a number, treat as Excel date serial number
                    if isinstance(date_value, (int, float)):
                        try:
                            # Excel date serial number (days since 1900-01-01, with some quirks)
                            # Convert to pandas datetime
                            excel_epoch = pd.Timestamp('1899-12-30')  # Excel's epoch (accounting for leap year bug)
                            return excel_epoch + pd.Timedelta(days=date_value)
                        except:
                            return pd.NaT

                    # If it's a string, try normal datetime parsing
                    try:
                        return pd.to_datetime(date_value)
                    except:
                        return pd.NaT

                transactions['BANK_DATE'] = transactions['BANK_DATE'].apply(convert_excel_date)

                # Log date conversion results
                valid_dates = transactions['BANK_DATE'].notna().sum()
                total_dates = len(transactions)
                self.log_audit_in_db(
                    log_msg=f'Date conversion: {valid_dates}/{total_dates} valid dates after Excel serial number conversion',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
            
            if 'BANK_AMOUNT' in transactions.columns:
                transactions['BANK_AMOUNT'] = pd.to_numeric(transactions['BANK_AMOUNT'], errors='coerce')
            
            # Remove rows with invalid data
            transactions = transactions.dropna(subset=['BANK_DATE', 'BANK_AMOUNT'])
            
            self.log_audit_in_db(
                log_msg=f'Generic batch parser processed {len(transactions)} transactions',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Export CSV after date processing for debugging
            # if not transactions.empty:
            #     csv_filename = f'processed_transactions_after_date_conversion.csv'
            #     transactions.to_csv(csv_filename, index=True)
            #     self.log_audit_in_db(
            #         log_msg=f'Exported {len(transactions)} processed transactions to {csv_filename}',
            #         process_type=self.PROCESS_TYPE,
            #         print_msg=self.PRINT_STATEMENTS,
            #         script_file_name=self.PROCESS_NAME
            #     )

            return transactions
            
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error in generic batch parser: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return None

    # Bank-specific batch parsers (implement based on your requirements)
    def _parse_associated_bank_batch(self, combined_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Parse Associated Bank transactions in batch"""
        return self._parse_generic_bank_batch(combined_data)

    def _parse_first_national_batch(self, combined_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Parse First National Bank transactions in batch"""
        return self._parse_generic_bank_batch(combined_data)

    def _parse_great_western_batch(self, combined_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Parse Great Western Bank transactions in batch"""
        return self._parse_generic_bank_batch(combined_data)

    def _parse_pinnacle_batch(self, combined_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Parse Pinnacle Bank transactions in batch"""
        return self._parse_generic_bank_batch(combined_data)

    def filter_transactions_by_date(self, transactions: pd.DataFrame) -> pd.DataFrame:
        """
        Filter transactions by the configured date range

        Args:
            transactions: DataFrame with bank transactions

        Returns:
            Filtered DataFrame containing only transactions within date range
        """
        try:
            if transactions.empty:
                return transactions

            # Ensure we have a date column
            date_columns = ['BANK_DATE', 'DATE', 'TRANSACTION_DATE']
            date_col = None

            for col in date_columns:
                if col in transactions.columns:
                    date_col = col
                    break

            if not date_col:
                self.log_audit_in_db(
                    log_msg='No date column found in transactions for filtering',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return transactions

            # Convert date column to datetime if not already
            transactions[date_col] = pd.to_datetime(transactions[date_col], errors='coerce')

            # Filter by date range
            start_date = pd.to_datetime(self.query_start_date)
            end_date = pd.to_datetime(self.query_end_date)

            filtered_transactions = transactions[
                (transactions[date_col] >= start_date) &
                (transactions[date_col] <= end_date)
            ].copy()

            self.log_audit_in_db(
                log_msg=f'Date filtering: {len(transactions)} -> {len(filtered_transactions)} transactions (range: {start_date.date()} to {end_date.date()})',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return filtered_transactions

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error filtering transactions by date: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return transactions

    def process_sheet_transactions_from_dataframe(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Tuple[Optional[pd.DataFrame], List]:
        """
        Process transactions from a DataFrame (from SharePoint Excel)
        
        Args:
            sheet_data: DataFrame containing sheet data from Excel
            result: ProcessingResult object to update
            
        Returns:
            Tuple of (transactions DataFrame, rejected transactions list)
        """
        try:
            self.log_audit_in_db(
                log_msg=f'Processing {len(sheet_data)} rows from sheet {result.sheetname}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            
            # Process based on bank type and import function
            # This would call different parsing methods based on result.bank or import_function_call
            transactions = self.parse_bank_transactions_from_dataframe(sheet_data, result)
            
            if transactions is not None and not transactions.empty:
                self.log_audit_in_db(
                    log_msg=f'Successfully parsed {len(transactions)} transactions from sheet {result.sheetname}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
            else:
                self.log_audit_in_db(
                    log_msg=f'No transactions parsed from sheet {result.sheetname}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
            
            return transactions, []
            
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Sheet processing error for {result.sheetname}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            result.data_issue = f"Sheet processing error: {str(e)}"
            return None, []

    def parse_bank_transactions_from_dataframe(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Optional[pd.DataFrame]:
        """
        Parse bank transactions from DataFrame based on bank type
        
        Args:
            sheet_data: Raw DataFrame from Excel sheet
            result: ProcessingResult with bank and processing info
            
        Returns:
            DataFrame with parsed transactions or None if parsing failed
        """
        try:
            # Add B_ID_LOCAL to all transactions for this location
            sheet_data['B_ID_LOCAL'] = result.b_id_local
            sheet_data['LOCATION'] = result.location
            sheet_data['BANK'] = result.bank
            
            # Determine parsing method based on bank or import function
            bank_name = result.bank.upper()
            
            if bank_name == 'ASSOCIATED':
                return self.parse_associated_bank_transactions(sheet_data, result)
            elif bank_name == 'FIRST_NATIONAL':
                return self.parse_first_national_transactions(sheet_data, result)
            elif bank_name == 'GREAT_WESTERN':
                return self.parse_great_western_transactions(sheet_data, result)
            elif bank_name == 'PINNACLE':
                return self.parse_pinnacle_transactions(sheet_data, result)
            else:
                # Generic parsing method
                return self.parse_generic_bank_transactions(sheet_data, result)
                
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error parsing transactions for bank {result.bank}: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return None

    def parse_generic_bank_transactions(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Optional[pd.DataFrame]:
        """
        Generic bank transaction parser - flexible column matching

        Args:
            sheet_data: Raw DataFrame from Excel
            result: ProcessingResult object

        Returns:
            Parsed transactions DataFrame
        """
        try:
            # Log available columns for debugging
            self.log_audit_in_db(
                log_msg=f'Available columns in {result.sheetname}: {list(sheet_data.columns)}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Flexible column mapping - try multiple possible column names
            column_patterns = {
                'DATE': ['DATE', 'TRANSACTION_DATE', 'TRANS_DATE', 'POST_DATE', 'POSTING_DATE', 'EFFECTIVE_DATE'],
                'DESCRIPTION': ['DESCRIPTION', 'DESC', 'MEMO', 'TRANSACTION_DESCRIPTION', 'DETAILS', 'REFERENCE'],
                'AMOUNT': ['AMOUNT', 'TRANSACTION_AMOUNT', 'DEBIT', 'CREDIT', 'NET_AMOUNT'],
                'BALANCE': ['BALANCE', 'RUNNING_BALANCE', 'ACCOUNT_BALANCE', 'ENDING_BALANCE']
            }

            # Find actual column mappings (case-insensitive)
            sheet_columns_upper = [str(col).upper() for col in sheet_data.columns]
            found_columns = {}

            for standard_name, possible_names in column_patterns.items():
                for possible_name in possible_names:
                    for i, sheet_col_upper in enumerate(sheet_columns_upper):
                        if possible_name in sheet_col_upper or sheet_col_upper in possible_name:
                            found_columns[standard_name] = sheet_data.columns[i]
                            break
                    if standard_name in found_columns:
                        break

            # If no standard columns found, try to infer from data patterns
            if not found_columns:
                self.log_audit_in_db(
                    log_msg=f'No standard column names found, attempting data pattern analysis',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Try to infer columns based on data patterns
                found_columns = self._infer_columns_from_data(sheet_data)

            # Log what we found
            self.log_audit_in_db(
                log_msg=f'Column mapping found: {found_columns}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Check if we have at least date and amount columns
            if 'DATE' not in found_columns or 'AMOUNT' not in found_columns:
                self.log_audit_in_db(
                    log_msg=f'Could not find required DATE and AMOUNT columns in sheet {result.sheetname}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return None
            
            # Basic data cleaning and standardization
            transactions = sheet_data.copy()

            # Create mapping from found columns to standard names
            actual_mapping = {}
            for standard_name, actual_col in found_columns.items():
                if standard_name == 'DATE':
                    actual_mapping[actual_col] = 'BANK_DATE'
                elif standard_name == 'DESCRIPTION':
                    actual_mapping[actual_col] = 'BANK_DESCRIPTION'
                elif standard_name == 'AMOUNT':
                    actual_mapping[actual_col] = 'BANK_AMOUNT'
                elif standard_name == 'BALANCE':
                    actual_mapping[actual_col] = 'BANK_BALANCE'

            # Rename columns to standard names
            transactions = transactions.rename(columns=actual_mapping)

            self.log_audit_in_db(
                log_msg=f'Applied column mapping: {actual_mapping}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            
            # Convert dates (handle Excel date serial numbers)
            if 'BANK_DATE' in transactions.columns:
                def convert_excel_date(date_value):
                    if pd.isna(date_value):
                        return pd.NaT

                    # If it's already a datetime, return as-is
                    if isinstance(date_value, (pd.Timestamp, datetime)):
                        return date_value

                    # If it's a number, treat as Excel date serial number
                    if isinstance(date_value, (int, float)):
                        try:
                            # Excel date serial number (days since 1900-01-01, with some quirks)
                            excel_epoch = pd.Timestamp('1899-12-30')  # Excel's epoch (accounting for leap year bug)
                            return excel_epoch + pd.Timedelta(days=date_value)
                        except:
                            return pd.NaT

                    # If it's a string, try normal datetime parsing
                    try:
                        return pd.to_datetime(date_value)
                    except:
                        return pd.NaT

                transactions['BANK_DATE'] = transactions['BANK_DATE'].apply(convert_excel_date)

                # Log date conversion results
                valid_dates = transactions['BANK_DATE'].notna().sum()
                total_dates = len(transactions)
                self.log_audit_in_db(
                    log_msg=f'Date conversion: {valid_dates}/{total_dates} valid dates after Excel serial number conversion',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Show date range if we have valid dates
                if valid_dates > 0:
                    min_date = transactions['BANK_DATE'].min()
                    max_date = transactions['BANK_DATE'].max()
                    self.log_audit_in_db(
                        log_msg=f'Converted date range: {min_date.strftime("%Y-%m-%d")} to {max_date.strftime("%Y-%m-%d")}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )
            
            # Convert amounts
            if 'BANK_AMOUNT' in transactions.columns:
                transactions['BANK_AMOUNT'] = pd.to_numeric(transactions['BANK_AMOUNT'], errors='coerce')
            
            # Remove rows with null dates or amounts
            transactions = transactions.dropna(subset=['BANK_DATE', 'BANK_AMOUNT'])
            
            self.log_audit_in_db(
                log_msg=f'Generic parser processed {len(transactions)} transactions for {result.bank}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Export CSV after date processing for individual sheet debugging
            # if not transactions.empty:
            #     csv_filename = f'processed_transactions_{result.bank}_{result.sheetname}_after_date_conversion.csv'
            #     transactions.to_csv(csv_filename, index=True)
            #     self.log_audit_in_db(
            #         log_msg=f'Exported {len(transactions)} processed transactions to {csv_filename}',
            #         process_type=self.PROCESS_TYPE,
            #         print_msg=self.PRINT_STATEMENTS,
            #         script_file_name=self.PROCESS_NAME
            #     )

            return transactions
            
        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error in generic parser: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return None

    # Placeholder methods for specific bank parsers - implement based on your bank-specific requirements
    def parse_associated_bank_transactions(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Optional[pd.DataFrame]:
        """Parse Associated Bank specific format"""
        self.log_audit_in_db(
            log_msg=f'Using Associated Bank parser for location {result.location}',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        # Implement Associated Bank specific parsing logic
        return self.parse_generic_bank_transactions(sheet_data, result)

    def parse_first_national_transactions(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Optional[pd.DataFrame]:
        """Parse First National Bank specific format"""
        self.log_audit_in_db(
            log_msg=f'Using First National parser for location {result.location}',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        # Implement First National specific parsing logic
        return self.parse_generic_bank_transactions(sheet_data, result)

    def parse_great_western_transactions(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Optional[pd.DataFrame]:
        """Parse Great Western Bank specific format"""
        self.log_audit_in_db(
            log_msg=f'Using Great Western parser for location {result.location}',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        # Implement Great Western specific parsing logic
        return self.parse_generic_bank_transactions(sheet_data, result)

    def parse_pinnacle_transactions(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> Optional[pd.DataFrame]:
        """Parse Pinnacle Bank specific format"""
        self.log_audit_in_db(
            log_msg=f'Using Pinnacle parser for location {result.location}',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )
        # Implement Pinnacle specific parsing logic
        return self.parse_generic_bank_transactions(sheet_data, result)

    def process_sheet_transactions(self, file_id: str, result: ProcessingResult) -> Tuple[Optional[pd.DataFrame], List]:
        """
        Process transactions from a specific sheet
        
        Args:
            file_id: Google Sheets file ID
            result: ProcessingResult object to update
            
        Returns:
            Tuple of (transactions DataFrame, rejected transactions list)
        """
        try:
            # Read sheet data
            sheet_data = self.google_client.read_sheet(file_id, result.sheetname)
            
            if sheet_data is None or sheet_data.empty:
                result.data_issue = "Sheet is empty or could not be read"
                return None, []
            
            # Set sheet URL
            result.gsht_link = self.google_client.get_sheet_url(file_id, result.sheetname)
            
            # Process based on import function (would need to implement bank-specific parsers)
            # This is a simplified version - you'd need to implement the specific bank parsers
            transactions = self.parse_bank_transactions(sheet_data, result)
            
            return transactions, []
            
        except Exception as e:
            result.data_issue = f"Sheet processing error: {str(e)}"
            return None, []

    def parse_bank_transactions(self, sheet_data: pd.DataFrame, result: ProcessingResult) -> pd.DataFrame:
        """
        Parse bank transactions based on bank type
        This would contain the logic from get_bank_trans_001, 002, 003, 004 functions
        """
        # Placeholder - implement specific bank parsing logic here
        # This would call different parsing methods based on result.bank or import_function_call
        pass

    def filter_transactions_by_date(self, transactions: pd.DataFrame) -> pd.DataFrame:
        """
        Filter transactions to only include those within the expected date range
        """
        if 'BANK_DATE' not in transactions.columns:
            return transactions
        
        mask = (
            (pd.to_datetime(transactions['BANK_DATE']) >= pd.to_datetime(self.query_start_date)) &
            (pd.to_datetime(transactions['BANK_DATE']) <= pd.to_datetime(self.query_end_date))
        )
        
        return transactions[mask]

    def load_transactions_to_database(self, transactions: List[Dict]) -> bool:
        """
        Load transactions to Snowflake database using MERGE/UPSERT for efficient duplicate handling

        Args:
            transactions: List of transaction dictionaries

        Returns:
            bool: True if ALL transactions loaded successfully, False if any failed
        """
        if not transactions:
            self.log_audit_in_db(
                log_msg='No transactions to load to database',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )
            return False

        self.log_audit_in_db(
            log_msg=f'Starting database MERGE operation for {len(transactions)} transactions',
            process_type=self.PROCESS_TYPE,
            print_msg=self.PRINT_STATEMENTS,
            script_file_name=self.PROCESS_NAME
        )

        try:
            # Process all transactions in a single MERGE operation
            return self.merge_transactions(transactions)

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"Error loading transactions to database: {e}",
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def merge_transactions(self, transactions: List[Dict]) -> bool:
        """
        Merge transactions into the database using UPSERT logic
        Updates existing records and inserts new ones in a single atomic operation

        Args:
            transactions: List of transaction dictionaries

        Returns:
            bool: True if merge succeeded, False otherwise
        """
        try:
            # Convert to DataFrame and prepare data
            df = pd.DataFrame(transactions)

            # Note: SEQ_ID generation is now handled in map_to_database_schema method
            # to avoid duplicate sequence generation and ensure proper integration

            self.log_audit_in_db(
                log_msg=f'Attempting MERGE operation for {len(df)} transactions',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Clean data before creating temporary table
            self.log_audit_in_db(
                log_msg=f'Cleaning data for MERGE operation. Columns: {list(df.columns)}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Clean the DataFrame to handle data type issues
            df_cleaned = self.clean_dataframe_for_database(df)

            # Map to actual database schema
            df_mapped = self.map_to_database_schema(df_cleaned)

            # Create temporary table with new data
            temp_table_name = f"TEMP_BANK_TRANS_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # First, create and populate temporary table
            columns_list = list(df_mapped.columns)
            data_list = df_mapped.values.tolist()

            #print(f"Columns list: {columns_list}")
            #print(f"Data list: {data_list[:4]}")

            # Create temporary table
            create_temp_query = f"""
            CREATE TEMPORARY TABLE {temp_table_name} AS
            SELECT * FROM {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.{self.TRANSACTIONS_TABLE} WHERE 1=0
            """

            #print(f"create_temp_query: {create_temp_query}")


            self.sf.execute_snowflake_query(create_temp_query)

            # Insert data into temporary table
            self.sf.bulk_insert(
                columns_list=columns_list,
                data_list=data_list,
                database=self.DATABASE_CSM_DATABASE,
                schema=self.DATABASE_CORPORATE_SCHEMA,
                table=temp_table_name
            )

            # Verify data was inserted into temporary table
            temp_count_query = f"SELECT COUNT(*) FROM {temp_table_name}"
            temp_count_result = self.sf.execute_snowflake_query(temp_count_query)
            #print(f"Temporary table count: {temp_count_result}")

            self.log_audit_in_db(
                log_msg=f'Temporary table {temp_table_name} contains {temp_count_result} records',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Build dynamic MERGE operation based on actual columns
            merge_query = self.build_merge_query(temp_table_name, columns_list)

            self.log_audit_in_db(
                log_msg=f'Executing MERGE query with {len(columns_list)} columns',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            self.log_audit_in_db(
                log_msg=f'Executing MERGE query: {merge_query[:200]}...',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            merge_result = self.sf.execute_snowflake_query(merge_query)

            self.log_audit_in_db(
                log_msg=f'MERGE query result: {merge_result}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Clean up temporary table
            drop_temp_query = f"DROP TABLE IF EXISTS {temp_table_name}"
            self.sf.execute_snowflake_query(drop_temp_query)

            # MERGE operation completed successfully if we got here without exceptions
            self.log_audit_in_db(
                log_msg=f'MERGE operation completed successfully for {len(df_mapped)} transactions',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )
            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'MERGE operation failed: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return False

    def clean_dataframe_for_database(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean DataFrame to handle data type issues before database operations

        Args:
            df: DataFrame to clean

        Returns:
            pd.DataFrame: Cleaned DataFrame ready for database operations
        """
        try:
            df_cleaned = df.copy()

            self.log_audit_in_db(
                log_msg=f'Cleaning DataFrame with shape {df_cleaned.shape} and columns: {list(df_cleaned.columns)}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Handle each column based on its expected type
            for col in df_cleaned.columns:
                if col in df_cleaned.columns:
                    # Convert empty strings to None for numeric columns
                    if df_cleaned[col].dtype == 'object':
                        # Replace empty strings with None
                        df_cleaned[col] = df_cleaned[col].replace('', None)
                        df_cleaned[col] = df_cleaned[col].replace(' ', None)

                        # Handle specific problematic columns
                        if 'serial' in col.lower() or 'check' in col.lower() or 'number' in col.lower():
                            # These might be numeric but have string values
                            df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
                            self.log_audit_in_db(
                                log_msg=f'Converted column {col} to numeric, NaN count: {df_cleaned[col].isna().sum()}',
                                process_type=self.PROCESS_TYPE,
                                print_msg=self.PRINT_STATEMENTS,
                                script_file_name=self.PROCESS_NAME
                            )

                    # Handle date columns
                    if 'date' in col.lower() and df_cleaned[col].dtype == 'object':
                        df_cleaned[col] = pd.to_datetime(df_cleaned[col], errors='coerce')

                    # Handle amount/balance columns
                    if any(keyword in col.lower() for keyword in ['amount', 'balance']):
                        df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')

            # Remove any columns that are entirely NaN after cleaning
            original_cols = len(df_cleaned.columns)
            df_cleaned = df_cleaned.dropna(axis=1, how='all')
            dropped_cols = original_cols - len(df_cleaned.columns)

            if dropped_cols > 0:
                self.log_audit_in_db(
                    log_msg=f'Dropped {dropped_cols} columns that were entirely NaN after cleaning',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

            # Fill remaining NaN values with appropriate defaults
            for col in df_cleaned.columns:
                if df_cleaned[col].dtype in ['float64', 'int64']:
                    df_cleaned[col] = df_cleaned[col].fillna(0)
                elif df_cleaned[col].dtype == 'object':
                    df_cleaned[col] = df_cleaned[col].fillna('')
                elif 'datetime' in str(df_cleaned[col].dtype):
                    # Keep datetime NaN as is - database can handle NULL dates
                    pass

            self.log_audit_in_db(
                log_msg=f'DataFrame cleaning completed. Final shape: {df_cleaned.shape}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return df_cleaned

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error cleaning DataFrame: {e}. Using original DataFrame.',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Warning'
            )
            return df

    def map_to_database_schema(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Map DataFrame columns to match the actual database schema

        Database schema: LOC_NUM, B_ID_LOCAL, BANK_DATE, TRANS_TYPE, AMOUNT, NOTE, SEQ_ID, _DLT_LOAD_ID, _DLT_ID

        Args:
            df: Cleaned DataFrame to map

        Returns:
            pd.DataFrame: DataFrame with columns matching database schema
        """
        try:
            self.log_audit_in_db(
                log_msg=f'Mapping DataFrame to database schema. Input columns: {list(df.columns)}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Create new DataFrame with database schema columns
            mapped_df = pd.DataFrame()

            # Map required columns
            # Always generate fresh SEQ_ID values from database sequence
            # Don't use existing SEQ_ID values as they may be temporary placeholders
            self.log_audit_in_db(
                log_msg=f'Generating fresh SEQ_ID values from database sequence for {len(df)} records',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            # Generate sequence IDs from database sequence
            mapped_df['SEQ_ID'] = self._generate_sequence_ids(len(df))

            # Debug: Log sample SEQ_ID values
            if len(mapped_df) > 0:
                sample_seq_ids = mapped_df['SEQ_ID'].head(3).tolist()
                self.log_audit_in_db(
                    log_msg=f'Sample generated SEQ_ID values: {sample_seq_ids}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

            mapped_df['B_ID_LOCAL'] = df.get('B_ID_LOCAL', '')
            mapped_df['BANK_DATE'] = df.get('BANK_DATE', pd.NaT)

            # Debug: Log sample BANK_DATE values before processing
            if 'BANK_DATE' in df.columns and len(df) > 0:
                sample_dates = df['BANK_DATE'].head(3).tolist()
                self.log_audit_in_db(
                    log_msg=f'Sample BANK_DATE values before mapping: {sample_dates}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

            # Map AMOUNT from various possible source columns
            if 'BANK_AMOUNT' in df.columns:
                mapped_df['AMOUNT'] = df['BANK_AMOUNT']
            elif 'AMOUNT' in df.columns:
                mapped_df['AMOUNT'] = df['AMOUNT']
            else:
                # Look for amount-like columns
                amount_cols = [col for col in df.columns if 'amount' in col.lower()]
                if amount_cols:
                    mapped_df['AMOUNT'] = df[amount_cols[0]]
                else:
                    mapped_df['AMOUNT'] = 0.0

            # Map NOTE from various possible source columns
            if 'BANK_DESCRIPTION' in df.columns:
                mapped_df['NOTE'] = df['BANK_DESCRIPTION']
            elif 'NOTE' in df.columns:
                mapped_df['NOTE'] = df['NOTE']
            elif 'DESCRIPTION' in df.columns:
                mapped_df['NOTE'] = df['DESCRIPTION']
            else:
                # Look for description-like columns
                desc_cols = [col for col in df.columns if 'desc' in col.lower() or 'note' in col.lower()]
                if desc_cols:
                    mapped_df['NOTE'] = df[desc_cols[0]]
                else:
                    mapped_df['NOTE'] = ''

            # Determine TRANS_TYPE based on AMOUNT
            mapped_df['TRANS_TYPE'] = mapped_df['AMOUNT'].apply(
                lambda x: 'CREDIT' if pd.notna(x) and x >= 0 else 'DEBIT'
            )

            # Map LOC_NUM from B_ID_LOCAL or LOCATION
            if 'LOCATION' in df.columns:
                mapped_df['LOC_NUM'] = df['LOCATION']
            else:
                # Extract numeric part from B_ID_LOCAL if possible
                mapped_df['LOC_NUM'] = mapped_df['B_ID_LOCAL']

            # Set DLT columns to null (these are for delta lake tracking)
            mapped_df['_DLT_LOAD_ID'] = None
            mapped_df['_DLT_ID'] = None

            # Ensure proper data types
            mapped_df['AMOUNT'] = pd.to_numeric(mapped_df['AMOUNT'], errors='coerce').fillna(0.0)
            mapped_df['NOTE'] = mapped_df['NOTE'].astype(str).fillna('')
            mapped_df['TRANS_TYPE'] = mapped_df['TRANS_TYPE'].astype(str)
            mapped_df['LOC_NUM'] = mapped_df['LOC_NUM'].astype(str)
            mapped_df['B_ID_LOCAL'] = mapped_df['B_ID_LOCAL'].astype(str)

            # Handle BANK_DATE for TIMESTAMP_NTZ(9) column
            if 'BANK_DATE' in mapped_df.columns:
                # Convert to proper datetime format for Snowflake
                mapped_df['BANK_DATE'] = pd.to_datetime(mapped_df['BANK_DATE'], errors='coerce')

                # Convert pandas Timestamp objects to ISO format strings for Snowflake
                # This prevents 'Invalid date' errors in the database
                def format_timestamp_for_snowflake(ts):
                    if pd.isna(ts):
                        return None
                    # Convert to ISO format string that Snowflake can parse
                    return ts.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # Remove last 3 digits for millisecond precision

                mapped_df['BANK_DATE'] = mapped_df['BANK_DATE'].apply(format_timestamp_for_snowflake)

                # Log date conversion results
                valid_dates = mapped_df['BANK_DATE'].notna().sum()
                total_dates = len(mapped_df)
                self.log_audit_in_db(
                    log_msg=f'BANK_DATE conversion: {valid_dates}/{total_dates} valid dates converted to string format for Snowflake',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )

                # Log sample converted dates
                if valid_dates > 0:
                    sample_converted = [str(d) for d in mapped_df['BANK_DATE'].dropna().head(3).tolist()]
                    self.log_audit_in_db(
                        log_msg=f'Sample converted BANK_DATE values: {sample_converted}',
                        process_type=self.PROCESS_TYPE,
                        print_msg=self.PRINT_STATEMENTS,
                        script_file_name=self.PROCESS_NAME
                    )

            self.log_audit_in_db(
                log_msg=f'Schema mapping completed. Output columns: {list(mapped_df.columns)}, Shape: {mapped_df.shape}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return mapped_df

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error mapping to database schema: {e}. Using cleaned DataFrame.',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            return df

    def _generate_sequence_ids(self, count: int) -> list:
        """
        Generate sequence IDs from the database sequence efficiently

        Args:
            count: Number of sequence IDs needed

        Returns:
            List of sequence ID values
        """
        try:
            if count <= 0:
                return []

            # Try simpler approach first - direct sequence calls
            seq_query = f"""
            SELECT {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.{self.BANK_TRANS_ID_SEQ}.NEXTVAL as NEXTVAL;
            """

            self.log_audit_in_db(
                log_msg=f'Generating {count} sequence IDs from {self.BANK_TRANS_ID_SEQ}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            seq_result = self.sf.execute_snowflake_query(seq_query)
            #print(f"Sequence result: {seq_result}")

            # Handle the result - it's a list of dictionaries but only returns 1 ID
            if isinstance(seq_result, list) and len(seq_result) > 0 and 'NEXTVAL' in seq_result[0]:
                # Get the starting sequence ID
                start_seq_id = seq_result[0]['NEXTVAL']

                # Generate consecutive sequence IDs starting from the returned value
                seq_ids = list(range(start_seq_id, start_seq_id + count))

                self.log_audit_in_db(
                    log_msg=f'Generated {len(seq_ids)} consecutive sequence IDs starting from {start_seq_id}: {seq_ids[:3]}{"..." if len(seq_ids) > 3 else ""}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
                return seq_ids
            elif hasattr(seq_result, 'iloc') and len(seq_result) > 0:
                # Fallback: It's a DataFrame - get the first column values
                seq_ids = seq_result.iloc[:, 0].tolist()
                self.log_audit_in_db(
                    log_msg=f'Successfully generated sequence IDs from DataFrame: {seq_ids[:3]}{"..." if len(seq_ids) > 3 else ""}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME
                )
                return seq_ids
            else:
                # Fallback: use simple incrementing numbers (not ideal but prevents failure)
                self.log_audit_in_db(
                    log_msg=f'Sequence query returned unexpected format, using fallback numbering. Result type: {type(seq_result)}, Result: {seq_result}',
                    process_type=self.PROCESS_TYPE,
                    print_msg=self.PRINT_STATEMENTS,
                    script_file_name=self.PROCESS_NAME,
                    log_type='Warning'
                )
                return list(range(1, count + 1))

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error generating sequence IDs: {e}. Using fallback numbering.',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            # Fallback: use simple incrementing numbers
            return list(range(1, count + 1))

    def build_merge_query(self, temp_table_name: str, columns_list: List[str]) -> str:
        """
        Build a dynamic MERGE query that handles column names with special characters

        Args:
            temp_table_name: Name of the temporary table
            columns_list: List of column names

        Returns:
            str: Complete MERGE SQL query
        """
        try:
            # Quote column names to handle special characters
            def quote_column(col_name):
                return f'"{col_name}"'

            # Define key columns for matching (business keys)
            key_columns = []
            update_columns = []

            # Categorize columns based on actual database schema
            for col in columns_list:
                if col in ['B_ID_LOCAL', 'BANK_DATE', 'AMOUNT', 'NOTE']:
                    key_columns.append(col)
                elif col not in ['SEQ_ID', '_DLT_LOAD_ID', '_DLT_ID']:  # Don't update these system columns
                    update_columns.append(col)

            # Build ON clause for matching
            on_conditions = []
            for col in key_columns:
                quoted_col = quote_column(col)
                on_conditions.append(f"target.{quoted_col} = source.{quoted_col}")

            on_clause = " AND ".join(on_conditions) if on_conditions else "1=0"  # Fallback if no key columns

            # Build UPDATE SET clause
            update_sets = []
            for col in update_columns:
                quoted_col = quote_column(col)
                update_sets.append(f"{quoted_col} = source.{quoted_col}")

            update_clause = ", ".join(update_sets) if update_sets else "SEQ_ID = source.SEQ_ID"  # Fallback

            # Build INSERT clause
            insert_columns = [quote_column(col) for col in columns_list]
            insert_values = [f"source.{quote_column(col)}" for col in columns_list]

            insert_columns_str = ", ".join(insert_columns)
            insert_values_str = ", ".join(insert_values)

            # Build complete MERGE query
            merge_query = f"""
            MERGE INTO {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.{self.TRANSACTIONS_TABLE} AS target
            USING {temp_table_name} AS source
            ON {on_clause}
            WHEN MATCHED THEN
                UPDATE SET {update_clause}
            WHEN NOT MATCHED THEN
                INSERT ({insert_columns_str})
                VALUES ({insert_values_str})
            """
            #print(f"Merge query: {merge_query}")

            self.log_audit_in_db(
                log_msg=f'Built MERGE query with {len(key_columns)} key columns and {len(update_columns)} update columns',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME
            )

            return merge_query

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f'Error building MERGE query: {e}',
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
            # Return a simple fallback query
            # return f"""
            # INSERT INTO {self.DATABASE_CSM_DATABASE}.{self.DATABASE_CORPORATE_SCHEMA}.{self.TRANSACTIONS_TABLE}
            # SELECT * FROM {temp_table_name}
            # """

    def check_for_exceptions(self) -> bool:
        """
        Check for various types of processing exceptions and log them to batch audit

        Returns:
            bool: True if exceptions were found, False otherwise
        """
        errors_found = False

        # Check for data issues
        data_issue_count = sum(1 for r in self.processing_results if r.data_issue)
        if data_issue_count > 0:
            errors_found = True
            location_text = "locations" if data_issue_count > 1 else "location"
            error_msg = (
                f"{data_issue_count} {location_text} where a DATA ISSUE prevented transaction processing. "
                "This is normally due to an expected column (either name or number) not being found in the Google sheet."
            )
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )

        # Check for NA processed transactions
        na_processed_count = sum(1 for r in self.processing_results if r.trans_processed is None)
        if na_processed_count > 0:
            errors_found = True
            na_stores = [r.location for r in self.processing_results if r.trans_processed is None]
            store_text = f"({', '.join(na_stores[:5])}) " if len(na_stores) < 6 else ""
            location_text = "locations" if na_processed_count > 1 else "location"

            error_msg = (
                f"{na_processed_count} {location_text} {store_text}where TRANS PROCESSED was NA. "
                "This can be due to the needed sheet not being found or it failed to load for some reason. "
                "See the 'GSHT LINK' column of the results for potential info on this."
            )
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )

        # Check for zero processed transactions
        zero_processed_count = sum(1 for r in self.processing_results if r.trans_processed == 0)
        if zero_processed_count > 0:
            errors_found = True
            zero_stores = [r.location for r in self.processing_results if r.trans_processed == 0]
            store_text = f"({', '.join(zero_stores[:5])}) " if len(zero_stores) < 6 else ""
            location_text = "locations" if zero_processed_count > 1 else "location"

            error_msg = (
                f"{zero_processed_count} {location_text} {store_text}where TRANS PROCESSED was 0. "
                "In this case, the needed sheet was found and loaded, but no valid transactions for the location "
                "were found in the expected sheet. This could also happen if the bank changed the layout of the "
                "transaction data; it wasn't pasted into the correct columns (always start pasting into cell A1); "
                "the account has just been activated or deactivated and wasn't used."
            )
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )
        
        # Check for rejected transactions
        rejected_count = sum(1 for r in self.processing_results if r.trans_rejected > 0)
        if rejected_count > 0:
            errors_found = True
            rejected_stores = [r.location for r in self.processing_results if r.trans_rejected > 0]
            store_text = f"({', '.join(rejected_stores[:5])}) " if len(rejected_stores) < 6 else ""
            location_text = "locations" if rejected_count > 1 else "location"

            error_msg = (
                f"{rejected_count} {location_text} {store_text}where TRANS REJECTED was > 0. "
                "The noted # of transactions for the location were rejected from the load. This could happen if "
                "the dates associated with them fell outside the expected date range. These ARE NOT included in "
                "the TRANS PROCESSED count."
            )
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )

        # Check for delete failures
        if self.delete_failed_locations:
            errors_found = True
            delete_count = len(self.delete_failed_locations)
            location_text = "locations" if delete_count > 1 else "location"

            error_msg = (
                f"{delete_count} {location_text} where the database DELETE statement failed. "
                "Previous transactions within the load date range were probably not deleted properly. Because of "
                "this the new transactions were not loaded to avoid creating duplicates."
            )
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )

        # Check for load failures
        if self.load_failed_locations:
            errors_found = True
            load_count = len(self.load_failed_locations)
            location_text = "locations" if load_count > 1 else "location"

            error_msg = (
                f"{load_count} {location_text} where the database INSERT statement failed. "
                "The number of transactions present in the database after the insert (TRANS LOADED column) "
                "didn't match the number in the TRANS_PROCESSED column in the results."
            )
            self.log_audit_in_db(
                log_msg=error_msg,
                process_type=self.PROCESS_TYPE,
                print_msg=self.PRINT_STATEMENTS,
                script_file_name=self.PROCESS_NAME,
                log_type='Error'
            )

        return errors_found

    def run_processing(self) -> bool:
        """
        Main execution method that orchestrates the entire processing workflow
        
        Returns:
            bool: True if processing completed successfully, False otherwise
        """
        try:
            self.log_audit_in_db(f"Starting {self.PROCESS_NAME} for period {self.email_start} to {self.email_end}", print_msg=self.PRINT_STATEMENTS)
            #self.log_audit_in_db(f"Processing period: {self.email_start} to {self.email_end}", print_msg=self.PRINT_STATEMENTS)
            #self.log_audit_in_db(f"Load mode: {'ACTUAL' if self.load_db else 'TEST'}", print_msg=self.PRINT_STATEMENTS)
            start_time = time.time()
            
            # Step 1: Get expected files and sheets
            expected_results = self.get_expected_files_sheets()
            #print(f"Expected results: {expected_results}")
            if expected_results.empty:
                self.log_audit_in_db(log_msg=f"No expected files/sheets found \n Total processing time: {self.sf.get_duration(start_time, show_secs=True)}", print_msg=self.PRINT_STATEMENTS, start_upload=True)

                return False
                        
            self.log_audit_in_db(f"Found {len(expected_results)} expected files/sheets for processing", print_msg=self.PRINT_STATEMENTS)
            self.log_audit_in_db(f"Starting processing for {len(expected_results)} locations", print_msg=self.PRINT_STATEMENTS)

            # Step 2: Process bank transactions
            processing_success = self.process_bank_transactions(expected_results)
            self.log_audit_in_db(f"Processing success and Database loading: {processing_success}", print_msg=self.PRINT_STATEMENTS)
            if not processing_success:
                self.log_audit_in_db("Transaction processing failed", print_msg=self.PRINT_STATEMENTS)
            
            self.log_audit_in_db(f"Completed processing for {len(self.processing_results)} locations", print_msg=self.PRINT_STATEMENTS)
            self.log_audit_in_db(f"Total transactions processed")
            
            # Step 3: Check for exceptions and send notifications
            exceptions_found = self.check_for_exceptions()

            end_time = self.sf.get_duration(start_time, show_secs=True)
            self.log_audit_in_db(f"Processing completed. Exceptions found: {exceptions_found} \n Total processing time: {end_time}", print_msg=self.PRINT_STATEMENTS, start_upload=True)

            return True
            
        except Exception as e:
            self.log_audit_in_db(f"Error in main processing: {e} \n Total processing time: {end_time}", print_msg=self.PRINT_STATEMENTS, start_upload=True)
            return False
            
                
    def log_audit_in_db(self, log_msg, log_type='Info', print_msg=False, print_data_list=True, start_upload=False,
                        process_type=None, script_file_name=None):
        """
        Store log messages in a buffer and upload them in bulk when requested
        
        Args:
            log_msg (str): Message to log
            log_type (str): Type of log (Info, Warning, Error, etc.)
            print_msg (bool): Whether to print the log message
            print_data_list (bool): Whether to print the data list
            start_upload (bool): Whether to upload the collected logs
        
        Returns:
            bool: Success status of the operation
        """
        try:            
            # Print log message if requested
            if print_msg:
                print(f"{self.PROCESS_NAME} - {log_type}: {log_msg}")
                
            # Create the log record and add to buffer
            uuid_str = str(uuid.uuid4())
            script_name = self.PROCESS_NAME
            rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            record = [uuid_str, script_name, log_type, self.PROCESS_TYPE, log_msg, rec_ins_date]
            self.log_buffer.append(record)

            
            if print_data_list:
                print(f"Added to log buffer. Current size: {len(self.log_buffer)}")

            if not self.LOG_TO_DB:
                return True
            
            # Upload logs if requested
            if start_upload and self.log_buffer:
                columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
                
                try:
                    self.sf.bulk_insert(
                        columns_list=columns_list, 
                        data_list=self.log_buffer, 
                        database=os.environ['DATABASE_RAW_DATABASE'], 
                        schema=self.LOG_SCHEMA, 
                        table=self.LOG_TABLE
                    )
                    print(f"Uploaded {len(self.log_buffer)} log entries in bulk")
                    
                    # Clear the buffer after successful upload
                    self.log_buffer = []
                    return True
                    
                except Exception as e:
                    print(f"Error uploading logs for WeeklyBankingImportTransactionsSFLoader: {e}")
                    return False
                    
            return True
            
        except Exception as e:
            print(f"Error in log_audit_in_db for WeeklyBankingImportTransactionsSFLoader: {e}")
            return False
        


def main():
    """Main execution function"""
    try:
        # Initialize clients

 
        # Create and run processor
        processor = WeeklyBankingImportTransactionsSFLoader(
            testing_emails=False
        )
        
        success = processor.run_processing()
        print(f"Successful run? {success}")
            
    except Exception as e:
        print(f"Critical error in main execution: {e}")
        
if __name__ == "__main__":
    main()