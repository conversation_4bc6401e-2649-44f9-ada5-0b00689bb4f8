# Updates table MOMS.ORDERS_SUMMARY_DAILY with daily summary of orders
#--------------------------------------------------------------------------------------
# Rev. No     Date      Author   Description
#---------------------------------------------------------------------------------------
#  1.0    5/16/2025    Jorge G<PERSON>funa   Initial Version
#---------------------------------------------------------------------------------------

from datetime import datetime, timedelta
import os
import time
import uuid
import libs.snowflake_helper as sf


class OrderSummaryLoaderHelper:

    LOG_TO_DB = True

    def __init__(self):

        # sets timezone to Central
        os.environ['TZ'] = 'America/Chicago'
        time.tzset()

        self.report_name = "Marco's MOMS.ORDERS_DAILY_SUMMARY-Snowflake Load"
        
        # Database table configuration
        self.my_schema = "MOMS"
        self.my_table = "ORDERS_SUMMARY_DAILY"


        # Query parameters
        self.query_date = datetime.now().strftime("%Y-%m-%d")
        self.query_periods = 2
        self.query_period_offset = 0
        
        self.envm=os.environ["DATABASE_ENVM"]
        self.csm_database = os.environ["DATABASE_CSM_DATABASE"]

        # Initialize Snowflake connection        
        self.sf = sf.SnowflakeHelper() # snowflake helper
        self.sf_conn = self.sf.conn

        self.script_name = __file__

        self.process_type = 'MOMS_ORDERS_SUMMARY_DAILY'
        
        print(f'MOMS_ORDERS_SUMMARY_DAILY env({self.envm}): {self.sf.conn}')

    def log_audit_in_db(self, log_msg, log_type='Info'):
        
        # only log to db if indicated
        if not self.LOG_TO_DB:
            return 
        
        uuid_str = str(uuid.uuid4())
        script_name = os.path.basename(self.script_name)
        rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        data_list = []
        record = [uuid_str, script_name, log_type, self.process_type, log_msg, rec_ins_date]
        columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
        data_list.append(record)
        # print(f"data_list: {data_list}")

        self.sf.bulk_insert(columns_list=columns_list, data_list=data_list,database=self.sf.raw_database, schema=self.sf.batch_audit_schema, table=self.sf.moms_log_tbl)

    def get_query_dates(self):
        """Get dates from MP_CALENDAR for main query"""
        query = f"""
            SELECT
            to_char(to_date(min(s_date))) as s_date
            , to_char(to_date(max(case when e_date > (TRUNC(CURRENT_DATE, 'day') - 1) 
                then (TRUNC(CURRENT_DATE, 'day') - 1) else e_date end))) as e_date
            from {self.envm}_CSM_DB.CORPORATE.mp_calendar
            where cal_id >= (
                SELECT i.cal_id - {self.query_periods + self.query_period_offset}
                from {self.envm}_CSM_DB.CORPORATE.mp_calendar i
                where to_date('{self.query_date}') between i.s_date and i.e_date
            )
            and cal_id <= (
                SELECT i.cal_id - {self.query_period_offset}
                from {self.envm}_CSM_DB.CORPORATE.mp_calendar i
                where to_date('{self.query_date}') between i.s_date and i.e_date
            )
        """

        self.p(query, print_log=False) # print for debugging

        return self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=True)

    def check_duplicate_orders(self, mydates):
        """Check for duplicate orders"""
        query = f"""
        SELECT store_number
        , business_date 
        , count(*) AS order_cnt 
        , count(DISTINCT ORDER_ID) AS order_cnt_distinct 
        FROM {self.envm}_CSM_DB.moms.ORDERSRESULT 
        WHERE BUSINESS_DATE >= to_date('{mydates['S_DATE']}','yyyy-mm-dd')
        AND BUSINESS_DATE <= to_date('{mydates['E_DATE']}','yyyy-mm-dd')
        AND void = 0 
        GROUP BY STORE_NUMBER, BUSINESS_DATE 
        HAVING count(*) <> count(DISTINCT ORDER_ID)
        """

        # self.p(query)

        return self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=False)

    def get_order_data(self, mydates):
        """Get main order data"""
        # The main query is very long, so I'll show a simplified version here
        # The full query would be the same as in the R script

        query = f""" 
            SELECT /* 20241223 add exclusion of 3PD orders to DELIVERY_ALL and DELIVERY_NDEF columns */
                ORD.store_number,
                ORD.business_date,
                ORD.business_date - 364 as ly_date,
                ORD.business_date - 7 as lw_date,
                count(distinct(ORD.order_id)) as  total_orders,
                round(sum(ORD.net),2) as net_sales,
                round(sum(ORD.gross - ORD.tax_amount),2) as gross_sales, /* FT equivalent version...MOMS gross includes tax collected*/
                count(DISTINCT(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.order_id END)) AS DELIVERY_ALL_COUNT,
                round(sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.net ELSE 0 END ),2) AS DELIVERY_ALL_NETSALES,
                round(sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.gross - ORD.tax_amount ELSE 0 END ),2) AS DELIVERY_ALL_GROSSSALES,
                count(DISTINCT(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.DISPATCH_TIME END)) AS DELIVERY_ALL_RUNS,
                count(DISTINCT(
                    CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
                    THEN ORD.order_id
                    END)
                ) AS DELIVERY_NDEF_COUNT,
                sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
                    THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DISPATCH_TIME)
                    END
                ) AS DELIVERY_NDEF_OTD_TIME,
                sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
                    THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME)
                    END
                ) AS DELIVERY_NDEF_DELIVERY_TIME,
                sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
                    THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.PROMISE_TIME)
                    END
                ) AS DELIVERY_NDEF_PROMISE_TIME,
                count(DISTINCT(
                    CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) <= 1800
                    THEN ORD.order_id
                    END)
                ) AS DELIVERY_NDEF_COUNT_L30,
                count(DISTINCT(
                    CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) > 2400
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
                    THEN ORD.order_id
                    END)
                ) AS DELIVERY_NDEF_COUNT_G40,
                count(DISTINCT(
                    CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
                        AND TPD.ORDER_ID IS NULL 
                        AND ORD.FUTURE = 0
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) > 3600
                        AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
                    THEN ORD.order_id
                    END)
                ) AS DELIVERY_NDEF_COUNT_G60,
                --count(DISTINCT(CASE WHEN UPPER(ORD.SOURCE) IN ('DOOR DASH', 'DOORDASH-DELIVERY', 'DOORDASH-TAKEOUT', 'GRUBHUB','GRUBHUB-DELIVERY','GRUBHUB-TAKEOUT','POSTMATES','POSTMATES-DELIVERY', 'POSTMATES-TAKE OUT', 'UBER EATS','UBEREATS-DELIVERY','UBEREATS-TAKEOUT') THEN ORD.order_id END)) AS DELIVERY_3PD_COUNT, /* */
                /* 2024-08-07 line below replaces line above due to new logic for 3PD */
                count(TPD.ORDER_ID) AS DELIVERY_3PD_COUNT, /* */
                --round(sum(CASE WHEN UPPER(ORD.SOURCE) IN ('DOOR DASH', 'DOORDASH-DELIVERY', 'DOORDASH-TAKEOUT', 'GRUBHUB','GRUBHUB-DELIVERY','GRUBHUB-TAKEOUT','POSTMATES','POSTMATES-DELIVERY', 'POSTMATES-TAKE OUT', 'UBER EATS','UBEREATS-DELIVERY','UBEREATS-TAKEOUT') THEN ORD.net END),2) AS DELIVERY_3PD_NETSALES,
                /* 2024-08-07 line below replaces line above due to new logic for 3PD */
                round(sum(CASE WHEN TPD.ORDER_ID IS NOT NULL THEN ORD.net ELSE 0 END),2) AS DELIVERY_3PD_NETSALES,
                --round(sum(CASE WHEN UPPER(ORD.SOURCE) IN ('DOOR DASH', 'DOORDASH-DELIVERY', 'DOORDASH-TAKEOUT', 'GRUBHUB','GRUBHUB-DELIVERY','GRUBHUB-TAKEOUT','POSTMATES','POSTMATES-DELIVERY', 'POSTMATES-TAKE OUT', 'UBER EATS','UBEREATS-DELIVERY','UBEREATS-TAKEOUT') THEN (ORD.gross - ORD.tax_amount) END),2) AS DELIVERY_3PD_GROSSSALES,
                /* 2024-08-07 line below replaces line above due to new logic for 3PD */
                round(sum(CASE WHEN TPD.ORDER_ID IS NOT NULL THEN (ORD.gross - ORD.tax_amount) ELSE 0 END),2) AS DELIVERY_3PD_GROSSSALES,
                count(DISTINCT(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN ORD.order_id END)) AS DELIVERY_DDD_COUNT,
                round(sum(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN ORD.net END),2) AS DELIVERY_DDD_NETSALES,
                round(sum(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN (ORD.gross - ORD.tax_amount) END),2) AS DELIVERY_DDD_GROSSSALES,
                count(DISTINCT(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN ORD.order_id END)) AS WEB_COUNT,
                ROUND(sum(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN ORD.net ELSE 0 END ),2) AS WEB_NETSALES,
                ROUND(sum(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN (ORD.gross - ORD.tax_amount) ELSE 0 END ),2) AS WEB_GROSSSALES,
                count(DISTINCT(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN ORD.order_id END)) AS APP_COUNT,
                sum(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN ORD.net ELSE 0 END ) AS APP_NETSALES,
                sum(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN (ORD.gross - ORD.tax_amount) ELSE 0 END ) AS APP_GROSSSALES,
                count(CASE WHEN ORD.FUTURE = 0
                    AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME) < 7200 /* Exclude any order where maketime > 2 hours */
                    AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME) > 0 /* Exclude any order where maketime = 0 seconds (e.g. Beverages, Gift Cards, etc) that skew results */
                    THEN ORD.ORDER_ID 
                    END
                ) AS MAKETIME_COUNT,
                sum(CASE WHEN ORD.FUTURE = 0
                    AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME) < 7200
                    THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME)
                    END
                ) AS MAKETIME_TIME,
                SUM(ORD.TOTAL_FOOD_COST) AS TOTAL_FOOD_COST_IDEAL, /* added 2024-08-07 */
                SUM(TFCV.total_food_cost) AS TOTAL_FOOD_COST_VOID, /* as of 7/18/2024, this is NOT being populated in Snowflake PROD table yet */
                sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') 
                    AND TPD.ORDER_ID IS NULL 
                    THEN ORD.TOTAL_FOOD_COST 
                    ELSE 0 END ) AS DELIVERY_ALL_FOOD_COST_IDEAL, /* added 2024-12-23 */
                sum(CASE WHEN TPD.ORDER_ID IS NOT NULL THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS DELIVERY_3PD_FOOD_COST_IDEAL, /* added 2024-12-23 */
                sum(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS DELIVERY_DDD_FOOD_COST_IDEAL, /* added 2024-12-23 */
                sum(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS WEB_FOOD_COST_IDEAL, /* added 2025-01-23 */
                sum(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS APP_FOOD_COST_IDEAL /* added 2025-01-23 */
            from {self.envm}_CSM_DB.MOMS.ORDERSRESULT ORD
            LEFT JOIN 
            (
                SELECT 
                STORE_NUMBER,
                ORDER_ID,
                MAX(MAKE_END_TIME) AS MAX_END_TIME
                FROM {self.envm}_CSM_DB.MOMS.ORDERSITEM
                WHERE REMOVED = 0
                --line below removed 2024-08-08 as all 0 maketime orders will be ignored in case-when of select
                --AND UPPER(CATEGORY) NOT IN ('BEVERAGES','EXTRAS','CHIPS','GIFT CARD','CATERING BEVERAGE') /* Exclude 0 maketime items to avoid skewing/manipulating results */
                GROUP BY STORE_NUMBER, ORDER_ID
            ) OI
                ON ORD.STORE_NUMBER = OI.STORE_NUMBER 
                AND ORD.ORDER_ID = OI.ORDER_ID 
            
            LEFT JOIN
            (
                SELECT STORE_NUMBER,
                    BUSINESS_DATE,
                    sum(total_food_cost) AS total_food_cost /* as of 7/18/2024, this is NOT being populated in Snowflake PROD table yet for VOID = 1 rows */
                FROM {self.envm}_CSM_DB.MOMS.ORDERSRESULT
                WHERE VOID = 1
                AND BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
                AND BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
                --AND BUSINESS_DATE BETWEEN '2024-07-01' AND '2024-07-14' /* TESTING */
                GROUP BY STORE_NUMBER, BUSINESS_DATE 
            ) TFCV
                ON ORD.STORE_NUMBER = TFCV.STORE_NUMBER
                AND ORD.BUSINESS_DATE = TFCV.BUSINESS_DATE
            /* next join added 2024-08-07 in order to identify 3PD columns correctly ('EZCater' was not in ORD.SOURCE column previously used) */
            LEFT JOIN 
            (
                SELECT DISTINCT 
                ORD.STORE_NUMBER,
                ORD.ORDER_ID
                --	 , ORD.ORDER_TYPE /* TESTING for 'Delivery' type */
                --	 , ORD.BUSINESS_DATE /* TESTING */
                FROM {self.envm}_CSM_DB.MOMS.ORDERSRESULT ORD
                INNER JOIN {self.envm}_CSM_DB.MOMS.ORDERSPAYMENT OP
                ON ORD.ORDER_ID = OP.ORDER_ID 
                AND ORD.STORE_NUMBER = OP.STORE_NUMBER 
                WHERE UPPER(OP.PAYMENT_TYPE) IN ('DOOR DASH','UBEREATS','GRUBHUB','SEAMLESS','POSTMATES','FOODA','EZCATER')
                AND ORD.BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
                AND ORD.BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
                -- AND ORD.BUSINESS_DATE BETWEEN '2024-07-01' AND '2024-07-14' /* TESTING */
            ) TPD
                ON ORD.ORDER_ID = TPD.ORDER_ID 
                AND ORD.STORE_NUMBER = TPD.STORE_NUMBER
            WHERE ORD.void = 0
                AND ORD.BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
                AND ORD.BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
                -- AND ORD.BUSINESS_DATE BETWEEN '2024-07-01' AND '2024-07-14' /* TESTING */
                -- AND ORD.STORE_NUMBER < 3510 /* TESTING */
            group by 
                ORD.store_number, 
                ORD.business_date, 
                ORD.business_date - 364, 
                ORD.business_date - 7
                -- ORDER BY ORD.STORE_NUMBER, ORD.BUSINESS_DATE /* TESTING */

        """

        # self.p(query, kill_now=True, print_log=True) # print for debugging

        return self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=False)

    def load_to_snowflake(self, mydata, mydates):
        """Load data to Snowflake"""

        # Check existing rows
        query = f"""
            SELECT count(*) as total
            from {self.envm}_CSM_DB.MOMS.ORDERS_SUMMARY_DAILY
            where BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
            and BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
        """

        row = self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=True)
        select_cnt = row['TOTAL']
        self.p(f"row: {row}\tselect_cnt: {select_cnt}", kill_now=False)

        # Delete existing rows
        delete_query = f"""
                DELETE from {self.envm}_CSM_DB.MOMS.ORDERS_SUMMARY_DAILY
                where BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
                and BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
        """
        cursor = self.sf_conn.cursor()
        cursor.execute(delete_query)
        delete_cnt = cursor.rowcount

        if delete_cnt != select_cnt:
            self.sf_conn.rollback()
            msg = f"Error deleting data from MOMS.ORDERS_SUMMARY_DAILY for dates: {mydates['S_DATE']} to {mydates['E_DATE']}. Expected {select_cnt} rows to be deleted, but got {delete_cnt}."
            print(msg)
            self.log_audit_in_db(msg, log_type='Error')
            return

        self.sf_conn.commit()

        # compile records for insert
        data_list = []
        columns_list = []
        counter = 0
        for row in mydata:
            if counter == 0: #track headers
                columns_list = list(row.keys())
            data_list.append(list(row.values()))
            # self.p(f"data_row({len(row)}): {row}\n", kill_now=False) # print for debugging
            counter += 1
            # if counter == 1:
            #     break

        # self.p(f"\ncolumns_list({len(columns_list)}):\n\t{columns_list} \n\ndata_list:\n\t {data_list}", kill_now=False) # print for debugging
        
        total_records = len(data_list)
        if total_records> 0:
            # cursor.execute(insert_query, data_list)
            # print(f"data_list: {data_list}")
            # exit(1)
            start_time_ = time.time()
            start_now_ = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    
            msg = f"About to insert {total_records} records to {self.my_table} table\t[time: {start_now_}] - ({mydates['S_DATE']} to {mydates['E_DATE']})"
            print(f"{msg}")
            # columns_list = ['STORE_NUMBER', 'B_DATE', 'TOT_HRLY_CREW_HRS', 'TOT_HRLY_CREW_WAGE', 'TOT_HRLY_DRVIN_HRS', 'TOT_HRLY_DRVIN_WAGE', 'TOT_HRLY_DRVOUT_HRS', 'TOT_HRLY_DRVOUT_WAGE', 'TOT_HRLY_MARKET_HRS', 'TOT_HRLY_MARKET_WAGE', 'TOT_HRLY_TRAIN_HRS', 'TOT_HRLY_TRAIN_WAGE']
            # data_list.append(record)

            self.sf.bulk_insert(columns_list=columns_list, data_list=data_list,database=self.csm_database, schema=self.my_schema, table=self.my_table)    

            end_now_ = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
            duration = self.sf.get_duration(start_time_, show_secs=True)
            msg = f"Finished inserting {total_records} records to {self.my_table} table\t[time: {end_now_}] [{duration}] - ({mydates['S_DATE']} to {mydates['E_DATE']})"
            print(f"\n\n{msg}\n")
            self.log_audit_in_db(log_msg=msg)        

        self.p(f"\n\nDone test: {mydates}\n\n", kill_now=False) # print for debugging


    def run(self):
        """Main execution method"""
        try:
            # Get dates
            mydates = self.get_query_dates()
            min_rows = 1
            self.p(msg=f"mydates: {mydates}\tlength of mydates: {len(mydates)}", kill_now=False) # print for debugging
            
            # if not data_status[0]:
            if mydates is None or (mydates is not None and len(mydates) == 0):

                msg = f"Error getting period dates from MP_CALENDAR for date dates: {self.query_date}"
                print(msg)
                self.log_audit_in_db(msg, log_type='Error')
                return

            # Check for duplicates
            my_dups = self.check_duplicate_orders(mydates)
            self.p(msg=f"my_dups: {my_dups}", kill_now=False) # print for debugging
            if len(my_dups) > 0:
                msg = f"Duplicate orders found for dates: {mydates['S_DATE']} to {mydates['E_DATE']}.  {my_dups}"
                print(msg)
                self.log_audit_in_db(msg, log_type='Warning')
                return

            # Get main data
            mydata = self.get_order_data(mydates)
            self.p(msg=f"mydata: {mydata}", kill_now=False, print_log=False) # print for debugging

            min_rows = 28 * (self.query_periods - 1)
            # data_status = self.check_df_rows(mydata, min_rows, self.report_name)
            
            # if not data_status[0]:
            if mydata is None or (mydata is not None and len(mydata) < min_rows):
                # self._handle_data_error(data_status[2], min_rows)
                self.log_audit_in_db(f"Error getting data from MOMS.ORDERSRESULT for dates: {mydates['S_DATE']} to {mydates['E_DATE']}. We need at least {min_rows} records to continue", log_type='Error')
                return

            # Load to Snowflake
            self.load_to_snowflake(mydata, mydates)

        except Exception as e:
            msg = f"Unexpected error in {self.report_name}: {str(e)}"
            print(msg)
            self.log_audit_in_db(msg, log_type='Error')
        # finally:
        #     if hasattr(self, 'sf_conn'):
        #         self.sf_conn.close()


    def p(self, msg, print_log=True, kill_now=False):
        """ print utility """
        if print_log:
            start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    
            print(f"{start_now}: {msg}")
        if kill_now:
            exit(1)        

if __name__ == "__main__":
    loader = OrderSummaryLoaderHelper()
    loader.run() 