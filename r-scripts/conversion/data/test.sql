TRUNCATE TABLE DEV_CSM_DB.CORPORATE.MP_BANK_TRANS_CLONE;

SELECT * FROM DEV_CSM_DB.CORPORATE.MP_BANK_TRANS LIMIT 5;
SELECT * FROM DEV_CSM_DB.CORPORATE.MP_BANK_TRANS_CLONE;
SELECT COUNT(*) FROM DEV_CSM_DB.CORPORATE.MP_BANK_TRANS_CLONE;
DESCRIBE TABLE DEV_CSM_DB.CORPORATE.MP_BANK_TRANS_CLONE;

SELECT DEV_CSM_DB.CORPORATE.MP_BANK_TRANS_ID_SEQ.NEXTVAL;
SELECT * FROM DEV_CSM_DB.CORPORATE.MP_BANK_TRANS 
WHERE LOC_NUM = '3536'
AND BANK_DATE LIKE '%2025-06%';



SELECT
    L.*,
    M.*
FROM DEV_CSM_DB.CORPORATE.MP_BANK_ID_LOCAL L
JOIN DEV_CSM_DB.CORPORATE.MP_BANK_ID_MASTER M
    ON L.B_ID_MASTER = M.B_ID_MASTER
WHERE 
    (L.E_DATE IS NULL OR L.E_DATE >= TO_DATE('2025-06-11','YYYY-MM-DD'))
ORDER BY L.LOC_NUM, L.B_ID_MASTER;

SELECT M.* FROM DEV_CSM_DB.CORPORATE.MP_BANK_ID_MASTER M
WHERE M.ACTIVE = 1
ORDER BY M.NAME_LONG, M.B_ID_MASTER;