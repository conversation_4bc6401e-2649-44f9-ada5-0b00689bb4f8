# Updates table MOMS.MP_LABOR_SUMMARY_DAILY_IDEAL with daily summary
#--------------------------------------------------------------------------------------
# Rev. No     Date      Author   Description
#---------------------------------------------------------------------------------------
#  1.0    5/16/2025    Jorge Garifuna   Initial Version
#---------------------------------------------------------------------------------------

from datetime import datetime, timedelta
import os
import time
import uuid
import libs.snowflake_helper as sf


class MpLaborSummaryDailyIdealLoaderHelper:

    LOG_TO_DB = True

    def __init__(self):

        # sets timezone to Central
        os.environ['TZ'] = 'America/Chicago'
        time.tzset()

        self.report_name = "Marco's MOMS.LABOR_SUMMARY_DAILY_IDEAL-Snowflake Load"
        
        # Database table configuration
        self.my_schema = "CORPORATE"
        self.my_table = "MP_LABOR_SUMMARY_DAILY_IDEAL"


        # Query parameters
        self.query_date = datetime.now().strftime("%Y-%m-%d")
        self.query_periods = 2
        self.query_period_offset = 0
        self.hours_worked_gm = 50 #Avg GM hours per week = 50 as of start of FY 2025 (2024-12-30)
        
        self.envm=os.environ["DATABASE_ENVM"]
        self.csm_database = os.environ["DATABASE_CSM_DATABASE"]

        # Initialize Snowflake connection        
        self.sf = sf.SnowflakeHelper() # snowflake helper
        self.sf_conn = self.sf.conn

        self.script_name = __file__

        self.process_type = 'MP_LABOR_SUMMARY_DAILY_IDEAL'
        
        print(f'MP_LABOR_SUMMARY_DAILY_IDEAL env({self.envm}): {self.sf.conn}')

    def log_audit_in_db(self, log_msg, log_type='Info'):
        
        # only log to db if indicated
        if not self.LOG_TO_DB:
            return 
        
        uuid_str = str(uuid.uuid4())
        script_name = os.path.basename(self.script_name)
        rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        data_list = []
        record = [uuid_str, script_name, log_type, self.process_type, log_msg, rec_ins_date]
        columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE','PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
        data_list.append(record)
        # print(f"data_list: {data_list}")

        self.sf.bulk_insert(columns_list=columns_list, data_list=data_list,database=self.sf.raw_database, schema=self.sf.batch_audit_schema, table=self.sf.moms_log_tbl)

    def get_query_dates(self):
        """Get dates from MP_CALENDAR for main query"""
        query = f"""
            SELECT
            to_char(to_date(min(s_date))) as s_date
            , to_char(to_date(max(case when e_date > (TRUNC(CURRENT_DATE, 'day') - 1) 
                then (TRUNC(CURRENT_DATE, 'day') - 1) else e_date end))) as e_date
            from {self.envm}_CSM_DB.CORPORATE.mp_calendar
            where cal_id >= (
                SELECT i.cal_id - {self.query_periods + self.query_period_offset}
                from {self.envm}_CSM_DB.CORPORATE.mp_calendar i
                where to_date('{self.query_date}') between i.s_date and i.e_date
            )
            and cal_id <= (
                SELECT i.cal_id - {self.query_period_offset}
                from {self.envm}_CSM_DB.CORPORATE.mp_calendar i
                where to_date('{self.query_date}') between i.s_date and i.e_date
            )
        """

        self.p(query, print_log=False) # print for debugging

        return self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=True)

    # def check_duplicate_orders(self, mydates):
    #     """Check for duplicate orders"""
    #     query = f"""
    #     SELECT store_number
    #     , business_date 
    #     , count(*) AS order_cnt 
    #     , count(DISTINCT ORDER_ID) AS order_cnt_distinct 
    #     FROM {self.envm}_CSM_DB.moms.ORDERSRESULT 
    #     WHERE BUSINESS_DATE >= to_date('{mydates['S_DATE']}','yyyy-mm-dd')
    #     AND BUSINESS_DATE <= to_date('{mydates['E_DATE']}','yyyy-mm-dd')
    #     AND void = 0 
    #     GROUP BY STORE_NUMBER, BUSINESS_DATE 
    #     HAVING count(*) <> count(DISTINCT ORDER_ID)
    #     """

    #     # self.p(query)

    #     return self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=False)

    def get_order_data(self, mydates):
        """Get main order data"""
        # The main query is very long, so I'll show a simplified version here
        # The full query would be the same as in the R script

        query = f""" 
            SELECT 
                ORDERS.STORE_NUMBER,
                DATE_TRUNC(DAY,ORDERS.BUSINESS_DATE) AS BUSINESS_DATE,
                MCW.PERIOD_YEAR,
                MCW.PERIOD_NUM,
                MCW.WEEK_NUM,
                DATE_TRUNC('DAY',MCW.E_DATE) AS WEEK_E_DATE,
                ORDERS.NET_SALES,
                MPT.TIER,
                CASE WHEN NVL(MLTA.HOURS_MIN,0) > MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) 
                    THEN MLTA.HOURS_MIN 
                    ELSE MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1)
                    END 
                    AS IDEAL_ADJ_HOURS, /* Starting with FY 2025, this is HOURS without avg GM hours */
                CASE WHEN NVL(MLTA.HOURS_MIN,0) > MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) 
                    THEN MLTA.HOURS_MIN 
                    --ELSE MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) - (50/7 * IFNULL(MGR.CNT, 0)) /* TESTING LINE, assumes GM 50 hour week */
                    ELSE MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) - ({self.hours_worked_gm}/7 * IFNULL(MGR.CNT, 0))
                    END * MLWR.WAGE_RATE_GOAL 
                    AS IDEAL_LABOR_DOLLARS /* Starting with FY 2025, this is $ without avg GM hours */
            FROM 
            (
                SELECT /* LIVE orders info */
                    ORD.STORE_NUMBER
                ,    DATE_TRUNC('DAY',ORD.BUSINESS_DATE) AS BUSINESS_DATE
                ,    round(sum(ORD.net),2) as NET_SALES
                FROM {self.envm}_CSM_DB.MOMS.ORDERSRESULT ORD
                WHERE VOID = 0
                --	AND ORD.BUSINESS_DATE >= TRUNC(TO_DATE('2024-08-06', 'YYYY-MM-DD'), 'week') - 7 /* TESTING LINE */
                --	AND ORD.BUSINESS_DATE <= TO_DATE('2024-08-06', 'YYYY-MM-DD') /* TESTING LINE */
                    AND ORD.BUSINESS_DATE >= to_date('{mydates['S_DATE']}') /* report start date */
                    AND ORD.BUSINESS_DATE <= to_date('{mydates['E_DATE']}') /* report end date */
                GROUP BY ORD.STORE_NUMBER
                ,	DATE_TRUNC('DAY',ORD.BUSINESS_DATE)
            ) ORDERS
            LEFT JOIN {self.envm}_CSM_DB.CORPORATE.MP_CALENDAR_WEEKLY MCW
            ON ORDERS.BUSINESS_DATE >= MCW.S_DATE
                AND ORDERS.BUSINESS_DATE <= MCW.E_DATE
            LEFT JOIN {self.envm}_CSM_DB.CORPORATE.MP_LABOR_DAILY_IDEAL mldi 
            ON TRUNC(ORDERS.NET_SALES) >= mldi.SALES_LOW 
                AND TRUNC(ORDERS.NET_SALES) <= mldi.SALES_HIGH 
                AND ORDERS.BUSINESS_DATE >= MLDI.S_DATE
                AND (ORDERS.BUSINESS_DATE <= MLDI.E_DATE OR MLDI.E_DATE IS NULL)
            LEFT JOIN {self.envm}_CSM_DB.CORPORATE.MP_PRICING_TIER mpt 
            ON ORDERS.STORE_NUMBER = MPT.STORE_NUMBER 
                AND ORDERS.BUSINESS_DATE >= MPT.S_DATE
                AND (ORDERS.BUSINESS_DATE <= MPT.E_DATE OR MPT.E_DATE IS NULL)
            LEFT JOIN {self.envm}_CSM_DB.CORPORATE.MP_LABOR_TIER_ADJ mlta 
            ON ORDERS.BUSINESS_DATE >= MLTA.S_DATE
                AND (ORDERS.BUSINESS_DATE <= MLTA.E_DATE OR MLTA.E_DATE IS NULL)
                AND MPT.TIER = MLTA.TIER
            LEFT JOIN {self.envm}_CSM_DB.CORPORATE.MP_LABOR_WAGE_RATE_GOAL mlwr
            ON ORDERS.STORE_NUMBER = MLWR.STORE_NUMBER
                AND ORDERS.BUSINESS_DATE >= MLWR.S_DATE
                AND (ORDERS.BUSINESS_DATE <= MLWR.E_DATE OR MLWR.E_DATE IS NULL)
            -- WHERE ORDERS.BUSINESS_DATE >= TO_DATE('2024-01-01', 'YYYY-MM-DD') /* TESTING LINE */
            LEFT JOIN
            ( /* added 20250206 per Deanna request to remove $ above equivalent to avg daily GM hours (50 hrs/7 days) */
                SELECT DATEADD('DAY', 1, WEH.SUNDAY) AS S_DATE
                , WEH.HOME_STORE AS STORE_NUMBER
                , COUNT(WEH.*) AS CNT
                FROM {self.envm}_CSM_DB.CORPORATE.FAMV_WEEKLY_EMPLOYEE_HISTORY WEH
                LEFT JOIN {self.envm}_CSM_DB.CORPORATE.FAMV_PAYROLL_POSITION FPP
                ON WEH.POSITION = FPP.POSITION
                WHERE FPP.SUB_CATEGORIES LIKE '%MGR_HISTORY%'
                --AND WEH.HOME_STORE = 3536 /* TESTING LINE ONLY */
                AND DATEADD('DAY', 1, WEH.SUNDAY) >= '2024-12-30'::TIMESTAMPNTZ  /* START IN FY 2025 ONLY */
                GROUP BY DATEADD('DAY', 1, WEH.SUNDAY)
                , WEH.HOME_STORE
            ) MGR
            ON ORDERS.STORE_NUMBER = MGR.STORE_NUMBER  
            AND MCW.S_DATE = MGR.S_DATE 
            ORDER BY ORDERS.BUSINESS_DATE, ORDERS.STORE_NUMBER

        """

        # self.p(query, kill_now=True, print_log=True) # print for debugging

        return self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=False)

    def load_to_snowflake(self, mydata, mydates):
        """Load data to Snowflake"""

        

        # Check existing rows
        query = f"""
            SELECT count(*) as total
            from {self.envm}_CSM_DB.{self.my_schema}.{self.my_table}
            where BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
            and BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
        """

        row = self.sf.execute_snowflake_query(query, print_query=False, pull_only_one_record=True)
        select_cnt = row['TOTAL']
        self.p(f"row: {row}\tselect_cnt: {select_cnt}", kill_now=False)

        # Delete existing rows
        delete_query = f"""
                DELETE from {self.envm}_CSM_DB.{self.my_schema}.{self.my_table}
                where BUSINESS_DATE >= to_date('{mydates['S_DATE']}')
                and BUSINESS_DATE <= to_date('{mydates['E_DATE']}')
        """
        cursor = self.sf_conn.cursor()
        cursor.execute(delete_query)
        delete_cnt = cursor.rowcount

        if delete_cnt != select_cnt:
            self.sf_conn.rollback()
            msg = f"Error deleting data from MP_LABOR_SUMMARY_DAILY_IDEAL for dates: {mydates['S_DATE']} to {mydates['E_DATE']}. Expected {select_cnt} rows to be deleted, but got {delete_cnt}."
            print(msg)
            self.log_audit_in_db(msg, log_type='Error')
            return

        self.sf_conn.commit()

        # self.p(f"\n\nDone test: {mydates} {mydata}\n\n", kill_now=True) # print for debugging

        # compile records for insert
        data_list = []
        columns_list = []
        counter = 0
        for row in mydata:
            if counter == 0: #track headers
                columns_list = list(row.keys())
            row['WEEK_E_DATE'] = row['WEEK_E_DATE'].strftime('%Y-%m-%d') # convert to string
            data_list.append(list(row.values()))
            # self.p(f"data_row({len(row)}): {row}\n", kill_now=True) # print for debugging
            counter += 1
            # if counter == 1:
            #     break

        # self.p(f"\ncolumns_list({len(columns_list)}):\n\t{columns_list} \n\ndata_list:\n\t {data_list}", kill_now=False) # print for debugging
        
        total_records = len(data_list)
        if total_records> 0:
            # cursor.execute(insert_query, data_list)
            # print(f"data_list: {data_list}")
            # exit(1)
            start_time_ = time.time()
            start_now_ = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    
            msg = f"About to insert {total_records} records to {self.my_table} table\t[time: {start_now_}] - ({mydates['S_DATE']} to {mydates['E_DATE']})"
            print(f"{msg}")
            # columns_list = ['STORE_NUMBER', 'B_DATE', 'TOT_HRLY_CREW_HRS', 'TOT_HRLY_CREW_WAGE', 'TOT_HRLY_DRVIN_HRS', 'TOT_HRLY_DRVIN_WAGE', 'TOT_HRLY_DRVOUT_HRS', 'TOT_HRLY_DRVOUT_WAGE', 'TOT_HRLY_MARKET_HRS', 'TOT_HRLY_MARKET_WAGE', 'TOT_HRLY_TRAIN_HRS', 'TOT_HRLY_TRAIN_WAGE']
            # data_list.append(record)

            self.sf.bulk_insert(columns_list=columns_list, data_list=data_list,database=self.csm_database, schema=self.my_schema, table=self.my_table)    

            end_now_ = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
            duration = self.sf.get_duration(start_time_, show_secs=True)
            msg = f"Finished inserting {total_records} records to {self.my_table} table\t[time: {end_now_}] [{duration}] - ({mydates['S_DATE']} to {mydates['E_DATE']})"
            print(f"\n\n{msg}\n")
            self.log_audit_in_db(log_msg=msg)        

        self.p(f"\n\nDone test: {mydates}\n\n", kill_now=True) # print for debugging


    def run(self):
        """Main execution method"""
        try:
            # Get dates
            mydates = self.get_query_dates()
            min_rows = 1
            self.p(msg=f"mydates: {mydates}\tlength of mydates: {len(mydates)}", kill_now=False) # print for debugging
            
            # if not data_status[0]:
            if mydates is None or (mydates is not None and len(mydates) == 0):

                msg = f"Error getting period dates from MP_CALENDAR for date dates: {self.query_date}"
                print(msg)
                self.log_audit_in_db(msg, log_type='Error')
                return

            # Check for duplicates
            # my_dups = self.check_duplicate_orders(mydates)
            # self.p(msg=f"my_dups: {my_dups}", kill_now=False) # print for debugging
            # if len(my_dups) > 0:
            #     msg = f"Duplicate orders found for dates: {mydates['S_DATE']} to {mydates['E_DATE']}.  {my_dups}"
            #     print(msg)
            #     self.log_audit_in_db(msg, log_type='Warning')
            #     return

            # Get main data
            mydata = self.get_order_data(mydates)
            self.p(msg=f"mydata: {mydata}", kill_now=False, print_log=False) # print for debugging

            min_rows = 28 * (self.query_periods - 1)
            # data_status = self.check_df_rows(mydata, min_rows, self.report_name)
            
            # if not data_status[0]:
            if mydata is None or (mydata is not None and len(mydata) < min_rows):
                # self._handle_data_error(data_status[2], min_rows)
                self.log_audit_in_db(f"Error getting data from MOMS.ORDERSRESULT for dates: {mydates['S_DATE']} to {mydates['E_DATE']}. We need at least {min_rows} records to continue", log_type='Error')
                return

            # Load to Snowflake
            self.load_to_snowflake(mydata, mydates)

        except Exception as e:
            msg = f"Unexpected error in {self.report_name}: {str(e)}"
            print(msg)
            self.log_audit_in_db(msg, log_type='Error')
        # finally:
        #     if hasattr(self, 'sf_conn'):
        #         self.sf_conn.close()


    def p(self, msg, print_log=True, kill_now=False):
        """ print utility """
        if print_log:
            start_now = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    
            print(f"{start_now}: {msg}")
        if kill_now:
            exit(1)        

if __name__ == "__main__":
    loader = MpLaborSummaryDailyIdealLoaderHelper()
    loader.run() 