import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
from sqlalchemy import create_engine, text
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import gmail_api
import os
import json
import logging
from typing import Dict, List, Optional, Union
import re

# Constants
TESTING_EMAILS = False
VERSION = "20250217"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mileage_tip_exceptions.log'),
        logging.StreamHandler()
    ]
)

class MileageTipExceptions:
    def __init__(self):
        self.report_name = "Marcos Mileage and Tip Exceptions"
        self.script_folder = "MARCOS_Mileage_Tip_Exceptions"
        self.rpt_folder = "reports"
        self.okay_to_continue = True
        
        # Emoji constants
        self.EMOJI_MONOCLE = "👨‍💼"
        self.EMOJI_PICKUP = "🚚"
        self.EMOJI_DELTRUCK = "🚛"
        self.EMOJI_STOPSIGN = "🛑"
        
        # Initialize paths
        self.central_path = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles")
        self.tableau_path = os.path.join("C:", "Users", "table", "Documents", "ReportFiles")
        
        # Set up logging
        self.log_path = os.path.join("C:", "Users", "steve", "Documents", "ReportFiles", self.script_folder)
        self.log_name = f"{self.report_name} - LOG.csv"
        
        # Google Sheets configuration
        self.gsht_info = pd.DataFrame({
            'FN': ['Mileage and Tip Exception Approvals'],
            'F_ID': ['1AFiKiGUO0qgnG-pZkX_cfHbchc8itLpCrtup6PLHPfE'],
            'SN': ['Exceptions']
        })
        
        # Column definitions
        self.gsht_columns = pd.DataFrame({
            'GshtHdr': [
                'District', 'Regional', 'Store #', 'Store Name', 'Trans Date',
                'Trans ID', 'ID', 'SUID', 'Type', 'Description', 'Employee Name',
                'Order Subtotal (Tips)', 'Order Total (Tips)', 'Amount To Approve',
                'ALTERNATE Approved Amount', 'Approved or Denied',
                'Approval User Email', 'Notes (required for \'Denied\' or \'ALTERNATE Approved Amount\' rows)'
            ],
            'NewHdr': [
                'DM', 'RM', 'STORE_NUMBER', 'STORE_NAME', 'TRANSACTION_DATE',
                'TRANSACTION_ID_TRIM', 'TRANSACTION_ID', 'SUID', 'ITEM_TYPE',
                'DESCRIPTION', 'EMPLOYEE_FULLNAME', 'NET_SUB', 'TOTAL',
                'AMOUNT', 'ADJUSTED_AMOUNT', 'APPROVAL_STATUS',
                'APPROVAL_EMAIL', 'COMMENTS'
            ],
            'RMEmail': [True, False, True, False, False, True, False, False, False, True, True, False, False, True, False, False, False, False],
            'DMEmail': [False, False, True, False, False, True, False, False, False, True, True, False, False, True, False, False, False, False],
            'DataType': ['character', 'character', 'integer', 'character', 'as.is', 'integer', 'character', 'character', 'character', 'character', 'character', 'numeric', 'numeric', 'numeric', 'numeric', 'character', 'character', 'character']
        })

    def update_log(self, event: str, desc: str, log_exists: bool = False) -> None:
        """Update the log file with event information."""
        log_data = pd.DataFrame({
            'RPT_PERIOD': [datetime.now().strftime("%Y%m%d %H%M%S")],
            'TIME': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            'EVENT': [event],
            'DESCRIPTION': [desc]
        })
        
        log_file = os.path.join(self.log_path, self.log_name)
        log_data.to_csv(log_file, mode='a', header=not log_exists, index=False)

    def initialize_database_connection(self) -> None:
        """Initialize Snowflake database connection."""
        try:
            # Snowflake connection parameters
            sf_params = {
                'account': 'your_account',
                'user': 'your_user',
                'password': 'your_password',
                'database': 'PROD_CSM_DB',
                'schema': 'CORPORATE',
                'warehouse': 'PROD_DATA_ANA_WH',
                'role': 'AR_PROD_CONSUMPTION_RW'
            }
            
            # Create SQLAlchemy engine
            self.engine = create_engine(
                f"snowflake://{sf_params['user']}:{sf_params['password']}@{sf_params['account']}/{sf_params['database']}/{sf_params['schema']}?warehouse={sf_params['warehouse']}&role={sf_params['role']}"
            )
            
            # Set timezone
            with self.engine.connect() as conn:
                conn.execute(text("ALTER SESSION SET TIMEZONE = 'America/Chicago'"))
                
            logging.info("Database connection established successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize database connection: {str(e)}")
            self.okay_to_continue = False

    def initialize_google_services(self) -> None:
        """Initialize Google Sheets and Gmail services."""
        try:
            # Google API scopes
            SCOPES = [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/gmail.send'
            ]
            
            # Load credentials
            creds = None
            if os.path.exists('token.json'):
                with open('token.json', 'r') as token:
                    creds = Credentials.from_authorized_user_info(json.load(token), SCOPES)
            
            # If credentials don't exist or are invalid, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        'credentials.json', SCOPES)
                    creds = flow.run_local_server(port=0)
                
                # Save credentials
                with open('token.json', 'w') as token:
                    token.write(creds.to_json())
            
            # Build services
            self.sheets_service = build('sheets', 'v4', credentials=creds)
            self.drive_service = build('drive', 'v3', credentials=creds)
            self.gmail_service = build('gmail', 'v1', credentials=creds)
            
            logging.info("Google services initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize Google services: {str(e)}")
            self.okay_to_continue = False

    def send_email(self, recipient: Union[str, List[str]], subject: str, body: str,
                  attachment: Optional[str] = None, inline: bool = False,
                  sender: str = "<EMAIL>",
                  test: bool = False, test_recipient: Optional[str] = None) -> None:
        """Send email using Gmail API."""
        try:
            if test:
                recipients = test_recipient
                body = f"<p><b>TEST SEND (normal recipient: {recipient})</b></p>{body}"
            else:
                recipients = recipient if isinstance(recipient, list) else [recipient]
            
            # Create message
            message = {
                'to': ', '.join(recipients),
                'subject': subject,
                'html': body
            }
            
            # Add attachments if any
            if attachment:
                # Handle attachment logic here
                pass
            
            # Send message
            self.gmail_service.users().messages().send(
                userId='me',
                body=message
            ).execute()
            
            logging.info(f"Email sent successfully to {recipients}")
            
        except Exception as e:
            logging.error(f"Failed to send email: {str(e)}")

    def read_google_sheet(self, spreadsheet_id: str, range_name: str) -> pd.DataFrame:
        """Read data from Google Sheet."""
        try:
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()
            
            values = result.get('values', [])
            if not values:
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(values[1:], columns=values[0])
            return df
            
        except Exception as e:
            logging.error(f"Failed to read Google Sheet: {str(e)}")
            return pd.DataFrame()

    def write_google_sheet(self, spreadsheet_id: str, range_name: str,
                          data: pd.DataFrame) -> None:
        """Write data to Google Sheet."""
        try:
            values = [data.columns.tolist()] + data.values.tolist()
            body = {
                'values': values
            }
            
            self.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()
            
            logging.info(f"Data written successfully to Google Sheet")
            
        except Exception as e:
            logging.error(f"Failed to write to Google Sheet: {str(e)}")

    def check_google_sheet_parameters(self) -> None:
        """Check parameters in Google Sheet."""
        try:
            spreadsheet_id = self.gsht_info['F_ID'].iloc[0]
            
            # Check if Parameters sheet exists
            sheet_metadata = self.sheets_service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()
            
            sheets = sheet_metadata.get('sheets', [])
            params_sheet = next((s for s in sheets if s['properties']['title'] == 'Parameters'), None)
            
            if params_sheet:
                # Read run_allowed parameter
                result = self.sheets_service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range='Param_R_run_allowed'
                ).execute()
                
                values = result.get('values', [])
                if values:
                    run_allowed = values[0][0].lower() == 'true'
                    self.okay_to_continue = run_allowed
                    
                    if not self.okay_to_continue:
                        logging.warning("Parameters in Google Sheet stopped this routine from running!")
            
        except Exception as e:
            logging.error(f"Failed to check Google Sheet parameters: {str(e)}")
            self.okay_to_continue = False

    def get_gmail_addresses(self) -> pd.DataFrame:
        """Get Gmail addresses with paynums from database."""
        try:
            query = """
                SELECT * FROM CORPORATE.GOOGLE_USER_EMAILS
            """
            
            with self.engine.connect() as conn:
                df = pd.read_sql(query, conn)
            
            if not df.empty:
                df = df.rename(columns={'PAYNUM': 'APPROVER_EMPLOYEE_NUMBER'})
                logging.info("Successfully retrieved Gmail addresses")
                return df
            else:
                logging.warning("No Gmail addresses found")
                return pd.DataFrame()
                
        except Exception as e:
            logging.error(f"Failed to get Gmail addresses: {str(e)}")
            return pd.DataFrame()

    def get_store_staff_assignments(self) -> pd.DataFrame:
        """Get store staff assignments and emails from database."""
        try:
            query = """
                SELECT
                    srdm.store,
                    gue.email,
                    CASE 
                        WHEN srdm.RM_PAYNUM = gue.PAYNUM THEN 'Regional'
                        WHEN srdm.DM_PAYNUM = gue.PAYNUM THEN 'District'
                        WHEN srdm.MGR_PAYNUM = gue.PAYNUM THEN 'Manager'
                    END AS Position
                FROM CORPORATE.GOOGLE_USER_EMAILS gue 
                LEFT JOIN CORPORATE.SO_RM_DM_MGR srdm
                    ON srdm.RM_PAYNUM = gue.PAYNUM 
                    OR srdm.DM_PAYNUM = gue.PAYNUM 
                    OR srdm.MGR_PAYNUM = gue.PAYNUM 
                WHERE srdm.STORE IS NOT NULL 
                AND srdm.LOC_INFO15_ALPHA = 'HF'
                ORDER BY srdm.store
            """
            
            with self.engine.connect() as conn:
                df = pd.read_sql(query, conn)
            
            if not df.empty:
                logging.info("Successfully retrieved store staff assignments")
                return df
            else:
                logging.warning("No store staff assignments found")
                return pd.DataFrame()
                
        except Exception as e:
            logging.error(f"Failed to get store staff assignments: {str(e)}")
            return pd.DataFrame()

    def get_current_exceptions(self) -> pd.DataFrame:
        """Get current exceptions from database."""
        try:
            query = """
                SELECT
                    rdm.rm_fullname as RM,
                    rdm.dm_fullname as DM,
                    exc.store_number as STORE_NUMBER,
                    rdm.loc_name AS STORE_NAME,
                    exc.transaction_date as TRANSACTION_DATE,
                    CAST(SUBSTRING(exc.transaction_id, 1, POSITION('_' IN exc.transaction_id) - 1) AS INTEGER) AS TRANSACTION_ID_TRIM,
                    exc.transaction_id as TRANSACTION_ID,
                    exc.suid AS SUID,
                    exc.item_type AS ITEM_TYPE,
                    exc.description as DESCRIPTION,
                    CONCAT(exc.first_name, ' ', exc.last_name) as EMPLOYEE_FULLNAME,
                    ROUND(orb.subtotal - orb.discount, 2) as NET_SUB,
                    ROUND(orb.total, 2) AS TOTAL,
                    exc.amount AS AMOUNT,
                    exc.dm_notif as DM_NOTIF,
                    rdm.rm_email as RM_EMAIL,
                    rdm.dm_email as DM_EMAIL
                FROM MOMS.BRANCH_TIPS_MILEAGE_EXCEP exc
                LEFT JOIN MOMS.DRIVERCASHOUTRESULT_B dcb
                    ON exc.suid = dcb.suid
                LEFT JOIN MOMS.ORDERSRESULT mor
                    ON SUBSTRING(exc.transaction_id, 1, POSITION('_' IN exc.transaction_id) - 1) = mor.order_id
                LEFT JOIN MOMS.ordersresult_branch orb
                    ON SUBSTRING(exc.transaction_id, 1, POSITION('_' IN exc.transaction_id) - 1) = orb.order_id
                LEFT JOIN CORPORATE.so_rm_dm_mgr rdm
                    ON exc.store_number = rdm.store
                WHERE exc.approval_status IS NULL
                ORDER BY rm, dm, store_number, item_type, exc.transaction_date, transaction_id_trim
            """
            
            with self.engine.connect() as conn:
                df = pd.read_sql(query, conn)
            
            if not df.empty:
                logging.info("Successfully retrieved current exceptions")
                return df
            else:
                logging.warning("No current exceptions found")
                return pd.DataFrame()
                
        except Exception as e:
            logging.error(f"Failed to get current exceptions: {str(e)}")
            return pd.DataFrame()

    def update_exception_status(self, transaction_id: str, adjusted_amount: float,
                              approval_status: str, comments: str,
                              approver_employee_number: str) -> bool:
        """Update exception status in database."""
        try:
            query = """
                UPDATE MOMS.BRANCH_TIPS_MILEAGE_EXCEP
                SET 
                    ADJUSTED_AMOUNT = :adjusted_amount,
                    APPROVAL_STATUS = :approval_status,
                    APPROVAL_DATE = CURRENT_TIMESTAMP,
                    COMMENTS = :comments,
                    APPROVER_EMPLOYEE_NUMBER = :approver_employee_number
                WHERE TRANSACTION_ID = :transaction_id
            """
            
            with self.engine.connect() as conn:
                result = conn.execute(
                    text(query),
                    {
                        'adjusted_amount': adjusted_amount,
                        'approval_status': approval_status,
                        'comments': comments,
                        'approver_employee_number': approver_employee_number,
                        'transaction_id': transaction_id
                    }
                )
                conn.commit()
            
            if result.rowcount > 0:
                logging.info(f"Successfully updated exception status for transaction {transaction_id}")
                return True
            else:
                logging.warning(f"No rows updated for transaction {transaction_id}")
                return False
                
        except Exception as e:
            logging.error(f"Failed to update exception status: {str(e)}")
            return False

    def update_dm_notification(self, transaction_ids: List[str]) -> bool:
        """Update DM notification timestamp in database."""
        try:
            query = """
                UPDATE MOMS.BRANCH_TIPS_MILEAGE_EXCEP
                SET DM_NOTIF = CURRENT_TIMESTAMP
                WHERE TRANSACTION_ID IN :transaction_ids
            """
            
            with self.engine.connect() as conn:
                result = conn.execute(
                    text(query),
                    {'transaction_ids': tuple(transaction_ids)}
                )
                conn.commit()
            
            if result.rowcount > 0:
                logging.info(f"Successfully updated DM notification for {len(transaction_ids)} transactions")
                return True
            else:
                logging.warning("No rows updated for DM notification")
                return False
                
        except Exception as e:
            logging.error(f"Failed to update DM notification: {str(e)}")
            return False

    def create_first_notification_email(self, curr_name: str, curr_rows: int,
                                      curr_rows_old: int, email_table: pd.DataFrame,
                                      except_cols: List[str], curr_incomplete_ids: pd.DataFrame,
                                      rm_or_dm: str) -> str:
        """Create HTML content for first notification email."""
        try:
            # Create intro based on RM or DM version
            if rm_or_dm == "DM":
                intro = f"""
                    <p>{curr_rows} new (or incomplete) exceptions below were added 
                    and need to be approved or denied in the 
                    <a href="{self.gsht_web_view_link}">{self.gsht_info['FN'].iloc[0]}</a>
                    sheet.</p>
                """
            else:
                intro = f"""
                    <p>{curr_rows} new (or incomplete) exceptions below were added. Districts 
                    listed have been notified and have 24 hours to address these in the 
                    <a href="{self.gsht_web_view_link}">{self.gsht_info['FN'].iloc[0]}</a>
                    sheet.</p>
                """
                
                if email_table['District'].isna().any():
                    intro += """
                        <h3><FONT COLOR="#ff0000">There are locations WITHOUT a District, the 
                        REGIONAL is expected to approve or deny those rows A.S.A.P.</FONT></h3>
                    """
            
            # Create incomplete section if applicable
            if not curr_incomplete_ids.empty:
                incomplete = f"""
                    <hr><p>The following IDs from above were marked as approved or 
                    denied, but are INCOMPLETE in some way and <strong>HAVE NOT BEEN 
                    PROCESSED YET...PLEASE REVISIT!</strong></p>
                    {curr_incomplete_ids.to_html(index=False, border=2)}
                    <br>'Denied' rows, and rows with an alternate approval $ amount, 
                    <strong>REQUIRE a note entry. If an alternate approved $ amount 
                    is present, then the transaction <strong>must</strong> be marked as 
                    <strong>'approved'</strong>. </strong>ALL rows require a paynum 
                    to be found (matched to the email address who last edited the row). 
                    <br>
                """
            else:
                incomplete = ""
            
            # Create previous section
            if curr_rows_old == 1:
                previous = f"""
                    <p><em>There is {curr_rows_old} additional exception 
                    previously sent that is also waiting for approval.</em></p>
                """
            elif curr_rows_old > 1:
                previous = f"""
                    <p><em>There are {curr_rows_old} additional exceptions 
                    previously sent that are also waiting for approval.</em></p>
                """
            else:
                previous = ""
            
            # Create main body
            html_content = f"""
                <html><head></head><body>
                <h3>{self.report_name} - {curr_name}</h3>
                <p>
                {intro}
                {email_table[except_cols].to_html(index=False, border=2)}
                <p>If you enter an 'alternate' corrected amount, you must also select 
                'approved' and enter a note describing why alternate amount is appropriate. 
                Entering 'denied' in the sheet invalidates the ENTIRE transaction.</p>
                {previous}
                <br>
                {incomplete}
                <br>
                {self.normal_signature}
                </body></html>
            """
            
            return html_content
            
        except Exception as e:
            logging.error(f"Failed to create first notification email: {str(e)}")
            return ""

    def create_escalation_email(self, curr_name: str, curr_rows: int,
                              curr_rows_old: int, email_table: pd.DataFrame,
                              except_cols: List[str], curr_incomplete_ids: pd.DataFrame) -> str:
        """Create HTML content for escalation email."""
        try:
            intro = f"""
                <p>{curr_rows} <strong>OVERDUE</STRONG> exceptions below that haven't 
                been fully addressed in the expected time. Please approve or deny in the 
                <a href="{self.gsht_web_view_link}">{self.gsht_info['FN'].iloc[0]}</a>
                sheet.</p>
            """
            
            # Create incomplete section if applicable
            if not curr_incomplete_ids.empty:
                incomplete = f"""
                    <hr><p>The following IDs from above were marked as approved or 
                    denied, but are INCOMPLETE in some way and <strong>HAVE NOT BEEN 
                    PROCESSED YET...PLEASE REVISIT!</strong></p>
                    {curr_incomplete_ids.to_html(index=False, border=2)}
                    <br>'Denied' rows, and rows with an alternate approval $ amount, 
                    <strong>REQUIRE a note entry.</strong> ALL rows require a paynum 
                    to be found (matched to the email address who last edited the row). 
                    If an alternate approved $ amount is present, then the transaction 
                    <strong>must</strong> be marked as <strong>'approved'</strong>.
                    <br>
                """
            else:
                incomplete = ""
            
            # Create previous section
            if curr_rows_old == 1:
                previous = f"""
                    <p><em>There is {curr_rows_old} additional exception 
                    previously sent that is also waiting for approval.</em></p>
                """
            elif curr_rows_old > 1:
                previous = f"""
                    <p><em>There are {curr_rows_old} additional exceptions 
                    previously sent that are also waiting for approval.</em></p>
                """
            else:
                previous = ""
            
            # Create main body
            html_content = f"""
                <html><head></head><body>
                <h3><FONT COLOR="#ff0000">!OVERDUE! </FONT> {self.report_name} - {curr_name}</h3>
                <p>
                {intro}
                {email_table[except_cols].to_html(index=False, border=2)}
                {previous}
                <br>
                {incomplete}
                <br>
                {self.normal_signature}
                </body></html>
            """
            
            return html_content
            
        except Exception as e:
            logging.error(f"Failed to create escalation email: {str(e)}")
            return ""

    def send_notifications(self, exceptions_df: pd.DataFrame) -> None:
        """Send notifications for exceptions."""
        try:
            time_now = datetime.now(pytz.UTC)
            
            # Get DM notifications
            dm_notify = exceptions_df[
                (exceptions_df['DM_NOTIF'].isna() | exceptions_df['APPROVAL_STATUS'].notna()) &
                exceptions_df['DM_EMAIL'].notna()
            ]
            
            if not dm_notify.empty:
                dm_emails = dm_notify['DM_EMAIL'].unique()
                except_cols = self.gsht_columns[
                    self.gsht_columns['DMEmail']
                ]['GshtHdr'].tolist()
                
                for curr_email in dm_emails:
                    curr_exceptions = dm_notify[dm_notify['DM_EMAIL'] == curr_email]
                    curr_name = curr_exceptions['DM'].iloc[0]
                    
                    # Get incomplete approvals
                    curr_incomplete_ids = curr_exceptions[
                        curr_exceptions['APPROVAL_STATUS'].notna()
                    ][['TRANSACTION_ID_TRIM', 'APPROVAL_STATUS', 'APPROVAL_EMAIL', 'APPROVER_EMPLOYEE_NUMBER']]
                    
                    curr_rows = len(curr_exceptions)
                    curr_rows_old = len(exceptions_df[exceptions_df['DM_EMAIL'] == curr_email]) - curr_rows
                    
                    # Check if okay to send
                    okay_to_send = True
                    if curr_rows == len(curr_incomplete_ids):
                        if time_now.hour < 4 or time_now.hour > 22:
                            okay_to_send = False
                        elif time_now.hour % 4 != 0:
                            okay_to_send = False
                    
                    if okay_to_send:
                        # Create and send email
                        body_html = self.create_first_notification_email(
                            curr_name, curr_rows, curr_rows_old,
                            curr_exceptions, except_cols, curr_incomplete_ids, "DM"
                        )
                        
                        subject = f"{self.EMOJI_MONOCLE}NEW {self.report_name} {self.EMOJI_DELTRUCK} ({time_now.strftime('%m/%d/%y %H:%M%Z')})"
                        
                        self.send_email(
                            recipient=curr_email,
                            subject=subject,
                            body=body_html,
                            test=TESTING_EMAILS
                        )
                        
                        # Update DM_NOTIF
                        update_ids = curr_exceptions[
                            curr_exceptions['DM_NOTIF'].isna()
                        ]['TRANSACTION_ID'].tolist()
                        
                        if update_ids:
                            self.update_dm_notification(update_ids)
            
            # Get RM notifications
            rm_notify = exceptions_df[
                (exceptions_df['DM_NOTIF'].isna() | exceptions_df['APPROVAL_STATUS'].notna())
            ]
            
            if not rm_notify.empty:
                rm_emails = rm_notify['RM_EMAIL'].unique()
                except_cols = self.gsht_columns[
                    self.gsht_columns['RMEmail']
                ]['GshtHdr'].tolist()
                
                for curr_email in rm_emails:
                    curr_exceptions = rm_notify[rm_notify['RM_EMAIL'] == curr_email]
                    curr_name = curr_exceptions['RM'].iloc[0]
                    
                    # Get incomplete approvals
                    curr_incomplete_ids = curr_exceptions[
                        curr_exceptions['APPROVAL_STATUS'].notna()
                    ][['TRANSACTION_ID_TRIM', 'APPROVAL_STATUS', 'APPROVAL_EMAIL', 'APPROVER_EMPLOYEE_NUMBER']]
                    
                    curr_rows = len(curr_exceptions)
                    curr_rows_old = len(exceptions_df[exceptions_df['RM_EMAIL'] == curr_email]) - curr_rows
                    
                    # Check if okay to send
                    okay_to_send = True
                    if curr_rows == len(curr_incomplete_ids):
                        if time_now.hour < 4 or time_now.hour > 22:
                            okay_to_send = False
                        elif time_now.hour % 4 != 0:
                            okay_to_send = False
                    
                    if okay_to_send:
                        # Create and send email
                        body_html = self.create_first_notification_email(
                            curr_name, curr_rows, curr_rows_old,
                            curr_exceptions, except_cols, curr_incomplete_ids, "RM"
                        )
                        
                        subject = f"{self.EMOJI_MONOCLE}NEW {self.report_name} {self.EMOJI_DELTRUCK} ({time_now.strftime('%m/%d/%y %H:%M%Z')})"
                        
                        self.send_email(
                            recipient=curr_email,
                            subject=subject,
                            body=body_html,
                            test=TESTING_EMAILS
                        )
                        
                        # Update DM_NOTIF for rows without DM
                        update_ids = curr_exceptions[
                            (curr_exceptions['DM'].isna()) &
                            (curr_exceptions['DM_NOTIF'].isna())
                        ]['TRANSACTION_ID'].tolist()
                        
                        if update_ids:
                            self.update_dm_notification(update_ids)
            
        except Exception as e:
            logging.error(f"Failed to send notifications: {str(e)}")

    def send_escalation_notifications(self, exceptions_df: pd.DataFrame) -> None:
        """Send escalation notifications for overdue exceptions."""
        try:
            time_now = datetime.now(pytz.UTC)
            
            # Get overdue exceptions
            rm_escalate_notify = exceptions_df[
                (exceptions_df['DM_NOTIF'].notna()) &
                (exceptions_df['DM_NOTIF'].apply(
                    lambda x: (time_now - x).total_seconds() / 3600 > 23
                )) &
                (exceptions_df['DM_NOTIF'].apply(
                    lambda x: (int((time_now - x).total_seconds() / 3600) - 24) % 24 == 0
                ))
            ]
            
            if not rm_escalate_notify.empty:
                rm_emails = rm_escalate_notify['RM_EMAIL'].unique()
                except_cols = self.gsht_columns[
                    self.gsht_columns['RMEmail']
                ]['GshtHdr'].tolist()
                except_cols.append('First Notification')
                
                for curr_email in rm_emails:
                    curr_exceptions = rm_escalate_notify[rm_escalate_notify['RM_EMAIL'] == curr_email]
                    curr_name = curr_exceptions['RM'].iloc[0]
                    
                    # Get incomplete approvals
                    curr_incomplete_ids = curr_exceptions[
                        curr_exceptions['APPROVAL_STATUS'].notna()
                    ][['TRANSACTION_ID_TRIM', 'APPROVAL_STATUS', 'APPROVAL_EMAIL', 'APPROVER_EMPLOYEE_NUMBER']]
                    
                    curr_rows = len(curr_exceptions)
                    curr_rows_old = len(exceptions_df[exceptions_df['RM_EMAIL'] == curr_email]) - curr_rows
                    
                    # Format DM_NOTIF for display
                    curr_exceptions['DM_NOTIF'] = curr_exceptions['DM_NOTIF'].apply(
                        lambda x: x.strftime('%b %d %H:%M:%S%Z')
                    )
                    
                    # Create and send email
                    body_html = self.create_escalation_email(
                        curr_name, curr_rows, curr_rows_old,
                        curr_exceptions, except_cols, curr_incomplete_ids
                    )
                    
                    subject = f"{self.EMOJI_STOPSIGN}!OVERDUE! {self.report_name} ({time_now.strftime('%m/%d/%y %H:%M%Z')})"
                    
                    # Add additional recipients
                    recipients = [
                        curr_email,
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>"
                    ]
                    recipients.extend(curr_exceptions['DM_EMAIL'].dropna().unique())
                    recipients = list(set(recipients))
                    
                    self.send_email(
                        recipient=recipients,
                        subject=subject,
                        body=body_html,
                        test=TESTING_EMAILS
                    )
            
        except Exception as e:
            logging.error(f"Failed to send escalation notifications: {str(e)}")

    def run(self) -> None:
        """Main execution method."""
        try:
            logging.info(f"Starting {self.report_name}")
            self.update_log("Routine Start", f"PARAMS - Testing Emails: {TESTING_EMAILS}")
            
            # Initialize database connection
            self.initialize_database_connection()
            
            if not self.okay_to_continue:
                logging.error("Failed to initialize required components")
                return
                
            # Main processing logic will go here
            
            self.update_log("Routine End", "Complete")
            
        except Exception as e:
            logging.error(f"Error in main execution: {str(e)}")
            self.update_log("Routine End", f"Error: {str(e)}")

if __name__ == "__main__":
    app = MileageTipExceptions()
    app.run() 