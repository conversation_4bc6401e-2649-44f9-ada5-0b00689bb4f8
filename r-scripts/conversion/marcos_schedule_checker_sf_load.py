import os
import sys
import datetime
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import keyring
import snowflake.connector
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import gmail
from gmail import Gmail
import tabulate
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import json
import re
from typing import List, Dict, Any, Optional, Union

# Constants
TESTING_EMAILS = True  # NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance

# Version 20240926
# Changes:
# - Converted from R to Python
# - Removed RJDBC library reference
# - Converted from mailR package to gmail API
# - Modified to populate Snowflake (previously Oracle)

# Parameters
OKAY_TO_CONTINUE = True

REPORT_NAME = "<PERSON>'s Schedule Checker Import"
SCRIPT_FOLDER = "MARCOS_Schedule_Checker"
REPORT_FOLDER = "reports"
LOG_PATH = os.path.join("C:", "Users", "table", "Documents", "ReportFiles", SCRIPT_FOLDER)
GSHEET_AUTH_EMAIL = "<EMAIL>"
GDRIVE_MAIN_URL = 'https://docs.google.com/spreadsheets/d/19QlpgP5-eLI4f17-0qu0euBKulyDlrQlzDrqeTe8uPo/'
GDRIVE_MAIN_URL_EMAIL = 'https://drive.google.com/drive/u/0/folders/1IgOY7tJe_x8cETx2EdK-Af0xdVM_OeJi'
GDRIVE_FOLDER_PREFIX = 'https://drive.google.com/drive/folders/'
GDRIVE_SHEET_PREFIX = "https://docs.google.com/spreadsheets/d/"

# Email parameters
WARN_RECIPIENTS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
]
WARN_SIGNATURE = """<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"""
RDO_RECIPIENTS = ["<EMAIL>"]
DM_RECIPIENTS = ["<EMAIL>"]
NORM_SIGNATURE = """<b><span style='font-weight:bold'>Steve Olson</span></b><br/>
                   Sr. Analytics Mgr.<br/>
                   <b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>
                   2500 Lehigh Ave.<br/>
                   Glenview, IL 60026<br/>
                   Ph: 847/904-9043<br/></span></font>"""
TEST_RECIPIENTS = ["<EMAIL>"]
TEST_CC_RECIPIENTS = ["<EMAIL>"]
HV_SIG_LOGO_PATH = os.path.join("C:", "Users", "table", "Documents", "ReportFiles", "HV Logo Email Signature.png")

# Test computers
TEST_COMPUTERS = ["STEVEO-PLEX7010", "LAPTOPTOSHIBA13", "STEVEANDJENYOGA"]
TESTING_PC = os.environ.get("COMPUTERNAME") in TEST_COMPUTERS

if TESTING_PC:
    # Steve PC testing paths
    LOG_PATH = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles", SCRIPT_FOLDER)
    HV_SIG_LOGO_PATH = os.path.join("//*************", "public", "steveo", "R Stuff", "ReportFiles", "HV Logo Email Signature.png")

REPORT_PATH = os.path.join(LOG_PATH, REPORT_FOLDER)

# Date and time variables
QUERY_DATE = datetime.now().strftime("%d-%b-%y")
# QUERY_DATE = "20-Sep-24"  # TEST OR MANUAL RUN ONLY

QUERY_STARTDATE = (datetime.strptime(QUERY_DATE, "%d-%b-%y") + timedelta(days=7)).strftime("%d-%b-%y")
QUERY_ENDDATE = (datetime.strptime(QUERY_DATE, "%d-%b-%y") + timedelta(days=13)).strftime("%d-%b-%y")

REPORT_YEAR = datetime.strptime(QUERY_STARTDATE, "%d-%b-%y").year
REPORT_STARTDATE = datetime.strptime(QUERY_DATE, "%d-%b-%y") + timedelta(days=7)

REPORT_REMOVESTART = (datetime.strptime(QUERY_DATE, "%d-%b-%y") - timedelta(days=49)).strftime("%Y%m%d")
REPORT_REMOVEEND = (datetime.strptime(QUERY_DATE, "%d-%b-%y") - timedelta(days=35)).strftime("%Y%m%d")
RPT_START = datetime.strptime(QUERY_STARTDATE, "%d-%b-%y").strftime("%Y%m%d")
RPT_END = datetime.strptime(QUERY_ENDDATE, "%d-%b-%y").strftime("%Y%m%d")

# Determine if we should load to database
LOAD_DB = True  # TEST OR MANUAL RUN ONLY where database load is needed
EMAIL_MISSING = True  # when True, emails addresses in RDO_RECIPIENTS when rows have missing data

# Google Sheet column names
GSHEET_STORE_COLNAME = "Store"
GSHEET_STORE_META_COLNAME = ["City", "RDO", "DM"]
GSHEET_GM_CORRECT_COLNAME = "GM working the correct days and hours"
GSHEET_APPROVED_COLNAME = "Schedule Approved by DM"
GSHEET_OT_COLNAME = "OT Hours"
GSHEET_COLNAMES_EXPECTED = [
    GSHEET_STORE_COLNAME,
    *GSHEET_STORE_META_COLNAME,
    "Monday Forecast",
    "Monday Scheduled hours",
    "Tuesday Forecast",
    "Tuesday Scheduled hours",
    "Wednesday Forecast",
    "Wednesday Scheduled hours",
    "Thursday Forecast",
    "Thursday Scheduled hours",
    "Friday Forecast",
    "Friday Scheduled hours",
    "Saturday Forecast",
    "Saturday Scheduled hours",
    "Sunday Forecast",
    "Sunday Scheduled hours",
    GSHEET_OT_COLNAME,
    GSHEET_GM_CORRECT_COLNAME,
    GSHEET_APPROVED_COLNAME
]
GSHEET_LAST_COLNAME = GSHEET_APPROVED_COLNAME
DAY_FORE_APPEND = " Forecast"
DAY_SCHED_APPEND = " Scheduled hours"

# Snowflake Connection
SF_ENVIRON = "PROD"  # or "STAGE"
if SF_ENVIRON == "STAGE":
    SF_DB = "STAGE_CSM_DB"
    SF_SCHEMA = "CORPORATE"
    SF_WH = "STAGE_DATA_ANA_WH"
    SF_ROLE = "AR_STAGE_CONSUMPTION_RW"
    SF_USER = keyring.get_password("SfHV", "tableau_ID_stage")
    SF_PW = keyring.get_password("SfHV", "tableau_PW_stage")
else:
    SF_DB = "PROD_CSM_DB"
    SF_SCHEMA = "CORPORATE"
    SF_WH = "PROD_DATA_ANA_WH"
    SF_ROLE = "AR_PROD_CONSUMPTION_RW"
    SF_USER = keyring.get_password("SfHV", "tableau_ID_prod")
    SF_PW = keyring.get_password("SfHV", "tableau_PW_prod")

# Initialize Snowflake connection
def get_snowflake_connection():
    conn = snowflake.connector.connect(
        user=SF_USER,
        password=SF_PW,
        account="your_account",  # Replace with actual account
        warehouse=SF_WH,
        database=SF_DB,
        schema=SF_SCHEMA,
        role=SF_ROLE
    )
    return conn

# Email sending function
def send_email(recipient: List[str], subject: str, body: str, attachment: Optional[str] = None, 
               inline: bool = False, sender: str = GSHEET_AUTH_EMAIL, test: bool = False, 
               test_recipient: Optional[List[str]] = None, report_name: str = REPORT_NAME):
    email_regex = r"([_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4}))"
    my_email = re.findall(email_regex, sender)[0][0]
    sender = f"{report_name} <<EMAIL>>"
    my_replyto = my_email

    if test:
        recipients = test_recipient
        body = f"<p><b>TEST SEND (normal recipient: {'; '.join(recipient)})</b></p>{body}"
    else:
        recipients = recipient

    # Initialize Gmail API
    gmail_service = build('gmail', 'v1', credentials=get_gmail_credentials())
    
    # Create message
    message = {
        'to': ', '.join(recipients),
        'from': sender,
        'reply-to': my_replyto,
        'subject': subject,
        'body': body
    }

    # Add attachments if any
    if attachment:
        # Handle attachment logic here
        pass

    # Send message
    gmail_service.users().messages().send(userId='me', body=message).execute()

# Check dataframe rows
def check_df_rows(df: pd.DataFrame, min_num_rows: int, report_name: Optional[str] = None) -> tuple:
    if isinstance(df, pd.DataFrame):
        if len(df) >= min_num_rows:
            error_status = f"{report_name}: OKAY"
            temp_nrow = len(df)
            temp_bool = True
        else:
            temp_bool = False
            temp_nrow = len(df)
            error_status = f"{report_name}: INCOMPLETE"
    else:
        temp_bool = False
        temp_nrow = 0
        error_status = f"{report_name}: ERROR"
    return temp_bool, temp_nrow, error_status

# Write to Excel
def write_xlsx(dirpath: str, fname: str, sname: str = "Sheet1", rpt_df: pd.DataFrame, 
               colnames: bool = True, colwidths: Optional[pd.DataFrame] = None, writeover: bool = True):
    my_fn = os.path.join(dirpath, fname)
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sname

    # Write data
    for r_idx, row in enumerate(rpt_df.itertuples(index=False), start=1):
        for c_idx, value in enumerate(row, start=1):
            ws.cell(row=r_idx, column=c_idx, value=value)

    # Style header
    header_font = Font(bold=True, size=12, name="Arial Narrow")
    header_fill = PatternFill(start_color="D6D6D6", end_color="D6D6D6", fill_type="solid")
    for cell in ws[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(vertical='center', wrap_text=True)

    # Set column widths
    if colwidths is not None:
        for _, row in colwidths.iterrows():
            col_idx = list(rpt_df.columns).index(row['colname']) + 1
            ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = row['width']

    # Create directory if it doesn't exist
    os.makedirs(dirpath, exist_ok=True)

    # Save file
    try:
        wb.save(my_fn)
    except PermissionError:
        # Try with timestamp
        my_new_fn = f"{datetime.now().strftime('%H:%M:%S')}-{my_fn}"
        try:
            wb.save(my_new_fn)
            # Send email about alternate filename
            body_text = f"""This is an automated email to inform you that it appears <b>the following file WAS SAVED 
                         WITH AN ALTERNATE FILENAME</b> during the <b>{REPORT_NAME}</b> routine.<br/><br/>
                         {my_new_fn}<br/><br/>It appears that the original filename ({fname}) was open in another process or locked.
                         <br/><br/>The routine should continue.<br/> <br/>{WARN_SIGNATURE}"""
            send_email(WARN_RECIPIENTS, f"{REPORT_NAME} : REPORT FILE SAVING ERROR", body_text, inline=True)
        except:
            # Send email about failed save
            body_text = f"""This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> 
                         during the <b>{REPORT_NAME}</b> routine.<br/><br/>{my_fn}<br/><br/>
                         Either the path wasn't accessible or the file was open in another process.
                         <br/><br/>The routine should continue without saving this file.<br/> <br/>{WARN_SIGNATURE}"""
            send_email(WARN_RECIPIENTS, f"{REPORT_NAME} : REPORT FILE SAVING ERROR", body_text, inline=True)

# Check for list columns
def check_list_columns(test_df: pd.DataFrame) -> List[str]:
    my_results = ["Data missing or no column names/indices supplied"]
    if isinstance(test_df, pd.DataFrame):
        my_results = []
        for col in test_df.columns:
            if test_df[col].apply(lambda x: isinstance(x, list)).any():
                list_types = test_df[col].apply(type).value_counts()
                error_type = list_types.index[0]
                error_positions = test_df[test_df[col].apply(lambda x: isinstance(x, list))].index + 1
                error_message = f"{col}: check for {error_type} entry at row{'s' if len(error_positions) > 1 else ''} {', '.join(map(str, error_positions))}"
                print(error_message)
                my_results.append(error_message)
    return my_results

# Snowflake Insert Schedule
def snowflake_insert_sched(data: pd.DataFrame, schema: str, tablename: str, stores: List[int],
                          start_date: datetime, end_date: datetime, date_col_name: str) -> pd.DataFrame:
    my_stores_query = ','.join(map(str, stores))
    
    if not isinstance(start_date, str):
        start_date = start_date.strftime("%Y-%m-%d")
    if not isinstance(end_date, str):
        end_date = end_date.strftime("%Y-%m-%d")

    # Get Snowflake connection
    conn = get_snowflake_connection()
    cursor = conn.cursor()

    # Check existing records
    myquery_select = f"""
        select count(*)
        from {schema}.{tablename}
        where STORE in ({my_stores_query})
        and trunc({date_col_name},'day') >= to_date('{start_date}','YYYY-MM-DD')
        and trunc({date_col_name},'day') <= to_date('{end_date}','YYYY-MM-DD')
    """
    cursor.execute(myquery_select)
    select_cnt = cursor.fetchone()[0]

    # Delete existing records
    myquery_delete = f"""
        delete from {schema}.{tablename}
        where STORE in ({my_stores_query})
        and trunc({date_col_name},'day') >= to_date('{start_date}','YYYY-MM-DD')
        and trunc({date_col_name},'day') <= to_date('{end_date}','YYYY-MM-DD')
    """
    cursor.execute(myquery_delete)
    delete_cnt = cursor.rowcount

    if delete_cnt != select_cnt:
        conn.rollback()
        cursor.close()
        conn.close()
        return pd.DataFrame({'STORE': stores, 'ISSUE': 'DELETE FAILED, No Insert'})
    else:
        conn.commit()

        # Get count before insert
        cursor.execute(myquery_select)
        select_cnt_pre = cursor.fetchone()[0]

        # Insert new records
        my_curr_load = data[data['STORE'].isin(stores)]
        for _, row in my_curr_load.iterrows():
            cursor.execute(f"""
                INSERT INTO {schema}.{tablename} 
                VALUES ({','.join(['%s'] * len(row))})
            """, tuple(row))

        # Get count after insert
        cursor.execute(myquery_select)
        select_cnt_post = cursor.fetchone()[0]

        myload_numrows = select_cnt_post - select_cnt_pre
        mydata_numrows = len(my_curr_load)

        if myload_numrows != mydata_numrows:
            # Get load counts by STORE
            myquery_loaded = f"""
                select STORE, COUNT(*) AS LOADED
                from {schema}.{tablename}
                where STORE in ({my_stores_query})
                and trunc({date_col_name},'day') >= to_date('{start_date}','YYYY-MM-DD')
                and trunc({date_col_name},'day') <= to_date('{end_date}','YYYY-MM-DD')
                group by STORE
            """
            cursor.execute(myquery_loaded)
            my_load_stores_curr = pd.DataFrame(cursor.fetchall(), columns=['STORE', 'LOADED'])
            my_load_stores_expect = my_curr_load.groupby('STORE').size().reset_index(name='n')
            my_load_stores_failed = pd.merge(my_load_stores_expect, my_load_stores_curr, on='STORE', how='left')
            my_load_stores_failed = my_load_stores_failed[
                (my_load_stores_failed['LOADED'] != my_load_stores_failed['n']) | 
                my_load_stores_failed['LOADED'].isna()
            ]
            return pd.DataFrame({'STORE': my_load_stores_failed['STORE'], 'ISSUE': "Didn't load 1 or more rows of data"})

        cursor.close()
        conn.close()
        return pd.DataFrame()

# Main execution
if __name__ == "__main__":
    # Initialize Google API credentials
    creds = None
    if os.path.exists('token.json'):
        with open('token.json', 'r') as token:
            creds = Credentials.from_authorized_user_info(json.load(token))
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                'credentials.json', ['https://www.googleapis.com/auth/gmail.send'])
            creds = flow.run_local_server(port=0)
        with open('token.json', 'w') as token:
            token.write(creds.to_json())

    # Initialize Google Sheets API
    sheets_service = build('sheets', 'v4', credentials=creds)
    drive_service = build('drive', 'v3', credentials=creds)

    # Get spreadsheet
    spreadsheet = sheets_service.spreadsheets().get(spreadsheetId=GDRIVE_MAIN_URL.split('/')[-1]).execute()
    
    # Get sheet names
    sheet_names = [sheet['properties']['title'] for sheet in spreadsheet['sheets']]
    
    # Get current week's sheet name
    conn = get_snowflake_connection()
    cursor = conn.cursor()
    cursor.execute(f"""
        select 'P'||PERIOD_NUM||'W'||WEEK_NUM||period_year as sheetname
        from corporate.mp_calendar_weekly
        where s_date <= to_date('{QUERY_STARTDATE}','DD-MON-YY')
        and e_date >= trunc(to_date('{QUERY_STARTDATE}','DD-MON-YY'),'day')
    """)
    current_sheet = cursor.fetchone()[0]
    cursor.close()
    conn.close()

    if current_sheet not in sheet_names:
        body_text = f"""<p>This is an automated email to inform you that it appears that 
                     the expected Google sheet is missing in the {REPORT_NAME} routine! </p>
                     <p>The routine is aborting without an update.</p> 
                     <b>Google File Info:</b><ul>
                     <li>Google filename: {spreadsheet['properties']['title']}</li>
                     <li>File URL: {GDRIVE_MAIN_URL}</li>
                     <li>Sheetname expected (not found): <font color="red">{current_sheet}</font></li>
                     <li>FYI, the first 8 sheets in the file are: {', '.join(sheet_names[:8])}</li>
                     </ul></p>{WARN_SIGNATURE}"""
        send_email(WARN_RECIPIENTS, f"{REPORT_NAME} Issue: Sheet Not Found In Google File", body_text, inline=True)
        sys.exit(1)

    # Read sheet data
    result = sheets_service.spreadsheets().values().get(
        spreadsheetId=GDRIVE_MAIN_URL.split('/')[-1],
        range=f"{current_sheet}!A1:Z1000"
    ).execute()
    
    values = result.get('values', [])
    if not values:
        print('No data found.')
        sys.exit(1)

    # Convert to DataFrame
    df = pd.DataFrame(values[1:], columns=values[0])
    
    # Check for list columns
    bad_columns = check_list_columns(df)
    if bad_columns:
        rpt_url = f"{GDRIVE_SHEET_PREFIX}{GDRIVE_MAIN_URL.split('/')[-1]}/edit#gid={spreadsheet['sheets'][0]['properties']['sheetId']}"
        body_text = f"""<p>This is an automated email to inform you that it appears that 
                     one or more expected columns of the Google sheet had unexpected 
                     data during the '{REPORT_NAME}' routine! </p>
                     <p>The routine is aborting without an update.</p> 
                     <p>Check the following column(s) in the '
                     <a href="{rpt_url}">{current_sheet}</a>
                     ' sheet for improper data 
                     (text in number columns or vice versa): <br><br><b>
                     {chr(10).join(bad_columns)}
                     </b></p>{WARN_SIGNATURE}"""
        send_email(WARN_RECIPIENTS, f"{REPORT_NAME} Issue: Issue with Data in Google Sheet", body_text, inline=True)
        sys.exit(1)

    # Check for duplicate stores
    stores_present = df[GSHEET_STORE_COLNAME].astype(int)
    dup_stores = stores_present[stores_present.duplicated()].unique()
    if len(dup_stores) > 0:
        rpt_url = f"{GDRIVE_SHEET_PREFIX}{GDRIVE_MAIN_URL.split('/')[-1]}/edit#gid={spreadsheet['sheets'][0]['properties']['sheetId']}"
        body_text = f"""<p>This is an automated email to inform you that it appears that 
                     there are one or more duplicated restaurant #s in the Google sheet 
                     data during the '{REPORT_NAME}' routine! </p>
                     <p>The routine is aborting without an update.</p> 
                     <p>Check the following restaurant #s in the '
                     <a href="{rpt_url}">{current_sheet}</a>
                     ' sheet for multiple rows: <br><br><b>
                     {chr(10).join(map(str, dup_stores))}
                     </b></p>{WARN_SIGNATURE}"""
        send_email(WARN_RECIPIENTS, f"{REPORT_NAME} Issue: Issue with Data in Google Sheet", body_text, inline=True)
        sys.exit(1)

    # Process data for Snowflake load
    results_sched = pd.DataFrame()
    results_notes = pd.DataFrame()
    results_missing = pd.DataFrame()

    # Process daily data
    for i in range(7):
        curr_date = REPORT_STARTDATE + timedelta(days=i)
        curr_dow = curr_date.strftime("%A")
        curr_fore_colname = f"{curr_dow}{DAY_FORE_APPEND}"
        curr_sched_colname = f"{curr_dow}{DAY_SCHED_APPEND}"
        
        day_data = df[[GSHEET_STORE_COLNAME, curr_fore_colname, curr_sched_colname]].copy()
        day_data['BUS_DATE'] = curr_date
        day_data.columns = ['STORE', 'FORE_SALES', 'SCHED_HOURS', 'BUS_DATE']
        
        # Filter out incomplete rows
        day_data_comp = day_data.dropna()
        day_data_comp = day_data_comp.sort_values('STORE')
        
        # Track missing stores
        day_data_missing = set(stores_present) - set(day_data_comp['STORE'])
        if day_data_missing:
            curr_missing = pd.DataFrame({
                'STORE': list(day_data_missing),
                'MISSING': curr_date.strftime("%a")
            })
            results_missing = pd.concat([results_missing, curr_missing])
        
        results_sched = pd.concat([results_sched, day_data_comp])

    # Process notes data
    curr_notes = df[[GSHEET_STORE_COLNAME, GSHEET_OT_COLNAME, GSHEET_GM_CORRECT_COLNAME, GSHEET_APPROVED_COLNAME]].copy()
    curr_notes['E_DATE'] = REPORT_STARTDATE + timedelta(days=6)
    curr_notes.columns = ['STORE', 'SCHED_HOURS_OT', 'GM_DAYS_HOURS', 'DM_APPROVED', 'E_DATE']
    
    results_notes = curr_notes.dropna(subset=['STORE', 'E_DATE', 'GM_DAYS_HOURS', 'DM_APPROVED'])
    results_notes['SCHED_HOURS_OT'] = results_notes['SCHED_HOURS_OT'].fillna(0)
    
    notes_missing_stores = set(stores_present) - set(results_notes['STORE'])
    if notes_missing_stores:
        notes_missing = pd.DataFrame({
            'STORE': list(notes_missing_stores),
            'MISSING': 'GM days/hours or DM Approval'
        })
        results_missing = pd.concat([results_missing, notes_missing])

    # Handle missing data email
    if len(results_missing) > 0 and EMAIL_MISSING:
        missing_stores = results_missing.groupby('STORE')['MISSING'].agg(lambda x: ', '.join(x)).reset_index()
        missing_email_tbl = pd.merge(
            missing_stores,
            df[[GSHEET_STORE_COLNAME, *GSHEET_STORE_META_COLNAME]],
            left_on='STORE',
            right_on=GSHEET_STORE_COLNAME
        ).sort_values(['RDO', 'DM', 'STORE'])
        
        rpt_url = f"{GDRIVE_SHEET_PREFIX}{GDRIVE_MAIN_URL.split('/')[-1]}/edit#gid={spreadsheet['sheets'][0]['properties']['sheetId']}"
        body_text = f"""<h2>{REPORT_NAME}: Missing Data </h2>
                     <p>The following stores were missing 
                     <a href="{rpt_url}">Schedule Checker ({current_sheet})</a>
                     data as noted. 
                     {f"<b>The data IS BEING LOADED and future updates to the sheet will no longer be included in the data pull for Tableau.</b>"
                     if LOAD_DB else "This is just a status update for your benefit, the data IS NOT being loaded at this time."}
                     </p>
                     <p>Criteria for inclusion in this report:<ul>
                     <li>Any day listed was missing either the 
                     forecasted $ or the scheduled hours</li>
                     <li>Either of the 
                     GMs working correct days/hours or the DM approved 
                     columns are blank</li>
                     </ul></p>
                     {tabulate.tabulate(missing_email_tbl, headers='keys', tablefmt='html')}
                     <br><br>{NORM_SIGNATURE}"""
        
        send_email(
            RDO_RECIPIENTS + DM_RECIPIENTS,
            f"{REPORT_NAME}: Missing Data",
            body_text,
            inline=True,
            test=TESTING_EMAILS,
            test_recipient=TEST_RECIPIENTS
        )

    # Load to Snowflake if appropriate
    if OKAY_TO_CONTINUE and LOAD_DB and (len(results_sched) + len(results_notes) > 0):
        my_delete_stores = list(set(results_sched['STORE'].unique()) | set(results_notes['STORE'].unique()))
        my_num_loops = (len(my_delete_stores) + 999) // 1000  # Ceiling division
        
        for i in range(my_num_loops):
            mystart = i * 1000
            mylimit = min(len(my_delete_stores), (i + 1) * 1000)
            my_stores_curr = my_delete_stores[mystart:mylimit]
            
            # Load daily results
            rs_load_daily = snowflake_insert_sched(
                results_sched,
                SF_SCHEMA,
                "MP_SCHED_CHECK_DAILY",
                my_stores_curr,
                datetime.strptime(QUERY_STARTDATE, "%d-%b-%y"),
                datetime.strptime(QUERY_ENDDATE, "%d-%b-%y"),
                "BUS_DATE"
            )
            
            # Load notes results
            rs_load_notes = snowflake_insert_sched(
                results_notes,
                SF_SCHEMA,
                "MP_SCHED_CHECK_NOTES",
                my_stores_curr,
                datetime.strptime(QUERY_STARTDATE, "%d-%b-%y"),
                datetime.strptime(QUERY_ENDDATE, "%d-%b-%y"),
                "E_DATE"
            )
            
            # Handle load errors
            if len(rs_load_daily) > 0 or len(rs_load_notes) > 0:
                mydata_load_issues = pd.concat([rs_load_daily, rs_load_notes])
                
                # Write issues to Excel
                my_xlsx_colwidths = pd.DataFrame({
                    'colname': ['STORE', 'ISSUE'],
                    'width': [9.5, 42]
                })
                
                write_xlsx(
                    REPORT_PATH,
                    f"{REPORT_NAME}.xlsx",
                    QUERY_DATE,
                    mydata_load_issues,
                    colwidths=my_xlsx_colwidths
                )
                
                # Send load error email
                rpt_url = f"{GDRIVE_SHEET_PREFIX}{GDRIVE_MAIN_URL.split('/')[-1]}/edit#gid={spreadsheet['sheets'][0]['properties']['sheetId']}"
                body_text = f"""<h2>REPORT: {REPORT_NAME}</h2>
                             <p>See the attached Excel file for details on issues 
                             noted below for the 
                             <a href="{rpt_url}">Schedule Checker ({current_sheet})</a>
                             load.</p>"""
                
                if len(rs_load_daily) > 0:
                    body_text += f"""<h3>There were {len(rs_load_daily)}
                                 stores with errors loading the 'MP_SCHED_CHECK_DAILY' table.</h3> 
                                 <br>"""
                
                if len(rs_load_notes) > 0:
                    body_text += f"""<h3>There were {len(rs_load_notes)}
                                 stores with errors loading the 'MP_SCHED_CHECK_NOTES' table.</h3> 
                                 <br>"""
                
                body_text += WARN_SIGNATURE
                
                send_email(
                    WARN_RECIPIENTS,
                    f"{REPORT_NAME}: Load Issues",
                    body_text,
                    inline=True,
                    test=TESTING_EMAILS,
                    test_recipient=TEST_RECIPIENTS
                ) 