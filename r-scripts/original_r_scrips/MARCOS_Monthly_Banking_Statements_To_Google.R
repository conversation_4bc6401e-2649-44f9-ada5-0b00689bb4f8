
library(DBI)
library(RO<PERSON>le)
library(odbc)
library(keyring)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(xtable)
library(lubridate)
library(formattable)
library(stringr)
library(tidyverse)
library(ggthemes)
library(grid)
library(gridExtra)
library(gtable)
library(scales)
library(mime)
library(googledrive)
library(googlesheets4)
library(openxlsx)
library(data.table)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE
overwrite_results <- FALSE #NORMAL
#overwrite_results <- TRUE #only when you desired to replace any/all existing files for the period


# Version ********

### ******** change:
### moved to Snowflake DB connection and tables

### ******** change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of ******** SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced Signature logo from local file to published image URL (to avoid inline image attachment)

### version: ********
### added warning when bank statements not found in last 5 days of month...also
### fixed bug when files present, but none are from desired month. In that case,
### routine was continuing to attempt move, but no files in attempt list caused error.


### Version: ********
### fixed bug where boolean was false instead of FALSE

### Version: ********
### added in log file to track if monthly run was completed

### Version: ********
### new file based on ******** version of 'MARCOS_Monthly_Banking_Trans_To_PDF' file
### this file copies monthly bank statement files (from stmtpath path) to the appropriate
### to the appropriate store's Google Drive folder. This routine will also expect a
### file for each store that has transactions in the AC_BANK table

### Version: ********
### beta version

### ******** 
### new file, alpha version


# Parameters
options(stringsAsFactors = FALSE)

#P:\steveo\R Stuff\ReportFiles\MARCOS_Monthly_Banking
query_date <- Sys.Date() #other variables adjust to only query month prior to this
#query_date <- floor_date(query_date, "month") - months(1)  #test or manual run only
myReportName <- "MARCOS Monthly Banking Statements To Google"
scriptfolder <- "MARCOS_Monthly_Banking"
rptfolder <- "Plots"
stmtfolder <- "Bank Statements"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
date.header.text <- paste0("Updated ", format(query_date, "%m-%d-%Y"))
report.monthyear.txt <- format(floor_date(query_date, "month") - months(1), "%B %Y")
report.yyyymm.txt <- format(floor_date(query_date, "month") - months(1), "%Y%m")
report.yymm.txt <- format(floor_date(query_date, "month") - months(1), "%y%m")
report.loglimit <- format(floor_date(query_date, "month") - months(6), "%y%m") #only log rows >= this are retained to prevent log bloat
report.date.txt <- format(Sys.Date(), "%Y%m%d") #actual date routine runs
report.time.txt <- format(Sys.time(), "%H%M%S%Z") #time routine runs
sql_sdate <- format(floor_date(query_date, "month") - months(1),"%d-%b-%y")
sql_edate <- format(floor_date(query_date, "month"),"%d-%b-%y")
old_dates_delete <- seq(query_date - 56, query_date - 29, by = "1 days") %>% format("%Y%m%d")

okaytocontinue <- TRUE

logname <- paste0(myReportName," - LOG.csv")
store_log <- data.frame(Location = integer(), Time = character(), Uploaded = logical(), Result=character()) #%>% 
store_files <- data.frame(Filename = character(), StoreNum = integer()) #%>% 
store_files_found <- FALSE

# Google sheets/Drive parameters, create empty df then add rows with needed info
#gSht_auth_email <- "<EMAIL>"
gSht_auth_email <- "<EMAIL>"
#gDrv_mainURL <- 'https://drive.google.com/drive/folders/1eHkd3j2U8oo18ew68m81BSIQM2A0eNIP' #"Bank Rec's" folder
gDrv_mainURL <- 'https://drive.google.com/drive/folders/1C-859i8gzfkFAC7JUBkBKzaCmOAEkI7H' # Regionals Bank Recs
gDrv_destinationfoldername <- 'Bank Statement' #sometimes it's plural for some locations, but API call is pattern match so that should be okay depending on case


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("Maria Lotz<<EMAIL>>", "Steve Olson<<EMAIL>>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("Maria Lotz<<EMAIL>>", "Steve Olson<<EMAIL>>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("Steve Olson<<EMAIL>>")
sig_logo <- FALSE

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

#if(testing_pc){
# Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
#  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
#  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
#}
# ******** change, always run this using network paths for files (NOT local)
logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)

#routine specific paths
plotpath <- file.path(logpath, rptfolder)
stmtpath <- file.path(logpath, stmtfolder)
myReportPath <- file.path(logpath, rptfolder)

#append signature logo
#(HVLTD Corp with Brands)
sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
if(exists("norm_st_from")){norm_st_from <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_sig")){norm_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_RM_DM_from")){norm_RM_DM_from <- paste0(norm_RM_DM_from, "<br/>", sig_image_src)}



### define some functions ###


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ='America/Chicago')
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)




### log file
#update function
update_log <- function(mydata){
  #logdata <- paste0("\n", paste0(myvector,collapse = ","))
  #logdata <- paste0(myvector,collapse = ",")
  #cat(logdata, file = logfn, sep = ",", append = TRUE)
  write.table(mydata, file = logfn, sep = ",", row.names = FALSE, col.names = FALSE, append = TRUE)
}
### check if log present/up-to-date ###
logfn <- file.path(logpath, logname)
loghdr <- data.frame(RPT_PERIOD = character(), TIME = POSIXct(tz = Sys.timezone()), EVENT = character(), DESCRIPTION = character())
logdata <- loghdr %>% add_row(
  RPT_PERIOD = report.yymm.txt,
  TIME = Sys.time(),
  EVENT = "Routine Start",
  DESCRIPTION = paste0("PARAMS - Testing Emails: ", testing_emails, "; Overwrite Results: ", overwrite_results)
)

#Check if run already succeeded this month and trim old log entries from file if needed
if(file.exists(logfn) ) {
  MyLog <- read.csv(file = logfn, sep=",", stringsAsFactors = FALSE)
  #remove rows earlier than report.loglimit to prevent log file bloat as save updated file
  NewLog <- MyLog %>%
    dplyr::filter(RPT_PERIOD >= report.loglimit)
  if(nrow(MyLog)>nrow(NewLog)){
    #replace existing log with reduced rows
    write.table(NewLog, file = logfn, sep = ",", row.names = FALSE, col.names = TRUE, append = FALSE)
  }
  if(overwrite_results == FALSE){
    #check if log indicates completed run this month
    lastrun <- MyLog %>%
      dplyr::filter(RPT_PERIOD == report.yymm.txt) %>%
      dplyr::filter(EVENT == "Routine End") %>%
      dplyr::filter(DESCRIPTION == "Success")
    if(nrow(lastrun)>0){
      #completed okay previously and no overwrite requested...ABORT
      okaytocontinue <- FALSE
    }
  }
  if(okaytocontinue){
    #add new row for routine start
    update_log(logdata)
  }
  
} else {
  # log not found, create new log values
  write.table(logdata, file = logfn, sep = ",", row.names = FALSE, col.names = TRUE, append = FALSE)
}


#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}



check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



curr_time <- function(){
  t <- Sys.time() %>% format("%m/%d/%Y %H:%M:%S %Z")
  return(t)
}


numeric_to_currency <- function(x, na.rm = FALSE) format(x, big.mark=',', nsmall = 2)


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext,
             test = testing_emails, 
             testrecipient = test_recip
    )
  }
}







###Google OAuth and main folder verify
if(okaytocontinue){
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Google Drive Access",
    DESCRIPTION = paste0("Checking...PARAMS - Auth Email: ", gSht_auth_email, "; Base Folder URL: ", gDrv_mainURL)
  )
  update_log(logdata)
  isFolder <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was for folder
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isFolder <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("folder")
    if(!isFolder){
      okaytocontinue <- FALSE
    }else{
      mySearch <- drive_ls(drv_get_main$id, type = "folder", recursive = FALSE) #dribble of drv_get_main folders
      if(nrow(mySearch)>0){
        #for(i in 1:nrow(mySearch)){
        #  temp_search <- drive_ls(path = mySearch$id[i], type = "folder", recursive = TRUE)
        #temp_search <- drive_ls(path = drv_get_main$id, type = "folder", pattern = '3701 - ', recursive = TRUE)
        #  if(i==1){
        #    myFolders <- temp_search
        #  }else{
        #    myFolders <- rbind(myFolders, temp_search)
        #  }
        #}
      }else{
        okaytocontinue <- FALSE
      }
    }
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file folder for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Folder URL resolved: ", isFolder, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Access Issue"),
             body = bodytext,
             test = testing_emails, 
             testrecipient = test_recip
    )
    gdrv_status <- paste0("FAILED...PARAMS - Drive token okay: ", drive_has_token(), "; Folder URL resolved: ", isFolder)
  }else{
    gdrv_status <- "Success"
  }
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Google Drive Access",
    DESCRIPTION = gdrv_status
  )
  update_log(logdata)
}



if(okaytocontinue){
  # check if statement files are present with desired prefix
  
  stmt_status <- "Checking"
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Statement File Status",
    DESCRIPTION = stmt_status
  )
  update_log(logdata)
  
  if(dir.exists(stmtpath)) {
    filestocheck <- dir(stmtpath, full.names = FALSE)
    filestocheck <- dir(path = stmtpath, pattern = paste0(report.yymm.txt, "*"), full.names = FALSE)
    if(length(filestocheck) >  0){
      #unlink(filestocheck, recursive = FALSE, force = TRUE)
      #check each file for expected name...if it matches, add store to dataframe
      myfns <- filestocheck[filestocheck %like% report.yymm.txt && lapply(filestocheck, function(x) is.na(as.integer(substring(x, 5, 8)))) == FALSE]
      mystorenums <- unlist(lapply(myfns, function(x) as.integer(substring(x, 5, 8))))
      if(length(myfns) > 0 && length(myfns)==length(mystorenums) ){
        store_files <- data.frame(Filename = myfns, StoreNum = mystorenums)
        store_files_found <- TRUE
        stmt_status <- "Success"
      }else{
        okaytocontinue <- FALSE
        if(length(myfns) > 0){
          stmt_status <- paste0("FAILED...", length(myfns), " files found, but ", length(mystorenums), " store numbers extracted from those filenames")
        }else{
          stmt_status <- paste0("FAILED...No files found for period '", report.yymm.txt, "'")
        }
      }
    }else{
      #no files found, abort
      okaytocontinue <- FALSE
      stmt_status <- paste0("FAILED...No files found for period '", report.yymm.txt, "'")
    }
  }else{
    stmt_status <- paste0("FAILED...Directory not found: ",stmtpath)
  }
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Statement File Status",
    DESCRIPTION = stmt_status
  )
  update_log(logdata)
  
  if(store_files_found == FALSE && query_date >= ceiling_date(query_date, 'month') - days(5)){
    #within last 5 days of month, send warning email to add files by end of month (and routine starts attempting next month)
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "aren't any bank statements with filenames beginning with ", report.yymm.txt,
                       " present for the ",
                       myReportName, " routine! </p>",
                       "<p>After the last day of the month the routine will no ",
                       "longer move bank statements for last month! Please ",
                       "copy needed files this folder: ",
                       "<a href=\"", stmtpath, "\">", stmtpath, "</a> ",
                       "</p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Monthly Files Not Found"),
             body = bodytext,
             test = testing_emails, 
             testrecipient = test_recip
    )
    logdata <- loghdr %>% add_row(
      RPT_PERIOD = report.yymm.txt,
      TIME = Sys.time(),
      EVENT = "Statement File Status",
      DESCRIPTION = paste0("End of month warning sent to: ", paste(norm_recip, collapse = ", "))
    )
    update_log(logdata)
  }
}





###Get expected Bank Transactions and create .PDF for each store###
if(okaytocontinue){
  upload_status <- "Checking Bank Transactions"
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Upload Files",
    DESCRIPTION = upload_status
  )
  update_log(logdata)
  myquery <- paste0(
    '
      Select bank.id
      , bank.store_number as "Location"
      , to_char(bank.t_date, \'mm/dd/yy  hh:mi AM\') as "Date"
      , BANK.paynum as "Emp #"
      , upper(emp.fname)||\' \'||upper(emp.lname) as "Employee"
      , reason as "Description"
      --, to_char(amount, \'999,999.99\') as "Amount"
      , amount as "Amount"
      , r_date
      from CORPORATE.AC_BANK BANK
      inner join CORPORATE.HR_LOCATIONS_ALL hla
      on lpad(bank.store_number,4,\'0\') = hla.location_code
      left join CORPORATE.AB_EMPLOYEES emp on bank.paynum = emp.paynum
      where BANK.T_DATE >= to_date(\'', sql_sdate, '\',\'dd-mon-yy\')
      and BANK.T_DATE < to_date(\'', sql_edate, '\',\'dd-mon-yy\')
      and (hla.inactive_date is null or hla.inactive_date >= to_date(\'', sql_sdate, '\',\'dd-mon-yy\'))
      --and bank.store_number < 3510          /*TEST ONLY*/
      order by bank.store_number, bank.t_date
    '
  )
  #********: bank_trans <- dbGetQuery(myOracleDB, myquery)
  bank_trans <- dbGetQuery(mySfDB, myquery)
  
  curr_stores <- c(bank_trans$Location, store_files$StoreNum) %>% unique()
  stmt_stores <- store_files$StoreNum %>% unique()
  bank_stores <- bank_trans$Location %>% unique()
  stores_stmt_notrans <- setdiff(stmt_stores, bank_stores)
  stores_trans_nostmt <- setdiff(bank_stores, stmt_stores)
  
  for(i in 1:nrow(store_files)){
    store <- store_files$StoreNum[i]
    curr_FN <- store_files$Filename[i]
    store_saved <- FALSE
    store_issue <- ""
    upload_status <- paste0("Uploading store: ", store, "; filename: ", curr_FN)
    logdata <- loghdr %>% add_row(
      RPT_PERIOD = report.yymm.txt,
      TIME = Sys.time(),
      EVENT = "Upload Files",
      DESCRIPTION = upload_status
    )
    update_log(logdata)
    
    
    myPDFFile <- file.path(stmtpath, curr_FN)
    #push file to location's Google folder
    if(file.exists(myPDFFile)){
      
      
      #check if store report folder present
      find_pattern <- paste0("name contains '", store, " - '")
      store_folder <- drive_find(q = find_pattern, q = "mimeType = 'application/vnd.google-apps.folder'", q = "trashed = false")
      mydata_status <- check_mydf_rows(store_folder, MinNumRows = 1, ReportName = myReportName)
      
      if(mydata_status[[1]]){
        #folder found, check if just one
        if(nrow(store_folder) > 1){
          #more than one folder found, log issue
          store_issue <- paste0(nrow(store_folder), " folders beginning with '", store, " - ' were found. File NOT saved, unsure which is correct.")
        }else{
          #just one folder identified, proceed
          report_folder <- drive_ls(path = store_folder$id, type = "folder", pattern = gDrv_destinationfoldername)
          mydata_status_rptfolder <- check_mydf_rows(report_folder, MinNumRows = 1, ReportName = myReportName)
          if(mydata_status_rptfolder[[1]]){
            rs_upload <- drive_put(myPDFFile, path = report_folder$id[[1]], name = curr_FN)
            mydata_status_upload <- check_mydf_rows(rs_upload, MinNumRows = 1, ReportName = myReportName)
            if(mydata_status_upload[[1]]){
              #file apparently saved
              store_saved <- TRUE
              store_issue <- rs_upload$drive_resource[[1]]$webViewLink
            }else{
              #issue saving file, log issue
              store_issue <- "Issue uploading file: Destination folder was found, but upload failed"
            }
          }else{
            #destination folder not found, log issue
            store_issue <- paste0("Issue uploading file: '", gDrv_destinationfoldername, "(s)' folder NOT found! Check for proper case, spaces. Store folder: ",store_folder$drive_resource[[1]]$webViewLink)
          }
        }
      }else{
        #folder not found
        store_issue <- paste0("Unable to locate folder beginning with '", store, " - ', check for proper case, spaces. File NOT saved.")
        okaytocontinue <- FALSE
      }
    }else{
      #local file does not exist, add to exception report
      store_issue <- "Error creating/saving .PDF (prior to upload)"
    }
    #log store results
    store_log <- store_log %>% add_row(Location = as.integer(store), Time = curr_time(), Uploaded = store_saved, Result = store_issue)
  }
  
  #Save log file and email results
  #logpath
  upload_status <- paste0("Saving results file")
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Upload Files",
    DESCRIPTION = upload_status
  )
  update_log(logdata)
  
  
  
  #specify report column widths where alternate width desired
  myXLSXColWidths <- data.frame (
    colname  = c(
      "Location",
      "Time",
      "Uploaded",
      "Result"
      #"",
    )
    ,
    width = c(
      10,
      23,
      10.5,
      if(max(nchar(na.omit(store_log[,"Result"]))) > 78){min(120, max(nchar(na.omit(store_log[,"Result"]))))}else{83}
    )
    #,
    #stringsAsFactors = FALSE
  ) #myXLSXColWidths
  myFN <- paste0(myReportName, " - ", report.yyyymm.txt, " RESULTS.xlsx")
  mySN <- paste0(report.date.txt, ' ',report.time.txt)
  writeXLSX(dirpath = logpath, fname = myFN, sname = mySN,  RptDF = store_log, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
  myemailfiles <- file.path(logpath, myFN)
  
  result_cnt_success <- sum(store_log$Uploaded, na.rm = TRUE)
  result_cnt_fail <- nrow(store_files) - result_cnt_success
  result_success_dec <- result_cnt_success/(result_cnt_success + result_cnt_fail)
  
  
  
  
  if(result_cnt_fail > 0){
    if(result_cnt_fail > 1){
      bodytext_fail <- paste0(
        '<p><font color="red"><strong>These ', result_cnt_fail, " locations"," DID NOT upload properly:</strong><br>",
        paste0(store_log$Location[which(store_log$Uploaded == FALSE)], collapse = ", "),
        "<br>See attached file for details.</font></p>"
      )
    }else{
      bodytext_fail <- paste0(
        '<p><font color="red"><strong>Location #', 
        paste0(store_log$Location[which(store_log$Uploaded == FALSE)], collapse = ", "),
        " DID NOT upload properly! See attached file for details.</strong></font></p>"
      )
    }
    
  }else{
    bodytext_fail <- ""
  }
  
  
  if(length(stores_stmt_notrans)>0){
    #stores had a statement file, but no transactions in the database
    if(length(stores_stmt_notrans) > 1){
      bodytext_fail <- paste0(
        bodytext_fail,
        '<p><font color="red"><strong>These ', length(stores_stmt_notrans), " locations"," DID NOT have any database transactions:</strong><br>",
        paste0(stores_stmt_notrans, collapse = ", "),
        "<br>They won't have a corresponding 'Bank Transaction' file for this month!</font></p>"
      )
    }else{
      bodytext_fail <- paste0(
        bodytext_fail,
        '<p><font color="red"><strong>Location #', 
        paste0(stores_stmt_notrans, collapse = ", "),
        " had a bank statement, but no transactions in the database. They won't have corresponding 'Bank Transaction' files for this month!</strong></font></p>"
      )
    }
  }
  
  
  if(length(stores_trans_nostmt)>0){
    #stores had a statement file, but no transactions in the database
    if(length(stores_trans_nostmt) > 1){
      bodytext_fail <- paste0(
        bodytext_fail,
        '<p><font color="red"><strong>These ', length(stores_trans_nostmt), " locations"," DID NOT have bank statements present:</strong><br>",
        paste0(stores_trans_nostmt, collapse = ", "),
        "<br>They had bank transactions in the database, but NO BANK STATEMENTS to upload in this routine!</font></p>"
      )
    }else{
      bodytext_fail <- paste0(
        bodytext_fail,
        '<p><font color="red"><strong>Location #', 
        paste0(stores_trans_nostmt, collapse = ", "),
        " had bank transactions in the database, but NO BANK STATEMENT to upload in this routine!</strong></font></p>"
      )
    }
  }
  
  bodytext <- paste0("<h2>", myReportName, "</h2>",
                     "<h3>", report.monthyear.txt, " Transactions</h3>",
                     bodytext_fail,
                     "</p>",
                     result_cnt_success,
                     " files were uploaded successfully. See attached file for links to each.",
                     "</p>",
                     "<br><br>",
                     norm_sig,
                     "<br/><br/>This script located in the Tableau/R computer at: ",
                     logpath
  )
  rs <- mailsend(recipient = norm_recip,
                 subject = paste0(myReportName),
                 body = bodytext,
                 if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                 inline = sig_logo,
                 test = testing_emails, testrecipient = test_recip
  )
  
  #log final routine status
  if(result_success_dec >= 0.75){
    upload_status <- paste0("Success")
  }else{
    upload_status <- paste0("FAILED...", label_percent()(result_success_dec), " of files uploaded.")
  }
  logdata <- loghdr %>% add_row(
    RPT_PERIOD = report.yymm.txt,
    TIME = Sys.time(),
    EVENT = "Routine End",
    DESCRIPTION = upload_status
  )
  update_log(logdata)
  
  myemailfiles <- NA
  
}







