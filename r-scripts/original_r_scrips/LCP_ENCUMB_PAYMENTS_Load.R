library(xtable)
library(reshape2)
library(dplyr)
library(tidyr)
#library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR)
library(gmailr) #replaces prior MailR package which used SMTP, gmailr uses OAuth and Google APIs
library(magrittr) #forward-pipe operator %>% (part of Tidyverse)
library(purrr) #used for attachments to emails
library(stringr)
library(utils)
library(keyring)
library(googledrive)
library(googlesheets4)
library(DBI)
#library(ROracle)
library(odbc)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20250131

### 20250131 change:
### added Monthly Interest and Annual Interest columns to Gsht and DB table

### 20250127 change:
### added SQL logic to prevent multiple loan rows in Google sheet when 
### multiple CURRENTOWED values are present
### added email notification when a new load is added to the Google sheet

### 20250108 change:
### bug fixes in email signature creation

### 20241209 change
### completed conversion to Snowflake, changed email send from to generic Legacy Reporting email

### 20241016 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### updated email signature to use latest format provided by Stephanie Forsberg earlier in 2024
### replaced check_mydata_rows() function with more universal check_mydf_rows()

### 20230807 change:
### new file


# Parameters

okaytocontinue <- TRUE

myReportName <- "LCP Encumbrance Principal Payments Load"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
scriptfolder <- "LEGACY_ENCUMB_Payments"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
gSht_auth_email <- "<EMAIL>"
gSht_key <- "1S4Z-VcLT-5bpYqXkhq1MpKH-DcLuCduPAjTaPlXdwaE"
mySheets <- c("Sheet1") #sheet name needed from gSht_key file

gSht_needed_cols <- c(
  "CMPY_ID",
  "LOANID",
  "Principal Payment",
  "Annual Principal Payment",
  "Monthly Interest",
  "Annual Interest",
  "Notes"
)
old_names <- c("Principal Payment",
               "Annual Principal Payment",
               "Monthly Interest",
               "Annual Interest"
               )
new_names <- c("PRINCIPLE_MONTHLY_PYMT",
               "PRINCIPLE_ANNUAL_PYMT",
               "INTEREST_MONTHLY_PYMT",
               "INTEREST_ANNUAL_PYMT"
               )
db_cols <- c(
  "CMPY_ID",
  "LOANID",
  "BLDG_CNT",
  "PRINCIPLE_MONTHLY_PYMT",
  "PRINCIPLE_ANNUAL_PYMT",
  "INTEREST_MONTHLY_PYMT",
  "INTEREST_ANNUAL_PYMT"
)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>", "<EMAIL>")
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
sig_logo <- FALSE


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- file.path(logpath, rptfolder)


#20241016: mySchema <- "STEVE"
mySchema <- "CORPORATE"
myTable <- "LCP_ENCUMB_PAYMENTS"
myTableName <- paste(mySchema, myTable, sep = ".")


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)




mailsend_old <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, " <<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


###Get email signatures###
get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
sig_logo <- FALSE
if(file.exists(HVSigPath)){
  sigName <- 'NA'
  sigTitle <- 'NA'
  sigEmail <- 'NA'
  sigTemplate <- 'LCP Reporting' #LCP Reporting doesn't use any personal info substitutions
  sigPhone <- 'NA'
  #update_log(
  #  event = "Signature Template", 
  #  desc = paste0("Creating...PARAMS - Name: ", sigName, "; Email: ", sigEmail, "; Signature Template: ", sigTemplate)
  #)
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == sigTemplate)],
    Name = sigName,
    Title = sigTitle,
    Email = sigEmail,
    Phone = sigPhone
  )
}
warn_sig <- norm_sig


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null || 'NULL')] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)




# check google sheet status
if(okaytocontinue){
  
  gs4_auth(email = gSht_auth_email)
  gSht_get <- gs4_get(gSht_key)
  
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    
    #Get number of sheets present that match expected names (mySheets)
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>There weren't any sheets with the expected names (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}


# read in existing gSht info
if(okaytocontinue){
  with_gs4_quiet(
    gSht_curr <- read_sheet(
      ss = as_id(gSht_key),
      sheet = mySheets[1]
    )
  )
  if(nrow(gSht_curr) < 1){
    okaytocontinue <- FALSE
  }else{
    prev_data <- select(gSht_curr, any_of(gSht_needed_cols))
  }
  
}


# query MRI for current term loans where owed > 1 and update Google
if(okaytocontinue){
  myquery <- paste0(
    "
      SELECT distinct
      UNQ.CMPY_ID
      , UNQ.LOANID
      , LISTAGG(trim(TO_VARCHAR(UNQ.CURRENTOWED,'$999,999,999,990.00')), '; ') WITHIN GROUP (ORDER BY UNQ.CURRENTOWED ASC) AS CURRENTOWED
      , SUM(CNT.BLDGS) as BLDG_CNT
      from 
      (
   			SELECT DISTINCT
  				CMPY_ID
  			,	LOANID
  			,	CURRENTOWED
  			FROM MRI.ENCUMB ie
  			WHERE ie.TABLEID = 'BLDG'
  				and ie.CURRENTOWED > 1
  				AND ie.DEBTTYPEID != 'LOC'
      ) UNQ
      LEFT JOIN 
      (
   			SELECT
  				ce.CMPY_ID
  			,	ce.LOANID
  			,	COUNT(*) AS BLDGS
  			FROM MRI.ENCUMB ce
  			WHERE ce.TABLEID = 'BLDG'
  				and ce.CURRENTOWED > 1
  				AND ce.DEBTTYPEID != 'LOC'
  			GROUP BY 
  			ce.CMPY_ID
  			,	ce.LOANID
  			,	ce.CURRENTOWED
      ) CNT
      ON UNQ.CMPY_ID = CNT.CMPY_ID
      AND UNQ.LOANID = CNT.LOANID
      where UNQ.CURRENTOWED > 1
      group by UNQ.CMPY_ID, UNQ.LOANID
      order by UNQ.CMPY_ID, UNQ.LOANID
    "
  )
  #20241016: myupdated <- dbGetQuery(mySSdb, myquery)
  myupdated <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydf_rows(myupdated, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]]){
    #join previous AP data (payments and notes)
    myupdated[] <- lapply(myupdated[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
    #myupdated <- as_tibble(myupdated)
    curr_data <- myupdated %>%
      dplyr::left_join(prev_data, by = c("CMPY_ID","LOANID"))
    #add updated column
    updated <- paste0("Updated: ",format(now(), "%a, %b %d, %Y at %I:%M%p %Z"))
    curr_data[, updated] <- NA
    #clear gSht
    with_gs4_quiet(
      range_clear(
        ss = as_id(gSht_key),
        sheet = mySheets[1],
        reformat = FALSE
      )
    )
    Sys.sleep(2)
    
    #write new data to gSht
    with_gs4_quiet(
      range_write(
        ss = as_id(gSht_key), 
        data = curr_data,
        sheet = mySheets[1], 
        col_names = TRUE,
        reformat = FALSE
      )
    )
    
    #truncate table to prep for write data to Oracle
    myquery_trunc <- paste0(
      "TRUNCATE TABLE ",
      myTable
    )
    #20241209: dbSendQuery(myOracleDB, myquery_trunc)
    rs<- dbGetQuery(mySfDB, myquery_trunc)
    
    #write data
    mydata <- copy(curr_data)
    setnames(mydata, 
             old = old_names, 
             new = new_names,
             skip_absent = TRUE)
    mydata <- select(mydata, any_of(db_cols)) #remove extra cols
    mydata <- mydata[which(!is.na(mydata$PRINCIPLE_MONTHLY_PYMT)),] #exclude any rows where a principal payment is not present
    #20241209: rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata)
    #get new count of rows in table
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName
    )
    #20241016: rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    #20241209: rs_sel <- dbSendQuery(mySfDB, myquery_select)
    #20241209: select_cnt_post <- dbFetch(rs_sel, n = -1)
    select_cnt_post <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    #20241209: if(nrow(mydata) != nrow(select_cnt_post)){
    if(nrow(mydata) != select_cnt_post){
      #warn of issue
      bodytext <- paste0("<p>This is an automated email to inform you that the data load ",
                         "for the '", myReportName, "' routine resulted in an unexpected number of rows!</p>",
                         "<p>The data to write to the '", myTableName, "' table had ", nrow(mydata),
                         " rows of data and the load resulted in a total of ", nrow(select_cnt_post),
                         " rows in the table.</p>",
                         "<p><em>Check the routine and re-load once the error has been identified.</em></p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Data Load Mismatch"),
               bodytext,
               attachment = NULL,
               inline = sig_logo,
               test = testing_emails, 
               testrecipient = test_recip
      )
    }
    
    #notify recipient if a new loanid is present
    mydata_emailbody <- setdiff(myupdated[,c("CMPY_ID","LOANID")],  prev_data[, c("CMPY_ID","LOANID")])
    if(nrow(mydata_emailbody)>0){
      bodytable <- paste0(
        "<p>",
        print(xtable(mydata_emailbody, 
                     #caption = paste0(this_ReportName, " (", query.date, ")"),
                     #digits = rep(0,ncol(mydata_emailbody)+1)
        ),
        #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
        html.table.attributes = "border=2 cellspacing=1",
        type = "html",
        caption.placement = "top",
        include.rownames=FALSE
        ),
        "</p>"
      )
      bodytext <- paste0(
        "<p>This is an automated email to let you know that the following loan(s) ",
        "appear to be new additions in the '", myReportName, "' routine. Please ",
        "enter any appropriate principal payments in the ",
        "<a href=\"", gSht_get$spreadsheet_url, "\">", gSht_get$name, "</a>",
        " Google sheet so they can be accessed by the needed reporting.</p>",
        bodytable,
        "<br>",
        norm_sig
      )
      #send mail
      mailsend(recipient = norm_recip,
               subject = paste0(myReportName, " Update: New Loan(s)"),
               body = bodytext,
               attachment = NULL,
               test = testing_emails, 
               testrecipient = test_recip
      )
    }
    
    
  }else{
    #error or no rows of data, abort
    okaytocontinue <- FALSE
    #email about potential issue
    bodytext <- paste0("<p>This is an automated email to inform you that the MRI query ",
                       "for the '", myReportName, "' routine didn't return any data!</p>",
                       "<p><em>The routine is aborting without an update!</em></p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: No MRI Data"),
             bodytext,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, 
             testrecipient = test_recip
    )
  }
}


DBI::dbDisconnect(mySfDB)

