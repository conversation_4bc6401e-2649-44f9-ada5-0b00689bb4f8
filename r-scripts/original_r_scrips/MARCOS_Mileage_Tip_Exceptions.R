library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(xtable)
library(utils)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyverse)
library(readxl)
library(DBI)
library(ROracle)
library(keyring)
library(glue)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20240917

### 20240917 change
### replaced mailR with gmailr package
### gmailr masks base message() function, replaced with explicit base::message()
### added tryCatch to trap Google Drive access error

### 20240531 change:
### switch order of RM and DM columns, on some mobile devices, the second column
### which was DM, is not always visible when zoomed...since DMs primarily
### complete this, switched to keep their name visible more often
### added list of approved emails for store for validation of appropriate
### email allowed for each store

### 20240522 change:
### added emojis to email subject lines to help them stand out

### 20240521 change:
### added date and time to subject to avoid emails from being caught up
### in conversation view or other filters

### 20240510 change:
### added logging in several places,
### added check so that 'denied' rows DON'T get picked up IF and alternate $ amount present (denials should deny entire transaction)

### 20240422 change:
### added Convert_to_ColClass function to ensure gSht columns read in are 
### desired datatypes (join errors otherwise), used on gSht_existing dataframe...
### sometimes they are read incorrectly when empty (e.g.logical vs character)

### 20240410 change:
### added section to read parameters of gSht for flag that allows this script to run or not (allowing remote activation)
### caught bug where notification emails were failing if no preserve_data rows were present

### ******** change:
### new file, outline of operations as follows:
### Google Workspace authorization is obtained and desired worksheet located
### Attempt to find 'Parameters' sheet in gSht and cell that flags whether this routine should run or not
### STEVE.GOOGLE_USER_EMAILS is queried to get paynums for all email accounts registered in AC_EMAIL table
### The existing contents of the Google exception worksheet (gSht) is read in 
###   (if data is okay, sheet is cleared)
### existing approved exceptions are updated in MOMS_BRANCH_TIPS_MILEAGE_EXCEP,
###   UPDATE statement updates the "ADJUSTED_AMOUNT","APPROVAL_STATUS","APPROVAL_DATE","COMMENTS" and "APPROVER_EMPLOYEE_NUMBER" columns
###   incomplete are preserved for writing back to gSht
### current exceptions are queried from DB and incomplete data from prior edits joined back to it
### updated exceptions are written back to gSht
### First notifications are made to DMs and RMs (where DM_NOTIF is NULL), 
###   DM_NOTIF column is updated with Oracle SYSDATE datetime for the 
###   'current_exceptions' in each email that haven't already been updated
### Escalation notifications are made when rounded hours between DM_NOTIF and 
###   Sys.time is 24 hours
### ? further emails after 24 hour notifications???
### Note: there is also APPS SCRIPT code in the Google Sheet that helps validation prior to this routine running


# Parameters
options(stringsAsFactors = FALSE)

myReportName <- "Marcos Mileage and Tip Exceptions"
scriptfolder <- "MARCOS_Mileage_Tip_Exceptions"
rptfolder <- "reports"
msg_text <- paste0("Routine Starting: ", myReportName)
base::message(msg_text)
emoji_monocle <- "=?UTF-8?B?8J+nkA==?="
emoji_pickup <- "=?UTF-8?B?8J+buw==?="
emoji_deltruck <- "=?UTF-8?B?8J+amg==?="
emoji_stopsign <- "=?UTF-8?B?8J+bkQ==?="

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles")

logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
logname <- paste0(myReportName," - LOG.csv")
report.loglimit <- format(floor_date(Sys.Date(), "month") - months(2), "%Y%m%d 000000") #only log rows >= this are retained to prevent log bloat
report.logruntime <- format(Sys.time(), "%Y%m%d %H%M%S")
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))


okaytocontinue <- TRUE


# Google sheets parameters, create empty df then add rows with needed info
# S_GID, GRID_ROWS, GRID_COLUMNS will be filled with gSht meta data once found
gSht_info <- data.frame(
  FN=character(), 
  F_ID=character(), 
  SN=character(), 
  S_GID=numeric(), 
  GRID_ROWS=numeric(), 
  GRID_COLUMNS=numeric() 
)
gSht_info <- gSht_info %>% add_row(
  FN = "Mileage and Tip Exception Approvals", 
  F_ID = "1AFiKiGUO0qgnG-pZkX_cfHbchc8itLpCrtup6PLHPfE", 
  SN = "Exceptions"
)

##############################TEST LINEs DISABLE BEFORE PUBLISHING and ACTIVATE 5 ROWS ABOVE!!!########################################################
#gSht_info <- gSht_info %>% add_row(
#  FN = "TEST Copy of Mileage and Tip Exception Approvals", 
#  F_ID = "1eSaeHvir525Co_bTkJ84qYI-IDe1pfOnv3YYJ50Khlw", 
#  SN = "Exceptions"
#)
##############################END OF TEST LINEs##################################################################################


gSht_auth_email <- "<EMAIL>"
#allowed DataType entries below (case ignored) are: numeric, integer, character, date, as.is (no conversion)
#the order of columns in gSht_columns determines the order of columns written to the Google sheet
gSht_columns <- data.frame(GshtHdr = character(), NewHdr = character(), RMEmail = logical(), DMEmail = logical(), DataType=character()) %>% 
  add_row(GshtHdr = "District", NewHdr = "DM", RMEmail = TRUE, DMEmail = FALSE, DataType = "character") %>% 
  add_row(GshtHdr = "Regional", NewHdr = "RM", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") %>% 
  add_row(GshtHdr = "Store #", NewHdr = "STORE_NUMBER", RMEmail = TRUE, DMEmail = TRUE, DataType = "integer") %>%  
  add_row(GshtHdr = "Store Name", NewHdr = "STORE_NAME", RMEmail = TRUE, DMEmail = TRUE, DataType = "character") %>%  
  #add_row(GshtHdr = "Trans Date", NewHdr = "TRANSACTION_DATE", RMEmail = TRUE, DMEmail = TRUE, DataType = "as.is") %>%   #20240618: Changed to line below with addition of 'Description' column
  add_row(GshtHdr = "Trans Date", NewHdr = "TRANSACTION_DATE", RMEmail = FALSE, DMEmail = FALSE, DataType = "as.is") %>%  
  add_row(GshtHdr = "Trans ID", NewHdr = "TRANSACTION_ID_TRIM", RMEmail = TRUE, DMEmail = TRUE, DataType = "integer") %>%  
  add_row(GshtHdr = "ID", NewHdr = "TRANSACTION_ID", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") %>% 
  add_row(GshtHdr = "SUID", NewHdr = "SUID", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") %>%  
  #add_row(GshtHdr = "Type", NewHdr = "ITEM_TYPE", RMEmail = TRUE, DMEmail = TRUE, DataType = "character") %>% #20240618: Changed to line below with addition of 'Description' column
  add_row(GshtHdr = "Type", NewHdr = "ITEM_TYPE", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") %>%
  add_row(GshtHdr = "Description", NewHdr = "DESCRIPTION", RMEmail = TRUE, DMEmail = TRUE, DataType = "character") %>% 
  add_row(GshtHdr = "Employee Name", NewHdr = "EMPLOYEE_FULLNAME", DataType = "character") %>%  
  add_row(GshtHdr = "Order Subtotal (Tips)", NewHdr = "NET_SUB", RMEmail = FALSE, DMEmail = FALSE, DataType = "numeric") %>%  
  add_row(GshtHdr = "Order Total (Tips)", NewHdr = "TOTAL", RMEmail = FALSE, DMEmail = FALSE, DataType = "numeric") %>%  
  add_row(GshtHdr = "Amount To Approve", NewHdr = "AMOUNT", RMEmail = TRUE, DMEmail = TRUE, DataType = "numeric") %>%  
  add_row(GshtHdr = "ALTERNATE Approved Amount", NewHdr = "ADJUSTED_AMOUNT", RMEmail = FALSE, DMEmail = FALSE, DataType = "numeric") %>%  
  add_row(GshtHdr = "Approved or Denied", NewHdr = "APPROVAL_STATUS", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") %>%  
  add_row(GshtHdr = "Approval User Email", NewHdr = "APPROVAL_EMAIL", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") %>%  
  add_row(GshtHdr = "Notes (required for 'Denied' or 'ALTERNATE Approved Amount' rows)", NewHdr = "COMMENTS", RMEmail = FALSE, DMEmail = FALSE, DataType = "character") #%>%  
  #add_row(GshtHdr = "", NewHdr = "", DataType = "numeric") %>% 

gSht_needed_names <- gSht_columns$GshtHdr
r_needed_names <- gSht_columns$NewHdr
District_email_cols <- gSht_columns$GshtHdr[which(gSht_columns$DMEmail)]
Regional_email_cols <- c(gSht_columns$GshtHdr[which(gSht_columns$RMEmail)], "DM_NOTIF")

preserveDataStructure <- data.frame(
  ADJUSTED_AMOUNT = numeric(),
  APPROVAL_STATUS = character(),
  APPROVAL_EMAIL = character(),
  COMMENTS = character()
)

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  mainpath <- centralPath
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}


logpath <- file.path(mainpath,scriptfolder)
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
myReportPath <- file.path(mainpath, scriptfolder, rptfolder)
smtp_relay <- FALSE #not currently using SMTP relay for these emails, this is noted in LOG so var created here

### Log initialize and define some functions ###
### log file
#update function
update_log <- function(rptPeriod = report.logruntime, event, desc, logExists = logFound){
  logData <- data.frame(
    RPT_PERIOD = as.character(rptPeriod), 
    TIME = as.character(Sys.time()), 
    EVENT = event, 
    DESCRIPTION = desc
  )
  #write.table(logData, file = logFN, sep = ",", row.names = FALSE, col.names = FALSE, append = TRUE)
  write.table(logData, file = logFN, sep = ",", row.names = FALSE, col.names = !logExists, append = logExists)
}

### check if log present/up-to-date ###
logFN <- file.path(logpath, logname)
logFound <- FALSE
prev_runcompleted <- FALSE
if(file.exists(logFN) ) {
  logFound <- TRUE
  MyLog <- read.csv(file = logFN, sep=",", colClasses=c(rep("character",4)), stringsAsFactors = FALSE)
  #remove rows earlier than report.loglimit to prevent log file bloat as save updated file
  NewLog <- MyLog %>%
    dplyr::filter(RPT_PERIOD >= report.loglimit)
  if(nrow(MyLog)>nrow(NewLog)){
    #replace existing log with reduced(current) rows
    write.table(NewLog, file = logFN, sep = ",", row.names = FALSE, col.names = TRUE, append = FALSE)
  }
}
if(okaytocontinue){
  #add new row to log for routine start
  update_log(
    event = "Routine Start", 
    desc = paste0("PARAMS - Testing Emails: ", testing_emails, "; SMTP Relay: ", smtp_relay)
  )
  logFound <- TRUE
}




# ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ='UTC')
#Sys.setenv(ORA_SDTZ='UTC')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)

mySchema <- "STEVE"
exceptionTable <- "MOMS_BRANCH_TIPS_MILEAGE_EXCEP"
exceptionSchema <- "FAMVDBA"
#specify columns to use as keys
exceptionKeyCols <- c(
  "TRANSACTION_ID"
)

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


Convert_to_ColClass <- function(mydata, names_class_df){
  #names_class_df should be a dataframe with two columns, ColName with DF columns and DataType with the expected class
  classes <- names_class_df$DataType %>% unique(.) %>% toupper(.)
  myresult <- mydata
  if(length(classes)>=1){
    myresult <- mydata
    for(i in 1:length(classes)){
      curr_class <- classes[i]
      convert_cols <- names_class_df$ColName[which(toupper(names_class_df$DataType) == curr_class)]
      if(curr_class == 'INTEGER'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.integer))}
      if(curr_class == 'NUMERIC'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.numeric))}
      if(curr_class == 'DATE'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.Date))}
      if(curr_class == 'CHARACTER'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.character))}
    }
  }
  return(myresult)
}

dbUpdateCustom = function(x, key_cols, con, schema_name, table_name) {
  
  if (nrow(x) != 1) stop("Input dataframe must be exactly 1 row")
  if (!all(key_cols %in% colnames(x))) stop("All columns specified in 'key_cols' must be present in 'x'")
  
  # Build the update string --------------------------------------------------
  
  df_key     <- dplyr::select(x,  one_of(key_cols))
  df_upt     <- dplyr::select(x, -one_of(key_cols))
  
  set_str    <- purrr::map_chr(colnames(df_upt), ~glue::glue_sql('{`.x`} = {x[[.x]]}', .con = con))
  
  set_str    <- paste(set_str, collapse = ", ")
  
  where_str  <- purrr::map_chr(colnames(df_key), ~glue::glue_sql("{`.x`} = {x[[.x]]}", .con = con))
  where_str  <- paste(where_str, collapse = " AND ")
  
  update_str <- glue::glue('UPDATE {schema_name}.{table_name} SET {set_str} WHERE {where_str}')
  # special substitution to add datetime or trunc(date) to DATE columns ------
  to_datetime <- paste0("= to_date('", format(Sys.time(), "%m/%d/%Y %H:%M:%S"), "', 'mm/dd/yyyy hh24:mi:ss')")
  update_str <- gsub("= 'SYSTIME'", to_datetime, update_str)
  to_date <- paste0("= to_date('", format(Sys.Date(), "%m/%d/%Y"), "', 'mm/dd/yyyy')")
  update_str <- gsub("= 'SYSDATE'", to_date, update_str)
  # Execute ------------------------------------------------------------------
  update_rs <- tryCatch(
    expr = {
      suppressWarnings(DBI::dbSendQuery(con, update_str))
    },
    error = function(cond) {
      # Choose a return value in case of error
      err_msg <- paste0("<p>Update error: <em>",
                        conditionMessage(cond), "</em> ",
                        "Update string: <em>", 
                        update_str, "</em></p>"
      )
      print(err_msg)
    }
  )    
  if(class(update_rs)[1]=="character"){
    result <- update_rs
  }else{
    result <- dbGetInfo(update_rs, what = "rowsAffected")[[1]]
  }
  
  return (invisible(result))
}


dbUpdate_DM_NOTIF = function(x, key_col, update_col_name, con, schema_name, table_name) {
  
  if (!all(key_col %in% colnames(x))) stop("Column specified in 'key_col' must be present in 'x'")
  
  # Build the update string for Oracle
  df_upd     <- dplyr::select(x,  one_of(key_col))
  set_str    <- paste0('"', update_col_name,'" = sysdate')
  where_str  <- paste0('"', colnames(df_upd), '" IN (', paste0("'", df_upd[,1], "'", collapse = ","), ')')
  update_str <- glue::glue('UPDATE {schema_name}.{table_name} SET {set_str} WHERE {where_str}')
  # Execute
  update_rs <- tryCatch(
    expr = {
      suppressWarnings(DBI::dbSendQuery(con, update_str))
    },
    error = function(cond) {
      # Choose a return value in case of error
      err_msg <- paste0("<p>Update error: <em>",
                        conditionMessage(cond), "</em> ",
                        "Update string: <em>", 
                        update_str, "</em></p>"
      )
      print(err_msg)
    }
  )    
  if(class(update_rs)[1]=="character"){
    result <- update_rs
  }else{
    result <- dbGetInfo(update_rs, what = "rowsAffected")[[1]]
  }
  
  return (invisible(result))
}


###email template for FIRST NOTIFICATION send
First_send_msg <- function(msg_myReportName = myReportName, 
                           msg_RMorDM = RMorDM,
                           msg_curr_name = curr_name,
                           msg_curr_rows = curr_rows,
                           msg_curr_rows_old = curr_rows_old,
                           msg_email_table = email_table,
                           msg_except_cols = except_cols,
                           msg_curr_incomplete_ids = curr_incomplete_ids,
                           msg_signature = norm_sig){
  
  #create intro based on RM or DM version
  if(msg_RMorDM=="DM"){
    intro <- paste0(
      msg_curr_rows, " new (or incomplete) exceptions below were added ",
      "and need to be approved or denied in the ",
      "<a href=\"", gSht_get$spreadsheet_url, "\">", gSht_info$FN[1], "</a>",
      " sheet.</p>"
    )
  }else{
    intro <- paste0(
      msg_curr_rows, " new (or incomplete) exceptions below were added. Districts ",
      "listed have been notified and have 24 hours to address these in the ",
      "<a href=\"", gSht_get$spreadsheet_url, "\">", gSht_info$FN[1], "</a>",
      " sheet.</p>"
    )
    if(sum(is.na(email_table$District))>0){
      #add in graph about No District rows
      intro <- paste0(
        intro,
        "<h3><FONT COLOR=\"#ff0000\">There are locations WITHOUT a District, the ",
        "REGIONAL is expected to approve or deny those rows A.S.A.P.</FONT></h3>"
      )
    }
  }
  #create incomplete section if applicable
  if(nrow(msg_curr_incomplete_ids)>0){
    incomplete <- paste0(
      "<hr><p>The following IDs from above were marked as approved or ",
      "denied, but are INCOMPLETE in some way and <strong>HAVE NOT BEEN ",
      "PROCESSED YET...PLEASE REVISIT!</strong></p>",
      print(
        xtable(msg_curr_incomplete_ids, 
               digits = rep(0,ncol(msg_curr_incomplete_ids)+1)
        ),
        html.table.attributes = "border=2 cellspacing=1",
        type = "html",
        caption.placement = "top",
        include.rownames=FALSE
      ),
      "<br>'Denied' rows, and rows with an alternate approval $ amount, ",
      "<strong>REQUIRE a note entry. If an alternate approved $ amount ",
      "is present, then the transaction <strong>must</strong> be marked as ",
      "<strong>'approved'</strong>. </strong>ALL rows require a paynum ",
      "to be found (matched to the email address who last edited the row). ",
      "<br>"
    )
  }else{
    incomplete <- ""
  }
  
  #create previous section
  previous <- ""
  if(msg_curr_rows_old==1){
    previous <- paste0(
      "<p><em>There is ", msg_curr_rows_old, " additional exception ",
      "previously sent that is also waiting for approval.</em></p>"
    )
  }
  if(msg_curr_rows_old>1){
    previous <- paste0(
      "<p><em>There are ", msg_curr_rows_old, " additional exceptions ",
      "previously sent that are also waiting for approval.</em></p>"
    )
  }
  
  
  #create main body of message
  msg_html <- paste0(
    "<html><head></head><body>",
    "<h3>",msg_myReportName, " - ", msg_curr_name, "</h3>",
    "<p>",
    intro,
    print(
      xtable(msg_email_table[, msg_except_cols], 
             digits = c(rep(0,ncol(msg_email_table[, msg_except_cols])-1), 2, 2)
      ),
      html.table.attributes = "border=2 cellspacing=1",
      type = "html",
      caption.placement = "top",
      include.rownames=FALSE
    ),
    "<p>If you enter an 'alternate' corrected amount, you must also select ",
      "'approved' and enter a note describing why alternate amount is appropriate. ",
      "Entering 'denied' in the sheet invalidates the ENTIRE transaction.</p>",
    previous,
    "<br>",
    incomplete,
    "<br>",
    msg_signature,
    "</body></html>"
  )
  return (msg_html)
}


###email template for ESCALATION NOTIFICATION send
Escalate_send_msg <- function(msg_myReportName = myReportName, 
                              msg_RMorDM = RMorDM,
                              msg_curr_name = curr_name,
                              msg_curr_rows = curr_rows,
                              msg_curr_rows_old = curr_rows_old,
                              msg_email_table = email_table,
                              msg_except_cols = except_cols,
                              msg_curr_incomplete_ids = curr_incomplete_ids,
                              msg_signature = norm_sig){
  
  #create intro
  intro <- paste0(
    msg_curr_rows, " <strong>OVERDUE</STRONG> exceptions below that haven't ",
    "been fully addressed in the expected time. Please approve or deny in the ",
    "<a href=\"", gSht_get$spreadsheet_url, "\">", gSht_info$FN[1], "</a>",
    " sheet.</p>"
  )
  
  #create incomplete section if applicable
  if(nrow(msg_curr_incomplete_ids)>0){
    incomplete <- paste0(
      "<hr><p>The following IDs from above were marked as approved or ",
      "denied, but are INCOMPLETE in some way and <strong>HAVE NOT BEEN ",
      "PROCESSED YET...PLEASE REVISIT!</strong></p>",
      print(
        xtable(msg_curr_incomplete_ids, 
               digits = rep(0,ncol(msg_curr_incomplete_ids)+1)
        ),
        html.table.attributes = "border=2 cellspacing=1",
        type = "html",
        caption.placement = "top",
        include.rownames=FALSE
      ),
      "<br>'Denied' rows, and rows with an alternate approval $ amount, ",
      "<strong>REQUIRE a note entry.</strong> ALL rows require a paynum ",
      "to be found (matched to the email address who last edited the row). ",
      "If an alternate approved $ amount is present, then the transaction ",
      "<strong>must</strong> be marked as <strong>'approved'</strong>.",
      "<br>"
    )
  }else{
    incomplete <- ""
  }
  
  #create previous section
  previous <- ""
  if(msg_curr_rows_old==1){
    previous <- paste0(
      "<p><em>There is ", msg_curr_rows_old, " additional exception ",
      "previously sent that is also waiting for approval.</em></p>"
    )
  }
  if(msg_curr_rows_old>1){
    previous <- paste0(
      "<p><em>There are ", msg_curr_rows_old, " additional exceptions ",
      "previously sent that are also waiting for approval.</em></p>"
    )
  }
  
  ###-----------------------###
  #create main body of message#
  ###-----------------------###
  msg_html <- paste0(
    "<html><head></head><body>",
    "<h3><FONT COLOR=\"#ff0000\">!OVERDUE! </FONT> ",msg_myReportName, " - ", msg_curr_name, "</h3>",
    "<p>",
    intro,
    print(
      xtable(msg_email_table[, msg_except_cols], 
             digits = c(rep(0,ncol(msg_email_table[, msg_except_cols])-1), 2, 2)
      ),
      html.table.attributes = "border=2 cellspacing=1",
      type = "html",
      caption.placement = "top",
      include.rownames=FALSE
    ),
    previous,
    "<br>",
    incomplete,
    "<br>",
    msg_signature,
    "</body></html>"
  )
  return (msg_html)
}



get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}


###Get email signatures###
warn_recip <- c("Steve Olson<<EMAIL>>", "Sean Coyle <<EMAIL>>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
corp_recip_HRG <- c("Zach McLaughlin <<EMAIL>>")
norm_recip <- c("Steve Olson<<EMAIL>>")
test_recip <- c("Steve Olson<<EMAIL>>")
sig_logo <- FALSE
if(file.exists(HVSigPath)){
  sigName <- 'Steve Olson'
  sigTitle <- 'Sr. Analytics Mgr.'
  sigEmail <- '<EMAIL>'
  sigTemplate <- 'HRG Normal'
  sigPhone <- '(*************'
  update_log(
    event = "Signature Template", 
    desc = paste0("Creating...PARAMS - Name: ", sigName, "; Email: ", sigEmail, "; Signature Template: ", sigTemplate)
  )
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == sigTemplate)],
    Name = sigName,
    Title = sigTitle,
    Email = sigEmail,
    Phone = sigPhone
  )
}



###----------------------------------------------------------------------------###
###auth googledrive and googlesheets4 and check if supplied folder URL is valid###
###----------------------------------------------------------------------------###
if(okaytocontinue){
  update_log(
    event = "Google Drive Access", 
    desc = paste0("Checking...PARAMS - Auth Email: ", gSht_auth_email, 
                  "; File ID: ", paste0(gSht_info$F_ID, collapse = ", "),
                  "; File Name: ", paste0(gSht_info$FN, collapse = ", "),
                  "; Sheet Name: ", paste0(gSht_info$SN, collapse = ", ")
    )
  )
  isFile <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  #gs4_auth(email = gSht_auth_email)
  #get unique file IDs to get
  gSht_fnd_ID_cnt <- 0
  gSht_F_IDs <- unique(gSht_info$F_ID[!is.na(gSht_info$F_ID)])
  
  drv_test <- tryCatch(
    expr = {
      suppressWarnings(drive_get(id = as_id(gSht_F_IDs[1])))
    },
    error = function(cond) {
      # Choose a return value in case of error
      err_msg <- paste0("<p>googledrive::drive_get() error: <em>",
                        conditionMessage(cond), "</em> ",
                        "Google file ID: <em>", 
                        gSht_F_IDs[1], "</em></p>"
      )
      print(err_msg)
    }
  )    
  if(class(drv_test)[1]=="character"){
    #error on test drive_get()...log error and abort (de-auth gs4 to avoid error in next loop)
    okaytocontinue <- FALSE
    update_log(
      event = "Google Drive Access", 
      desc = paste0("Access Failure: ", drv_test)
    )
    #send warning email that sheet doesn't exist
    bodytext <- paste0(
      "<p>This is an automated email to inform you that it appears there ",
      "is an error accessing Google Drive in the ",
      myReportName, " routine! ",
      "<p>The routine is aborting without an update.</p> ",
      "<p>ERROR:<br>", 
      drv_test,
      "</p><br>",
      warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Drive Issue"),
             bodytext,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
  if (gs4_has_token() && class(drv_test)[1]!="character") {
    #auth okay, check if file IDs and sheets are found
    if(length(gSht_F_IDs)>0){
      #check file IDs and then metadata for needed sheets
      for(i in 1:length(gSht_F_IDs)){
        #gSht_get <- gs4_get(as_id(gSht_info$F_ID[i]))
        drv_get_check <- drive_get(id = as_id(gSht_F_IDs[i]))
        isFile <- drv_get_check$drive_resource[[1]]$mimeType == drive_mime_type("spreadsheet")
        if(isFile){
          #check if sheet names are in file
          gSht_get <- gs4_get(as_id(gSht_F_IDs[i]))
          curr_info_idx <- which(gSht_info$F_ID == gSht_F_IDs[i])
          #gSht_sheets <- gSht_info$SN[which(gSht_info$F_ID == gSht_F_IDs[i])]
          gSht_sheets <- gSht_info$SN[curr_info_idx]
          diff <- setdiff(gSht_sheets, gSht_get$sheets$name)
          if(!is_empty(diff)){
            okaytocontinue <- FALSE
            #send warning email that sheet doesn't exist
            bodytext <- paste0(
              "<p>This is an automated email to inform you that it appears there ",
              "is at least one missing sheet needed in the ",
              myReportName, " routine! ",
              "<p>The routine is aborting without an update.</p> ",
              "<b>Google Sheet Info:</b><ul>",
              "<li>Google sheet filename: ", gSht_get$name, "</li>",
              "<li>Sheetname(s) NOT FOUND in file above:<b> ", paste0(diff, collapse = '; '), "</b></li>",
              "</ul></p><br>",
              warn_sig
            )
            #send mail
            mailsend(warn_recip,
                     paste0(myReportName, " Issue: Google File Issue"),
                     bodytext,
                     attachment = NULL,
                     inline = sig_logo,
                     test = testing_emails, testrecipient = test_recip
            )
            update_log(
              event = "ERROR", 
              desc = paste0("Following sheet(s) not found in Google file: ",
                            paste0(diff, collapse = '; ')
              )
            )
            break
          }else{
            #add found info to gSht_info
            for(i in curr_info_idx){
              curr_SN <- gSht_info$SN[i]
              
              gSht_get_idx <- which(gSht_get$sheets$name == curr_SN)
              gSht_info$S_GID[i] <- gSht_get$sheets$id[gSht_get_idx]
              gSht_info$GRID_ROWS[i] <- gSht_get$sheets$grid_rows[gSht_get_idx]
              gSht_info$GRID_COLUMNS[i] <- gSht_get$sheets$grid_columns[gSht_get_idx]
            }
          }
          gSht_fnd_ID_cnt <- gSht_fnd_ID_cnt + 1
        }else{
          #desired file not found, abort
          okaytocontinue <- FALSE
          curr_info_idx <- which(gSht_info$F_ID == gSht_F_IDs[i])
          #gSht_sheets <- gSht_info$SN[which(gSht_info$F_ID == gSht_F_IDs[i])]
          gSht_Filename <- unique(gSht_info$FN[curr_info_idx])
          #send warning email that sheet doesn't exist
          bodytext <- paste0(
            "<p>This is an automated email to inform you that it appears there ",
            "is at least one missing file needed in the ",
            myReportName, " routine! ",
            "<p>The routine is aborting without an update.</p> ",
            "<b>Google Sheet Info:</b><ul>",
            "<li>Google sheet ID not found:<b> ", gSht_F_IDs[i], "</b><</li>",
            "<li>File name of sheet not found:<b> ", gSht_Filename, "</b><</li>",
            "</ul></p><br>",
            warn_sig
          )
          #send mail
          mailsend(warn_recip,
                   paste0(myReportName, " Issue: Google File Issue"),
                   bodytext,
                   attachment = NULL,
                   inline = sig_logo,
                   test = testing_emails, testrecipient = test_recip
          )
          update_log(
            event = "ERROR", 
            desc = paste0("Google sheet ID not found: ", gSht_F_IDs[i],
                          " ; File name: ", gSht_Filename
            )
          )
          break
        }
      }
      
    }else{
      #no IDs found in gSht_info dataframe, abort
      okaytocontinue <- FALSE
      
    }
    
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update.</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Access Issue"),
             bodytext,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
    update_log(
      event = "ERROR", 
      desc = paste0("Issue: Google Access Issue!",
                    " Googledrive package token: ", drive_has_token(),
                    " Googlesheets4 package token: ", gs4_has_token()
      )
    )
  }
  rm(drv_test)
}




###-------------------------------###
###Check for gSht Parameters info ###
###-------------------------------###
if(okaytocontinue){
  msgText <- "Checking parameters stored in 'PARAMS'"
  base::message(msgText)
  update_log(
    event = "Google Sheet Config Values", 
    desc = msgText
  )
  ###check flag in google sheet that indicates if this routine should continue running###
  if(!!length(which(gSht_get$sheets$name == 'Parameters')) &
     !!length(which(gSht_get$named_ranges$name == 'Param_R_run_allowed'))){
    #gSht parameters sheet and run flag for this routine found
    run_self <- range_read(ss = gSht_info$F_ID[i], 
                           range = 'Param_R_run_allowed',
                           col_names = FALSE)
    run_self_status <- check_mydf_rows(mydf = run_self, MinNumRows = 1, ReportName = myReportName)
    if(run_self_status[[1]]){
      if(typeof(run_self[1,1][[1]])=='logical'){
        #named range is boolean as expected...
        okaytocontinue <- run_self[1,1][[1]]
      }else{
        #wasn't boolean, try string contents
        if(toupper(as.character(run_self[1,1][[1]])) %in% c("TRUE", "FALSE")){
          okaytocontinue <- case_when(
            toupper(as.character(run_self[1,1][[1]])) == "TRUE" ~ TRUE,
            toupper(as.character(run_self[1,1][[1]])) == "FALSE" ~ TRUE
          )
        }
      }
    }
    if(!okaytocontinue){
      #echo reason for stopping
      msgText <- "Google Sheet parameters stopped this routine from running!"
      warning(msgText)
      update_log(
        event = "Google Sheet Config Values", 
        desc = msgText
      )
      Sys.sleep(4)
    }
  }
}




###--------------------------------###
###Get Gmail addresses with paynums###
###--------------------------------###
if(okaytocontinue){
  msgText <- "Getting Gmail addresses with paynums"
  base::message(msgText)
  update_log(
    event = "Oracle", 
    desc = msgText
  )
  paynumsFound <- FALSE
  mySchema <- "STEVE"
  myTable_email <- "GOOGLE_USER_EMAILS"
  myTableName <- paste(mySchema, myTable_email, sep = ".")
  myquery <- paste0(
    "select * from ", myTableName
  )
  gmail_addresses <- dbGetQuery(myOracleDB, myquery)
  gmail_addresses_status <- check_mydf_rows(mydf = gmail_addresses, MinNumRows = 1, ReportName = myReportName)
  if(gmail_addresses_status[[1]]){
    paynumsFound <- TRUE
    setnames(gmail_addresses, 
             old = "PAYNUM", 
             new = "APPROVER_EMPLOYEE_NUMBER",
             skip_absent = TRUE)
  }else{
    msgText <- "No Gmail addresses found or query failure"
    base::message(msgText)
    update_log(
      event = "ERROR", 
      desc = msgText
    )
  }
}


###----------------------------------###
###Get Store Staff Assignments/Emails###
###----------------------------------###
if(okaytocontinue){
  msgText <- "Getting Store Staff Assigments/Emails"
  base::message(msgText)
  update_log(
    event = "Oracle", 
    desc = msgText
  )
  mySchema <- "STEVE"
  myTable_rmdmmgr <- "SO_RM_DM_MGR"
  myquery <- paste0(
    "
      SELECT
      	srdm.store,
      	gue.email,
      	CASE WHEN srdm.RM_PAYNUM = gue.PAYNUM THEN 'Regional'
      		WHEN srdm.DM_PAYNUM = gue.PAYNUM THEN 'District'
      		WHEN srdm.MGR_PAYNUM = gue.PAYNUM THEN 'Manager'
      	END AS Position
      FROM ", mySchema, ".", myTable_email, " gue 
      LEFT JOIN ", mySchema, ".", myTable_rmdmmgr, " srdm
      on srdm.RM_PAYNUM = gue.PAYNUM 
      OR srdm.DM_PAYNUM = gue.PAYNUM 
      OR srdm.MGR_PAYNUM = gue.PAYNUM 
      WHERE srdm.STORE IS NOT NULL 
      AND srdm.LOC_INFO15_ALPHA = 'HF'
      ORDER BY srdm.store
    "
  )
  gSht_emails <- dbGetQuery(myOracleDB, myquery)
  gSht_emails_status <- check_mydf_rows(mydf = gSht_emails, MinNumRows = 1, ReportName = myReportName)
  if(gSht_emails_status[[1]]){
    #write current assignments to Google sheet
    gSht_emails_sn <- 'Emails'
    if(!!length(which(gSht_get$sheets$name == gSht_emails_sn))){
      #clear existing values
      with_gs4_quiet(
        range_clear(
          ss = as_id(gSht_info$F_ID), 
          sheet = gSht_emails_sn, 
          #range = cell_rows(c(2, NA)), 
          reformat = FALSE
        )
      )
      #write new values
      rs <- range_write(
        ss = as_id(gSht_info$F_ID), 
        data = gSht_emails, 
        sheet = gSht_emails_sn, 
        range = "A1",
        col_names = TRUE,
        reformat = FALSE
      )
    }
  }else{
    msgText <- "No 'Store Staff Assigments/Emails' found or query failure"
    base::message(msgText)
    update_log(
      event = "ERROR", 
      desc = msgText
    )
  }
}


###------------------------------###
###Get existing Google Sheet data###
###------------------------------###
if(okaytocontinue){
  msgText <- "Reading existing Google Sheet data"
  base::message(msgText)
  update_log(
    event = "Google Sheet", 
    desc = msgText
  )
  gSht_existing <- data.frame(empty = character())
  for(i in 1:nrow(gSht_info)){
    if(complete.cases(gSht_info[i, c("FN","SN")])){
      curr_SN <- gSht_info$SN[i]
      if(curr_SN == "Exceptions"){
        gSht_existing <- range_read(ss = gSht_info$F_ID[i], 
                             sheet = curr_SN
        )
        setnames(gSht_existing, 
                 old = gSht_needed_names, 
                 new = r_needed_names,
                 skip_absent = TRUE)
        #exclude unknown columns from df and convert datatypes of columns
        gSht_existing <- gSht_existing[,r_needed_names]
        names_class <- copy(gSht_columns[,c("NewHdr", "DataType")]) %>%
          dplyr::rename(ColName = NewHdr)
        gSht_existing <- Convert_to_ColClass(gSht_existing, names_class)
        
      }else{
        #this routine only expecting one sheet
        okaytocontinue <- FALSE
      }
    }
  }
  gSht_existing_status <- check_mydf_rows(mydf = gSht_existing, MinNumRows = 1, ReportName = myReportName)
  if(paynumsFound==FALSE & gSht_existing_status[[1]]==TRUE){
    #Send error email indicating that data in Gsheet, but unable to get paynums for email addresses
    gSht_existing_approvalcnt <- gSht_existing$APPROVAL_STATUS[which(!is.na(gSht_existing$APPROVAL_STATUS))] %>% length()
    myTableName <- paste(mySchema, myTable_email, sep = ".")
    body_html <- paste0(
      "<html><head></head><body>",
      "<h2>WARNING! Unable to get payroll numbers for email addresses in the ",
      myReportName, " routine.</h2>",
      "<p>Check ", myTableName, " Oracle table data! </p>",
      "<p>", gSht_existing_approvalcnt, " addressed exceptions were found in the ",
      "<a href=\"", gSht_get$spreadsheet_url, "\">", gSht_info$FN[1], "</a>",
      " sheet, but unable to assign paynums based on email address.</p>",
      warn_sig,
      "</body></html>"
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Email Paynums Issue"),
             body_html,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
  if(paynumsFound & gSht_existing_status[[1]]){
    ###-----------------------  
    #clear gSht starting at row 2 (all columns)
    ###-----------------------
    with_gs4_quiet(
      range_clear(
        ss = as_id(gSht_info$F_ID), 
        sheet = gSht_info$SN, 
        range = cell_rows(c(2, NA)), 
        reformat = FALSE
      )
    )
  }
  if(gSht_existing_status[[1]] == FALSE){
    msgText <- "No existing Google Sheet data found"
    base::message(msgText)
    update_log(
      event = "Google Sheet", 
      desc = msgText
    )
  }
}







###-------------------------------------------------###
###Check for and Update DB with addressed exceptions###
###-------------------------------------------------###
if(okaytocontinue && gSht_existing_status[[1]] && paynumsFound){
  msgText <- "Checking existing data for approved and denied rows"
  base::message(msgText)
  update_log(
    event = "Google Sheet", 
    desc = msgText
  )
  #filter to 'addressed' rows having an approval or denial, then check for completeness
  gSht_existing_addressed <- gSht_existing %>%
    filter(!is.na(APPROVAL_STATUS)) %>%
    left_join(gmail_addresses[,c("EMAIL", "APPROVER_EMPLOYEE_NUMBER")], by = c("APPROVAL_EMAIL" = "EMAIL"))
  gSht_results <- gSht_existing_addressed[0,]
  
  #get asis approvals where approved without adjustments and with found paynum
  temp_data <- gSht_existing_addressed %>%
    filter(APPROVAL_STATUS=="approved", is.na(ADJUSTED_AMOUNT)|AMOUNT==ADJUSTED_AMOUNT, !is.na(APPROVER_EMPLOYEE_NUMBER) )
  gSht_results <- rbind(gSht_results, temp_data)
  
  #get adjusted approvals where approved with adjustments, paynum and comments present
  #todo: should probably have some min COMMENT length (here and in Gsht validation)
  temp_data <- gSht_existing_addressed %>%
    filter(APPROVAL_STATUS=="approved", !is.na(ADJUSTED_AMOUNT), AMOUNT!=ADJUSTED_AMOUNT, !is.na(APPROVER_EMPLOYEE_NUMBER), !is.na(COMMENTS) )
  gSht_results <- rbind(gSht_results, temp_data)
  
  #get denied that have paynum and comments present, but DON'T have an alternate approved amount
  #temp_data <- gSht_existing_addressed %>%
  #  filter(APPROVAL_STATUS=="denied", !is.na(APPROVER_EMPLOYEE_NUMBER), !is.na(COMMENTS))
  temp_data <- gSht_existing_addressed %>%
    filter(APPROVAL_STATUS=="denied", 
           !is.na(APPROVER_EMPLOYEE_NUMBER), 
           !is.na(COMMENTS),
           ADJUSTED_AMOUNT == 0|is.na(ADJUSTED_AMOUNT)
    )
  gSht_results <- rbind(gSht_results, temp_data)
  
  
  #compare results to addresses to find incomplete entries
  gSht_incomplete <- setdiff(gSht_existing_addressed, gSht_results)
  
  ###------------------------------------------------------------###
  ###update MOMS_BRANCH_TIPS_MILEAGE_EXCEP with gSht_results data###
  ###------------------------------------------------------------###
  if(nrow(gSht_results)>0){
    base::message("Updating DB table with approved and denied rows")
    #add APPROVAL_DATE datetime column,
    #"SYSDATE" will be substituted with current trunc(date) in dbUpdateCustom
    #"SYSTIME" will be substituted with current datetime in dbUpdateCustom
    gSht_results$APPROVAL_DATE <- "SYSTIME"
    curr_columns <- c(
      exceptionKeyCols,
      "ADJUSTED_AMOUNT",
      "APPROVAL_STATUS",
      "APPROVAL_DATE",
      "COMMENTS",
      "APPROVER_EMPLOYEE_NUMBER"
    )
    results_update_cnt <- 0
    update_errors <- ""
    for(i in i:nrow(gSht_results)){
      curr_results_row <- gSht_results[i, curr_columns]
      #debugonce(dbUpdateCustom)
      #dbUpdateCustom returns integer of RowsAffected if no error, otherwise string containing error message and update string attempted
      update_results <- dbUpdateCustom(
        x = curr_results_row, 
        key_cols = exceptionKeyCols, 
        con = myOracleDB, 
        schema_name = exceptionSchema, 
        table_name = exceptionTable
      )
      if(is.numeric(update_results)){
        results_update_cnt <- results_update_cnt + update_results
      }else{
        results_update_cnt <- 0
        update_errors <- paste0(update_errors, "<br><br>", update_results)
      }
    }
    if(results_update_cnt == nrow(gSht_results)){
      #commit results
      dbCommit(myOracleDB)
    }else{
      #rollback
      warning("issue updating completed results -- rolling back updates to exception table")
      dbRollback(myOracleDB)
      results_error <- TRUE
    }
  }
  
  #check if any data needs to be joined back to query of current exceptions (incomplete and failed updates)
  if(nrow(gSht_incomplete)>0 | exists("results_error")){
    preserve_columns <- c(
      exceptionKeyCols,
      names(preserveDataStructure)
    )
    #create empty df for data to preserve
    preserve_data <- gSht_existing[0, preserve_columns]
    if(nrow(gSht_incomplete)>0){
      preserve_data <- rbind(preserve_data, gSht_incomplete[,preserve_columns])
    }
    if(exists("results_error")){
      preserve_data <- rbind(preserve_data, gSht_results[,preserve_columns])
    }
  }
}




###----------------------###
###Get current Exceptions###
###----------------------###
if(okaytocontinue){
  msgText <- "Querying DB table for current exceptions"
  base::message(msgText)
  update_log(
    event = "Oracle", 
    desc = msgText
  )
  myquery <- paste0(
    "
      select
        rdm.rm_fullname as RM
      , rdm.dm_fullname as DM
      , exc.store_number as STORE_NUMBER
      , rdm.loc_name AS STORE_NAME
      , exc.transaction_date as TRANSACTION_DATE
      , to_number(substr(exc.transaction_id, 1, instr(exc.transaction_id, '_')-1)) AS TRANSACTION_ID_TRIM
      , exc.transaction_id as TRANSACTION_ID
      , exc.suid AS SUID
      , exc.item_type AS ITEM_TYPE
      , exc.description as DESCRIPTION
      , exc.first_name||' '||exc.last_name as EMPLOYEE_FULLNAME
      , orb.subtotal - orb.discount as NET_SUB
      , orb.total AS TOTAL
      , exc.amount AS AMOUNT
      , exc.dm_notif as DM_NOTIF
      , rdm.rm_email as RM_EMAIL
      , rdm.dm_email as DM_EMAIL
      from MOMS_BRANCH_TIPS_MILEAGE_EXCEP exc
      left join MOMS_ORDERSPAYMENT_BRANCH opb
      on exc.suid = opb.suid
      left join MOMS_DRIVERCASHOUTRESULT_B dcb
      on exc.suid = dcb.suid
      left join MOMS_ORDERSRESULT mor
      on opb.order_id = mor.order_id
      left join moms_ordersresult_branch orb
      ON opb.order_id = orb.order_id
      left join steve.so_rm_dm_mgr rdm
      on exc.store_number = rdm.store
      where exc.approval_status is null
      order by rm, dm, store_number, item_type, exc.transaction_date, transaction_id_trim
    "
  )
  
  new_exceptions <- dbGetQuery(myOracleDB, myquery)
  new_exceptions_status <- check_mydf_rows(mydf = new_exceptions, MinNumRows = 1, ReportName = myReportName)
  
  if(new_exceptions_status[[1]]){
    base::message("Exceptions found, continuing")
    gSht_new <- copy(new_exceptions)
    
    if(exists("preserve_data")){
      #join preserve data (incomplete approvals and failed approval db updates)
      gSht_new <- gSht_new %>%
        dplyr::left_join(preserve_data, by = exceptionKeyCols)
    }else{
      #add empty columns otherwise added by preserve_data
      newsize <- nrow(gSht_new)
      preserveDataStructure[1:newsize,] <- NA
      gSht_new <- cbind(gSht_new, preserveDataStructure[,names(preserveDataStructure) %notin% names(gSht_new)])
    }
    #Remove extra columns if present
    gSht_new_write <- gSht_new[, names(gSht_new) %in% r_needed_names]
    
    #Rename write columns to gSht version
    setnames(gSht_new_write,
             old = r_needed_names, 
             new = gSht_needed_names,
             skip_absent = TRUE)
    #reorder columns to match gSht_columns df order
    gSht_new_write <- gSht_new_write[ , gSht_columns$GshtHdr]
    
    #move DM_NOTIF column to the end of gSht_new for later use in email to staff
    temp_cols <- c(names(gSht_new)[names(gSht_new) %in% r_needed_names], "DM_NOTIF", "RM_EMAIL", "DM_EMAIL")
    gSht_new <- gSht_new[, temp_cols]
  }else{
    if(exists("new_exceptions")){
      msgEvent <- "Oracle"
      msgText <- "No current exceptions found"
    }else{
      msgEvent <- "ERROR"
      msgText <- "Query failure when getting current exceptions!"
    }
    base::message(msgText)
    update_log(
      event = msgEvent, 
      desc = msgText
    )
  }
}


###----------------------------------###
###WRITE current exceptions to Google###
###----------------------------------###
if(okaytocontinue & exists("gSht_new_write")){
  msgText <- "Writing exceptions to Google Sheet"
  base::message(msgText)
  update_log(
    event = "Google Sheet", 
    desc = msgText
  )
  #write new_exceptions (if present) to google sheet
  #write new data [starting in CELL A1]
  rs <- range_write(
    ss = as_id(gSht_info$F_ID), 
    data = gSht_new_write, 
    sheet = gSht_info$SN, 
    range = "A1",
    col_names = TRUE,
    reformat = FALSE
  )
}




###---------------------------------------------------------------------###
###First notification to DMs and RMs for NEW or incomplete rows for them###
###---------------------------------------------------------------------###
if(okaytocontinue & exists("gSht_new_write")){
  msgText <- "Checking if first notification emails are needed"
  base::message(msgText)
  update_log(
    event = "Emails", 
    desc = msgText
  )
  #filter newly added exceptions (DM_NOTIF is NA) and repeat rows that failed approval validation or updating
  incomplete_cols <- c("TRANSACTION_ID_TRIM", "APPROVAL_STATUS", "APPROVAL_EMAIL", "APPROVER_EMPLOYEE_NUMBER")
  incomplete_cols_newnames <- c("Trans ID", "Status", "Approval email captured", "Paynum")
  timeNow <- Sys.time()
  
  DM_NOTIF_update_errors <- "" #create var for error results (for DMs and RMs in this )
  
  ###get District level rows that need first notification
  DM_notify <- copy(gSht_new) %>%
    filter((is.na(DM_NOTIF)|!is.na(APPROVAL_STATUS)), !is.na(DM_EMAIL))
  if(nrow(DM_notify)>0){
    #get unique primary email addresses
    DM_emails <- DM_notify$DM_EMAIL[which(!is.na(DM_notify$DM_EMAIL))] %>% unique()
    except_cols <- gSht_columns$GshtHdr[which(gSht_columns$DMEmail==TRUE)]
    RMorDM = "DM"
    for(curr_email in DM_emails){
      #filter list to this DMs rows
      curr_exceptions <- copy(DM_notify) %>%
        filter(DM_EMAIL==curr_email)
      curr_name <- curr_exceptions$DM %>% unique()
      #get Trans IDs for incomplete approvals
      curr_incomplete_ids <- curr_exceptions[which(!is.na(curr_exceptions$APPROVAL_STATUS)), names(curr_exceptions) %in% incomplete_cols] %>%
        dplyr::left_join(gmail_addresses[,c("EMAIL", "APPROVER_EMPLOYEE_NUMBER")], by = c("APPROVAL_EMAIL" = "EMAIL"))
      curr_rows <- nrow(curr_exceptions)
      curr_rows_old <- length(gSht_new$DM_EMAIL[which(gSht_new$DM_EMAIL==curr_email)]) -
        curr_rows
      #curr_exceptions$TRANSACTION_DATE <- format(curr_exceptions$TRANSACTION_DATE, "%b %d") #DEPRECATED in 20240618 version
      #rename columns to user friendly version (gSht versions)
      email_table <- copy(curr_exceptions)
      setnames(email_table,
               old = r_needed_names, 
               new = gSht_needed_names,
               skip_absent = TRUE
      )
      setnames(curr_incomplete_ids,
               old = incomplete_cols, 
               new = incomplete_cols_newnames,
               skip_absent = TRUE
      )
      #send mail (when email would ONLY include incomplete approvals, limit sends to certain hours)
      okaytosend <- case_when(
        curr_rows == nrow(curr_incomplete_ids) & 
          (hour(timeNow) < 4 | hour(timeNow) > 22) ~ FALSE, #do NOT send if before 4AM OR after 10PM (system time)
        curr_rows == nrow(curr_incomplete_ids) &
          hour(timeNow) %% 4 != 0 ~ FALSE,  #do NOT send when above is okay but HOUR doesn't divide evenly by 4
        TRUE ~ TRUE # if none of the above apply, okay to send
      )
      if(okaytosend){
        msg_text <- paste0("Sending notification to ", curr_email)
        base::message(msg_text)
        body_html <- First_send_msg()
        mailsend(curr_email,
                 paste0(emoji_monocle,"NEW ", myReportName, emoji_deltruck, ' (', format(Sys.time(), "%m/%d/%y %H:%M%Z"), ')'),
                 body_html,
                 attachment = NULL,
                 inline = sig_logo,
                 test = testing_emails, testrecipient = test_recip
        )
        update_log(
          event = "Email INITIAL and/or INCOMPLETE DM notification", 
          desc = paste0("Emailed: ", paste0(curr_email, collapse = ";"))
        )
        #update db with DM_NOTIF datetime for these curr_exceptions rows
        update_exceptions <- copy(curr_exceptions) %>%
          filter(is.na(DM_NOTIF))
        if(nrow(update_exceptions)>0){
          update_log(
            event = "Oracle", 
            desc = paste0("Update ", exceptionTable, ".DM_NOTIF column")
          )
          update_results <- dbUpdate_DM_NOTIF(
            x = update_exceptions, 
            key_col = exceptionKeyCols[1],
            update_col_name = "DM_NOTIF",
            con = myOracleDB, 
            schema_name = exceptionSchema, 
            table_name = exceptionTable
          )
          if(is.numeric(update_results)){
            #commit
            dbCommit(myOracleDB)
          }else{
            dbRollback(myOracleDB)
            DM_NOTIF_update_errors <- paste0(
              DM_NOTIF_update_errors, "<br><br>", update_results
            )
            warn_str <- paste0(
              "issue updating 'DM_NOTIF' datetime for ", curr_email,
              " -- rolling back uncommitted updates to exception table"
            )
            warning(warn_str)
          }
        }
      }
    }
  }
  
  
  ###get Regional level rows that need first notification
  RM_notify <- copy(gSht_new) %>%
    filter((is.na(DM_NOTIF)|!is.na(APPROVAL_STATUS)))
  if(nrow(RM_notify)>0){
    #get unique primary email addresses
    RM_emails <- RM_notify$RM_EMAIL[which(!is.na(RM_notify$RM_EMAIL))] %>% unique()
    except_cols <- gSht_columns$GshtHdr[which(gSht_columns$RMEmail==TRUE)]
    RMorDM = "RM"
    for(curr_email in RM_emails){
      #filter list to this RMs rows
      curr_exceptions <- RM_notify %>%
        filter(RM_EMAIL==curr_email)
      curr_name <- curr_exceptions$RM %>% unique()
      #get Trans IDs for incomplete approvals
      curr_incomplete_ids <- curr_exceptions[which(!is.na(curr_exceptions$APPROVAL_STATUS)), names(curr_exceptions) %in% incomplete_cols] %>%
        dplyr::left_join(gmail_addresses[,c("EMAIL", "APPROVER_EMPLOYEE_NUMBER")], by = c("APPROVAL_EMAIL" = "EMAIL"))
      curr_rows <- nrow(curr_exceptions)
      curr_rows_old <- length(gSht_new$RM_EMAIL[which(gSht_new$RM_EMAIL==curr_email)]) -
        curr_rows
      #curr_exceptions$TRANSACTION_DATE <- format(curr_exceptions$TRANSACTION_DATE, "%b %d") #DEPRECATED in 20240618 version
      #rename EMAIL columns to user friendly version (gSht versions)
      email_table <- copy(curr_exceptions)
      setnames(email_table,
               old = r_needed_names, 
               new = gSht_needed_names,
               skip_absent = TRUE
      )
      setnames(curr_incomplete_ids,
               old = incomplete_cols, 
               new = incomplete_cols_newnames,
               skip_absent = TRUE
      )
      #send mail (when email would ONLY include incomplete approvals, limit sends to certain hours)
      okaytosend <- case_when(
        curr_rows == nrow(curr_incomplete_ids) & 
          (hour(timeNow) < 4 | hour(timeNow) > 22) ~ FALSE, #do NOT send if before 4AM OR after 10PM (system time)
        curr_rows == nrow(curr_incomplete_ids) &
          hour(timeNow) %% 4 != 0 ~ FALSE,  #do NOT send when above is okay but HOUR doesn't divide evenly by 4
        TRUE ~ TRUE # if none of the above apply, okay to send
      )
      if(okaytosend){
        msg_text <- paste0("Sending notification to ", curr_email)
        base::message(msg_text)
        body_html <- First_send_msg()
        mailsend(curr_email,
                 paste0(emoji_monocle, "NEW ", myReportName, emoji_deltruck, ' (', format(Sys.time(), "%m/%d/%y %H:%M%Z"), ')'),
                 body_html,
                 attachment = NULL,
                 inline = sig_logo,
                 test = testing_emails, testrecipient = test_recip
        )
        update_log(
          event = "Email INITIAL and/or INCOMPLETE RM notification", 
          desc = paste0("Emailed: ", paste0(curr_email, collapse = ";"))
        )
        #update db with DM_NOTIF datetime for curr_exceptions rows where DM is NA and no DM_NOTIF value present
        update_exceptions <- copy(curr_exceptions[which(is.na(curr_exceptions$DM)),]) %>%
          filter(is.na(DM_NOTIF))
        if(nrow(update_exceptions)>0){
          update_results <- dbUpdate_DM_NOTIF(
            x = update_exceptions, 
            key_col = exceptionKeyCols[1],
            update_col_name = "DM_NOTIF",
            con = myOracleDB, 
            schema_name = exceptionSchema, 
            table_name = exceptionTable
          )
          if(is.numeric(update_results)){
            #commit
            dbCommit(myOracleDB)
          }else{
            dbRollback(myOracleDB)
            DM_NOTIF_update_errors <- paste0(
              DM_NOTIF_update_errors, "<br><br>", update_results
            )
            msgText <- paste0(
              "issue updating 'DM_NOTIF' datetime for ", curr_email,
              " -- rolling back uncommitted updates to exception table"
            )
            warning(msgText)
            base::message(msgText)
            update_log(
              event = "Emails", 
              desc = msgText
            )
          }
        }
      }
    }
  }
  
  #send warning email when DM_NOTIF column wasn't updated as expected for one or more first notifications
  if(DM_NOTIF_update_errors != ""){
    #send warning email
    #body_html <- First_send_msg()
    body_html <- paste0(
      "<html><head></head><body>",
      "<h2>WARNING! Failures when updating 'DM_NOTIF' column in ",
      myReportName, " routine.</h2>",
      "<p>Check ", exceptionTable, " Oracle table data! </p>",
      "<p>When emailing the field about new exceptions in the table, the ",
      "update to the 'DM_NOTIF' column, which indicates when the first ",
      "notification went out failed in the instances listed below.</p>",
      DM_NOTIF_update_errors,
      "<br><br>",
      warn_sig,
      "</body></html>"
    )

    #send mail
    mailsend(warn_recip,
             paste0("DM_NOTIF Warnings for ", myReportName),
             body_html,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
}




###--------------------------------------###
###Notify RMs, DMs and Corp of escalation###
###--------------------------------------###
if(okaytocontinue & exists("gSht_new_write")){
  msgText <- "Checking if ESCALATION (overdue) emails are needed"
  base::message(msgText)
  update_log(
    event = "Emails", 
    desc = msgText
  )
  ###(first notification failed to approve...Sys.time()-DM_NOTIF >= ~24 hours)
  timeNow <- Sys.time()
  RM_escalate_notify <- copy(gSht_new) %>%
    #filter(round(difftime(timeNow, DM_NOTIF, units = "hours")) == 24) %>%
    filter(round(difftime(timeNow, DM_NOTIF, units = "hours")) > 23,
           (as.integer(round(difftime(timeNow, DM_NOTIF, units = "hours"))) - 24) %% 24 == 0 #every 24 hours
    )
  
  if(nrow(RM_escalate_notify)>0){
    #get unique primary email addresses
    RM_emails <- RM_escalate_notify$RM_EMAIL[which(!is.na(RM_escalate_notify$RM_EMAIL))] %>% unique()
    except_cols <- gSht_columns$GshtHdr[which(gSht_columns$RMEmail==TRUE)]
    except_cols <- c(except_cols, "First Notification") %>% unique()
    RMorDM = "RM"
    for(curr_email in RM_emails){
      #filter list to this RMs rows
      curr_exceptions <- copy(RM_escalate_notify) %>%
        filter(RM_EMAIL==curr_email)
      curr_name <- curr_exceptions$RM %>% unique()
      #get Trans IDs for incomplete approvals
      curr_incomplete_ids <- curr_exceptions[which(!is.na(curr_exceptions$APPROVAL_STATUS)), names(curr_exceptions) %in% incomplete_cols] %>%
        dplyr::left_join(gmail_addresses[,c("EMAIL", "APPROVER_EMPLOYEE_NUMBER")], by = c("APPROVAL_EMAIL" = "EMAIL"))
      curr_rows <- nrow(curr_exceptions)
      curr_rows_old <- length(gSht_new$DM_EMAIL[which(gSht_new$DM_EMAIL==curr_email)]) -
        curr_rows
      #curr_exceptions$TRANSACTION_DATE <- format(curr_exceptions$TRANSACTION_DATE, "%b %d") #DEPRECATED in 20240618 version
      curr_exceptions$DM_NOTIF <- format(curr_exceptions$DM_NOTIF, "%b %d %H:%M:%S%Z")
      #rename EMAIL columns to user friendly version (gSht versions)
      email_table <- copy(curr_exceptions)
      setnames(email_table,
               old = r_needed_names, 
               new = gSht_needed_names,
               skip_absent = TRUE
      )
      setnames(email_table,
               old = "DM_NOTIF", 
               new = "First Notification",
               skip_absent = TRUE
      )
      setnames(curr_incomplete_ids,
               old = incomplete_cols, 
               new = incomplete_cols_newnames,
               skip_absent = TRUE
      )
      body_html <- Escalate_send_msg()
      #add additional recipients
      dm_recip <- curr_exceptions$DM_EMAIL
      curr_recip <- c(curr_email, 
                      corp_recip_HRG, 
                      warn_recip,
                      dm_recip) %>% na.omit() %>% unique()
      #send email
      mailsend(curr_recip,
               paste0(emoji_stopsign, "!OVERDUE! ", myReportName, ' (', format(Sys.time(), "%m/%d/%y %H:%M%Z"), ')'),
               body_html,
               attachment = NULL,
               inline = sig_logo,
               test = testing_emails, testrecipient = test_recip
      )
      msgText <- "Sending OVERDUE emails!!!!"
      base::message(msgText)
      update_log(
        event = "Email ESCALATION (overdue) notification", 
        desc = paste0("Emailed: ", paste0(curr_recip, collapse = ";"))
      )
    }
  }#if(nrow(RM_escalate_notify)>0){
}




###------------------------------------------------------------------------###
###Notify Corp if Approval not present when Sys.time()-DM_NOTIF >= 48 hours###
###NOT CURRENTLY USED###
#if(okaytocontinue & exists("gSht_new_write")){
  #timeNow <- Sys.time()
  #CORP_escalate_notify <- copy(gSht_new) %>%
  #  filter(round(difftime(timeNow, DM_NOTIF, units = "hours")) > 23,
  #         (as.integer(round(difftime(timeNow, DM_NOTIF, units = "hours"))) - 24) %% 24 == 0 #every 24 hours
  #  )
  #if(nrow(CORP_escalate_notify)>0){
    #send CORPORATE escalation
  #}
#}
#if(exists("results_error")){#email list of results that didn't update}


if(okaytocontinue){
  update_log(
    event = "Routine End", 
    desc = "Complete"
  )
}else{
  update_log(
    event = "Routine End", 
    desc = "okaytocontinue == FALSE"
  )
}


Sys.sleep(4) #allow for final messages echoed to screen to persist a few seconds






