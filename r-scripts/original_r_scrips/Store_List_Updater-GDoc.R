library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(dbplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(stringi)
library(readr)
library(utils)
library(keyring)
library(DBI)
library(odbc)
library(ROracle)
library(googlesheets4)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20240925

### 20240925 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present

### 20220610 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

rptfolder <- "HV_StoreList"
myReportName <- "Google Sheet Store List Updater"
mySheets <- c("Sheet1", "StoreMatches")
logpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder)

#P:\steveo\R Stuff\ReportFiles\HV_StoreList

#SSMS connection
#mySSdb <- dbConnect(odbc(),
#                 Driver = "ODBC Driver 17 for SQL Server",
#                 Server = "************",
#                 Database = "HIGHLANDPROD",
#                 UID = "SteveO_ro",
#                 PWD = key_get("MRI_bak", "SteveO_ro"),
#                 Port = 1433)

#Oracle connection
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password = key_get("Oracle", "steve"), dbname = connect.string)
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')

# email parameters: recipient(s) of warning emails and signatures
norm_recip <- c("<EMAIL>")
#norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>"
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")

rptpath <- logpath

if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010" || Sys.getenv("COMPUTERNAME") == "LAPTOPTOSHIBA13"){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  rptpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
}


### define some functions ###

check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


`%notin%` <- Negate(`%in%`)


rename_dup_colnames <- function(myDF, myDesiredColnames){
  #this functions takes a df with potential duplicate names
  #where R renames them with "..." & column # appended
  #and tries to find the desired column (one most populated) and rename it
  #with it's originally expected name
  myDF_Curr_Colnames <- colnames(myDF)
  myColIntersect <- intersect(myDesiredColnames, myDF_Curr_Colnames)
  mySetDiff <- setdiff(myDesiredColnames, myColIntersect)
  if(length(mySetDiff) > 0){
    #remove special characters from column names
    myDF_Curr_Colnames_comp <- str_replace_all(myDF_Curr_Colnames, "[:punct:]|[:space:]|[$]",".")
    myDesiredColnames_comp <- str_replace_all(mySetDiff, "[:punct:]|[:space:]|[$]",".")
    #loop through each myColNames that wasn't found & search for column BEGINNING with that name
    for(i in 1:length(mySetDiff)){
      #test_matches <- which(myDF_Curr_Colnames_comp %like% paste0("^", myDesiredColnames_comp[i]) )
      test_matches <- which(myDF_Curr_Colnames_comp %like% paste0("^", myDesiredColnames_comp[i], "...") & myDF_Curr_Colnames %notin% myColIntersect )
      if(length(test_matches) > 0){
        #one or more columns might be a match, find how many rows are populated and select one with the most (or last if no rows populated)
        fndcol <- FALSE
        popcols <- data.frame(COLNUM = integer(),
                              COLROWS_POPULATED = numeric()
        )
        for(x in 1:length(test_matches)){
          #test how many rows populated
          numpop <- nrow(myDF) - colSums(is.na(myDF[,test_matches[[x]]]) | myDF[,test_matches[[x]]] == "" )
          if(numpop > 0 ){
            #add column # and # of rows populated to temp df
            popcols <- rbind(popcols, data.frame(COLNUM = c(test_matches[[x]]),
                                                 COLROWS_POPULATED = c(numpop)
            )
            )
            fndcol <- TRUE
          }else{
            #check if no populated column found and on last match and use that column if so
            if(fndcol == FALSE & x == length(test_matches) ){
              popcols <- data.frame(COLNUM = c(test_matches[[x]]), COLROWS_POPULATED = c(numpop))
            }
          }
        }
        if(length(popcols) > 0){
          #select column with most rows populated as desired column
          fndcolnum <- popcols$COLNUM[match(max(popcols$COLROWS_POPULATED, na.rm = TRUE), popcols$COLROWS_POPULATED)]
          #change column name to original desired name
          colnames(myDF)[fndcolnum] <- mySetDiff[i]
        }
      }
      
    }
  }
  return(myDF)
}




if(okaytocontinue){
  #check Google for workbook and sheets needed
  gs4_auth(email = "<EMAIL>")
  
  #Is it OK to cache OAuth access credentials in the folder 'C:/Users/<USER>/.R/gargle/gargle-oauth' between R sessions?
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive package first, then use the same token
  #in googlesheets4 something like this:
  #tk <- drive_auth()
  #gs4_auth(token = drive_token())
  #gSht_Closings <- sheets_get('1xoLPaRKdPvDdwl9wvCEiBp8ABrGgxhff9GSn55_yArc')
  #above was deprecated as of googlesheets4 0.2.0
  
  
  #https://docs.google.com/spreadsheets/d/1Y6y6caVI3PTjqnQIZwp4yOWHSsaj6GTGIkoDUwDm7nM/edit#gid=1402039407
  gSht_get <- gs4_get('1Y6y6caVI3PTjqnQIZwp4yOWHSsaj6GTGIkoDUwDm7nM')
  
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH OKAY")
    #MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    #writelog(MyErrorLog)
    #read data in from desired sheet
    #gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RE Taxes Data")
    
    #Get number of sheets like '% P&L Full Sort' sheets
    #gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %ilike% mySheetsLike)]
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that at least ONE sheet found
    if(length(mySheets) != gSht_Sheets_num){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>One or more expected sheets (", 
                         paste(mySheets, collapse = "; "),
                         ") were NOT found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Missing Expected Sheet Name(s)"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}


### Get Store-Building matches
if(okaytocontinue){
  this_ReportName <- paste0(myReportName, " - Get St-BLDG Matches")
  print(paste0("Starting: ", this_ReportName) )
  myquery <- "select * from STEVE.MRI_ST_BLDG_MATCH order by STORE"
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    mydata_stbldg <- mydata
    stbldg_okay <- TRUE
  }else{
    stbldg_okay <- FALSE
    #okay to continue with attempt to load/populate store list info, but send warning email
    bodytext <- paste0("This is an automated email to inform you that it appears the following <strong>error</strong> occured ",
                       "during the <strong>", this_ReportName, "</strong> routine.<br/><br/>",
                       mydata_status[[3]],
                       "<br/><br/>The query results didn't produce data as expected and ",
                       "the routine will continue attempting to load Store List info.<br/> <br/>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(this_ReportName, " : REPORT DATA ERROR"),
             bodytext
    )
  }
}


### Get Store List info
if(okaytocontinue){
  this_ReportName <- paste0(myReportName, " - Get Store List info")
  print(paste0("Starting: ", this_ReportName) )
  myquery <- paste0(
    "
      select
      ab_store.st as STORE,
      null as RENT,
      null as ASD_Tier,
      hrloc.description as STORENAME,
      ab_store.address as ADDRESS,
      ab_store.city AS CITY,
      ab_store.state AS STATE,
      (case when ab_store.zip is null 
       then null
       else ab_store.zip end) AS ZIP,
      (case when ab_store.PHONE is not null
       then substr(ab_store.PHONE,1,3)||'-'||substr(ab_store.PHONE,4,3)||'-'||substr(ab_store.PHONE,7,4)
       end) AS PHONE,
      (case when rdm.Mgr_fname is not null
       then rdm.mgr_fullname
       else 'No Mgr' end) as MANAGER,
      (case when rdm.dm_fname is not null
       then rdm.dm_fullname
       end) as DM,
      rdm.dm_email AS DM_EMAIL,
      (case when rdm.rm_fname is not null
       then rdm.rm_fullname
       end) as RM,
      rdm.rm_email as RM_EMAIL,
      ab_store.o_date as OPEN_DATE,
      --(case when ab_store.st BETWEEN 2 and 888
         (case when upper(hrloc.LOC_INFORMATION15) like 'FVMC%'
           then 'fv0'||lpad(ab_store.st,3,0)||'@fvmc.com'
           --when ab_store.st BETWEEN 2100 and 2499
           when upper(hrloc.LOC_INFORMATION15) like 'VET%'
           then 'vt'||lpad(ab_store.st,4,0)||'@familyvetgroup.com'
           --when ab_store.st BETWEEN 3500 and 3999
           when upper(hrloc.LOC_INFORMATION15) like 'HF%'
           then 'mp'||lpad(ab_store.st,4,0)||'@fvmc.com'
           --when ab_store.st BETWEEN 6500 and 6999
           when upper(hrloc.LOC_INFORMATION15) like 'STFIT%'
           then 'sf'||lpad(ab_store.st,4,0)||'@stayfit24.com'
           end) as STORE_EMAIL,
         hrloc.attribute19 as CORNERS,
         null as UPDATED
         from ab_store
         left join steve.so_rm_dm_mgr rdm
         on ab_store.st = rdm.store
         left join HR.hr_locations_all hrloc
         on lpad(ab_store.st,4,0) = hrloc.location_code
         where ab_store.st >= 2
         and ab_store.st < 7000
         order by ab_store.st
    "
  )
  
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    stlist <- mydata
    if(exists("dates")){rm(dates)}
    dates <- paste0("Updated: ", format(Sys.Date(), format = "%m/%d/%Y"), collapse = NULL) 
    colnames(stlist) <- c( "Store Number",
                           "Rent",
                           "ASD Tier",
                           "Store Name",
                           "Address",
                           "City",
                           "State",
                           "Zip",
                           "Phone",
                           "Manager/MIT",
                           "DM",
                           "DM Email",
                           "RM",
                           "RM Email",
                           "Open Date",
                           "Store Email",
                           "Street Corners",
                           dates)
    
  }else{
    #issue with query, fail
    okaytocontinue <- FALSE
    print(mydata_status[[3]])
    #FAILED error msg
    bodytext <- paste0("This is an automated email to inform you that it appears the following <strong>error</strong> occured ",
                       "during the <strong>", this_ReportName, "</strong> routine.<br/><br/>",
                       mydata_status[[3]],
                       "<br/><br/>The query results didn't produce data as expected and ",
                       "the routine will abort.<br/> <br/>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(this_ReportName, " : REPORT DATA ERROR"),
             bodytext
    )
  }
}


### Store-Building Matches - Populate Google Sheet
if(okaytocontinue & stbldg_okay){
  myReportPath <- rptpath
  this_recip <- c(norm_recip)
  this_ReportName <- paste0(myReportName, " - Populate StoreMatches")
  print(paste0("Starting: ", this_ReportName) )
  mySheets_Curr <- c("StoreMatches")
  
  gSht_Sheet <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets_Curr)]
  gSht_Sheet_num <- length(gSht_Sheet)
  
  if(gSht_Sheet_num > 0){
    #sheet found, continue
    #clear existing data in MRI sheet
    range_clear(gSht_get$spreadsheet_id, sheet = mySheets_Curr, range = NULL, reformat = FALSE)
    #write new data
    sheet_write(mydata_stbldg, ss = gSht_get$spreadsheet_id, sheet = mySheets_Curr)
    
  }
}



### Store List main sheet - Populate Google Sheet
if(okaytocontinue){
  myReportPath <- rptpath
  this_recip <- c(norm_recip)
  this_ReportName <- paste0(myReportName, " - Populate Store List (main)")
  print(paste0("Starting: ", this_ReportName) )
  mySheets_Curr <- c("Sheet1")
  
  gSht_Sheet <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets_Curr)]
  gSht_Sheet_num <- length(gSht_Sheet)
  
  if(gSht_Sheet_num > 0){
    #sheet found, continue
    #clear existing data in MRI sheet
    range_clear(gSht_get$spreadsheet_id, sheet = mySheets_Curr, range = NULL, reformat = FALSE)
    #write new data
    sheet_write(stlist, ss = gSht_get$spreadsheet_id, sheet = mySheets_Curr)
    
  }
}





DBI::dbDisconnect(myOracleDB)




