library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)

library(DBI)
library(odbc)


# written by <PERSON> March 2022
# based on HV_PNL_LINE_STRUCTURE loading script

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241015

### 20241015 change:
### fix path bug

### 20241010 change:
### converted SQL queries to Snowflake DB
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### updated email signature to use latest format provided by <PERSON> earlier in 2024

### 20230925 change:
### added SAFETY_CULTURE_ID column to Oracle table populated with corresponding data from Gsheet

### 20230217 change:
### added EXP_VISITS_YEAR column to Oracle based on Google sheet "Expected Visits Per Year" column

### 20230206 change:
### no longer loading Oracle 'SUPPORT_RM' column ('Additional RM Support' in Google)
### added BLDG_TYPE column to Oracle using data in Google sheet 'Building Type/Details' column
### also converted from odbc Oracle connection to ROracle and DBI for more robust loading

### 20220829 change:
### Added OCCUPIED SINGLE TENANT? column to Oracle (from Summary sheet) where x gets populated as 'Y' and
### NULL values are 'N' in database


### 20220630 change:
### Added MRI Asset Mgr sheet populate and keyring package

### 20220317 change:
### new script based on HV PNL LINE STRUCTURE script version 20220317
### added in section to populate 'MRI data' sheet of Google sheet with
### select MRI data to keep other sheets in that file up-to-date


# Parameters
okaytocontinue <- TRUE

myReportName <- "LCP_ASSIGNMENTS_SEAN_update"
scriptfolder <- "LEGACY_Property_Assignments_Sean"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")
mySheets <- c("Summary")
myMRISheet <- "MRI data"
myMRIAssetMgrSheet <- "MRI Asset Mgrs"
#mySheets <- " TESTFAIL"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)

# NOTE myColNames order dictates column order in resulting dataframe when filtered
# myColNames are the names in source Google Sheet
myColNames <- c("Building",
                "Long Building ID",
                "Address",
                "City",
                "State",
                "Zip",
                "RM",
                #"Additional RM support",
                "Support Leasing",
                "Support Property Management",
                "Prop Mgr",
                "OCCUPIED SINGLE TENANT?",
                "Building Type/Details",
                "Expected Visits Per Year",
                "Safety Culture Site ID"
                )
myColName_bldg <- c("Building")
gSht_bldg_colname_New <- "BLDG"
myColName_OST_New <- "OCC_SINGLE_TENANT"
#myColNames_New are db column names
myColNames_New <- c("BLDG",
                    "BLDGID",
                    "ADDRESS",
                    "CITY",
                    "STATE",
                    "ZIP",
                    "RM",
                    #"SUPPORT_RM",
                    "SUPPORT_LEASING",
                    "SUPPORT_PROP_MGMT",
                    "PROP_MGR",
                    "OCC_SINGLE_TENANT",
                    "BLDG_TYPE",
                    "EXP_VISITS_YEAR",
                    "SAFETY_CULTURE_ID"
                    )


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="GMT")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

mySchema <- "CORPORATE"
myTable <- "LCP_ASSIGNMENTS_SEAN"
myTableName <- paste(mySchema, myTable, sep = ".")

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

if(Sys.getenv("COMPUTERNAME") != "DESKTOP-TABLEAU"){
  testing_pc <- TRUE  #TESTING, changes some paths to network share instead of R/Tableau PC
  testing_pc_location <- "Office"
  #testing_pc_location <- "Laptop"
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
}


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


###Get email signature###
get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
  warn_sig <- norm_sig
}



check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




#--Query existing table data to compare to new run--#
if(okaytocontinue){
  myquery <- paste0("select 
                      *
                    from ", myTableName
  )
  #priordata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  priordata <- dbGetQuery(mySfDB, myquery)
  prioddata_saveas <- file.path(logpath, paste0(myTable,'-prior data.csv'))
  write.csv(priordata,prioddata_saveas, row.names = FALSE)
}



#--Query new data from Google Sheet--#
# check google sheet status
if(okaytocontinue){
  
  #MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  #MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  #writelog(MyErrorLog)
  
  gs4_auth(email = "<EMAIL>")
  
  #Is it OK to cache OAuth access credentials in the folder 'C:/Users/<USER>/.R/gargle/gargle-oauth' between R sessions?
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive package first, then use the same token
  #in googlesheets4 something like this:
  #tk <- drive_auth()
  #gs4_auth(token = drive_token())
  #gSht_Closings <- sheets_get('1xoLPaRKdPvDdwl9wvCEiBp8ABrGgxhff9GSn55_yArc')
  #above was deprecated as of googlesheets4 0.2.0

  
  #https://docs.google.com/spreadsheets/d/1ZP_7ZIIiMz6FjrYEJ8-K0TFFvnzsM0ogXnEV9P9wyIw/edit#gid=0
  gSht_get <- gs4_get('1ZP_7ZIIiMz6FjrYEJ8-K0TFFvnzsM0ogXnEV9P9wyIw')
  
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH OKAY")
    #MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    #writelog(MyErrorLog)
    #read data in from desired sheet
    #gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RE Taxes Data")
    
    #Get number of sheets like '% P&L Full Sort' sheets
    #gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %ilike% mySheetsLike)]
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>There weren't any sheets with the expected names (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}


### Populate MRI data sheet
if(okaytocontinue){
  # verify myMRISheet is present
  gSht_Sheet_MRI <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% myMRISheet)]
  gSht_Sheet_MRI_num <- length(gSht_Sheet_MRI)
  if(gSht_Sheet_MRI_num > 0){
    #sheet found, continue
    
    #query MRI
    myquery <- paste0(
      "
          SELECT /* SNOWFLAKE version */
          IFNULL(TO_CHAR(TRY_CAST(BLDG.BLDGID AS INT)),BLDG.BLDGID) AS BLDG
          ,	BLDG.BLDGID AS BLDGID
          ,	BLDG.INACTIVE
          ,	MNGR.MNGRNAME AS PROP_MGR
          FROM MRI.BLDG
          JOIN MRI.MNGR ON BLDG.MNGRID = MNGR.MNGRID
      "
    )
    #mydata <- sqlQuery(mySfDB, myquery, stringsAsFactors = FALSE)
    mydata <- dbGetQuery(mySfDB, myquery)
    mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
    if(mydata_status[[1]] == TRUE){
      #UPDATE MRI sheet
      #clear existing data in MRI sheet
      range_clear(gSht_get$spreadsheet_id, sheet = myMRISheet, range = NULL, reformat = FALSE)
      #write new data
      sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = myMRISheet)
      #replace headers to replace underscores with spaces
      #my_headers <- data.frame(gsub("_"," ",mydata[0,]))
      Sys.sleep(2)
    }
    
    
  }else{
    #MRI sheet not found, warn
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine!</p>",
                       "<p>There weren't any sheets named like '", myMRISheet, "' ",
                       #"<p>There weren't any sheets with the expected names (", 
                       #paste(mySheets, collapse = "; "),
                       " found in the '", gSht_get$name, "' workbook.",
                       "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Missing expected sheet"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}




### Populate MRI Asset Mgrs sheet
if(okaytocontinue){
  # verify myMRISheet is present
  gSht_Sheet_MRI <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% myMRIAssetMgrSheet)]
  gSht_Sheet_MRI_num <- length(gSht_Sheet_MRI)
  if(gSht_Sheet_MRI_num > 0){
    #sheet found, continue
    
    #query MRI
    myquery <- paste0(
      "
          SELECT /* SNOWFLAKE version */
          	p.projid AS PROJID,
          	e.entityid as ENTITYID,
          	IFNULL(TO_CHAR(TRY_CAST(BLDG.BLDGID AS INT)),BLDG.BLDGID) AS BLDG,
          	BLDG.BLDGID,
          	BLDG.INACTIVE,
          	p.assetmgr AS ASSETMGR
          from MRI.PROJ p
          left join MRI.ENTITY e
          on p.PROJID = e.PROJID
          left join MRI.BLDG
          on e.ENTITYID = BLDG.ENTITYID
          where (BLDG.INACTIVE is null or UPPER(BLDG.INACTIVE) = 'N')
      "
    )
    mydata <- dbGetQuery(mySfDB, myquery)
    mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
    if(mydata_status[[1]] == TRUE){
      #UPDATE MRI sheet
      #clear existing data in MRI sheet
      range_clear(gSht_get$spreadsheet_id, sheet = myMRIAssetMgrSheet, range = NULL, reformat = FALSE)
      #write new data
      sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = myMRIAssetMgrSheet)
      #replace headers to replace underscores with spaces
      #my_headers <- data.frame(gsub("_"," ",mydata[0,]))
      Sys.sleep(2)
    }
    
    
  }else{
    #MRI sheet not found, warn
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine!</p>",
                       "<p>There weren't any sheets named like '", myMRIAssetMgrSheet, "' ",
                       #"<p>There weren't any sheets with the expected names (", 
                       #paste(mySheets, collapse = "; "),
                       " found in the '", gSht_get$name, "' workbook.",
                       "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Missing expected sheet"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}




if(okaytocontinue){
  #loop through each sheet and read data
  #mydata_combined
  for(i in 1:gSht_Sheets_num){
    #read sheet into temp df
    gSht_Curr <- read_sheet(gSht_get$spreadsheet_id, sheet = gSht_Sheets[[i]])
    
    #copy only needed columns from data, filter in case of strings in GL column
    gSht_Curr_ColNames <- colnames(gSht_Curr)
    myColIntersect <- intersect(myColNames, gSht_Curr_ColNames)
    
    if(length(myColIntersect) < length(myColNames)){
      #One or more columns missing, create dummy df to trigger error log
      mydata <- data.frame(Issue = "Missing column(s)")
    }else{
      #columns present, populate rows
      mydata <- dplyr::select(dplyr::filter(gSht_Curr, 
                                            get(myColName_bldg) < 10000 & get(myColName_bldg) > 0),
                              all_of(myColNames)
      )
    }

    
    #check that expected # of rows/columns present
    if(ncol(mydata) < length(myColNames) || nrow(mydata) < 5 ){
      #no rows or not all expected columns present, log missing
      myissue <- case_when(
        ncol(mydata) < length(myColNames) ~ "Missing Columns",
        nrow(mydata) < 1 ~ "No Valid Rows",
        TRUE ~ "Unknown Issue"
      )
      if(exists("myerrors_sheets")){
        #add to existing errors
        myerrors_sheets[nrow(myerrors_sheets)+1, ] <- c(gSht_Sheets[[i]], myissue)
      }else{
        #create new error dataframe
        myerrors_sheets <- data.frame(Sheet = gSht_Sheets[[i]],
                                      Issue = myissue)
      }
    }else{
      #columns and rows okay
      #rename columns
      setnames(mydata, 
               old = myColNames, 
               new = myColNames_New,
               skip_absent = TRUE)
      if(exists("mydata_combined")){
        #add to existing data
        mydata_combined <- rbind(mydata_combined, mydata)
      }else{
        #create dataframe
        mydata_combined <- mydata
      }
      rm(mydata)
    }
    
  } #for(i in 1:gSht_Sheets_num)
  
  
  #if rows were present, truncate Snowflake table and load with results
  if(nrow(mydata_combined)>0){
    # myColName_OST_New, convert 'X' value in Google data to 'Y' and NULL (or anything else) to 'N'
    mydata_combined$OCC_SINGLE_TENANT <- sapply(mydata_combined$OCC_SINGLE_TENANT, function(x) if(is.na(x)){'N'}else{if(str_to_upper(as.character(x))=='X'){'Y'}else{'N'}} )
    mydata_combined$SUPPORT_RM <- NA
    #MOVE SUPPORT_RM column
    mydata_combined <- mydata_combined %>% relocate(SUPPORT_RM, .after = RM)
    #CLEAN DATA
    mydata_combined$BLDG_TYPE <- gsub("[^[:print:]]", "", mydata_combined$BLDG_TYPE)
    mydata_combined$ZIP <- gsub("[^0-9.-]", "", mydata_combined$ZIP)
    
    # Trunc SNOWFLAKE table
    dbBegin(mySfDB)
    myquery <- paste0('truncate table if exists ', myTableName)
    myTrucResults <- dbSendQuery(mySfDB, myquery)
    dbCommit(mySfDB)
    dbClearResult(myTrucResults)
    
    #populate Snowflake
    #20241010: rs_write <- dbWriteTable(myOracleDB, myTable, mydata_combined, row.names = FALSE , append = TRUE, schema = mySchema)
    rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata_combined)
    #20241010: dbCommit(myOracleDB)
    
    #compare sum of the BLDG column of the data frame to the database to ensure all rows inserted as expected
    myquery <- paste0(
      "select sum(", gSht_bldg_colname_New, ")
        from ", myTableName
    )
    #db_checksum <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    db_checksum <- dbGetQuery(mySfDB, myquery)
    mydata_checksum <- sum(as.numeric(mydata_combined[[gSht_bldg_colname_New]]),na.rm=TRUE)
    if(round(db_checksum[[1]],2) != round(mydata_checksum,2)){
      #send warning that sums are different
      # create body of warning email
      bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                         "have been an error in the ", myTableName,
                         " database load. The sum of the ", gSht_bldg_colname_New, " column in the Google sheet was: <br/>",
                         round(mydata_checksum,2), "<br/><br/>",
                         "The sum in the ", myTableName, " table is: <br/>",
                         round(db_checksum[[1]],2), "<br/><br/>",
                         "The query is contained in the ", myReportName, " script on the ",
                         "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                         warn_sig,
                         sep = ""
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Mis-match of Google Sheet sum vs. Database load"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
    }else{
      #send completion email
      bodytext <- paste0("This is an automated email to inform you that it appears the ",
                         "<b>", myReportName, "</b> routine has ",
                         "completed and results should now be available in the ", 
                         myTableName, " table.<br/> <br/>",
                         warn_sig)
      #send mail
      #mailsend(norm_recip,
      #         paste0(myReportName, " Status: COMPLETE"),
      #         bodytext,
      #         attachment = NULL,
      #         test = testing_emails,
      #         testrecipient = test_recip
      #)
    }
  
  }else{
    #no rows in results, send warning
    # create body of warning email
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       " database load. There weren't any rows in the final routine queries to upload. ",
                       "<br/><br/>",
                       "The query is contained in the ", myReportName, " script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Mis-match of Google Sheet sum vs. Database load"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
  #if routine completed, but there were errors in one or more sheets, send email
  if(exists("myerrors_sheets")){
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       " database load. The following sheets seemed to have issues and were not loaded. ",
                       "<br/><br/>",
                       print(xtable(myerrors_sheets, 
                                    caption = paste0("File: ", gSht_get$name, " (", report.time.txt, ")")),
                             align = c(rep("l",2), rep("l", ncol(myerrors_sheets) - 1)),
                             html.table.attributes = "border=2 cellspacing=1",
                             type = "html",
                             caption.placement = "top",
                             include.rownames=FALSE),
                       "<br/><br/>",
                       "If the issue above is noted as 'Missing Columns' the routine is expecting ",
                       "the following column headers in the Google sheets: ", paste(myColNames, collapse = "; "), ".<br/><br/>",
                       "The query is contained in the ", myReportName, " script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: One or more sheets not loaded"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    rm(myerrors_sheets)
  }
  
  
}




