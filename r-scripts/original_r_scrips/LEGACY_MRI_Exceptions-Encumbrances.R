#library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #20241121: replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
#library(RODBC)
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20250124

### 20250124 change:
### added new criteria for loan due date passed, but current owed > 0 and
### inactive bldgid but loan current owed > 0

### 20250123 change:
### fixed two bugs in SQL

### 20241209 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced check_mydata_rows() function with more universal check_mydf_rows()
### sending emails from generic Legacy Reporting email address

### 20230418 change:
### added details for multiple DEBTTYPE values

### 20220606 change:
### updated mailsend to use keyring

### 20220505 change:
### moved from RODBC to odbc & DBI SQL method to improve datatype consistency
### Updated email body with minor improvements

### 20220503 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

scriptfolder <- "LEGACY_MRI_Exceptions-Encumbrances"
myReportName <- "MRI Encumbrance Exceptions"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
Sys.sleep(1.5)
rptFN <- paste0("MRI_Encumbrance_Exception_Details", ".xlsx")
myReportCriteria <- paste0("<p><b>Criteria for inclusion in the report:</b> (note any building with $0 owed is EXCLUDED)<ul>",
                           "<li>Current loan has multiple CURRENT OWED AMOUNTS</li>",
                           "<li>Current loan has multiple ORIGINAL OWED AMOUNTS</li>",
                           "<li>Current loan has multiple DUE DATES</li>",
                           "<li>Loan DUE DATE in past, but CURRENT OWED > $0</li>",
                           "<li>Inactive bldg has loan where CURRENT OWED > $0</li>",
                           "<li>Current loan has multiple INTEREST RATES</li>",
                           "<li>Current loan has multiple DEBT TYPES</li>",
                           "</ul></p>"
                           )

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}
logpath <- file.path(mainpath,scriptfolder)
rptpath <- logpath
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")

# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>")
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")




### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load...log
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
}





### Find Exceptions and email results
if(okaytocontinue){
  
  myReportPath <- rptpath
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery_exceptions <- paste0(
    "
      SELECT CMPY_ID AS CMPY_ID
      ,	LOANID AS LOAN_ID
      ,	ISSUE
      ,	ISSUE_COUNT
      ,	ISSUE_VALUES
      ,	BLDGID
      ,	BLDG_VALUES
      FROM
      (
      	SELECT /* Rows where Multiple CURRENTOWED values */
      		ENCUMB.CMPY_ID
      	,	ENCUMB.LOANID
      	,	'Multiple Current Owed Values' AS ISSUE
      	,	MCO.ISSUE_COUNT
      	,	MCO.ISSUE_VALUES
      	,	ENCUMB.TABLEKEY AS BLDGID
      	,	TO_VARCHAR(ENCUMB.CURRENTOWED,'999,999,999,990.00') AS BLDG_VALUES
      	FROM MRI.ENCUMB
      	JOIN
      	(
      		SELECT /* Multiple CURRENTOWED values */
      			CMPY_ID
      		,	LOANID
      		,	'Multiple Current Owed Values' AS ISSUE
      		,	COUNT(*) as ISSUE_COUNT
      		--,	STRING_AGG(FORMAT(CURRENTOWED,'#,0.00'), '; ') WITHIN GROUP (ORDER BY CURRENTOWED ASC) AS [ISSUE_VALUES]
      		,	LISTAGG(TO_VARCHAR(CURRENTOWED,'999,999,999,990.00'), '; ') WITHIN GROUP (ORDER BY CURRENTOWED ASC) AS ISSUE_VALUES
      		--,	TO_VARCHAR(CURRENTOWED,'999,999,999,990.00')
      		FROM
      		(
      			SELECT DISTINCT
      				CMPY_ID
      			,	LOANID
      			,	CURRENTOWED
      			FROM MRI.ENCUMB ie
      			WHERE ie.TABLEID = 'BLDG'
      				and ie.CURRENTOWED > 0
      			GROUP BY 
      				CMPY_ID
      			,	LOANID
      			,	CURRENTOWED
      		) CNT
      		GROUP BY
      			CNT.CMPY_ID
      		,	CNT.LOANID
      		--, 	CURRENTOWED
      		HAVING COUNT(*) > 1
      	) MCO
      	ON ENCUMB.CMPY_ID = MCO.CMPY_ID
      		AND ENCUMB.LOANID = MCO.LOANID
      
      	UNION ALL
      
      	SELECT /* Rows where Multiple Original AMOUNT values */
      		ENCUMB.CMPY_ID
      	,	ENCUMB.LOANID
      	,	'Multiple Original Amount Values' AS ISSUE
      	,	MOO.ISSUE_COUNT
      	,	MOO.ISSUE_VALUES
      	,	ENCUMB.TABLEKEY AS BLDGID
      	,	TO_VARCHAR(ENCUMB.AMOUNT,'999,999,999,990.00') AS BLDG_VALUES
      	FROM MRI.ENCUMB
      	JOIN
      	(
      		SELECT /* Multiple Original Amount of Debt values */
      			CMPY_ID
      		,	LOANID
      		,	'Multiple Original Amount Values' AS ISSUE
      		,	COUNT(*) as ISSUE_COUNT
      		,	LISTAGG(TO_VARCHAR(AMOUNT,'999,999,999,990.00'), '; ') WITHIN GROUP (ORDER BY AMOUNT ASC) AS ISSUE_VALUES
      		FROM
      		(
      			SELECT DISTINCT
      				CMPY_ID
      			,	LOANID
      			,	AMOUNT
      			FROM MRI.ENCUMB ie
      			WHERE ie.TABLEID = 'BLDG'
      				and ie.CURRENTOWED > 0
      			GROUP BY 
      				CMPY_ID
      			,	LOANID
      			,	AMOUNT
      		) CNT
      		GROUP BY
      			CNT.CMPY_ID
      		,	CNT.LOANID
      		HAVING COUNT(*) > 1
      	) MOO
      	ON ENCUMB.CMPY_ID = MOO.CMPY_ID
      		AND ENCUMB.LOANID = MOO.LOANID
      
      	UNION ALL
      
      	SELECT /* Rows where Multiple DUE DATE values */
      		ENCUMB.CMPY_ID
      	,	ENCUMB.LOANID
      	,	'Multiple Due Date Values' AS ISSUE
      	,	MDD.ISSUE_COUNT
      	,	MDD.ISSUE_VALUES
      	,	ENCUMB.TABLEKEY AS BLDGID
      	--,	convert(date, ENCUMB.DUEDATE) AS BLDG_VALUES
      	,	TO_VARCHAR(ENCUMB.DUEDATE, 'MM/dd/yyyy')  AS BLDG_VALUES
      	FROM MRI.ENCUMB
      	JOIN
      	(
      		SELECT /* Multiple Due Date of Debt values */
      			CMPY_ID
      		,	LOANID
      		,	'Multiple Due Date Values' AS ISSUE
      		,	COUNT(*) as ISSUE_COUNT
      		,	LISTAGG(TO_VARCHAR(DUEDATE, 'MM/dd/yyyy'), '; ') WITHIN GROUP (ORDER BY DUEDATE ASC) AS ISSUE_VALUES
      		FROM
      		(
      			SELECT DISTINCT
      				CMPY_ID
      			,	LOANID
      			,	DUEDATE
      			FROM MRI.ENCUMB ie
      			WHERE ie.TABLEID = 'BLDG'
      				and ie.CURRENTOWED > 0
      			GROUP BY 
      				CMPY_ID
      			,	LOANID
      			,	DUEDATE
      		) CNT
      		GROUP BY
      			CNT.CMPY_ID
      		,	CNT.LOANID
      		HAVING COUNT(*) > 1
      	) MDD
      	ON ENCUMB.CMPY_ID = MDD.CMPY_ID
      		AND ENCUMB.LOANID = MDD.LOANID  
      		
      	UNION ALL
      
		SELECT /* Due Date has passed but CURRENTOWED > 0 and BLDGID is active */
			CMPY_ID
		,	LOANID
		,	'Due Date in past but $ OWED > 0' AS ISSUE
		,	COUNT(*) AS ISSUE_COUNT
		,	TO_VARCHAR(DUEDATE, 'MM/dd/yyyy')||' $'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS ISSUE_VALUES
		,	IE.TABLEKEY AS BLDGID
		,   TO_VARCHAR(DUEDATE, 'MM/dd/yyyy')||' $'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS BLDG_VALUES
		FROM MRI.ENCUMB ie
		LEFT JOIN MRI.BLDG B
		ON IE.TABLEKEY = B.BLDGID
		WHERE ie.TABLEID = 'BLDG'
			and ie.CURRENTOWED > 0
			AND TO_DATE(ie.DUEDATE) < CURRENT_DATE
			AND IFNULL(B.INACTIVE, 'N') = 'N'
		GROUP BY 
			CMPY_ID
		,	LOANID
		,	DUEDATE
		,	CURRENTOWED 
		,	IE.TABLEKEY
      
      	UNION ALL
      
      	SELECT /* CURRENTOWED > 0 and BLDGID is INACTIVE */
			CMPY_ID
		,	LOANID
		,	'Inactive BLDGID, but Current Owed > 0' AS ISSUE
		,	COUNT(*) AS ISSUE_COUNT
		,	'$'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS ISSUE_VALUES
		,	IE.TABLEKEY AS BLDGID
		,   '$'||TRIM(TO_VARCHAR(CURRENTOWED, '999,999,999,999.00')) AS BLDG_VALUES
		FROM MRI.ENCUMB ie
		LEFT JOIN MRI.BLDG B
		ON IE.TABLEKEY = B.BLDGID
		WHERE ie.TABLEID = 'BLDG'
			and ie.CURRENTOWED > 0
			AND IFNULL(B.INACTIVE, 'N') = 'Y'
		GROUP BY 
			CMPY_ID
		,	LOANID
		,	DUEDATE
		,	CURRENTOWED 
		,	IE.TABLEKEY
      
      	UNION ALL
      
      	SELECT /* Rows where Multiple Interest Rate values */
      		ENCUMB.CMPY_ID
      	,	ENCUMB.LOANID
      	,	'Multiple Interest Rate Values' AS ISSUE
      	,	MIR.ISSUE_COUNT
      	,	MIR.ISSUE_VALUES
      	,	ENCUMB.TABLEKEY AS BLDGID
      	,	TO_VARCHAR(ENCUMB.RATECUR) AS BLDG_VALUES
      	FROM MRI.ENCUMB
      	JOIN
      	(
      		SELECT /* Multiple Interest Rate values */
      			CMPY_ID
      		,	LOANID
      		,	'Multiple Interest Rate Values' AS ISSUE
      		,	COUNT(*) as ISSUE_COUNT
      		,	LISTAGG(RATECUR, '; ') WITHIN GROUP (ORDER BY RATECUR ASC) AS ISSUE_VALUES
      		FROM
      		(
      			SELECT DISTINCT
      				CMPY_ID
      			,	LOANID
      			,	RATECUR
      			FROM MRI.ENCUMB ie
      			WHERE ie.TABLEID = 'BLDG'
      				and ie.CURRENTOWED > 0
      			GROUP BY 
      				CMPY_ID
      			,	LOANID
      			,	RATECUR
      		) CNT
      		GROUP BY
      			CNT.CMPY_ID
      		,	CNT.LOANID
      		HAVING COUNT(*) > 1
      	) MIR
      	ON ENCUMB.CMPY_ID = MIR.CMPY_ID
      		AND ENCUMB.LOANID = MIR.LOANID
      	
      	UNION ALL
      	
      	SELECT /* Rows where Multiple DEBTTYPE values - added 20230418 */
      		ENCUMB.CMPY_ID
      	,	ENCUMB.LOANID
      	,	'Multiple Debt Type Values' AS ISSUE
      	,	MDEBT.ISSUE_COUNT
      	,	MDEBT.ISSUE_VALUES
      	,	ENCUMB.TABLEKEY AS BLDGID
      	,	RTRIM(DEBTTYPEID) AS BLDG_VALUES
      	FROM MRI.ENCUMB
      	JOIN
      	(
      		SELECT /* Multiple DEBT TYPE values */
      			CMPY_ID
      		,	LOANID
      		,	'Multiple Debt Type Values' AS ISSUE
      		,	COUNT(*) as ISSUE_COUNT
      		,	LISTAGG(RTRIM(DEBTTYPEID), '; ') WITHIN GROUP (ORDER BY DEBTTYPEID ASC) AS ISSUE_VALUES
      		FROM
      		(
      			SELECT DISTINCT
      				CMPY_ID
      			,	LOANID
      			,	DEBTTYPEID
      			FROM MRI.ENCUMB ie
      			WHERE ie.TABLEID = 'BLDG'
      				and ie.CURRENTOWED > 0
      			GROUP BY 
      				CMPY_ID
      			,	LOANID
      			,	DEBTTYPEID
      		) CNT
      		GROUP BY
      			CNT.CMPY_ID
      		,	CNT.LOANID
      		HAVING COUNT(*) > 1
      	) MDEBT
      	ON ENCUMB.CMPY_ID = MDEBT.CMPY_ID
      		AND ENCUMB.LOANID = MDEBT.LOANID
      	
      ) DETAILS
      ORDER BY DETAILS.CMPY_ID
      ,	DETAILS.LOANID
      ,	DETAILS.ISSUE
      ,	DETAILS.BLDG_VALUES
      ,	DETAILS.BLDGID
    "
  )
  #20241209: mydata <- dbGetQuery(mySSdb, myquery_exceptions)
  mydata <- dbGetQuery(mySfDB, myquery_exceptions)
  #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
  mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    #replace underscores in column names with space
    names(mydata) <- gsub("_", " ", names(mydata))
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("CMPY ID",
                                                "LOAN ID",
                                                "ISSUE",
                                                "ISSUE COUNT",
                                                "ISSUE VALUES",
                                                "BLDGID",
                                                "BLDG VALUES"
                                                #"",
                                                )
                                   ,
                                   width = c(10,
                                             if(max(nchar(na.omit(mydata[,2]))) > 12){min(30, max(nchar(na.omit(mydata[,2]))))}else{12},
                                             if(max(nchar(na.omit(mydata[,3]))) > 24){min(38, max(nchar(na.omit(mydata[,3]))))}else{25},
                                             8,
                                             if(max(nchar(na.omit(mydata[,5]))) > 23){min(36, max(nchar(na.omit(mydata[,5]))))}else{24},
                                             8,
                                             if(max(nchar(na.omit(mydata[,7]))) > 10){min(38, max(nchar(na.omit(mydata[,7]))))}else{11}
                                             )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    myemailbodycols <- c(1,2,3,4,5)
    mydata_emailbody <- mydata[,myemailbodycols]
    #DEDUP mydata_emailbody to get summary vs details of full dataframe
    mydata_emailbody <- mydata_emailbody %>% distinct()
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata_emailbody)<20){
      bodytable <- paste0("<p>The info below contains MRI data (from yesterday) that ",
                          "appears to be an exception. <b>See attached Excel file for full details.</b> ",
                          "</p>",
                          "<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)
                          ),
                          #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                          html.table.attributes = "border=2 cellspacing=1",
                          type = "html",
                          caption.placement = "top",
                          include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p><strong><em>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</em></strong></p>"
      )
    }
    bodytext <- paste0("<p><h2>REPORT: ", this_ReportName, "</h2>",
                       "</p>",
                       myReportCriteria,
                       bodytable,
                       "<br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   inline = TRUE,
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}


#20241209: DBI::dbDisconnect(mySSdb)
DBI::dbDisconnect(mySfDB)



