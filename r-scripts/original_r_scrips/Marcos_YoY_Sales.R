library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(readr)
library(keyring)

# written by <PERSON> July 2019
#
# This script builds a .csv file of Marcos Sales for last year
# and this year, this is used by a Marcos advertising plan
# macro that builds an advertising calendar for each store
# which includes their previous year and current year sales

# Version 20240530

### 20240530 change:
### updated SQL to fix bug where new stores didn't show up when prior year
### sales weren't present

### 20231206 change:
### revised oracle queries to use new steve.ft_order_summary_weekly table
### VASTLY speeding up this routine from 20 minutes to 20 seconds

### 20230612 change:
### added <PERSON> to warn_recip

### 20220606 change:
### updated mailsend to use keyring

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)
### also modified to only create PY file 

# 190725 version changes:
### extended query to include first 4 weeks of the next year 
### for visibility into PY sales in first month of the new year (plan calendars)

# 190716 version changes:
### New file


logname <- "Marcos-YoYSales-Log.csv"

logpath <- "C:/Users/<USER>/Documents/ReportFiles/MARCOS_YoY/"
rptpath <- "C:/Users/<USER>/Documents/ReportFiles/MARCOS_YoY/"
# Steve PC testing paths below (normal use is the two lines above)
#logpath <- "C:/Users/<USER>/Documents/R Scripts/TestScripts/Marcos/YoY Sales/"
#rptpath <- "C:/Users/<USER>/Documents/R Scripts/TestScripts/Marcos/YoY Sales/"

rptpath_central <- "//*************/public/steveo/monday_reports/"


mydb <- odbcConnect("FVPA64", "deanna", key_get("Oracle", "deanna"))


# define some variables #

query.date <- format(Sys.Date(), "%d-%b-%y")
# next line(s) are test lines that replace line(s) above for test purposes only
#query.date <- format(as.Date("05-MAY-19","%d-%b-%y"),"%d-%b-%y")


report.date <- format(as.Date(query.date, "%d-%b-%y"), "%Y%m%d")  # YYYYMMDD format of date for saves of report files
report.time <- format(Sys.time(), "%H%M%S")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("Steve Olson<<EMAIL>>", "Sean Coyle<<EMAIL>>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")


### define some functions ###

mailsend <- function(recipient, subject, body, attachment = NULL){
  library(mailR)
  sender <- "Marcos YoY Sales Report <<EMAIL>>"
  recipients <- recipient
  send.mail(from = sender,
            to = recipients,
            replyTo = "<EMAIL>",
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = "<EMAIL>",            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            send = TRUE)
}


writelog <- function(LogTable){
  fn <- paste0(logpath, logname)
  write.csv(LogTable, file = fn, row.names=FALSE)
}


file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}


day_ord <- function(dates){
  dayy <- day(dates)
  suff <- case_when(dayy %in% c(11,12,13) ~ "th",
                    dayy %% 10 == 1 ~ 'st',
                    dayy %% 10 == 2 ~ 'nd',
                    dayy %% 10 == 3 ~'rd',
                    TRUE ~ "th")
  return(paste0(dayy, suff))
}


### check if log present/up-to-date ###

NewErrorLog <- FALSE
if(file.exists(paste0(logpath, logname)) ) {
  MyErrorLog <- read.csv(file = paste0(logpath, logname), sep=",", stringsAsFactors = FALSE)
  #check if log is from prior week, is so replace values with default starting values
  if( MyErrorLog[1,"QUERY_DATE"] != query.date ){
    #log is from previous date, replace with new default values
    NewErrorLog <- TRUE
  }
} else {
  # log not found, create new log values
  NewErrorLog <- TRUE
}
if( NewErrorLog ) {
  MyErrorLog <- data.frame(QUERY_DATE = query.date, 
                           SALES_STATUS = 'NO LOG FILE',
                           QUERY_STATUS = 'NO LOG FILE',
                           #ST_EMAIL_STATUS = 'NO LOG FILE',
                           PROGRESS = 'NO LOG FILE',
                           stringsAsFactors = FALSE)
}

# check if FT_ORDER table loaded for the week (compare store counts too)
# and that steve.ft_order_summary_weekly has been populated
myquery <- paste0(
  "
    SELECT 
    DISTINCT TRUNC(o.DAY) AS DAY,
    COUNT(DISTINCT o.STORE_NUMBER) AS ST_COUNT,
    COUNT(DISTINCT(w.e_date)) as WEEKLY_COUNT
    FROM ft_order o
    left join steve.ft_order_summary_weekly w on o.day = w.e_date
    WHERE o.DAY >= trunc(to_date('",query.date,"') - 7, 'IW') 
    and o.DAY < trunc(to_date('",query.date,"') - 0, 'IW') 
    --WHERE o.DAY >= trunc(to_date('13-dec-23') - 7, 'IW') 
    --and o.DAY < trunc(to_date('13-dec-23') - 0, 'IW') 
    GROUP BY TRUNC(o.DAY)
    ORDER BY TRUNC(o.DAY)
  "
)
ftorder.stcnt <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
ftorder.stcnt[] <- lapply(ftorder.stcnt[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
ftorder.status <- case_when(
  nrow(ftorder.stcnt) < 7 ~ 'NOT READY',
  min(ftorder.stcnt$ST_COUNT) <= (max(ftorder.stcnt$ST_COUNT) * .03 + 6) ~ 'NOT READY',
  TRUE ~ 'READY'
)
ftweekly.status <- case_when(
  max(ftorder.stcnt$WEEKLY_COUNT) == 1 ~ 'READY',
  TRUE ~ 'NOT READY'
)

sales.status <- data.frame(QUERY_DATE = query.date, SALES_STATUS = ftorder.status, WEEKLY_SUMMARY_STATUS = ftweekly.status, stringsAsFactors = FALSE)


### testing line ###
#sales.status[1,"SALES_STATUS"] <- 'NOT READY'


if (sales.status[1,"SALES_STATUS"] != 'READY' || sales.status[1,"WEEKLY_SUMMARY_STATUS"] != 'READY' ) {
  # ABORT: SALES not ready, send warning email, log status and skip main procedure
  
  query.status <- data.frame(QUERY_STATUS = 'NOT STARTED', stringsAsFactors = FALSE)
  progress <- c()
  if(sales.status[1,"SALES_STATUS"] != 'READY'){
    progress <- c(progress,'SALES STATUS')
  }
  if(sales.status[1,"WEEKLY_SUMMARY_STATUS"] != 'READY'){
    progress <- c(progress,'FT_ORDER_SUMMARY_WEEKLY STATUS')
  }
  progress.status <- data.frame(PROGRESS = paste(progress, collapse = "; "), stringsAsFactors = FALSE)
  
  # create error LOG so task scheduler tries again (sales.status contains QUERY_DATE & SALES_STATUS columns)
  MyErrorLog <- cbind(sales.status, query.status, progress.status)
  writelog(MyErrorLog)
  
  print('TABLES NOT READY')
  
  # create body of warning email
  ftorder.stcnt$DAY <- as.character(ftorder.stcnt$DAY)
  bodytext <- paste0("This is an automated email to inform you that it appears that the SALES table ",
                     "needed for the <b>Marco's YoY Sales Report</b> routine is not ready yet.<br/><br/>",
                     print(xtable(MyErrorLog, 
                                  caption = paste0("Marcos YoY Sales Report (", report.time.txt, ")")),
                           align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                           html.table.attributes = "border=2 cellspacing=1",
                           type = "html",
                           caption.placement = "top",
                           include.rownames=FALSE),
                     "<br><br>Here are the store counts per day per the <b>ft_orders</b> table:<br>",
                     print(xtable(ftorder.stcnt, 
                                  digits = rep(0,ncol(ftorder.stcnt)+1),
                                  ),
                                html.table.attributes = "border=2 cellspacing=1",
                                type = "html",
                                caption.placement = "top",
                                include.rownames=FALSE
                     ),
                     "<br/>The routine will attempt to run again in 40 minutes.<br/> <br/>",
                     warn_sig,
                     sep = ""
  )
  #send mail
  mailsend(warn_recip,
           "Marco's YoY Sales Issue: TABLES NOT READY",
           bodytext
  )
  
  # early end of routine
  
}

if (sales.status[1,"SALES_STATUS"] == 'READY' & sales.status[1,"WEEKLY_SUMMARY_STATUS"] == 'READY' & MyErrorLog[1,"PROGRESS"] != 'COMPLETE' ) {
  query.status <- data.frame(QUERY_STATUS = 'STARTED', stringsAsFactors = FALSE)
  progress.status <- data.frame(PROGRESS = 'QUERY STATUS', stringsAsFactors = FALSE)
  MyErrorLog <- cbind(sales.status, query.status, progress.status)
  writelog(MyErrorLog)
  mydatesquery <- paste0(
    "
      select 
        min(cy.period_year) as period_year, 
        to_char(max(cy.e_date) + 28, 'DD-MON-RR') as max_e_date,
        to_char(min(py.e_date), 'DD-MON-RR') as min_py_e_date
      from mp_calendar_weekly cy
      left join mp_calendar_weekly py on (cy.period_year - 1) = py.period_year
      where cy.period_year = (select period_year from mp_calendar where to_date('",query.date,"') between s_date and e_date)
    "
  )
  mydates <- sqlQuery(mydb, mydatesquery, stringsAsFactors = FALSE)
  #mydates$PERIOD_YEAR[1]
  #mydates$MAX_E_DATE[1]
  #mydates$MIN_PY_E_DATE[1]
  
  mysalesquery <- paste0(
    "
      select
          STORE_NUMBER,
          S_DATE,
          E_DATE,
          PERIOD_NUM,
          WEEK_NUM,
          SUM(LASTYEAR_SALES) AS LASTYEAR_SALES,
          SUM(THISYEAR_SALES) AS THISYEAR_SALES,
          SUM(LASTYEAR_ORDERS) AS LASTYEAR_ORDERS,
          SUM(THISYEAR_ORDERS) AS THISYEAR_ORDERS
      from
      (
            select
                CySales.store_number
            ,   cal.s_date
            ,   cal.e_date
            ,   cal.period_num
            ,   cal.week_num
            ,   CySales.LASTYEAR_SALES
            ,   CySales.THISYEAR_SALES
            ,   CySales.LASTYEAR_ORDERS
            ,   CySales.THISYEAR_ORDERS
            from mp_calendar_weekly cal
            left join
            (
                select
                    store_number
                ,   e_date
                ,   to_number(NULL) as LASTYEAR_SALES
                ,   netsales as THISYEAR_SALES
                ,   to_number(NULL) as LASTYEAR_ORDERS
                ,   totalorders as THISYEAR_ORDERS
                from steve.ft_order_summary_weekly
                where e_date >= to_date('", mydates$MIN_PY_E_DATE[1], "')
                --/* TEST LINE */where e_date >= to_date('01-JAN-23')
            ) CySales
            on cal.e_date = CySales.e_date
            where cal.period_year >= ", mydates$PERIOD_YEAR[1], "
            --/* TEST LINE */where cal.period_year >= 2024
            and cal.e_date <= to_date('", mydates$MAX_E_DATE[1], "')
            --/* TEST LINE */and cal.e_date <= to_date('26-JAN-25')
            --/* TEST LINE */and CySales.store_number = 3692
           
           union
      
            select
                PySales.store_number
            ,   cal.s_date
            ,   cal.e_date
            ,   cal.period_num
            ,   cal.week_num
            ,   PySales.LASTYEAR_SALES
            ,   PySales.THISYEAR_SALES
            ,   PySales.LASTYEAR_ORDERS
            ,   PySales.THISYEAR_ORDERS
            from mp_calendar_weekly cal
            left join
            (
                select
                    store_number
                ,   e_date
                ,   netsales as LASTYEAR_SALES
                ,   to_number(NULL) as THISYEAR_SALES
                ,   totalorders as LASTYEAR_ORDERS
                ,   to_number(NULL) as THISYEAR_ORDERS
                from steve.ft_order_summary_weekly
                where e_date >= to_date('", mydates$MIN_PY_E_DATE[1], "')
                --/* TEST LINE */where e_date >= to_date('01-JAN-23')
            ) PySales
            on (cal.e_date - 364) = PySales.e_date
            where cal.period_year >= ", mydates$PERIOD_YEAR[1], "
            --/* TEST LINE */where cal.period_year >= 2024
            and cal.e_date <= to_date('", mydates$MAX_E_DATE[1], "')
            --/* TEST LINE */and cal.e_date <= to_date('26-JAN-25')
            --/* TEST LINE */and PySales.store_number = 3692
      )combined
      group by
          STORE_NUMBER,
          S_DATE,
          E_DATE,
          PERIOD_NUM,
          WEEK_NUM
      ORDER BY S_DATE, STORE_NUMBER
    ",
    SEP = ""
  )
  
  yoy_sales <- sqlQuery(mydb, mysalesquery, stringsAsFactors = FALSE)
  #Add query date as final column header (blank column)
  yoy_sales[, query.date] <- NA
  
  query.status <- data.frame(QUERY_STATUS = 'COMPLETE', stringsAsFactors = FALSE)
  progress.status <- data.frame(PROGRESS = 'SAVING LOCAL REPORT', stringsAsFactors = FALSE)
  MyErrorLog <- cbind(sales.status, query.status, progress.status)
  writelog(MyErrorLog)
  
  #save local copy of the report first
  myFN <- paste0(rptpath, "MARCOS_YoY_Sales", ".csv")
  if (dir.exists(rptpath) & file.opened(myFN) == FALSE) {
    #write.csv(yoy_sales, file = myFN, na="", row.names=FALSE)
    write_excel_csv(yoy_sales, myFN, na="")
  } else {
    #send error msg
    bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                       "during the <b>Marco's YoY Sales Report</b> routine.<br/><br/>",
                       as.character(myFN),
                       "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                       "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             "Marco's YoY Sales Issue: REPORT FILE NOT SAVED",
             bodytext
    )
  }
  
  progress.status <- data.frame(PROGRESS = 'SAVING CENTRAL REPORT', stringsAsFactors = FALSE)
  MyErrorLog <- cbind(sales.status, query.status, progress.status)
  writelog(MyErrorLog)
  
  #save CENTRAL copy of the report
  myFN <- paste0(rptpath_central, "MARCOS_YoY_Sales", ".csv")
  if (dir.exists(rptpath_central) & file.opened(myFN) == FALSE) {
    #write.csv(yoy_sales, file = myFN, row.names=FALSE)
    #write.csv(yoy_sales, file = myFN, na="", row.names=FALSE)
    write_excel_csv(yoy_sales, myFN, na="")
    
    #only log as complete if the file is saved on central
    progress.status <- data.frame(PROGRESS = 'COMPLETE', stringsAsFactors = FALSE)
    MyErrorLog <- cbind(sales.status, query.status, progress.status)
    writelog(MyErrorLog)
    
  } else {
    #send error msg
    bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                       "during the <b>Marco's YoY Sales Report</b> routine.<br/><br/>",
                       as.character(myFN),
                       "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                       "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             "Marco's YoY Sales Issue: REPORT FILE NOT SAVED",
             bodytext
    )
  }
  
  
  
  
  
  
  
  ####------------------------------------------------------------####
  # 20200114: CHECK IF PRIOR YEAR REPORT IS PRESENT, IF NOT, COMPILE #
  ####------------------------------------------------------------####
  myquery <- paste0(
    "select PERIOD_YEAR - 1 
     from MP_CALENDAR 
     where next_day(to_date('",query.date,"') - 7, 'SUN') - 1 >= S_DATE 
      and next_day(to_date('",query.date,"') - 7, 'SUN') - 1 < E_DATE + 1
    "
  )
  py_salesyear <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  myFNpy <- paste0(rptpath_central, "MARCOS_YoY_Sales_", py_salesyear, ".csv")
  
  if(!file.exists(myFNpy) || yday(as.Date(query.date,"%d-%b-%y")) <= 36){
    #previous year file not found on central or within 1st period, create
    #previous year file to ensure all stores/data present
    mypydatesquery <- paste0(
      "
      select 
        min(cy.period_year) as period_year, 
        to_char(max(cy.e_date) + 28, 'DD-MON-RR') as max_e_date,
        to_char(min(py.e_date), 'DD-MON-RR') as min_py_e_date
      from mp_calendar_weekly cy
      left join mp_calendar_weekly py on (cy.period_year - 1) = py.period_year
      where cy.period_year = (select period_year - 1 from mp_calendar where to_date('",query.date,"') between s_date and e_date)
    "
    )
    mypydates <- sqlQuery(mydb, mypydatesquery, stringsAsFactors = FALSE)
    
    mypysalesquery <- paste0(
      "
      select
          STORE_NUMBER,
          S_DATE,
          E_DATE,
          PERIOD_NUM,
          WEEK_NUM,
          SUM(LASTYEAR_SALES) AS LASTYEAR_SALES,
          SUM(THISYEAR_SALES) AS THISYEAR_SALES,
          SUM(LASTYEAR_ORDERS) AS LASTYEAR_ORDERS,
          SUM(THISYEAR_ORDERS) AS THISYEAR_ORDERS
      from
      (
            select
                CySales.store_number
            ,   cal.s_date
            ,   cal.e_date
            ,   cal.period_num
            ,   cal.week_num
            ,   CySales.LASTYEAR_SALES
            ,   CySales.THISYEAR_SALES
            ,   CySales.LASTYEAR_ORDERS
            ,   CySales.THISYEAR_ORDERS
            from mp_calendar_weekly cal
            left join
            (
                select
                    store_number
                ,   e_date
                ,   to_number(NULL) as LASTYEAR_SALES
                ,   netsales as THISYEAR_SALES
                ,   to_number(NULL) as LASTYEAR_ORDERS
                ,   totalorders as THISYEAR_ORDERS
                from steve.ft_order_summary_weekly
                where e_date >= to_date('", mypydates$MIN_PY_E_DATE[1], "')
                --/* TEST LINE */where e_date >= to_date('01-JAN-23')
            ) CySales
            on cal.e_date = CySales.e_date
            where cal.period_year >= ", mypydates$PERIOD_YEAR[1], "
            --/* TEST LINE */where cal.period_year >= 2024
            and cal.e_date <= to_date('", mypydates$MAX_E_DATE[1], "')
            --/* TEST LINE */and cal.e_date <= to_date('26-JAN-25')
            --/* TEST LINE */and CySales.store_number = 3692
           
           union
      
            select
                PySales.store_number
            ,   cal.s_date
            ,   cal.e_date
            ,   cal.period_num
            ,   cal.week_num
            ,   PySales.LASTYEAR_SALES
            ,   PySales.THISYEAR_SALES
            ,   PySales.LASTYEAR_ORDERS
            ,   PySales.THISYEAR_ORDERS
            from mp_calendar_weekly cal
            left join
            (
                select
                    store_number
                ,   e_date
                ,   netsales as LASTYEAR_SALES
                ,   to_number(NULL) as THISYEAR_SALES
                ,   totalorders as LASTYEAR_ORDERS
                ,   to_number(NULL) as THISYEAR_ORDERS
                from steve.ft_order_summary_weekly
                where e_date >= to_date('", mypydates$MIN_PY_E_DATE[1], "')
                --/* TEST LINE */where e_date >= to_date('01-JAN-23')
            ) PySales
            on (cal.e_date - 364) = PySales.e_date
            where cal.period_year >= ", mypydates$PERIOD_YEAR[1], "
            --/* TEST LINE */where cal.period_year >= 2024
            and cal.e_date <= to_date('", mypydates$MAX_E_DATE[1], "')
            --/* TEST LINE */and cal.e_date <= to_date('26-JAN-25')
            --/* TEST LINE */and PySales.store_number = 3692
      )combined
      group by
          STORE_NUMBER,
          S_DATE,
          E_DATE,
          PERIOD_NUM,
          WEEK_NUM
      ORDER BY S_DATE, STORE_NUMBER    
    ",
      SEP = ""
    )
    
    yoy_pysales <- sqlQuery(mydb, mypysalesquery, stringsAsFactors = FALSE)
    #Add query date as final column header (blank column)
    yoy_pysales[, query.date] <- NA
    
    #save local copy of PY sales
    myFNpy <- paste0(rptpath, "MARCOS_YoY_Sales_", py_salesyear, ".csv")
    if (dir.exists(rptpath) & file.opened(myFNpy) == FALSE) {
      write_excel_csv(yoy_pysales, myFNpy, na="")
    } else {
      #send error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>Marco's YoY Sales Report</b> routine.<br/><br/>",
                         as.character(myFNpy),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               "Marco's YoY Sales Issue: REPORT FILE NOT SAVED",
               bodytext
      )
    }
    
    #save central copy of PY sales
    myFNpy <- paste0(rptpath_central, "MARCOS_YoY_Sales_", py_salesyear, ".csv")
    if (dir.exists(rptpath_central) & file.opened(myFNpy) == FALSE) {
      write_excel_csv(yoy_pysales, myFNpy, na="")
    } else {
      #send error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>Marco's YoY Sales Report</b> routine.<br/><br/>",
                         as.character(myFNpy),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               "Marco's YoY Sales Issue: REPORT FILE NOT SAVED",
               bodytext
      )
    }
    
    
    
  }
  
  #rm(yoy_sales)
} else {
  # SALES not ready or previously completed
}

