#library(RODBC) #********: not needed with change to new DB conn methods
library(xtable)
library(reshape2)
library(dplyr)
#********: library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #********: replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(keyring)
#********: library(ROracle)
library(DBI)
library(odbc)


# written by <PERSON> March 2022
# based on HV_PNL_LINE_STRUCTURE loading script

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version ********
### updated to Snowflake connection and updated Oracle from RODBC to odbc (and DBI queries)
### updated email from MailR that used username/PW to gmailr that uses OAuth
### updated normsig to corp standard at this time

### 20220323 change:
### New file



# Parameters
okaytocontinue <- TRUE

myTableName <- "STEVE.NA"
myReportName <- "LCP_MRI_GACC_Summary_update"
scriptfolder <- "LEGACY_MRI_GLSUM_Summary"

### DB connections
#Oracle connection
#********: Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ="GMT")
#Sys.setenv(ORA_SDTZ="GMT")
#********: drv <- dbDriver("Oracle")
#********: connect.string <- paste0(
#********:   "(DESCRIPTION=",
#********:   "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
#********:   "(CONNECT_DATA=(SID=", "fvpa", ")))"
#********: )
#********: myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")



centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}
logpath <- file.path(mainpath,scriptfolder)
rptpath <- logpath
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}





# Read MRI data
if(okaytocontinue){
  
  #MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  #MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  #writelog(MyErrorLog)
  
  #get main data
  myTableName_MRI <- "GACC"
  myquery <- paste0(
    "
      SELECT
      	ACCTNUM
      , IFNULL(GLMAP.NEW_ACCOUNT_NUMBER, SUBSTRING(GACC.ACCTNUM,3,5)) AS GL_ACCOUNT
      ,	ACCTNAME
      ,	TYPE
      ,	M_1099ACCT
      ,	LEGALACCT
      ,	ACTIVE
      FROM MRI.GACC
      LEFT JOIN CORPORATE.ORACLE_TO_SAGE_GL_MAPPING GLMAP
      ON SUBSTRING(GACC.ACCTNUM, 1, 2) = 'MR'
      AND TRY_CAST(SUBSTRING(GACC.ACCTNUM,3,4) AS number) = GLMAP.OLD_ACCOUNT_NUMBER
    "
  )
  #********: mydata <- sqlQuery(mySSdb, myquery, stringsAsFactors = FALSE)
  mydata <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
  #********: mySchema <- "STEVE"
  mySchema <- "CORPORATE"
  myTable <- "MRI_GACC"
  myTableName <- paste(mySchema, myTable, sep = ".")
  if(mydata_status[[1]] == TRUE){
    # load database
    # get column names and datatypes of Oracle table
    #********: myTableName <- "STEVE.MRI_GACC"
    gSht_glaccount_colname <- "GL_ACCOUNT"
    #********: tmpDBinfo <- sqlColumns(mydb, myTableName)
    #********: columnTypes <- as.character(tmpDBinfo$TYPE_NAME)
    #********: names(columnTypes) <- as.character(tmpDBinfo$COLUMN_NAME)
    # Trunc Oracle table
    #********: myquery <- paste0('truncate table ', myTableName, ' drop storage')
    #********: myTrucResults <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    #********: myTrucResults <- dbGetQuery(myOracleDB, myquery)
    #commit
    #********: myquery <- paste0('commit work')
    #********: sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    
    #populate Oracle
    #********: sqlSave(mydb, 
    #********:         mydata, 
    #********:         tablename = myTableName,  
    #********:         append = TRUE, 
    #********:         rownames = FALSE, 
    #********:         colnames = FALSE, 
    #********:         safer = TRUE,
    #********:         fast = FALSE,
    #********:         addPK = FALSE, 
    #********:         varTypes = columnTypes,
    #********:         nastring = NULL)
    #********: rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    
    # Trunc SNOWFLAKE table
    dbBegin(mySfDB)
    myquery <- paste0('truncate table if exists ', myTableName)
    myTrucResults <- dbSendQuery(mySfDB, myquery)
    dbCommit(mySfDB)
    dbClearResult(myTrucResults)
    #populate Snowflake
    rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata)
    
    #compare COUNT of a column of the data frame to database to ensure all rows inserted
    myquery <- paste0(
      "select count(*) as cnt_gl
       , round(sum(", gSht_glaccount_colname, "),2) as sum_gl
        from ", myTableName
    )
    #********: oracle_checksum <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    db_data_checksum <- dbGetQuery(mySfDB, myquery)
    db_checksum <- db_data_checksum$SUM_GL[[1]]
    db_rowcnt <- db_data_checksum$CNT_GL[[1]]
    
    #mydata_checksum <- sum(as.numeric(mydata[[gSht_glaccount_colname]]),na.rm=TRUE)
    mydata_checksum <- round(sum(as.numeric(mydata[[gSht_glaccount_colname]]),na.rm=TRUE),2)
    mydata_rowcnt <- nrow(mydata)
    
    if(db_checksum != mydata_checksum || db_rowcnt != mydata_rowcnt){
      #send warning that sums are different
      # create body of warning email
      bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                         "have been an error in the ", myTableName,
                         " database load. The number or rows in the MRI data was: <br/>",
                         mydata_rowcnt, "<br/><br/>",
                         "The rows in the results ", myTableName, " table is: <br/>",
                         oracle_rowcnt, "<br/><br/>",
                         "The sum of the ", gSht_glaccount_colname, " column in the MRI data was: <br/>",
                         mydata_checksum, "<br/><br/>",
                         "The sum in the results ", myTableName, " table is: <br/>",
                         oracle_checksum, "<br/><br/>",
                         "The query is contained in the ", myReportName, " script on the ",
                         "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                         warn_sig,
                         sep = ""
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " Issue: Mis-match of MRI vs. Database Load"),
               body = bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
    }else{
      #send completion email
      bodytext <- paste0("This is an automated email to inform you that it appears the ",
                         "<b>", myReportName, "</b> routine has ",
                         "completed and results should now be available in the ", 
                         myTableName, " table.<br/> <br/>",
                         warn_sig)
      #send mail
      #mailsend(norm_recip,
      #         paste0(myReportName, " Status: COMPLETE"),
      #         bodytext,
      #         attachment = NULL,
      #         test = testing_emails,
      #         testrecipient = test_recip
      #)
    }
    
    
    
    
    
    
    
  }else{
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine!</p>",
                       "<p>The query of the ", myTableName, " table didn't yield ", 
                       "the expected number of rows or there was a query error.",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error - ", myTableName, " table"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
  
  
}

#if(exists("mydata")){rm(mydata)}
#********: DBI::dbDisconnect(myOracleDB)
DBI::dbDisconnect(mySfDB)




