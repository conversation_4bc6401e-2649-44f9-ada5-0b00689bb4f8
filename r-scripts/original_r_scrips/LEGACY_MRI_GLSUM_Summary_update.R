#********: library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
#********: library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #20241121: replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(keyring)
#********: library(RO<PERSON>le)
library(DBI)
library(odbc)


# written by <PERSON> March 2022
# based on HV_PNL_LINE_STRUCTURE loading script

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version ********

### ******** changes:
### converted to Snowflake MRI tables
### now loading Snowflake results table instead of Oracle
### improved db checksum to include row count

### ******** changes:
### converted to Snowflake MRI tables
### changed Oracle connection method to R<PERSON><PERSON><PERSON> and DBI queries
### changed sendmail to use OAuth method instead of old MailR package


### 20220318 change:
### Added in info for writing same info to another Google Sheet (Legacy Portfolio)

### 20220317 change:
### new script based on HV PNL LINE STRUCTURE script version 20220317
### added in section to populate 'MRI data' sheet of Google sheet with
### select MRI data to keep other sheets in that file up-to-date


# Parameters
scriptfolder <- "LEGACY_MRI_GLSUM_Summary"
#********: logpath <- file.path("C:","Users","table","Documents","ReportFiles","LEGACY_MRI_GLSUM_Summary")
okaytocontinue <- TRUE

myTableName <- "STEVE.NA"
myReportName <- "LCP_MRI_GLSUM_Summary_update"


#Oracle connection
#********: mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve"))
#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

#SSMS connection
#mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

#Oracle connection
#********: Sys.setenv(ORA_SDTZ='America/Chicago')
#********: drv <- dbDriver("Oracle")
#********: connect.string <- paste0(
#********:   "(DESCRIPTION=",
#********:   "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
#********:   "(CONNECT_DATA=(SID=", "fvpa", ")))"
#********: )
#********: myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}
logpath <- file.path(mainpath,scriptfolder)
rptpath <- logpath
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}





# Read MRI data
if(okaytocontinue){
  
  #MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  #MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  #writelog(MyErrorLog)
  
  #get main data
  myTableName_MRI <- "GLSUM"
  myquery <- paste0(
    "
          SELECT 
          	CAST(SUBSTRING(PERIOD.PERIOD, 1, 4) AS number) AS PERIOD_YEAR
          ,	CAST(SUBSTRING(PERIOD.PERIOD, 5, 2) AS number) AS PERIOD
          ,	CAST(SUBSTRING(GLSUM.ACCTNUM,3,5) AS VARCHAR(5)) AS GL_ACCOUNT
          ,	GLSUM.ACCTNUM AS ACCTNUM_MRI
          ,	GLSUM.ENTITYID AS COST_CENTER
          ,	GLSUM.BASIS
          ,	SUM(CASE WHEN GLSUM.PERIOD = PERIOD.PERIOD THEN GLSUM.ACTIVITY ELSE 0 END) AS PERIOD_NET
          ,	SUM(CASE WHEN GLSUM.PERIOD BETWEEN CONCAT(SUBSTRING(PERIOD.PERIOD, 1, 4), '01') AND PERIOD.PERIOD THEN GLSUM.ACTIVITY ELSE 0 END) AS BALANCE
          FROM MRI.GLSUM
          LEFT JOIN MRI.PERIOD ON GLSUM.ENTITYID = PERIOD.ENTITYID
          JOIN MRI.GACC ON GLSUM.ACCTNUM = GACC.ACCTNUM
          WHERE
          	GLSUM.BASIS != 'C'
          	AND GLSUM.DEPARTMENT = '@'
          	AND (
          			GLSUM.ACCTNUM BETWEEN 'HV40000' AND 'HV49999'
          			OR
          			GLSUM.ACCTNUM BETWEEN 'HV63000' AND 'HV63099'
          			OR
          			GLSUM.ACCTNUM BETWEEN 'HV70000' AND 'HV80100'
          			OR
          			GLSUM.ACCTNUM BETWEEN 'HV91000' AND 'HV91199'
          		)
          	AND GLSUM.PERIOD BETWEEN CONCAT(YEAR(GETDATE())-2,'01') AND CONCAT(YEAR(GETDATE()),'12')
          	AND PERIOD.PERIOD BETWEEN CONCAT(YEAR(GETDATE())-2,'01') AND CONCAT(YEAR(GETDATE()),'12')
          	--AND GLSUM.PERIOD BETWEEN CONCAT(YEAR(GETDATE())-0,'01') AND CONCAT(YEAR(GETDATE()),'12')  /* TEST */
          	--and GLSUM.ENTITYID = '0204' /* TEST */
          	--and GLSUM.ACCTNUM = 'MR43000000' /* TEST */
          GROUP BY
          	GLSUM.ENTITYID
          ,	GLSUM.ACCTNUM
          ,	GLSUM.BASIS
          ,	CAST(SUBSTRING(GLSUM.ACCTNUM,3,5) AS VARCHAR(5))
          ,	CAST(SUBSTRING(PERIOD.PERIOD, 1, 4) AS number)
          ,	CAST(SUBSTRING(PERIOD.PERIOD, 5, 2) AS number)
    "
  )
  #********: mydata <- sqlQuery(mySSdb, myquery, stringsAsFactors = FALSE)
  mydata <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
  #********: mySchema <- "STEVE"
  mySchema <- "CORPORATE"
  myTable <- "MRI_GLSUM_SUMMARY"
  myTableName <- paste(mySchema, myTable, sep = ".")
  if(mydata_status[[1]] == TRUE){
    # load Oracle
    # get column names and datatypes of Oracle table
    #********: myTableName <- "STEVE.MRI_GLSUM_SUMMARY"
    gSht_glaccount_colname <- "GL_ACCOUNT"
    #********: tmpDBinfo <- sqlColumns(mydb, myTableName)
    #********: columnTypes <- as.character(tmpDBinfo$TYPE_NAME)
    #********: names(columnTypes) <- as.character(tmpDBinfo$COLUMN_NAME)
    # Trunc Oracle table
    #********: myquery <- paste0('truncate table ', myTableName, ' drop storage')
    #********: myTrucResults <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    #********: myTrucResults <- dbGetQuery(myOracleDB, myquery)
    
    #populate Oracle
    #********: sqlSave(mydb, 
    #********:         mydata, 
    #********:         tablename = myTableName,  
    #********:         append = TRUE, 
    #********:         rownames = FALSE, 
    #********:         colnames = FALSE, 
    #********:         safer = TRUE,
    #********:         fast = FALSE,
    #********:         addPK = FALSE, 
    #********:         varTypes = columnTypes,
    #********:         nastring = NULL)
    #********: rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    
    # Trunc SNOWFLAKE table
    dbBegin(mySfDB)
    myquery <- paste0('truncate table if exists ', myTableName)
    myTrucResults <- dbSendQuery(mySfDB, myquery)
    dbCommit(mySfDB)
    dbClearResult(myTrucResults)
    #populate Snowflake
    rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata)
    
    #compare sum of the GL_ACCOUNT column of the data frame to Oracle to ensure all rows inserted
    myquery <- paste0(
      "select count(*) as cnt_gl
       , round(sum(", gSht_glaccount_colname, "),2) as sum_gl
       from ", myTableName
    )
    #********: oracle_checksum <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    #********: oracle_checksum <- dbGetQuery(myOracleDB, myquery)
    db_data_checksum <- dbGetQuery(mySfDB, myquery)
    db_checksum <- db_data_checksum$SUM_GL[[1]]
    db_rowcnt <- db_data_checksum$CNT_GL[[1]]
    
    #********: mydata_checksum <- sum(as.numeric(mydata[[gSht_glaccount_colname]]),na.rm=TRUE)
    mydata_checksum <- round(sum(as.numeric(mydata[[gSht_glaccount_colname]]),na.rm=TRUE),2)
    mydata_rowcnt <- nrow(mydata)
    
    #********: if(round(oracle_checksum[[1]],2) != round(mydata_checksum,2)){
    if(db_checksum != mydata_checksum || db_rowcnt != mydata_rowcnt){
      #send warning that sums are different
      # create body of warning email
      bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                         "have been an error in the ", myTableName,
                         " database load. The sum of the ", gSht_glaccount_colname_New, " column in the MRI data was: <br/>",
                         round(mydata_checksum,2), "<br/><br/>",
                         "The sum of the ", myTableName, " table is: <br/>",
                         round(oracle_checksum[[1]],2), "<br/><br/>",
                         "The query is contained in the ", myReportName, " script on the ",
                         "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                         warn_sig,
                         sep = ""
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " Issue: Mis-match of MRI vs. Database Load"),
               body = bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
    }else{
      #send completion email
      bodytext <- paste0("This is an automated email to inform you that it appears the ",
                         "<b>", myReportName, "</b> routine has ",
                         "completed and results should now be available in the ", 
                         myTableName_MRI, " table.<br/> <br/>",
                         warn_sig)
      #send mail
      #mailsend(norm_recip,
      #         paste0(myReportName, " Status: COMPLETE"),
      #         bodytext,
      #         attachment = NULL,
      #         test = testing_emails,
      #         testrecipient = test_recip
      #)
    }
    
    
    
    
    
    
    
  }else{
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine!</p>",
                       "<p>The query of the ", myTableName, " table didn't yield ", 
                       "the expected number of rows.",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Query error - ", myTableName, " table"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
  
  
}

#DBI::dbDisconnect(myOracleDB)
DBI::dbDisconnect(mySfDB)




