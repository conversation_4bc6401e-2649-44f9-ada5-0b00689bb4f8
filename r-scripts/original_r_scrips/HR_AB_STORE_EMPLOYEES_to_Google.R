library(xtable)
library(reshape2)
library(dplyr)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(tidyverse)
library(DBI)
library(ROracle)
library(googledrive)
library(googlesheets4)
library(keyring)

# written by <PERSON> August 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20230511

### 20230511 change:
### new file


# Parameters
options(stringsAsFactors = FALSE)

myReportName <- "HR Store Employees to Google"
scriptfolder <- "HR_Store_Employees_to_Google"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))

okaytocontinue <- TRUE

# Google sheets parameters, create empty df then add rows with needed info
gSht_info <- data.frame(FN=character(), F_ID=character(), SN=character(), S_GID=numeric())
gSht_info <- gSht_info %>% add_row(FN = "HV New Hire Document Tracker", F_ID = "1kCEvpK777evX9EF7n2upq7ZOoZtebNf6C41WFuqEFVA", SN = "AB_STORE_EMPLOYEES", S_GID = 438847412 )
gSht_info <- gSht_info %>% add_row(FN = "ADP databridge import templates", F_ID = "1_eG6arJQ2w1g-_F7dd44g1ChZH69BI37Jsv51vE63Qo", SN = "Store Employees", S_GID = 252421526 )
#gSht_info <- gSht_info %>% add_row(FN = as.character(NA), F_ID = as.character(NA), SN = as.character(NA), S_GID = as.numeric(NA) )

gSht_auth_email <- "<EMAIL>"


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("Sean Coyle<<EMAIL>>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
sig_logo <- FALSE

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  sig_logo <- TRUE
}


# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "15-APR-23" #TEST OR MANUAL RUN ONLY


### define some functions ###

# ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
myOracleDB_deanna <- dbConnect(drv, username = "deanna", password =  key_get("Oracle", "deanna"), dbname = connect.string)

mySchema <- "STEVE"

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, "<<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)



# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isFile <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  #gs4_auth(email = gSht_auth_email)
  if (gs4_has_token()) {
    #auth okay, check if file IDs and sheets are found
    gSht_fnd_ID_cnt <- 0
    gSht_F_IDs <- unique(gSht_info$F_ID[!is.na(gSht_info$F_ID)])
    if(length(gSht_F_IDs)>0){
      #check file IDs and then metadata for needed sheets
      for(i in 1:length(gSht_F_IDs)){
        #gSht_get <- gs4_get(as_id(gSht_info$F_ID[i]))
        drv_get_check <- drive_get(id = as_id(gSht_F_IDs[i]))
        isFile <- drv_get_check$drive_resource[[1]]$mimeType == drive_mime_type("spreadsheet")
        if(isFile){
          #check if sheet names are in file
          gSht_get <- gs4_get(as_id(gSht_F_IDs[i]))
          gSht_sheets <- gSht_info$SN[which(gSht_info$F_ID == gSht_F_IDs[i])]
          diff <- setdiff(gSht_sheets, gSht_get$sheets$name)
          if(!is_empty(diff)){
            okaytocontinue <- FALSE
            #send warning email that sheet doesn't exist
            bodytext <- paste0(
              "<p>This is an automated email to inform you that it appears there ",
              "is at least one missing sheet needed in the ",
              myReportName, " routine! ",
              "<p>The routine is aborting without an update.</p> ",
              "<b>Google Sheet Info:</b><ul>",
              "<li>Google sheet filename: ", gSht_get$name, "</li>",
              "<li>Sheetname(s) NOT FOUND in file above:<b> ", paste0(diff, collapse = '; '), "</b></li>",
              "</ul></p><br>",
              warn_sig
            )
            #send mail
            mailsend(warn_recip,
                     paste0(myReportName, " Issue: Google File Issue"),
                     bodytext,
                     attachment = NULL,
                     inline = sig_logo,
                     test = testing_emails, testrecipient = test_recip
            )
            break
          }
          gSht_fnd_ID_cnt <- gSht_fnd_ID_cnt + 1
        }else{
          #desired file not found, abort
          okaytocontinue <- FALSE
          #send warning email that sheet doesn't exist
          bodytext <- paste0(
            "<p>This is an automated email to inform you that it appears there ",
            "is at least one missing file needed in the ",
            myReportName, " routine! ",
            "<p>The routine is aborting without an update.</p> ",
            "<b>Google Sheet Info:</b><ul>",
            "<li>Google sheet ID not found:<b> ", gSht_F_IDs[i], "</b><</li>",
            "</ul></p><br>",
            warn_sig
          )
          #send mail
          mailsend(warn_recip,
                   paste0(myReportName, " Issue: Google File Issue"),
                   bodytext,
                   attachment = NULL,
                   inline = sig_logo,
                   test = testing_emails, testrecipient = test_recip
          )
          break
        }
      }
      
    }else{
      #no IDs found in gSht_info dataframe, abort
      okaytocontinue <- FALSE
      
    }
    
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update.</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Access Issue"),
             bodytext,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}


# Query Oracle for needed info #

if(okaytocontinue){
  myquery <- paste0(
    "select
      a.store
    , a.paynum
    , b.position
    , c.position_desc
    , c.category as position_category
    , b.fname
    , b.lname
    , b.status
    from ab_store_employees a
    left join ab_employees b
    on a.paynum = b.paynum
    left join famv_payroll_position c
    on b.position = c.position
    where b.status not in ('T','R','D')
    order by a.store, a.paynum
    "
  )
  
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  
  if(mydata_status[[1]]){
    #write to sheets
    for(i in 1:nrow(gSht_info)){
      if(complete.cases(gSht_info[i, c("FN","SN")])){
        curr_FN <- gSht_info$FN[i]
        #mydata_this_sht[] <- case_when(
        if(curr_FN == "HV New Hire Document Tracker"){
          mydata_this_sht <- mydata[, c("PAYNUM","STORE")]
        }else if(curr_FN == "ADP databridge import templates"){
          mydata_this_sht <- mydata
        }else{
          mydata_this_sht <- mydata[0, ]
        }
        #add updated column
        mydata_this_sht[, date.header.text] <- NA
        #write to sheet
        if(nrow(mydata_this_sht)>0){
          #clear previous data
          range_clear(gSht_info$F_ID[i], sheet = gSht_info$SN[i], range = NULL, reformat = FALSE)
          #write new data
          range_write(ss = gSht_info$F_ID[i], 
                      data = mydata_this_sht, 
                      sheet = gSht_info$SN[i], 
                      #range = cell_rows(mypaste_row:(mypaste_row+nrow(mydata_paste_curr)-1) ), 
                      col_names = TRUE,
                      reformat = FALSE
          )
        }else{
          #warn of no data written?
        }
      }

      
      
    }
    
  }
  
}





