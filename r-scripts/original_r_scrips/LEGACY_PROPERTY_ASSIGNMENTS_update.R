library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)

library(DBI)
library(ROracle)

# written by <PERSON> March 2022
# based on HV_PNL_LINE_STRUCTURE loading script

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241010

### 20241010 change:
### disabled sections that write MRI data back to <PERSON>'s Google Sheet, legacy MRI database isn't updating
### so new Snowflake version of this routine will do that...this is just updating OR<PERSON>LE to keep 
### legacy reports updated until decommissioning

### 20230925 change:
### added SAFETY_CULTURE_ID column to Oracle table populated with corresponding data from Gsheet

### 20230217 change:
### added EXP_VISITS_YEAR column to Oracle based on Google sheet "Expected Visits Per Year" column

### 20230206 change:
### no longer loading Oracle 'SUPPORT_RM' column ('Additional RM Support' in Google)
### added BLDG_TYPE column to Oracle using data in Google sheet 'Building Type/Details' column
### also converted from odbc Oracle connection to ROracle and DBI for more robust loading

### 20220829 change:
### Added OCCUPIED SINGLE TENANT? column to Oracle (from Summary sheet) where x gets populated as 'Y' and
### NULL values are 'N' in database


### 20220630 change:
### Added MRI Asset Mgr sheet populate and keyring package

### 20220317 change:
### new script based on HV PNL LINE STRUCTURE script version 20220317
### added in section to populate 'MRI data' sheet of Google sheet with
### select MRI data to keep other sheets in that file up-to-date


# Parameters
logpath <- file.path("C:","Users","table","Documents","ReportFiles","HV_PNL_Line_Structure")
okaytocontinue <- TRUE

myReportName <- "LCP_ASSIGNMENTS_SEAN_update"
mySheets <- c("Summary")
myMRISheet <- "MRI data"
myMRIAssetMgrSheet <- "MRI Asset Mgrs"
#mySheets <- " TESTFAIL"
# NOTE myColNames order dictates column order in resulting dataframe when filtered
# myColNames are the names in source Google Sheet
myColNames <- c("Building",
                "Long Building ID",
                "Address",
                "City",
                "State",
                "Zip",
                "RM",
                #"Additional RM support",
                "Support Leasing",
                "Support Property Management",
                "Prop Mgr",
                "OCCUPIED SINGLE TENANT?",
                "Building Type/Details",
                "Expected Visits Per Year",
                "Safety Culture Site ID"
                )
myColName_bldg <- c("Building")
gSht_bldg_colname_New <- "BLDG"
myColName_OST_New <- "OCC_SINGLE_TENANT"
#myColNames_New are Oracle db column names
myColNames_New <- c("BLDG",
                    "BLDGID",
                    "ADDRESS",
                    "CITY",
                    "STATE",
                    "ZIP",
                    "RM",
                    #"SUPPORT_RM",
                    "SUPPORT_LEASING",
                    "SUPPORT_PROP_MGMT",
                    "PROP_MGR",
                    "OCC_SINGLE_TENANT",
                    "BLDG_TYPE",
                    "EXP_VISITS_YEAR",
                    "SAFETY_CULTURE_ID"
                    )


#Oracle connection
#mydb <- odbcConnect("FVPA64", "steve",key_get("Oracle", "steve"))
#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ="GMT")
#Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
mySchema <- "STEVE"
myTable <- "LCP_ASSIGNMENTS_SEAN"
myTableName <- paste(mySchema, myTable, sep = ".")
#myTableName <- "STEVE.LCP_ASSIGNMENTS_SEAN"



#SSMS connection
mySSdb <- odbcConnect("SQLServer", "SteveO_ro",key_get("MRI_bak", "SteveO_ro"))

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010"){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
  testing_pc_location <- "Office"
  #testing_pc_location <- "Laptop"
}else{testing_pc <- FALSE}

if(testing_pc){
  if(testing_pc_location == "Office"){
    # Steve PC testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_PNL_Line_Structure")
    HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  }
  if(testing_pc_location == "Laptop"){
    # Steve HOME laptop testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("E:","Steve","Documents","R Scripts","TestScripts","HV_PNL_Line_Structure")
    rptpath_MARCOS_emails <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_PNL_Line_Structure","Email_Addys")
    HVSigLogopath <- file.path("E:","Steve","Documents","R Scripts","TestScripts","HV Logo Email Signature.png")
  }
  
}


### define some functions ###

mailsend <- function(recipient, subject, body, attachment = NULL, test = FALSE, testrecipient = NULL){
  library(mailR)
  sender <- "PNL Line Structure Updates <<EMAIL>>"
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  
  send.mail(from = sender,
            to = recipients,
            replyTo = "<EMAIL>",
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", port = 465, 
                        user.name = "<EMAIL>",            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            send = TRUE)
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




#--Query existing table data to compare to new run--#
if(okaytocontinue){
  myquery <- paste0("select 
                      *
                    from ", myTableName
  )
  #priordata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  priordata <- dbGetQuery(myOracleDB, myquery)
}





#--Query new data from Google Sheet--#
# check google sheet status
if(okaytocontinue){
  
  #MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  #MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  #writelog(MyErrorLog)
  
  gs4_auth(email = "<EMAIL>")
  
  #Is it OK to cache OAuth access credentials in the folder 'C:/Users/<USER>/.R/gargle/gargle-oauth' between R sessions?
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive package first, then use the same token
  #in googlesheets4 something like this:
  #tk <- drive_auth()
  #gs4_auth(token = drive_token())
  #gSht_Closings <- sheets_get('1xoLPaRKdPvDdwl9wvCEiBp8ABrGgxhff9GSn55_yArc')
  #above was deprecated as of googlesheets4 0.2.0

  
  #https://docs.google.com/spreadsheets/d/1ZP_7ZIIiMz6FjrYEJ8-K0TFFvnzsM0ogXnEV9P9wyIw/edit#gid=0
  gSht_get <- gs4_get('1ZP_7ZIIiMz6FjrYEJ8-K0TFFvnzsM0ogXnEV9P9wyIw')
  
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH OKAY")
    #MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    #writelog(MyErrorLog)
    #read data in from desired sheet
    #gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RE Taxes Data")
    
    #Get number of sheets like '% P&L Full Sort' sheets
    #gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %ilike% mySheetsLike)]
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>There weren't any sheets with the expected names (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}

if("stop using"=="20241010"){
  ### Populate MRI data sheet
  
  if(okaytocontinue){
    # verify myMRISheet is present
    gSht_Sheet_MRI <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% myMRISheet)]
    gSht_Sheet_MRI_num <- length(gSht_Sheet_MRI)
    if(gSht_Sheet_MRI_num > 0){
      #sheet found, continue
      
      #query MRI
      myquery <- paste0(
        "
          SELECT
          CASE WHEN TRY_CONVERT(INT,BLDG.BLDGID) IS NULL THEN BLDG.BLDGID ELSE CONVERT(NVARCHAR,TRY_CONVERT(INT,BLDG.BLDGID)) END AS [BLDG]
          ,	BLDG.BLDGID AS [BLDGID]
          ,	BLDG.INACTIVE
          ,	MNGR.MNGRNAME AS [PROP_MGR]
          FROM BLDG
          JOIN MNGR ON BLDG.MNGRID = MNGR.MNGRID
      "
      )
      mydata <- sqlQuery(mySSdb, myquery, stringsAsFactors = FALSE)
      mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
      if(mydata_status[[1]] == TRUE){
        #UPDATE MRI sheet
        #clear existing data in MRI sheet
        range_clear(gSht_get$spreadsheet_id, sheet = myMRISheet, range = NULL, reformat = FALSE)
        #write new data
        sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = myMRISheet)
        #replace headers to replace underscores with spaces
        #my_headers <- data.frame(gsub("_"," ",mydata[0,]))
        Sys.sleep(2)
      }
      
      
    }else{
      #MRI sheet not found, warn
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>There weren't any sheets named like '", myMRISheet, "' ",
                         #"<p>There weren't any sheets with the expected names (", 
                         #paste(mySheets, collapse = "; "),
                         " found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Missing expected sheet"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
    }
  }
  
  
  
  
  ### Populate MRI Asset Mgrs sheet
  
  if(okaytocontinue){
    # verify myMRISheet is present
    gSht_Sheet_MRI <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% myMRIAssetMgrSheet)]
    gSht_Sheet_MRI_num <- length(gSht_Sheet_MRI)
    if(gSht_Sheet_MRI_num > 0){
      #sheet found, continue
      
      #query MRI
      myquery <- paste0(
        "
          select
          	p.projid AS [PROJID],
          	e.entityid as [ENTITYID],
          	CASE WHEN TRY_CONVERT(INT,B.BLDGID) IS NULL THEN B.BLDGID ELSE CONVERT(NVARCHAR,TRY_CONVERT(INT,B.BLDGID)) END AS [BLDG],
          	b.BLDGID,
          	B.INACTIVE,
          	p.assetmgr AS ASSETMGR
          from proj p
          left join entity e
          on p.PROJID = e.PROJID
          left join bldg b
          on e.ENTITYID = b.ENTITYID
          where (b.INACTIVE is null or b.INACTIVE = 'N')
      "
      )
      mydata <- sqlQuery(mySSdb, myquery, stringsAsFactors = FALSE)
      mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
      if(mydata_status[[1]] == TRUE){
        #UPDATE MRI sheet
        #clear existing data in MRI sheet
        range_clear(gSht_get$spreadsheet_id, sheet = myMRIAssetMgrSheet, range = NULL, reformat = FALSE)
        #write new data
        sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = myMRIAssetMgrSheet)
        #replace headers to replace underscores with spaces
        #my_headers <- data.frame(gsub("_"," ",mydata[0,]))
        Sys.sleep(2)
      }
      
      
    }else{
      #MRI sheet not found, warn
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>There weren't any sheets named like '", myMRIAssetMgrSheet, "' ",
                         #"<p>There weren't any sheets with the expected names (", 
                         #paste(mySheets, collapse = "; "),
                         " found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Missing expected sheet"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
    }
  }
}



if(okaytocontinue){
  #loop through each sheet and read data
  #mydata_combined
  for(i in 1:gSht_Sheets_num){
    #read sheet into temp df
    gSht_Curr <- read_sheet(gSht_get$spreadsheet_id, sheet = gSht_Sheets[[i]])
    
    #copy only needed columns from data, filter in case of strings in GL column
    gSht_Curr_ColNames <- colnames(gSht_Curr)
    myColIntersect <- intersect(myColNames, gSht_Curr_ColNames)
    
    if(length(myColIntersect) < length(myColNames)){
      #One or more columns missing, create dummy df to trigger error log
      mydata <- data.frame(Issue = "Missing column(s)")
    }else{
      #columns present, populate rows
      mydata <- dplyr::select(dplyr::filter(gSht_Curr, 
                                            get(myColName_bldg) < 10000 & get(myColName_bldg) > 0),
                              all_of(myColNames)
      )
    }

    
    #check that expected # of rows/columns present
    if(ncol(mydata) < length(myColNames) || nrow(mydata) < 5 ){
      #no rows or not all expected columns present, log missing
      myissue <- case_when(
        ncol(mydata) < length(myColNames) ~ "Missing Columns",
        nrow(mydata) < 1 ~ "No Valid Rows",
        TRUE ~ "Unknown Issue"
      )
      if(exists("myerrors_sheets")){
        #add to existing errors
        myerrors_sheets[nrow(myerrors_sheets)+1, ] <- c(gSht_Sheets[[i]], myissue)
      }else{
        #create new error dataframe
        myerrors_sheets <- data.frame(Sheet = gSht_Sheets[[i]],
                                      Issue = myissue)
      }
    }else{
      #columns and rows okay
      #rename columns
      setnames(mydata, 
               old = myColNames, 
               new = myColNames_New,
               skip_absent = TRUE)
      if(exists("mydata_combined")){
        #add to existing data
        mydata_combined <- rbind(mydata_combined, mydata)
      }else{
        #create dataframe
        mydata_combined <- mydata
      }
      rm(mydata)
    }
    
  } #for(i in 1:gSht_Sheets_num)
  
  
  #if rows were present, truncate Oracle table and load with results
  if(nrow(mydata_combined)>0){
    # myColName_OST_New, convert 'X' value in Google data to 'Y' and NULL (or anything else) to 'N'
    mydata_combined$OCC_SINGLE_TENANT <- sapply(mydata_combined$OCC_SINGLE_TENANT, function(x) if(is.na(x)){'N'}else{if(str_to_upper(as.character(x))=='X'){'Y'}else{'N'}} )
    mydata_combined$SUPPORT_RM <- NA
    #MOVE SUPPORT_RM column
    mydata_combined <- mydata_combined %>% relocate(SUPPORT_RM, .after = RM)
    #CLEAN DATA
    mydata_combined$BLDG_TYPE <- gsub("[^[:print:]]", "", mydata_combined$BLDG_TYPE)
    mydata_combined$ZIP <- gsub("[^0-9.-]", "", mydata_combined$ZIP)
    # Trunc Oracle table
    myquery <- paste0('truncate table ', myTableName, ' drop storage')
    myTrucResults <- dbGetQuery(myOracleDB, myquery)
    dbCommit(myOracleDB)
    
    #populate Oracle
    rs_write <- dbWriteTable(myOracleDB, myTable, mydata_combined, row.names = FALSE , append = TRUE, schema = mySchema)
    dbCommit(myOracleDB)
    #compare sum of the BLDG column of the data frame to Oracle to ensure all rows inserted
    myquery <- paste0(
      "select sum(", gSht_bldg_colname_New, ")
        from ", myTableName
    )
    #oracle_checksum <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
    oracle_checksum <- dbGetQuery(myOracleDB, myquery)
    
    mydata_checksum <- sum(as.numeric(mydata_combined[[gSht_bldg_colname_New]]),na.rm=TRUE)
    
    if(round(oracle_checksum[[1]],2) != round(mydata_checksum,2)){
      #send warning that sums are different
      # create body of warning email
      bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                         "have been an error in the ", myTableName,
                         " Oracle load. The sum of the ", gSht_bldg_colname_New, " column in the Google sheet was: <br/>",
                         round(mydata_checksum,2), "<br/><br/>",
                         "The sum in the ", myTableName, " table is: <br/>",
                         round(oracle_checksum[[1]],2), "<br/><br/>",
                         "The query is contained in the ", myReportName, " script on the ",
                         "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                         warn_sig,
                         sep = ""
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Mis-match of Google Sheet sum vs. Oracle load"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
    }else{
      #send completion email
      bodytext <- paste0("This is an automated email to inform you that it appears the ",
                         "<b>", myReportName, "</b> routine has ",
                         "completed and results should now be available in the ", 
                         myTableName, " table.<br/> <br/>",
                         warn_sig)
      #send mail
      #mailsend(norm_recip,
      #         paste0(myReportName, " Status: COMPLETE"),
      #         bodytext,
      #         attachment = NULL,
      #         test = testing_emails,
      #         testrecipient = test_recip
      #)
    }
  
  }else{
    #no rows in results, send warning
    # create body of warning email
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       " Oracle load. There weren't any rows in the final routine queries to upload. ",
                       "<br/><br/>",
                       "The query is contained in the ", myReportName, " script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Mis-match of Google Sheet sum vs. Oracle load"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
  #if routine completed, but there were errors in one or more sheets, send email
  if(exists("myerrors_sheets")){
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       " Oracle load. The following sheets seemed to have issues and were not loaded. ",
                       "<br/><br/>",
                       print(xtable(myerrors_sheets, 
                                    caption = paste0("File: ", gSht_get$name, " (", report.time.txt, ")")),
                             align = c(rep("l",2), rep("l", ncol(myerrors_sheets) - 1)),
                             html.table.attributes = "border=2 cellspacing=1",
                             type = "html",
                             caption.placement = "top",
                             include.rownames=FALSE),
                       "<br/><br/>",
                       "If the issue above is noted as 'Missing Columns' the routine is expecting ",
                       "the following column headers in the Google sheets: ", paste(myColNames, collapse = "; "), ".<br/><br/>",
                       "The query is contained in the ", myReportName, " script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: One or more sheets not loaded"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    rm(myerrors_sheets)
  }
  
  
}




