library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(purrr)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(tidyr)
library(DBI)
library(ROracle)
library(keyring)
library(janitor)

# written by <PERSON> December 2023


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20240119

### 20240119 change:
### revise date range covered in update, updates through most recent Sunday

### 20231213 change:
### new file, based on MARCOS_FT_ORDER_SUMMARY_HOURLY_Load.R script


# Parameters

okaytocontinue <- TRUE

myReportName <- "<PERSON>'s HU_RANK-Oracle Load"
scriptfolder <- "MARCOS_Daily_Reports"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_recip <- c("<EMAIL>") #testing purposes, normal is line above
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}

# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "20-MAR-23" #testing line only

query.periods <- 2 #total number of periods to cover in update query
query.period_offset <- 0 #trailing period(s) for END of update query (start is s_date for query.periods + query.period_offset)
rpt.date <- as.Date(query.date, "%d-%b-%y")


### define some functions ###

#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ="GMT")
#Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)

mySchema <- "STEVE"
myTable <- "HU_RANK"
myTableName <- paste(mySchema, myTable, sep = ".")


mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, " <<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




# Query Oracle for recent table data, 
if(okaytocontinue){
  #get calendar dates to use in main data query
  myquery <- paste0(
    "
      select
      to_char(min(s_date)) as s_date
      , to_char(max(case when e_date > (trunc(sysdate, 'IW') - 1) then (trunc(sysdate, 'IW') - 1) else e_date end)) as e_date
      from mp_calendar
      where cal_id >= (select i.cal_id - ", query.periods + query.period_offset, 
    " from mp_calendar i where trunc(to_date('", query.date, "')) between i.s_date and i.e_date)
      and cal_id <= (select i.cal_id - ", query.period_offset, 
    " from mp_calendar i where trunc(to_date('", query.date, "')) between i.s_date and i.e_date)
    "
  )
  mydates <- dbGetQuery(myOracleDB, myquery)
  
  
  myquery <- paste0(
    "
      select store.st as HURankST,
        cal.e_date HURankWE,
        SUM(CALL.HANGUP_COUNT) /  sum(call.call_Count) as HUPerc,
        Rank()over(partition by cal.e_date order by SUM(CALL.HANGUP_COUNT) /  sum(call.call_Count)) as HURank        
      from ab_store store, mp_ast_call_summary call, mp_Calendar_weekly cal
      where store.st = call.Store_number
      and call.w_date<=cal.e_date 
      and call.w_date>=cal.s_date
      and cal.e_date between to_date('", mydates$S_DATE[1] , "') and to_date('", mydates$E_DATE[1] , "')
      and store.st between 3500 and 4250
      group by store.st, cal.e_Date
      order by HURankWE, HURankST
    "
  )
  mydata <- dbGetQuery(myOracleDB, myquery)
  min_row_qualifier <- (28 * (query.periods - 1))
  mydata_status <- check_mydf_rows(mydata, MinNumRows = min_row_qualifier, ReportName = myReportName)
  if(mydata_status[[1]]){
    
    
  }else{
    #apparent error, send warning email
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error querying the man-hours for the ", myReportName, " routine! ",
                       "The query failed or produced less than ", min_row_qualifier, " rows of results.</p>",
                       "<br>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, missing data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
  
}



#Load Oracle
if(okaytocontinue){
  #Delete rows within expected date range and replace with current data
  myDeleteRows_failed <- FALSE
  myLoadRows_failed <- FALSE
  myDeleteError_text <- ""
  
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName, "
      where HURANKWE >= to_date('", mydates$S_DATE[1] , "')
      and HURANKWE <= to_date('", mydates$E_DATE[1] , "')
    "
  )
  rs_sel <- dbSendQuery(myOracleDB, myquery_select)
  select_cnt <- dbFetch(rs_sel, n = -1)
  myquery_delete <- paste0(
    "
      delete from ", myTableName, "
      where HURANKWE >= to_date('", mydates$S_DATE[1], "')
      and HURANKWE <= to_date('", mydates$E_DATE[1], "')
      "
  )
  rs_del <- dbSendQuery(myOracleDB, myquery_delete)
  if(dbGetInfo(rs_del, what = "rowsAffected")[[1]] != select_cnt[[1]]){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(myOracleDB)
    myDeleteRows_failed <- TRUE
    myDeleteError_text <- paste0(
      "<p>There was an unexpected issue deleting previous data that might ",
      "have been present for dates between ", mydates$S_DATE[1],
      " and ", mydates$E_DATE[1],". <b>The routine has ",
      "ABORTED without attempting to load the current results!</b></p>"
    )
    #do not load since deletion apparently failed
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    dbCommit(myOracleDB)
    
    #Insert trans rows into myTable, count rows before and after to catch load issues
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName, "
        where HURANKWE >= to_date('", mydates$S_DATE[1], "')
        and HURANKWE <= to_date('", mydates$E_DATE[1], "')
      "
    )
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt_pre <- dbFetch(rs_sel, n = -1)
    dbClearResult(rs_sel)
    rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    #get new count of rows in table
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt_post <- dbFetch(rs_sel, n = -1)
    myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
    mydata_numrows <- nrow(mydata)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, get load counts by store
      myquery <- paste0(
        "
          select HURANKST
          ,   count(DAY) as COUNT_LOADED
          from ", myTableName, "
          where HURANKWE >= to_date('", mydates$S_DATE[1], "')
            and HURANKWE <= to_date('", mydates$E_DATE[1], "')
          group by HURANKST
          order by HURANKST
          "
      )
      myLoadStores_curr <- dbGetQuery(myOracleDB, myquery)
      #summarize dataframe to get counts by store
      myLoadStores_results_cnt <- mydata %>% select(HURANKST) %>% group_by(HURANKST) %>% summarize(QUERY_RESULTS = n())
      myLoad_failed <- myLoadStores_results_cnt %>% 
        dplyr::left_join(myLoadStores_curr, by = c("HURANKST")) %>%
        mutate_if(is.numeric,coalesce,0) %>%
        .[which(.$QUERY_RESULTS != .$COUNT_LOADED), ]
      
      
      if(nrow(myLoad_failed)>0){
        myLoadRows_failed <- TRUE
        myLoadError_text <- paste0(
          "<p>One or more stores failed to load the expected number or rows ",
          "into Oracle for the dates between ", mydates$S_DATE[1],
          " and ", mydates$E_DATE[1],". <b>The Oracle table (",
          myTableName, ")", " will have incomplete results for these dates!</b> ",
          "Investigate the stores shown below and re-run this routine when issue ",
          "has been addressed.<br>",
          print(
            xtable(myLoad_failed, 
                   digits = rep(0,ncol(myLoad_failed)+1),
                   align = c(rep("c", ncol(myLoad_failed) + 1))
            ),
            html.table.attributes = 'border=2 cellspacing=1 align="center"',
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          ),
          "</p>"
        )
      }
    }
    
    
    
  }
  if(myDeleteRows_failed || myLoadRows_failed){
    #email warning
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error populating Oracle in the ", 
                       myReportName, " routine. </p>",
                       if(myDeleteRows_failed){myDeleteError_text},
                       if(myLoadRows_failed){myLoadError_text},
                       "<br>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Oracle load error"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



