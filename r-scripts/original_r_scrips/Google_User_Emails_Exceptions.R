library(formattable)
library(xtable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(tidyverse)
library(dplyr)
library(DBI)
library(ROracle)
library(googledrive)
library(googlesheets4)
library(keyring)

# written by <PERSON> August 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20240312

### 20240312 change:
### added date to subject and added note that suspended accts are still charged

### 20231227 change:
### added query to add rows for Google Store List personnel staff (O or V positions)
### without corresponding AC_EMAIL row (causes no email to be displayed)
### and to call out that a person's on leave where there is a term date AND status is 'L'

### 20230816 change:
### fixed norm_recip issue with normal sends

### 20230614 change:
### new file


# Parameters
options(stringsAsFactors = FALSE)

myReportName <- "Google User Email Exceptions"
scriptfolder <- "HV_Google_Emails"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
subject_date_text <- format(Sys.Date(), "%m-%d-%Y")
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))

okaytocontinue <- TRUE

# Google sheets parameters, create empty df then add rows with needed info
gSht_info <- data.frame(FN=character(), F_ID=character(), SN=character(), S_GID=numeric())
gSht_info <- gSht_info %>% add_row(FN = "Workspace User Emails and Bulk Delete", F_ID = "1SjqqNRKSV2N25Z4GA53W4L-qCFgVU15faXt0qiOn7eA", SN = "All User Emails", S_GID = 149984372 )
#gSht_info <- gSht_info %>% add_row(FN = "", F_ID = "", SN = "", S_GID =  )
#gSht_info <- gSht_info %>% add_row(FN = as.character(NA), F_ID = as.character(NA), SN = as.character(NA), S_GID = as.numeric(NA) )

gSht_auth_email <- "<EMAIL>"


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("Steve Olson<<EMAIL>>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("Emp Changes<<EMAIL>>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("Steve Olson<<EMAIL>>")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
sig_logo <- FALSE

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  sig_logo <- TRUE
}

### define some functions ###

# ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
myOracleDB_deanna <- dbConnect(drv, username = "deanna", password =  key_get("Oracle", "deanna"), dbname = connect.string)

mySchema <- "STEVE"

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, "<<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)

#query Oracle for issues
if(okaytocontinue){
  myquery <- paste0(
    "
      select *
      from
      (
          select a.paynum as \"PAYNUM\",
               a.email as \"AC_EMAIL.EMAIL\",
               a.position AS \"POS\",
               e.company_code||': '||d.position_desc as \"COMP: POS DESC\",
               e.storenumber as \"LOCATION\",
               a.status as \"EMP STATUS\",
               to_char(a.t_date,'mm/dd/yyyy') as \"TERM DATE\",
               'NA' as \"GMAIL USER NAME\",
               NULL as \"ADDRESS TYPE\",
               substr(upper(a.fname),1,1) ||
                  substr(lower(a.fname),2) ||
                  ' ' ||
                  substr(upper(a.lname),1,1) ||
                  substr(lower(a.lname),2) ||
                  ' (' || trim(t.title) || ') ' ||
                  'insert paynum & email address in ac_email (create email if needed)' as \"ACTION LIKELY NEEDED\"
          from ab_employees a
          inner join ab_employee_title t
          on a.paynum = t.paynum
          left join famv_employees e
          on a.paynum = e.employeenumber
          inner join famv_payroll_position d
          on a.position = d.position
          where a.status = 'A'
            and a.position in ('O','V') /* 'personnel' list positions */
            and a.email is NULL
          --order by a.fname
          
          union all
                    
          select
          a.paynum as \"PAYNUM\"
          , a.email as \"AC_EMAIL.EMAIL\"
          , b.position AS \"POS\"
          , e.company_code||': '||d.position_desc as \"COMP: POS DESC\"
          , e.storenumber as \"LOCATION\"
          , b.status as \"EMP STATUS\"
          , to_char(b.t_date,'mm/dd/yyyy') as \"TERM DATE\"
          , c.user_name as \"GMAIL USER NAME\"
          , case when c.type = 'P' then 'Primary'
              when c.type = 'A' then 'Alias'
              else NULL end as \"ADDRESS TYPE\"
          , case
              when (d.position_desc = 'District' or d.position_desc = 'Regional')
                  and
                  e.company_code = 'HFL'
                  and trunc(sysdate) - b.t_date <= 14 then 'HRG RDO/DM within 14 days of term, acct probably being reviewed'
              when c.type = 'P' and b.status = 'L' then 'On LEAVE & probably okay as-is, check with H.R. before removing'
              when c.type = 'P' then 'Delete Gmail account when appropriate'
              when c.type = 'A' then 'If paynum termed and email added as alias to another acct, delete ac_email row'
              when b.t_date is not null and c.type is NULL and c.user_name is NULL then 'Delete from ac_email (termed and gmail apparently deleted)'
              when c.type is NULL and c.user_name is NULL then 'Update ac_email or gmail depending on issue' || 
                  case when b.position in ('B','C','D','E') then ' (position doesn''t normally get an email acct)' else '' end
              end as \"ACTION LIKELY NEEDED\"
          from ac_email a
          left join ab_employees b
          on a.paynum = b.paynum
          left join steve.google_user_emails c
          on a.email = c.email
          join famv_payroll_position d
          on b.position = d.position
          left join famv_employees e
          on a.paynum = e.employeenumber
          where 
           (b.t_date is not null or c.user_name is null)
          and not ((a.email like '%@familyvetgroup.com' OR a.email like '%familyvet.com')  and sysdate < '01-JUL-23') /*special exception for family vet before Jul 1 23 */
      )combined
      order by combined.\"TERM DATE\" desc
    "
  )
  term_issues <- dbGetQuery(myOracleDB, myquery)
  term_issues_status <- check_mydf_rows(mydf = term_issues, MinNumRows = 1, myReportName)
  if(term_issues_status[[1]]){
    #query returned results, email them
    bodytext <- paste0(
      "<h2>Details for ", myReportName, "</h2>",
      "<p>The following are details relating to potential issues identified with ",
      "either Google email accounts or the related ac_email Oracle table:<br>",
      print(xtable(term_issues, 
                   digits = rep(0,ncol(term_issues)+1),
                   width = 1200
      ),
      html.table.attributes = "border=2 cellspacing=1",
      type = "html",
      caption.placement = "top",
      include.rownames=FALSE
      ),
      "<br><em>(Note that suspended Gmail accounts still incur monthly charges)</em><br>",
      "<p> For issues or questions about this report, contact:",
      "<br><br>",
      norm_sig,
      "</p>"
    )
    #send mail
    mailsend(norm_recip,
             paste0(myReportName, " - ",subject_date_text),
             bodytext,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
}
