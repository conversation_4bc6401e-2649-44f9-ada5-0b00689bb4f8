library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(tidyr)
library(tibble)
library(DBI)
library(ROracle)
library(keyring)
library(janitor)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20240925

### 20240925 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### updated email signature to use latest format provided by <PERSON> earlier in 2024

### 20240528 change:
### updated initial query to look back over 28 days in runs after 2 PM

### 20240422 change:
### updated initial query to also look at rehire date and not just s_date
### as sometimes rehires were coming through if those were the only updates

### 20240215 change:
### added <PERSON> and removed <PERSON> from recipient list

### 20230705 change:
### added <PERSON> and removed <PERSON> from recipient list

### 20230515 change:
### added trim to NAME column to clean up trailing spaces when no middle name/init present

### 20230510 change:
### new file


# Parameters

okaytocontinue <- TRUE

myReportName <- "Marco's New Hires for Background Checks"
scriptfolder <- "MARCOS_Background"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)



# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
#norm_recip <- c("Stephanie Forsberg<<EMAIL>>","Morgan Brown<<EMAIL>>")
norm_recip <- c("Stephanie Forsberg<<EMAIL>>","Reilly Cooper<<EMAIL>>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("Steve Olson<<EMAIL>>")
test_cc_recip <- c("<EMAIL>")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
myemailfiles <- c()

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)
sig_logo <- FALSE
if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  sig_logo <- TRUE
}



# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "02-OCT-22"
#query.days <- 28
query.weeks <- 4
#query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) - (query.days - 1) + 7, "%d-%b-%y")# Oracle date format for start date of reporting
query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) - ((query.weeks - 1) * 7), "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) + 6, "%d-%b-%y")# Oracle date format for end date of reporting

report.year <- lubridate::year(as.Date(query.startdate, "%d-%b-%y"))# YEAR for start date of reporting, this will be the folder to create workbooks in
report.removestart <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 49, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
report.removeend <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 35, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files

report.datetime.txt <- paste0(as.Date(query.date, "%d-%b-%y"), ' ', format(Sys.time(), "%H%M%S %Z"))
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
email.executedate <- format(as.Date(query.enddate, "%d-%b-%y")+1, "%a, %b %d")
#rptFN_noEXT <- paste0("MP People Tracker Files ", rpt.start,"-", rpt.end)
rptFN_noEXT <- "MP New Hires for Background Checks"
rptFN <- paste0(rptFN_noEXT, ".xlsx")
#initialize timer to help limit API request rate and email sending rate
last_sent <- Sys.time()

### define some functions ###

#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ="GMT")
#Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
#myOracleDB_deanna <- dbConnect(drv, username = "deanna", password =  key_get("Oracle", "deanna"), dbname = connect.string)
mySchema <- "STEVE"
myTable <- "MP_BACKGROUND_SENT"
myTableName_BG <- paste(mySchema, myTable, sep = ".")


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null || 'NULL')] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


writeXLSX_banking <- function(dirpath, fname, sname = "Sheet1", RptDF, 
                              colnames = TRUE, colwidths = NULL,  writeover = TRUE,
                              hyperlink_google = NULL, hypercol_google = NULL,
                              hyperlink_bank = NULL, hypercol_bank = NULL){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  my_cmnt <- createComment(
    comment = paste0("Workbook created: ",report.datetime.txt),
    #author = gSht_auth_email,
    style = NULL
    ,visible = FALSE
    ,width = 2.75
    ,height = 2
  )
  writeComment(wb = myWB, sheet = sname, col = "A", row = 1, comment = my_cmnt)
  #write 'paste to' hyperlinks
  if(class(hyperlink_google) == 'hyperlink' & !is.null(hypercol_google)){
    #hyperlink_google <- sapply(hyperlink_google, function(x) replace(x,is.na(x),as.character(NA)))
    writeData(wb = myWB, sheet = sname, x = hyperlink_google, startRow = 2, startCol = hypercol_google)
  }
  #write bank login hyperlinks
  if(class(hyperlink_bank) == 'hyperlink' & !is.null(hypercol_bank)){
    writeData(wb = myWB, sheet = sname, x = hyperlink_bank, startRow = 2, startCol = hypercol_bank)
  }
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext,
             inline = TRUE
    )
  }
}





###---------------------------------------------------------------------------------------###
#check weekend count of new hires to determine if they've been loaded (and not already sent)#
###---------------------------------------------------------------------------------------###
if(okaytocontinue){
  if(lubridate::hour(Sys.time()) < 14){
    #in A.M., continue ONLY if one or more hire/rehire dates from the last weekend are present
    myquery <- paste0(
      "
      select a.paynum
      from ab_employees a
      left join ", myTableName_BG," b
      on a.paynum = b.paynum
      left join famv_employees c
    	on a.paynum = to_number(c.employeenumber)
      where nvl(a.ADPREHIREDATE, a.s_date) >= trunc(sysdate, 'iw') - 3
      and nvl(a.ADPREHIREDATE, a.s_date) < trunc(sysdate, 'iw')
      and b.paynum is NULL
      and c.company_code = 'HFL'
      "
    )
  }else{
    #in the mid-afternoon, proceed if any hire/rehires from the last 4 weeks
    myquery <- paste0(
      "
      select a.paynum
      from ab_employees a
      left join ", myTableName_BG," b
      on a.paynum = b.paynum
      left join famv_employees c
    	on a.paynum = to_number(c.employeenumber)
      where nvl(a.ADPREHIREDATE, a.s_date) >= trunc(sysdate, 'iw') - 28
      and nvl(a.ADPREHIREDATE, a.s_date) < trunc(sysdate, 'iw')
      and b.paynum is NULL
      and c.company_code = 'HFL'
      "
    )
  }

  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  
  if(mydata_status[[1]]){
    #okay to proceed
  }else{
    #no results, do not continue
    okaytocontinue <- FALSE
  }
}


###-----------------------------------###
#Get Marco's New Hires and email results#
###-----------------------------------###
if(okaytocontinue){
  #get results
  myquery <- paste0(
    "
    select
        a.paynum
    ,   trim(a.lname||', '||a.fname||' '||a.mname) as NAME 
    ,   to_number(stores.store) as STORE
    ,   c.company_code
    ,   a.position
    ,   d.position_desc
    ,   d.category as position_category
    ,   TRUNC(nvl(a.ADPREHIREDATE, a.s_date), 'iw') as WK_STARTING
    ,   nvl(a.ADPREHIREDATE, a.s_date) as HIRE_DATE
    ,   CASE WHEN a.ADPREHIREDATE IS NOT NULL then 'Y' ELSE 'N' end as REHIRE
    ,   CASE WHEN a.ADPREHIREDATE IS NOT NULL then a.s_date end as ORIG_HIRE_DATE
    ,   a.status as EMP_STATUS
    ,   case when (a.driverindicator is null or a.driverindicator != 'Y')
            and (b.shiftldr is null or b.shiftldr != 'Y')
            and d.category != 'MANAGER'
            and d.category not in ('REGIONAL','DISTRICT')
            then 'Y' else 'N' END AS \"CREW_Y/N\"
    ,   a.driverindicator as \"DRIVER_Y/N\"
    ,   b.shiftldr as \"SHIFT_LEADER_Y/N\"
    ,   case when d.category = 'MANAGER' then 'Y' else 'N' end as \"GM_Y/N\"
    ,   case when d.category in ('REGIONAL','DISTRICT') then 'Y' ELSE 'N' END as \"RDO-DM_ Y/N\"
    ,   e.rm_fullname as RDO
    ,   e.dm_fullname as DM
    from ab_employees a
    left join
    (
        select listagg(i.store, ';') within group (order by i.store) as store
        , i.paynum
        from ab_store_employees i
        group by i.paynum
    )stores
    on a.paynum = stores.paynum
    left join ft_shiftldr b
    on a.paynum = b.paynum
    left join famv_employees c
    on a.paynum = to_number(c.employeenumber)
    left join famv_payroll_position d
    on a.position = d.position
    left join STEVE.so_rm_dm_mgr e
    on c.storenumber = to_char(e.store)
    left join ", myTableName_BG," f
    on a.paynum = f.paynum
    where nvl(a.ADPREHIREDATE, a.s_date) >= trunc(sysdate - 28, 'iw')
    and nvl(a.ADPREHIREDATE, a.s_date) < trunc(sysdate, 'iw')
    and c.company_code = 'HFL'
    and f.paynum is NULL
    "
  )
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  
  if(mydata_status[[1]]){
    #data to insert into 'sent' table
    mydata_insert <- copy(mydata[,c("PAYNUM","WK_STARTING")])
    
    #replace headers to replace underscores with spaces
    names(mydata) <- gsub("_"," ",names(mydata))
    
    myFN <- paste0(rptFN_noEXT, " ", report.datetime.txt, ".xlsx")
    mySN <- report.datetime.txt
    mySaveAs <- file.path(myReportPath, myFN)
    
    #prepare Excel file
    myXLSXColWidths <- data.frame (
      colname  = c(
        "PAYNUM",
        "NAME",
        "STORE",
        "COMPANY CODE",
        "POSITION",
        "POSITION DESC",
        "POSITION CATEGORY",
        "WK STARTING",
        "HIRE DATE",
        "REHIRE",
        "ORIG HIRE DATE",
        "EMP STATUS",
        "CREW Y/N",
        "DRIVER Y/N",
        "SHIFT LEADER Y/N",
        "GM Y/N",
        "RDO-DM  Y/N",
        "RDO",
        "DM"
        #"",
      )
      ,
      width = c(
        8,
        32,
        8,
        10,
        5,
        11,
        10,
        11,
        11,
        8,
        11,
        8,
        8,
        8,
        8,
        8,
        8,
        26,
        26
        #if(max(nchar(na.omit(perm_ToDelete_final[,"Remove share permissions for:"]))) > 36){min(80, max(nchar(na.omit(perm_ToDelete_final[,"Remove share permissions for:"]))))}else{38}
      )
      ,
      stringsAsFactors = FALSE
    ) #myXLSXColWidths
    writeXLSX_banking(
      dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, 
      colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE
    )
    myemailfiles <- c(myemailfiles, file.path(myReportPath, myFN))
    
    #send email
    bodytext <- paste0(
      "<h3>", paste0(myReportName, " updates for ", format(as.Date(query.date, "%d-%b-%y"), "%m/%d/%Y")), "</h3>",
      "<p>Data attached in the following file(s) reflects hires from the previous week. ",
      "It may also include older hires from the last four weeks IF they were not sent in ",
      "previous emails (e.g. backdated hires, restaurant with internet issues).</p>", 
      "<br>", 
      norm_sig
    )
    mailsend(norm_recip,
             paste0(myReportName),
             bodytext,
             if(length(myemailfiles) == 0){attachment = NULL}else{attachment = myemailfiles},
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
    Sys.sleep(1)
    unlink(mySaveAs)
    Sys.sleep(2)
    
    #write file with generic name to preserve storage space
    myFN <- paste0(rptFN_noEXT, ".xlsx")
    writeXLSX_banking(
      dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, 
      colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE
    )
  }else{
    okaytocontinue <- FALSE
  }
}


###---------------------------------------------###
#update Oracle table of previously emailed results#
###---------------------------------------------###
if(okaytocontinue){
  #purge old records from Oracle table, then load new results
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName_BG, "
      where wk_starting < trunc(sysdate - 28, 'iw')
    "
  )
  rs_sel <- dbSendQuery(myOracleDB, myquery_select)
  select_cnt <- dbFetch(rs_sel, n = -1)
  
  myquery_delete <- paste0(
    "
    delete
    from ", myTableName_BG, "
    where wk_starting < trunc(sysdate - 28, 'iw')
    "
  )
  rs_del <- dbSendQuery(myOracleDB, myquery_delete)
  if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(myOracleDB)
    myDeleteStores_failed <- TRUE
    myDeleteError_text <- paste0(
      "<p>There was an unexpected issue deleting previous data that might ",
      "have been present for dates between ", query.startdate,
      " and ", format(mydates$E_DATE, "%d-%b-%y"),". <b>The routine has ",
      "ABORTED without attempting to load the current results!</b></p>"
    )
    
    
    
    #do not load since deletion apparently failed
  }else{
    #delete was apparently successful, commit and proceed with load of latest results
    dbCommit(myOracleDB)
    
    
    
    
    
    #Insert trans rows into myTable, count rows before and after to catch load issues
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName_BG
    )
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt_pre <- dbFetch(rs_sel, n = -1)
    dbClearResult(rs_sel)
    rs_write <- dbWriteTable(myOracleDB, myTable, mydata_insert, row.names = FALSE , append = TRUE, schema = mySchema)
    #get new count of rows in table
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt_post <- dbFetch(rs_sel, n = -1)
    myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
    mydata_numrows <- nrow(mydata_insert)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, create excel file of load data and warn
      myFN <- paste0(myTable, ' Failed Load Data.xlsx')
      myXLSXColWidths <- data.frame (
        colname  = c(
          "PAYNUM",
          "WK_STARTING"
          #"",
        )
        ,
        width = c(
          12,
          14
          #if(max(nchar(na.omit(perm_ToDelete_final[,"Remove share permissions for:"]))) > 36){min(80, max(nchar(na.omit(perm_ToDelete_final[,"Remove share permissions for:"]))))}else{38}
        )
        ,
        stringsAsFactors = FALSE
      ) #myXLSXColWidths
      
      writeXLSX_banking(
        dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata_insert, 
        colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE
      )
      myemailfiles <- c(file.path(myReportPath, myFN))
      failed_cnt <- mydata_numrows - myload_numrows
      bodytext <- paste0(
        "<p>", failed_cnt, " rows failed to load into the ",
        myTable, " Oracle table!</p>",
        "<p>The attached file contains all the rows that were ",
        "attempting to load. Investigate the data in the table ",
        "and manually insert any that are missing.</p><br>",
        warn_sig
      )
      mailsend(warn_recip,
               paste0(myReportName, " error loading Oracle"),
               bodytext,
               if(length(myemailfiles) == 0){attachment = NULL}else{attachment = myemailfiles},
               inline = sig_logo,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
}

