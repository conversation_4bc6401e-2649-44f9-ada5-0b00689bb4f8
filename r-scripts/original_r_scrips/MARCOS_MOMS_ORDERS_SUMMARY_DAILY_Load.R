library(xtable)
library(reshape2)
library(dplyr)
library(purrr)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(tidyr)
library(DBI)
library(odbc)
library(keyring)
library(janitor)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20250123

### 20250123 change:
### added WEB_FOOD_COST_IDEAL and APP_FOOD_COST_IDEAL columns to data, fixed where <PERSON> work PC not in test_computers list

### 20241231 change:
### fixed reversion of 9/17 bug fix due to 12/23 version using 9/16 version by accident.

### 20241223 change:
### added DELIVERY_ALL_FOOD_COST_IDEAL, DELIVERY_3PD_FOOD_COST_IDEAL and DELIVERY_DDD_FOOD_COST_IDEAL
### columns, also explicitly excluded 3PD order IDs from DELIVERY_ALL and DELIVERY_NDEF
### columns (some had 'delivery' as order type)

### 20240917 change:
### fixed bug validating mydates df rows

### 20240916 change:
### changed signature HTML "img src" to use published HVLTD logos instead of local inline file
### this IS NOT a full signature switch

### 20240910 change:
### converted from mailR package (SMTP), to gmailr (OAuth-GMail API) ahead of 20240930 SMTP deprecation in GMail

### 20240903 change:
### moved mydates query into it's own section with df okay check
### added query to detect duplicate order ids and email warning if they are detected.

### 20240826 change:
### modified to run on Tableau PC with Tableau credentials, also altered write to table method

### 20240808 change:
### modified MAKETIME_COUNT column to ignore any 0 second maketimes instead of excluding certain item categories

### 20240807 change:
### modified mydates query so max E_DATE can be up to sysdate - 1 (previously last Sunday)
### modified main query for revised DELIVERY_3PD logic (uses payment_type instead of SOURCE)
### added TOTAL_FOOD_COST_IDEAL column that was left out of previous version

### 20240729 change:
### new script, based on then current version of
### MARCOS_FT_ORDER_SUMMARY_DAILY_Load.R
### ported for Snowflake DB and MOMS tables
### and to run on Steve Desktop (vs Tableau PC) due to SSO security

# Parameters
okaytocontinue <- TRUE

myReportName <- "Marco's MOMS.ORDERS_DAILY_SUMMARY-Snowflake Load"
scriptfolder <- "MARCOS_Daily_Reports"
rptfolder <- "reports"
#20240826: use network shared drive paths as default, but can be changed depending on environment further down
logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")


###STAGE Snowflake versions###
#Sf_DB <- "STAGE_CSM_DB"
#Sf_schema <- "CORPORATE"
#Sf_wh <- "STAGE_DATA_ANA_WH"
#Sf_role <- "AR_STAGE_CONSUMPTION_RW"
#Sf_user <- key_get("SfHV", "tableau_ID_stage")
#Sf_pw <- key_get("SfHV", "tableau_PW_stage")
###PROD Snowflake versions###
Sf_DB <- "PROD_CSM_DB"
Sf_schema <- "CORPORATE"
Sf_wh <- "PROD_DATA_ANA_WH"
Sf_role <- "AR_PROD_CONSUMPTION_RW"
Sf_user <- key_get("SfHV", "tableau_ID_prod")
Sf_pw <- key_get("SfHV", "tableau_PW_prod")

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role
)
rm(Sf_user,Sf_pw)

mySchema <- "MOMS"
myTable <- "ORDERS_SUMMARY_DAILY"
myTableName <- paste(mySchema, myTable, sep = ".")



# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_recip <- c("<EMAIL>") #testing purposes, normal is line above
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

test_computers <- c("STEVEANDJENYOGA","STEVEO-PLEX7010")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to shared/central drive instead of local paths
}else{
  testing_pc <- FALSE
}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  # default assigned paths above are now network paths
}else if(this_computer == "DESKTOP-TABLEAU"){
  logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")
}else if(this_computer == "STEVEO-PLEX7010"){
  logpath <- file.path("C:","Users","steve","Documents","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("C:","Users","steve","Documents","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("C:","Users","steve","Documents","ReportFiles","HTML_signatures.csv")
}


myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo
  #20240916:
  #(HVLTD Corp with Brands)
  sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
  if(exists("norm_sig")){norm_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
}

# date and time variables
query.date <- format(Sys.Date(), "%Y-%m-%d")
#query.date <- "2024-03-24" #testing or re-load prior date line only 1/1/24-3/24/24
#query.date <- "2024-06-16" #testing or re-load prior date line only 3/25/24-6/16/24
#query.date <- "2024-08-11" #testing or re-load prior date line only 5/20/24-8/11/24
#query.date <- "2024-11-03" #testing or re-load prior date line only 8/12/24-11/03/24

query.periods <- 2 #total number of periods to cover in update query
query.period_offset <- 0 #trailing period(s) for END of update query (start is s_date for query.periods + query.period_offset)
rpt.date <- as.Date(query.date, "%Y-%m-%d")



### define some functions ###
mailsend <- function(
    recipient, subject, body, attachment = NULL, inline = FALSE, 
    sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file(s), gmailr gm_attach_file() function only attaches one at a time,
  #so purrr::reduce is used to allow for multiple
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



# Query Snowflake for desired query dates
if(okaytocontinue){
  #Get dates from MP_CALENDAR for main query
  myquery <- paste0(
    "
      select
      to_char(to_date(min(s_date))) as s_date
      , to_char(to_date(max(case when e_date > (TRUNC(CURRENT_DATE, 'day') - 1) then (TRUNC(CURRENT_DATE, 'day') - 1) else e_date end))) as e_date
      from CORPORATE.mp_calendar
      where cal_id >= (
        select i.cal_id - ", query.periods + query.period_offset, "
        from CORPORATE.mp_calendar i
        where to_date('", query.date, "') between i.s_date and i.e_date
      )
      and cal_id <= (
        select i.cal_id - ", query.period_offset, "
	      from CORPORATE.mp_calendar i
	      where to_date('", query.date, "') between i.s_date and i.e_date
      )
    "
  )
  mydates <- dbGetQuery(mySfDB, myquery)
  minRows <- 1
  mydata_status <- check_mydf_rows(mydates, MinNumRows = minRows, ReportName = myReportName)
  if(mydata_status[[1]]==FALSE){
    #unable to get dates, abort
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
         "been an error querying the start and end dates for the ", myReportName, " routine!</p>",
         "<br>",
         "<p>The routine is aborting without an update</p> ",
         warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, missing data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


# Query Snowflake for duplicate orders
if(okaytocontinue){
  #Get dates from MP_CALENDAR for main query
  myquery <- paste0(
    "SELECT store_number",
    ", business_date ",
    ", count(*) AS order_cnt ",
    ", count(DISTINCT ORDER_ID) AS order_cnt_distinct ",
    "FROM moms.ORDERSRESULT ",
    "WHERE BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "','yyyy-mm-dd')",
    "AND BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "','yyyy-mm-dd')",
    "AND void = 0 ",
    "GROUP BY STORE_NUMBER, BUSINESS_DATE ",
    "HAVING count(*) <> count(DISTINCT ORDER_ID)"
  )
  myDups <- dbGetQuery(mySfDB, myquery)
  minRows <- 1
  mydata_status <- check_mydf_rows(myDups, MinNumRows = minRows, ReportName = myReportName)
  if(mydata_status[[1]]){
    #duplicate order ids detected, abort
    #get list of unique dates
    myDupsDates <- myDups$BUSINESS_DATE %>% unique(.) %>% as.character(.)
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there appears ",
       "to be duplicate order ids found in the ", myReportName, " routine! There were <b>",
       nrow(myDups), " dates</b> were a store had duplicates in the MOMS.ORDERSRESULT table.</p>",
       "<p>The dates with duplicates detected are:<br>",
       paste0(myDupsDates, collapse = '; '),
       "</p>",
       "<p><b>The routine will continue with the update, but data for the above date(s) will ",
       "most likely be incorrect!</b></p> ",
       "<p>Run the following query for more specifics:<br>", 
       myquery, "</p>",
       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, DUPLICATE data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}

# Query Snowflake for recent ORDERSRESULT data, 
if(okaytocontinue){

  
  #Main Data Query
  myquery <- paste0(
    "
      SELECT /* 20241223 add exclusion of 3PD orders to DELIVERY_ALL and DELIVERY_NDEF columns */
        ORD.store_number,
        ORD.business_date,
        ORD.business_date - 364 as ly_date,
        ORD.business_date - 7 as lw_date,
        count(distinct(ORD.order_id)) as  total_orders,
        round(sum(ORD.net),2) as net_sales,
        round(sum(ORD.gross - ORD.tax_amount),2) as gross_sales, /* FT equivalent version...MOMS gross includes tax collected*/
        count(DISTINCT(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.order_id END)) AS DELIVERY_ALL_COUNT,
        round(sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.net ELSE 0 END ),2) AS DELIVERY_ALL_NETSALES,
        round(sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.gross - ORD.tax_amount ELSE 0 END ),2) AS DELIVERY_ALL_GROSSSALES,
        count(DISTINCT(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') AND TPD.ORDER_ID IS NULL THEN ORD.DISPATCH_TIME END)) AS DELIVERY_ALL_RUNS,
        count(DISTINCT(
        	CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
      		THEN ORD.order_id
      		END)
        ) AS DELIVERY_NDEF_COUNT,
        sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
      		THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DISPATCH_TIME)
      		END
        ) AS DELIVERY_NDEF_OTD_TIME,
        sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
      		THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME)
      		END
        ) AS DELIVERY_NDEF_DELIVERY_TIME,
        sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
      		THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.PROMISE_TIME)
      		END
        ) AS DELIVERY_NDEF_PROMISE_TIME,
        count(DISTINCT(
        	CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) <= 1800
      		THEN ORD.order_id
    		END)
        ) AS DELIVERY_NDEF_COUNT_L30,
        count(DISTINCT(
        	CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) > 2400
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
      		THEN ORD.order_id
      		END)
        ) AS DELIVERY_NDEF_COUNT_G40,
        count(DISTINCT(
        	CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE')
        		AND TPD.ORDER_ID IS NULL 
      			AND ORD.FUTURE = 0
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) > 3600
      			AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, ORD.DELIVERY_TIME) < 7200
      		THEN ORD.order_id
      		END)
        ) AS DELIVERY_NDEF_COUNT_G60,
        --count(DISTINCT(CASE WHEN UPPER(ORD.SOURCE) IN ('DOOR DASH', 'DOORDASH-DELIVERY', 'DOORDASH-TAKEOUT', 'GRUBHUB','GRUBHUB-DELIVERY','GRUBHUB-TAKEOUT','POSTMATES','POSTMATES-DELIVERY', 'POSTMATES-TAKE OUT', 'UBER EATS','UBEREATS-DELIVERY','UBEREATS-TAKEOUT') THEN ORD.order_id END)) AS DELIVERY_3PD_COUNT, /* */
        /* 2024-08-07 line below replaces line above due to new logic for 3PD */
        count(TPD.ORDER_ID) AS DELIVERY_3PD_COUNT, /* */
        --round(sum(CASE WHEN UPPER(ORD.SOURCE) IN ('DOOR DASH', 'DOORDASH-DELIVERY', 'DOORDASH-TAKEOUT', 'GRUBHUB','GRUBHUB-DELIVERY','GRUBHUB-TAKEOUT','POSTMATES','POSTMATES-DELIVERY', 'POSTMATES-TAKE OUT', 'UBER EATS','UBEREATS-DELIVERY','UBEREATS-TAKEOUT') THEN ORD.net END),2) AS DELIVERY_3PD_NETSALES,
        /* 2024-08-07 line below replaces line above due to new logic for 3PD */
        round(sum(CASE WHEN TPD.ORDER_ID IS NOT NULL THEN ORD.net ELSE 0 END),2) AS DELIVERY_3PD_NETSALES,
        --round(sum(CASE WHEN UPPER(ORD.SOURCE) IN ('DOOR DASH', 'DOORDASH-DELIVERY', 'DOORDASH-TAKEOUT', 'GRUBHUB','GRUBHUB-DELIVERY','GRUBHUB-TAKEOUT','POSTMATES','POSTMATES-DELIVERY', 'POSTMATES-TAKE OUT', 'UBER EATS','UBEREATS-DELIVERY','UBEREATS-TAKEOUT') THEN (ORD.gross - ORD.tax_amount) END),2) AS DELIVERY_3PD_GROSSSALES,
        /* 2024-08-07 line below replaces line above due to new logic for 3PD */
        round(sum(CASE WHEN TPD.ORDER_ID IS NOT NULL THEN (ORD.gross - ORD.tax_amount) ELSE 0 END),2) AS DELIVERY_3PD_GROSSSALES,
        count(DISTINCT(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN ORD.order_id END)) AS DELIVERY_DDD_COUNT,
        round(sum(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN ORD.net END),2) AS DELIVERY_DDD_NETSALES,
        round(sum(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN (ORD.gross - ORD.tax_amount) END),2) AS DELIVERY_DDD_GROSSSALES,
        count(DISTINCT(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN ORD.order_id END)) AS WEB_COUNT,
        ROUND(sum(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN ORD.net ELSE 0 END ),2) AS WEB_NETSALES,
        ROUND(sum(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN (ORD.gross - ORD.tax_amount) ELSE 0 END ),2) AS WEB_GROSSSALES,
        count(DISTINCT(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN ORD.order_id END)) AS APP_COUNT,
        sum(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN ORD.net ELSE 0 END ) AS APP_NETSALES,
        sum(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN (ORD.gross - ORD.tax_amount) ELSE 0 END ) AS APP_GROSSSALES,
        count(CASE WHEN ORD.FUTURE = 0
    		  AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME) < 7200 /* Exclude any order where maketime > 2 hours */
    		  AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME) > 0 /* Exclude any order where maketime = 0 seconds (e.g. Beverages, Gift Cards, etc) that skew results */
    		  THEN ORD.ORDER_ID 
    		  END
        ) AS MAKETIME_COUNT,
        sum(CASE WHEN ORD.FUTURE = 0
    		AND DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME) < 7200
    		THEN DATEDIFF(SECOND, ORD.ORDER_END_TIME, OI.MAX_END_TIME)
    		END
        ) AS MAKETIME_TIME,
        SUM(ORD.TOTAL_FOOD_COST) AS TOTAL_FOOD_COST_IDEAL, /* added 2024-08-07 */
        SUM(TFCV.total_food_cost) AS TOTAL_FOOD_COST_VOID, /* as of 7/18/2024, this is NOT being populated in Snowflake PROD table yet */
        sum(CASE WHEN upper(ORD.ORDER_TYPE) IN ('DELIVERY','DELIVERY ONLINE') 
        	AND TPD.ORDER_ID IS NULL 
        	THEN ORD.TOTAL_FOOD_COST 
        	ELSE 0 END ) AS DELIVERY_ALL_FOOD_COST_IDEAL, /* added 2024-12-23 */
        sum(CASE WHEN TPD.ORDER_ID IS NOT NULL THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS DELIVERY_3PD_FOOD_COST_IDEAL, /* added 2024-12-23 */
        sum(CASE WHEN UPPER(ORD.TIP_TYPE) = 'DOORDASH DRIVE' THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS DELIVERY_DDD_FOOD_COST_IDEAL, /* added 2024-12-23 */
        sum(CASE WHEN upper(ORD.SOURCE) IN ('WEB','ONLINE') THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS WEB_FOOD_COST_IDEAL, /* added 2025-01-23 */
        sum(CASE WHEN upper(ORD.SOURCE) IN ('MOBILE APP','IOS WEB APP','ANDROID WEB APP') THEN ORD.TOTAL_FOOD_COST ELSE 0 END) AS APP_FOOD_COST_IDEAL /* added 2025-01-23 */
      from MOMS.ORDERSRESULT ORD
      LEFT JOIN 
      (
      	SELECT 
      	STORE_NUMBER,
      	ORDER_ID,
      	MAX(MAKE_END_TIME) AS MAX_END_TIME
      	FROM MOMS.ORDERSITEM
      	WHERE REMOVED = 0
      	--line below removed 2024-08-08 as all 0 maketime orders will be ignored in case-when of select
      	--AND UPPER(CATEGORY) NOT IN ('BEVERAGES','EXTRAS','CHIPS','GIFT CARD','CATERING BEVERAGE') /* Exclude 0 maketime items to avoid skewing/manipulating results */
      	GROUP BY STORE_NUMBER, ORDER_ID
      ) OI
      	ON ORD.STORE_NUMBER = OI.STORE_NUMBER 
      	AND ORD.ORDER_ID = OI.ORDER_ID 
      
      LEFT JOIN
      (
      	SELECT STORE_NUMBER,
      		BUSINESS_DATE,
      		sum(total_food_cost) AS total_food_cost /* as of 7/18/2024, this is NOT being populated in Snowflake PROD table yet for VOID = 1 rows */
      	FROM MOMS.ORDERSRESULT
      	WHERE VOID = 1
      	AND BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
      	AND BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
      	--AND BUSINESS_DATE BETWEEN '2024-07-01' AND '2024-07-14' /* TESTING */
      	GROUP BY STORE_NUMBER, BUSINESS_DATE 
      ) TFCV
      	ON ORD.STORE_NUMBER = TFCV.STORE_NUMBER
      	AND ORD.BUSINESS_DATE = TFCV.BUSINESS_DATE
      /* next join added 2024-08-07 in order to identify 3PD columns correctly ('EZCater' was not in ORD.SOURCE column previously used) */
      LEFT JOIN 
      (
      	SELECT DISTINCT 
      	ORD.STORE_NUMBER,
      	ORD.ORDER_ID
        --	 , ORD.ORDER_TYPE /* TESTING for 'Delivery' type */
        --	 , ORD.BUSINESS_DATE /* TESTING */
      	FROM MOMS.ORDERSRESULT ORD
      	INNER JOIN MOMS.ORDERSPAYMENT OP
      	ON ORD.ORDER_ID = OP.ORDER_ID 
      	AND ORD.STORE_NUMBER = OP.STORE_NUMBER 
      	WHERE UPPER(OP.PAYMENT_TYPE) IN ('DOOR DASH','UBEREATS','GRUBHUB','SEAMLESS','POSTMATES','FOODA','EZCATER')
      	  AND ORD.BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
      	  AND ORD.BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
      	  --AND ORD.BUSINESS_DATE BETWEEN '2024-07-01' AND '2024-07-14' /* TESTING */
      ) TPD
        ON ORD.ORDER_ID = TPD.ORDER_ID 
        AND ORD.STORE_NUMBER = TPD.STORE_NUMBER
      WHERE ORD.void = 0
      	AND ORD.BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
      	AND ORD.BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
        --AND ORD.BUSINESS_DATE BETWEEN '2024-07-01' AND '2024-07-14' /* TESTING */
        --AND ORD.STORE_NUMBER < 3510 /* TESTING */
      group by 
      	ORD.store_number, 
      	ORD.business_date, 
      	ORD.business_date - 364, 
      	ORD.business_date - 7
      	--ORDER BY ORD.STORE_NUMBER, ORD.BUSINESS_DATE /* TESTING */
    "
  )
  mydata <- dbGetQuery(mySfDB, myquery)
  minRows <- (28 * (query.periods - 1))
  mydata_status <- check_mydf_rows(mydata, MinNumRows = minRows, ReportName = myReportName)
  if(mydata_status[[1]]){
    
    
  }else{
    #apparent error, send warning email
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error querying the data for the ", myReportName, " routine! ",
                       "The query failed or produced less than ", minRows, " rows of results.</p>",
                       "<br>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, missing data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}





#Load Snowflake
if(okaytocontinue){
  #Delete rows within expected date range and replace with current data
  myDeleteRows_failed <- FALSE
  myLoadRows_failed <- FALSE
  myDeleteError_text <- ""
  
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName, "
      where BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
      and BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
    "
  )
  #rs_sel <- dbSendQuery(mySfDB, myquery_select)
  #select_cnt <- dbFetch(rs_sel, n = -1)
  #dbClearResult(rs_sel)
  select_cnt <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
  dbBegin(mySfDB)
  myquery_delete <- paste0(
    "
      delete from ", myTableName, "
      where BUSINESS_DATE >= to_date('", mydates$S_DATE[1], "')
      and BUSINESS_DATE <= to_date('", mydates$E_DATE[1], "')
      "
  )
  rs_del <- dbSendQuery(mySfDB, myquery_delete)
  delete_cnt <- dbGetRowsAffected(rs_del)
  if(delete_cnt != select_cnt){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(mySfDB)
    myDeleteRows_failed <- TRUE
    myDeleteError_text <- paste0(
      "<p>There was an unexpected issue deleting previous data that might ",
      "have been present for dates between ", mydates$S_DATE[1],
      " and ", mydates$E_DATE[1],". <b>The routine has ",
      "ABORTED without attempting to load the current results!</b></p>"
    )
    dbClearResult(rs_del)
    #do not load since deletion apparently failed
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    dbCommit(mySfDB)
    dbClearResult(rs_del)
    #Insert trans rows into myTable, count rows before and after to catch load issues
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName, "
        where BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
        and BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
      "
    )
    #rs_sel <- dbSendQuery(mySfDB, myquery_select)
    #select_cnt_pre <- dbFetch(rs_sel, n = -1)
    #dbClearResult(rs_sel)
    select_cnt_pre <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    #rs_write <- dbWriteTable(mySfDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata)
    
    #get new count of rows in table
    #rs_sel <- dbSendQuery(mySfDB, myquery_select)
    #select_cnt_post <- dbFetch(rs_sel, n = -1)
    select_cnt_post <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    #myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
    myload_numrows <- select_cnt_post - select_cnt_pre
    mydata_numrows <- nrow(mydata)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, get load counts by store
      myquery <- paste0(
        "
          select store_number
          ,   count(BUSINESS_DATE) as COUNT_LOADED
          from ", myTableName, "
          where BUSINESS_DATE >= to_date('", mydates$S_DATE[1], "')
            and BUSINESS_DATE <= to_date('", mydates$E_DATE[1], "')
          group by store_number
          order by store_number
          "
      )
      myLoadStores_curr <- dbGetQuery(mySfDB, myquery)
      #summarize dataframe to get counts by store
      myLoadStores_results_cnt <- mydata %>% select(STORE_NUMBER) %>% group_by(STORE_NUMBER) %>% summarize(QUERY_RESULTS = n())
      myLoad_failed <- myLoadStores_results_cnt %>% 
        dplyr::left_join(myLoadStores_curr, by = c("STORE_NUMBER")) %>%
        mutate_if(is.numeric,coalesce,0) %>%
        .[which(.$QUERY_RESULTS != .$COUNT_LOADED), ]
      
      
      if(nrow(myLoad_failed)>0){
        myLoadRows_failed <- TRUE
        myLoadError_text <- paste0(
          "<p>One or more stores failed to load the expected number or rows ",
          "into Snowflake for the dates between ", mydates$S_DATE[1],
          " and ", mydates$E_DATE[1],". <b>The Snowflake table (",
          myTableName, ")", " will have incomplete results for these dates!</b> ",
          "Investigate the stores shown below and re-run this routine when issue ",
          "has been addressed.<br>",
          print(
            xtable(myLoad_failed, 
                   digits = rep(0,ncol(myLoad_failed)+1),
                   align = c(rep("c", ncol(myLoad_failed) + 1))
            ),
            html.table.attributes = 'border=2 cellspacing=1 align="center"',
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          ),
          "</p>"
        )
      }
    }
    
    
    
  }
  if(myDeleteRows_failed || myLoadRows_failed){
    #email warning
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error populating Snowflake in the ", 
                       myReportName, " routine. </p>",
                       if(myDeleteRows_failed){myDeleteError_text},
                       if(myLoadRows_failed){myLoadError_text},
                       "<br>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Snowflake load error"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}




#myquery <- "select * from moms.orders_summary_daily where business_date >= '2024-01-01'"
#TEST <- dbGetQuery(mySfDB, myquery)
