library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
library(RODBC)
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20250523

### 20250523 change:
### added 'SEP' as an approved value for INSLCODE in the SQL for owned buildings

### 20250321 change:
### SQL change due to BLDGID change in MRI, 'HV3RD ' changed to 'HV3RD' (sb excluded from results)

### ******** change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of ******** SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced check_mydata_rows() function with more universal check_mydf_rows()

### ******** change:
### excepted 'HV3RD' and 'HVCORP' building IDs from reporting per Rachael H. request


### ******** change:
### added INSLCODE 'BAN' (bank) to approved codes

### ******** change:
### updated mailsend to use keyring

### ******** change:
### added keyring package

### ******** change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

rptfolder <- "LEGACY_MRI_Exceptions-Insurance"
myReportName <- "MRI INSURANCE Exceptions"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)


#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="GMT")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>","<EMAIL>")
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
  logpath <- file.path(mainpath,rptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  rptpath <- file.path(logpath,rptfolder)
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
  logpath <- file.path(mainpath,rptfolder)
}

rptpath <- logpath


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try appending report time to filename
    myNewFN <- gsub(report.startdate, paste0(report.startdate,"-",report.time), myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext
    )
  }
}






### Find Insurance Exceptions and email Excel file with results if applicable
if(okaytocontinue){
  
  myReportPath <- rptpath
  myFN <- paste0("MRI_Insurance_Exceptions", ".xlsx")
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery_exceptions <- paste0(
    "
      	SELECT /* Combined Insurance Exceptions v********...revised ******** for SNOWFLAKE, BUT NOT TESTED AGAINST SQL SERVER VERSION YET!!! */
      	CASE
      	WHEN TO_DATE(ENTITY.DISPOSED) >= DATEADD('YEAR',-2,DATE_TRUNC('YEAR',CURRENT_DATE())) AND INS.CARRIER IS NOT NULL
      		THEN 'BLDG DISPOSED, currently has CARRIER'
      	WHEN (BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL) AND (POWNER.OWNED_RENTED = 'Owned' AND (INS.INSLCODE IS NULL or INS.INSLCODE NOT IN ('SLF','INS','BAN','SEP')) )
      		THEN 'BLDG OWNED, missing current INS info'
      	WHEN (INS.CARRIER IS NOT NULL or INS.INSLLIMI != 0 or INS.DEDUCT != 0)
      		AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
      		AND INS.INSLCODE = 'SLF'
      		THEN 'SLF INSURED, but has Carrier, Limit or Deductable Info'
      	END AS ISSUE
      ,	CASE
      	WHEN TO_DATE(ENTITY.DISPOSED) >= DATEADD('YEAR',-2,DATE_TRUNC('YEAR',CURRENT_DATE())) AND INS.CARRIER IS NOT NULL
      		THEN TO_DATE(ENTITY.DISPOSED)
      	WHEN (BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL) AND (POWNER.OWNED_RENTED = 'Owned' AND (INS.INSLCODE IS NULL or INS.INSLCODE NOT IN ('SLF','INS','BAN','SEP')) )
      		THEN IFNULL(TO_DATE(INS.INS_LAST_MOD_DATE), TO_DATE(ENTITY.ACQUIRED))
      	WHEN (INS.CARRIER IS NOT NULL or INS.INSLLIMI != 0 or INS.DEDUCT != 0)
      		AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
      		AND INS.INSLCODE = 'SLF'
      		THEN TO_DATE(INS.INS_LAST_MOD_DATE)
      	END AS \"CHG DATE\"
      ,	TO_CHAR(BLDG.BLDGID) as BLDGID
      ,	INS.INSLCODE AS \"INS CODE\"
      ,	INS.INSCTYPE AS \"INS TYPE\"
      ,	INS.PERTAIN
      ,	INS.CARRIER
      ,	INS.LIMIT
      ,	INS.DEDUCT AS DEDUCTABLE
      ,	INS.INS_START_DATE AS \"INS START DATE\"
      ,	INS.INS_END_DATE AS \"INS END DATE\"
      ,	INS.INS_LAST_MOD_DATE AS \"INS LAST MODIFIED\"
      FROM MRI.BLDG
      LEFT JOIN MRI.ENTITY
      ON BLDG.ENTITYID = ENTITY.ENTITYID
      LEFT JOIN
      ( /* Insurance current info */
      	SELECT
      		INSL.TABLEKEY AS BLDGID
      	,	INSL.RECNUM
      	,	INSL.INSLCODE
      	,	INSC.INSCTYPE
      	,	INSL.INSLLIMI
      	,	INSL.CARRIERID
      	,	TB_AM_INSCARRIER.INSCARRIER as CARRIER
      	,	TB_AM_INSCARRIER.PHONE AS PHONE
      	,	INSL.DEDUCT
      	,	INSL.INSLLIMI AS LIMIT
      	,	INSL.REPLACE
      	,	INSL.PERTAIN
      	,	INSL.LASTDATE AS INS_LAST_MOD_DATE
      	,	TO_DATE(INSL.INSLSTAR) AS INS_START_DATE
      	,	TO_DATE(INSL.INSLEND) AS INS_END_DATE
      	FROM MRI.INSL
      	LEFT JOIN MRI.INSC ON INSL.INSLCODE = INSC.INSCCODE
      	LEFT JOIN MRI.TB_AM_INSCARRIER ON INSL.CARRIERID = TB_AM_INSCARRIER.INSCARRIERID
      	WHERE INSL.RECNUM = (SELECT MAX(I.RECNUM) FROM MRI.INSL I WHERE I.TABLEID = 'BLDG' AND I.TABLEKEY = INSL.TABLEKEY AND I.INSLSTAR <= CURRENT_DATE AND I.INSLEND >= CURRENT_DATE )
      ) INS
      ON BLDG.BLDGID = INS.BLDGID
      
      LEFT JOIN
      (/* PRIMARY OWNER */
      	SELECT ENTITYID
      	,	OWNERID
      	,	OWNED_RENTED	
      	FROM
      	(
      	  SELECT O.ENTITYID
      	  ,	O.OWNERID
      	  ,	CASE WHEN UPPER(O.OWNERID) = 'RENTED' THEN 'Rented' ELSE 'Owned' END AS OWNED_RENTED
      	  ,	O.BEGPD
      	  ,	O.PRIMARYOWN
      	  ,	O.PERCENT
      	  ,	O.LASTDATE
      	  --,	RANK() OVER (PARTITION BY O.ENTITYID, O.BEGPD ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.LASTDATE DESC) AS RANK /* RANK OWNERSHIP INTERESTS SINCE MRI DOESN'T FULLY VALIDATE ENTRIES */
      	  ,	RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.BEGPD, O.LASTDATE DESC) AS RANK /* RANK OWNERSHIP INTERESTS SINCE MRI DOESN'T FULLY VALIDATE ENTRIES */
      	  FROM MRI.GOWN O
      	  WHERE (O.BEGPD = 
      		  (
      			  SELECT MAX(I.BEGPD)
      			  FROM MRI.GOWN I
      			  WHERE I.ENTITYID = O.ENTITYID
      				  AND I.BEGPD <= TO_CHAR(GETDATE(), 'yyyyMM')
      		  )
      	  OR (
      			  SELECT MAX(I.BEGPD)
      			  FROM MRI.GOWN I
      			  WHERE I.ENTITYID = O.ENTITYID
      		  ) IS NULL
      	  )
      	  AND (O.ENDPD IS NULL OR O.ENDPD >= TO_CHAR(GETDATE(), 'yyyyMM'))
      	) RANKED
      	WHERE RANK = 1
      ) POWNER
      ON ENTITY.ENTITYID = POWNER.ENTITYID
      LEFT JOIN MRI.GNAM 
      ON POWNER.OWNERID = GNAM.OWNERID
      WHERE trim(bldg.BLDGID) not in ('HV3RD','HVCORP')
      AND
      (
      		( /* BLDG disposed, but INS.CARRIER is not NULL (active insurance?) */
      			TO_DATE(ENTITY.DISPOSED) >= DATEADD('YEAR',-2,DATE_TRUNC('YEAR',CURRENT_DATE()))
      			AND INS.CARRIER IS NOT NULL
      		)
      		OR
      		( /* Owned BLDG (normally self insure) missing info */
      			(BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL)
      			AND (POWNER.OWNED_RENTED = 'Owned' AND (INS.INSLCODE IS NULL or INS.INSLCODE NOT IN ('SLF','INS','BAN','SEP')) )
      			AND UPPER(BLDG.BLDGID) NOT LIKE 'ROVER%'
      		)
      		OR
      		( /* Owned BLDG (self insure) but has limit, deductable or carrier present) */
      			(INS.CARRIER IS NOT NULL or INS.INSLLIMI != 0 or INS.DEDUCT != 0)
      			AND (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE <> 'Y')
      			AND INS.INSLCODE = 'SLF'
      		)
      )
      ORDER BY ISSUE, BLDGID
    "
  )
  #********: mydata <- sqlQuery(mySSdb, myquery_exceptions, stringsAsFactors = FALSE)
  mydata <- dbGetQuery(mySfDB, myquery_exceptions)
  
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("ISSUE",
                                                "CHG DATE",
                                                "BLDGID",
                                                "INS CODE",
                                                "INS TYPE",
                                                "LIMIT",
                                                "INS START DATE",
                                                "INS END DATE",
                                                "INS LAST MODIFIED"
                                                )
                                   ,
                                   width = c(47,
                                             11,
                                             8.5,
                                             7,
                                             18,
                                             9,
                                             12,
                                             12,
                                             12
                                             )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    bodytext <- paste0("The <b>", this_ReportName, "</b> data is attached. ",
                       "The attached file contains ", this_ReportName,
                       " from yesterday's MRI data.",
                       ".<br/><br/>",
                       #"The results are sorted by BLDG ID, SUITE ID and then by STATUS date.<br/><br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}




