library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR)#replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyr)
library(keyring)
library(janitor)
library(DBI)
library(odbc)
library(cellranger)


# written by <PERSON> August 2022

### NOTE there is at least one Google Apps Script that runs after this routine ("ExpandFilterRng_SNC")
### to expand the filters to cover all rows of data and also delete lines no longer needed

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241014

### 20241014 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present

### 20230713 change
### removed Execution date column from removal section (causing issues as list column)

### 20221014 change
### added Lease Agent data

### 20221010 change
### publish to existing PORTFOLIO workbook instead of standalone workbook and
### to add Asset Manager as an MRI column
### and to include removals in the email notification

### 20220929 change
### added ability to update SNC column ("Code") to identify when lease has been changed to 
### commenced

### 20220921 change
### new file, based on then current version of LEGACY_Property_Submittal_Responses.R script

#\\*************\public\steveo\R Stuff\ReportFiles\LEGACY_Construction_SNC_Report

# Parameters

okaytocontinue <- TRUE

myReportName <- "Legacy Construction SNC Report"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
scriptfolder <- "LEGACY_Construction_SNC_Report"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
rptfolder <- "reports"
gSht_auth_email <- "<EMAIL>"
#next line is original standalone workbook, no longer used after 20221010
#gDrv_mainURL <-'https://docs.google.com/spreadsheets/d/1iHLr-c5m_wdACW6lofIoMZ4DMYX8yUGt-DibKTs85_I/'
gDrv_mainURL <-'https://docs.google.com/spreadsheets/d/1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8/edit#gid=784276350'
#below is testing version 2024-10-08
###gDrv_mainURL <-'https://docs.google.com/spreadsheets/d/16QUYf48WFORTzDW3ihR8C-UP0-JwAwy39QcKjBngMIY/edit?gid=784276350#gid=784276350'
gSht_expected_FNs <- c("Construction SNC Leases")
gSht_report_main_SN <- "SNC"
gSht_parameters_SN <- "Parameters"
gSht_update_rngname <- "Last_MRI_Update"


gSht_email_rngname <- "Email_Sheets"
gSht_colname_corpnames <- "Relevant Corporate Name(s)"
gSht_colname_maplink <- "Google Map Link"
gSht_colname_emailbody <- c("to_sheet","Leasing Agent Name","City","Timestamp","Property Address","State","Zip Code")
emailbody_oldnames <- c("to_sheet","Leasing Agent Name","Timestamp","Zip Code")
emailbody_newnames <- c("Corporate Name","Leasing Agent","Date Submitted","Zip")



# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<h3>Legacy Commercial Property</span></h3>",
                   "<b><span style='font-weight:bold'>A Division of Highland Ventures, Ltd.</span></b><br/>",
                   "209 Powell Pl.<br/>",
                   "Brentwood, TN 37027<br/><br/>",
                   "For issues with this report contact Steve Olson: Ph 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")

if(Sys.getenv("COMPUTERNAME") != "DESKTOP-TABLEAU"){
  testing_pc <- TRUE  #TESTING, changes some paths to network share instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
}

myReportPath <- file.path(logpath, rptfolder)


# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "18-AUG-22" #testing line only
query.days <- 14
query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - (query.days - 3), "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) + 2, "%d-%b-%y")# Oracle date format for end date of reporting

rpt.end <- format(as.Date(query.date, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
email.end <- format(as.Date(query.date, "%d-%b-%y"), "%m/%d/%Y")

report.time.txt <- format(Sys.time(), "%H%M%S %Z")
rptFN_noEXT <- paste0(myReportName)
rptFN <- paste0(rptFN_noEXT, ".xlsx")
rptFN_remove <- paste0(rptFN_noEXT, " - removals", ".xlsx")

last_request <- Sys.time() # initialize variable for timer that limits Google sheet requests


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="GMT")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


###Get email signature###
get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
  warn_sig <- norm_sig
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- NA
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


invalid_cols <- function(testdf, col_list){
  my_results <- c("Data missing or no column names/indices supplied")
  if(is.data.frame(testdf) & length(col_list) > 0){
    my_results <- c()
    max_col_num <- ncol(testdf)
    col_names <- names(testdf)
    #col_list might include vectors so extra loop for each list item
    for(i in 1:length(col_list)){
      curr_issue <- NA
      for(x in 1:length(col_list[[i]])){
        if(!is.na(col_list[[i]][x])){
          if(is.numeric(col_list[[i]][x])){
            #test position
            curr_issue <- if(col_list[[i]][x] > 0 & col_list[[i]][x] <= max_col_num){NA}else{as.character(col_list[[i]][x])}
          }else{
            #test name
            curr_issue <- if(col_list[[i]][x] %in% col_names){NA}else{paste0('"', col_list[[i]][x], '"')}
          }
        }
        if(!is.na(curr_issue)){my_results <- c(my_results, curr_issue)}
      }
    }
    if(length(my_results) > 0){
      my_results <- paste(my_results, collapse = "; ") %>% paste0("Columns missing: ", .)
    }
  }
  
  return(my_results)
}



find_hdr_row <- function(mydf, hdr_colnames){
  output <- 0
  if(nrow(mydf) > 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames) %>% apply(., 1, sum)
    if(max(myhdr_matches) > 0){
      output <- myhdr_matches %>% which.max(.)
    }
  }
  if(nrow(mydf) == 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames)
    if(sum(myhdr_matches) > 0){output <- 1}
  }
  return(output)
}



writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  oldOpt <- options()
  options(xlsx.date.format="MM/dd/yyyy")
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(format(Sys.time(), "%H:%M:%S"), "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      openxlsx::saveWorkbook(myWB, file = myNewFN, overwrite = writeover)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext,
             test = testing_emails, testrecipient = test_recip
    )
  }
  options(oldOpt)
}



# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isSheet <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was spreadsheet
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isSheet <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("spreadsheet")
    if(!isSheet){okaytocontinue <- FALSE}
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Google Auth Email: ", gSht_auth_email, "</li>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Sheet URL resolved: ", isSheet, " (", gDrv_mainURL,
                       ")</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Access Issue"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


# check if desired file and response sheet is present
if(okaytocontinue){
  #set initial okaytocontinue flag to false and then only set it to true if file and response sheet(s) present
  okaytocontinue <- FALSE
  #check if sheet is present
  gSht_id <- drv_get_main$id
  gSht_info <- gs4_get(gSht_id)
  if(gSht_report_main_SN %in% c(gSht_info$sheets$name)){
    #sheetname present, okay to continue
    okaytocontinue <- TRUE
  }
  
  if(!okaytocontinue){
    #send warning email that file or response sheet not found
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error trying to find a sheet in the Google file ",
                       " for the ", myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Google Sheets Filename: ", paste(gSht_info$name, collapse = '; '), "</li>",
                       "<li>Sheet name expected for responses: ", gSht_report_main_SN, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Report Worksheet Not Found"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}  



#get master list of SNC leases from MRI db
if(okaytocontinue){
  myquery <- paste0(
    '
      SELECT /* SNOWFLAKE version, LEGACY_Construction_SNC_Report */
      	LEAS.BLDGID AS "Bldg ID"
      ,	LEAS.LEASID AS "Lease ID"
      ,	LEAS.MOCCPID AS "Occp ID"
      ,	BLDG.CITY AS "City"
      ,	BLDG.STATE AS "State"
      ,	LEAS.SUITID AS "Suite ID"
      ,	SUIT.SUITSQFT AS "Suite SQFT"
      ,	SQF.SQFT AS "Suite Details SQFT"
      ,	LEAS.OCCPNAME AS "Occupant Name"
      ,	CAST(LEAS.EXECDATE AS DATE) AS "Execution"
      ,	CAST(LEAS.BEGINDATE AS DATE) AS "Beginning"
      ,	CAST(LEAS.RENTSTRT AS DATE) AS "Rent Start"
      ,	CAST(LEAS.EXPIR AS DATE) AS "Expiration"
      ,	LEAS.GENCODE AS "Code"
      ,	CASE WHEN UPPER(LEAS.CONTINGENT) != \'Y\' OR LEAS.CONTINGENTDT < CURRENT_DATE OR LEAS.CONTINGENTDT IS NULL THEN NULL ELSE \'Y\' END AS "Contingent"
      ,	CAST(LEAS.CONTINGENTDT AS DATE) AS "Contingency Date"
      ,	LEASEAG.LEASEAGENT AS "Lease Agent"
      --,	MNGR.MNGRNAME AS "Manager Name"
      ,	ASSETMGR.NAME AS "Asset Manager"
    ',"
      FROM MRI.LEAS
      JOIN MRI.BLDG ON LEAS.BLDGID = BLDG.BLDGID
      JOIN MRI.SUIT ON LEAS.BLDGID = SUIT.BLDGID AND LEAS.SUITID = SUIT.SUITID
      LEFT JOIN 
      (
      		SELECT *
      		FROM MRI.SSQF
      		WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE)
      								FROM MRI.SSQF I
      								WHERE I.BLDGID = SSQF.BLDGID
      								AND I.SUITID = SSQF.SUITID
      								AND I.EFFDATE <= CURRENT_DATE
      								)
      ) SQF
      ON SUIT.BLDGID = SQF.BLDGID
      	AND SUIT.SUITID = SQF.SUITID
      	AND UPPER(SQF.SQFTTYPE) = 'GLA'
      LEFT JOIN
      (
      	SELECT
      		NOTE.BLDGID
      	,	NOTE.LEASID
      	,	LISTAGG(CONCAT(NOTE.NOTETEXT, ' (', TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yyyy'), ')'), '; ') WITHIN GROUP (ORDER BY NOTE.NOTEDATE DESC) AS LEASEAGENT
      	FROM MRI.NOTE
      	WHERE (NOTE.REF1 = 'LEASEAG' OR NOTE.REF2 = 'LEASEAG')
      	GROUP BY NOTE.BLDGID
      	, NOTE.LEASID
      ) LEASEAG
      ON LEAS.BLDGID = LEASEAG.BLDGID
      	AND LEAS.LEASID = LEASEAG.LEASID
      LEFT JOIN MRI.MNGR
      ON BLDG.MNGRID = MNGR.MNGRID
      LEFT JOIN MRI.ENTITY
      ON BLDG.ENTITYID = ENTITY.ENTITYID
      LEFT JOIN MRI.PROJ
      ON ENTITY.PROJID = PROJ.PROJID
      LEFT JOIN MRI.ASSETMGR
      ON PROJ.ASSETMGR = ASSETMGR.ASSETMGR
      WHERE 
      	UPPER(LEAS.GENCODE) = 'SNC'
      	AND UPPER(LEAS.OCCPSTAT) != 'I'
      ORDER BY LEAS.EXECDATE
    "
  )
  #20241014: mydata_full <- dbGetQuery(mySSdb, myquery)
  mydata_full <- dbGetQuery(mySfDB, myquery)
  mydata_full_status <- check_mydf_rows(mydf = mydata_full, 1, myReportName)
  if(mydata_full_status[[1]] != TRUE){
    #no results or query issue, send warning
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing the database for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Query info:</b><ul>",
                       "<li>Query status reported: ", mydata_full_status[[3]], "</li>",
                       "<li>Rows returned: ", mydata_full_status[[2]], "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Database Query Issue"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    okaytocontinue <- FALSE
  }else{
    #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
    mydata_full[] <- lapply(mydata_full[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
  }
  
}



#get existing list of SNC leases from Google Sheet
if(okaytocontinue){
  #find A1 range of last update named range and read last update time (disabled with 20221010 update)
  #gSht_update_rngA1 <- gSht_info$named_ranges$A1_range[[which(gSht_info$named_ranges$name == gSht_update_rngname)]]
  #gSht_update <- range_read(ss = gSht_id, range = gSht_update_rngA1)
  #gSht_update_readtime <- force_tz(Sys.time(), tz = "UTC")
  #get existing data
  mydata_existing <- range_read(ss = gSht_id, sheet = gSht_report_main_SN)
  mydata_existing_status <- check_mydf_rows(mydf = mydata_existing, 1, myReportName)
  if(mydata_existing_status[[1]] != TRUE){
    #sheet is empty, load 'mydata_full' results AS-IS
    rs <- range_write(ss = gSht_id, data = mydata_full, sheet = gSht_report_main_SN, reformat = false)
    #populate update date
    #rs <- range_write(ss = gSht_id, data = gSht_update, range = gSht_update_rngA1)
    #send warning email that entire sheet was re-populated
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "there wasn't any previous data in the '", gSht_report_main_SN, "' ",
                       "sheet for the ", myReportName, " routine! ",
                       "<p>The routine wrote ",mydata_full_status[[2]], " new rows of data.</p> ",
                       "<p>See sheet: <a href=\"", gDrv_mainURL, "\">", gSht_info$name, "</a> </p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Database Query Issue"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    okaytocontinue <- FALSE
  }
  
}




#define some columns (and widths) for any email table and Excel files that may be created
myemailfiles <- c()
myemailbodycols <- c("Bldg ID","Lease ID","City","State","Occupant Name", "Execution")
myXLSXColWidths <- data.frame (colname  = c("Bldg ID",
                                            "Lease ID",
                                            "Occp ID",
                                            "City",
                                            "State",
                                            "Suite ID",
                                            "Suite SQFT",
                                            "Suite Details SQFT",
                                            "Occupant Name",
                                            "Execution",
                                            "Beginning",
                                            "Rent Start",
                                            "Expiration",
                                            "Code",
                                            "Contingent",
                                            "Contingency Date",
                                            "Lease Agent",
                                            "Asset Manager",
                                            "Contractor",
                                            "Contractor Info",
                                            "Notes",
                                            "Date Last Actioned"
                                            #"",
)
,
width = c(8.5,
          9.5,
          9.5,
          20,
          8.5,
          9.5,
          9.5,
          9.5,
          45,
          10,
          10,
          10,
          10,
          8.5,
          10,
          12,
          20,
          20,
          9.75,
          9.75,
          9,
          11
)
,
stringsAsFactors = FALSE
) #myXLSXColWidths




#compare current SNC list to existing and append new rows
if(okaytocontinue){
  #compare columns of query (full) against existing worksheet (existing)
  #and copy full to match existing layout and columns
  mydata_full_colnames <- names(mydata_full)
  mydata_existing_colnames <- names(mydata_existing)
  test_compare <- mydata_full_colnames %in% mydata_existing_colnames
  mydata_full_colnames_import <- mydata_full_colnames[test_compare]
  Missing <- setdiff(mydata_existing_colnames, mydata_full_colnames_import)
  mydata_reshaped <- mydata_full[,mydata_full_colnames_import]
  mydata_reshaped[Missing] <- NA
  mydata_reshaped <- mydata_reshaped[mydata_existing_colnames]
  
  #filter import to only lease IDs not already present
  Missing_ids <- setdiff(mydata_reshaped$`Lease ID`, mydata_existing$`Lease ID`)
  
  if(length(Missing_ids) > 0){
    mydata_import <- mydata_reshaped %>% filter(., `Lease ID` %in% Missing_ids)
    mydata_import[] <- lapply(mydata_import[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    #append to existing sheet
    sheet_append(ss = gSht_id, data = mydata_import, sheet = gSht_report_main_SN)
    #write update time in Google sheet parameters to current time
    #gSht_update$Last_MRI_Update <- gSht_update_readtime
    #rs <- range_write(ss = gSht_id, data = gSht_update, range = gSht_update_rngA1, reformat = FALSE)
    #create Excel version of additions to attach to email
    
    
    myFN <- rptFN
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata_import, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    mydata_emailbody <- mydata_import[,myemailbodycols]
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    #email body for new rows added
    if(nrow(mydata_emailbody)<31){
      bodytable_additions <- paste0("<p><b>See attached Excel file for ",
                          "full details of these additions.</b> ",
                          "</p>",
                          "<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)
                          ),
                          #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                          html.table.attributes = "border=2 cellspacing=1",
                          type = "html",
                          caption.placement = "top",
                          include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable_additions <- paste0("<p><strong><em>There are ", nrow(mydata_emailbody), 
                          " new additions, see attached file for all.",
                          "</em></strong></p>"
      )
    }
    
    #email send moved later as of 20221010 (to catch removal rows as well)
    #bodytext <- paste0("<h2>REPORT: ", myReportName, "</h2>",
    #                   "<p>",
    #                   "New rows have been added to the ",
    #                   "<a href=\"", gDrv_mainURL, "\">", gSht_info$name, "</a>",
    #                   " Google sheet. <em>If any filters are applied to the sheet ",
    #                   "they may cause additions to be hidden from view.</em> ",
    #                   "</p>",
    #                   bodytable_additions,
    #                   "<br/>",
    #                   norm_sig
    #)
    #rs <- mailsend(recipient = norm_recip,
    #               subject = paste0(myReportName),
    #               body = bodytext,
    #               if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
    #               test = testing_emails, testrecipient = test_recip
    #)
    #myemailfiles <- NA
  }

}



#update existing SNC 'Code' column where value has changed
if(okaytocontinue){
  Remove_ids <- setdiff(mydata_existing$`Lease ID`, mydata_full$`Lease ID`)
  if(length(Remove_ids) > 0){
    mydata_code_col <- match("Code",mydata_existing_colnames)
    Remove_rows <- match(Remove_ids, mydata_existing$`Lease ID`) + 1
    mydata_remove <- data.frame(col = c("REMOVE"))
    
    #update cells in gsht 'Code' column to flag for deletion
    for(i in 1:length(Remove_ids)){
      my_write_rng <- R1C1_to_A1(paste0("R", Remove_rows[i], "C", mydata_code_col), strict = FALSE)
      range_write(ss = gSht_id, 
                  data = mydata_remove, 
                  sheet = gSht_report_main_SN,
                  range = my_write_rng,
                  col_names = FALSE,
                  reformat = FALSE
                  )
    }
    
    
    mydata_removals <- mydata_existing %>% filter(., `Lease ID` %in% Remove_ids)
    mydata_removals[] <- lapply(mydata_removals[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    
    #write removals Excel file
    myFN <- rptFN_remove
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata_removals, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    if(exists("myemailfiles")){
      myemailfiles <- c(myemailfiles, file.path(myReportPath, myFN))
    }else{
      myemailfiles <- file.path(myReportPath, myFN)
    }
    
    removalcols <- myemailbodycols[which(myemailbodycols %notin% "Execution")]
    mydata_emailbody <- mydata_removals[,removalcols]
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    
    #email body for rows removed
    if(nrow(mydata_emailbody)<31){
      bodytable_removals <- paste0("<p><b>See attached Excel removals ",
                                    "file for full details of these deletions.</b> ",
                                    "</p>",
                                    "<p>",
                                    print(xtable(mydata_emailbody, 
                                                 #caption = paste0(this_ReportName, " (", query.date, ")"),
                                                 digits = rep(0,ncol(mydata_emailbody)+1)
                                    ),
                                    #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                                    html.table.attributes = "border=2 cellspacing=1",
                                    type = "html",
                                    caption.placement = "top",
                                    include.rownames=FALSE
                                    ),
                                    "</p>"
      )
    }else{
      bodytable_removals <- paste0("<p><strong><em>There are ", nrow(mydata_emailbody), 
                                    " rows being REMOVED, see attached file for all.",
                                    "</em></strong></p>"
      )
    }
    
    #rptFN_remove
  }
  
  
  
}


if(length(myemailfiles) > 0){
  
  #create initial section of email body
  bodytext <- paste0("<h2>REPORT: ", myReportName, "</h2>")
  #if additions, add to body
  if(exists("bodytable_additions")){
    bodytext <- paste0(bodytext, 
                       "<p>",
                       "<h3>*ADDITIONS*</h3>",
                       "New rows have been <B>ADDED</B> to the ",
                       "<a href=\"", gDrv_mainURL, "\">", gSht_report_main_SN, "</a>",
                       " Google sheet based on yesterday's MRI data. ",
                       "<em>If any filters are applied to the sheet ",
                       "they may cause additions to be hidden from view.</em> ",
                       "</p>",
                       bodytable_additions,
                       "<br/>"
    )
  }
  #if removals, add to body
  if(exists("bodytable_removals")){
    bodytext <- paste0(bodytext, 
                       "<p>",
                       "<h3>*REMOVALS*</h3>",
                       "Rows have been <B>REMOVED</B> from the ",
                       "<a href=\"", gDrv_mainURL, "\">", gSht_report_main_SN, "</a>",
                       " Google sheet. <em>Review as it is possible there may continuing ",
                       "construction even though these are no longer flagged as SNC!</em>",
                       "</p>",
                       bodytable_removals,
                       "<br/>"
    )
  }
  #add signature before sending
  bodytext <- paste0(bodytext, 
                     "<p><em>This notification is ONLY sent to members of the ",
                     norm_recip, " email group.</em></p>",
                     norm_sig)
  rs <- mailsend(recipient = norm_recip,
                 subject = paste0(myReportName),
                 body = bodytext,
                 if(length(myemailfiles)==0){attachment = NULL}else{attachment = myemailfiles},
                 inline = TRUE,
                 test = testing_emails, testrecipient = test_recip
  )
}




