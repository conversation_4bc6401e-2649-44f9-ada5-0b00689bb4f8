#library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #********: replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
#library(RODBC) #********: replaced by DBI and odbc
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version ********

### ******** change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### updated norm_sig to standard requested by <PERSON> March 2024, sender is legacy email acct
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present

### 20220606 change:
### updated mailsend to use keyring

### 20220427 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

scriptfolder <- "LEGACY_MRI_Exceptions-Owner-Landlord"
myReportName <- "Monthly MRI Ownership Changes"
msg_text <- paste0("Routine Starting: ", myReportName)
base::message(msg_text)
rptFN <- paste0("MRI_Ownership_Changes_Monthly", ".xlsx")
myReportCriteria <- paste0("<p><b>Criteria for inclusion in the report:</b> (note: rented buildings not included)<ul>",
                           "<li>Ownership started or ended in the previous month</li>",
                           "<li>'Acquired' date since beginning of last month</li>",
                           "<li>'Disposed' date since beginning of last month (sold)</li>",
                           "</ul></p>"
                           )

#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>")
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- logpath

### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load...log
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext
    )
  }
}





### Find Exceptions and email results
if(okaytocontinue){
  
  #********: myReportPath <- rptpath
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery_exceptions <- paste0(
    "
      SELECT /* Snowflake version, Changes in ownership table GOWN */
      	BLDG.BLDGID AS BLDGID
      ,	array_to_string(
      		array_construct_compact(
      		CASE WHEN TO_DATE(CONCAT(GOWN.BEGPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
      			THEN CONCAT(TRIM(GOWN.OWNERID),' Ownership Begins ',GOWN.BEGPD, (CASE WHEN POWN.PREV_OWNER <> 'na' THEN CONCAT(' (Previous Owner: ', POWN.PREV_OWNER, ')') END) ) END,
      		--CASE WHEN TRY_CONVERT(DATE, CONCAT(GOWN.ENDPD,'01'), 112) >= CONVERT(DATE, dateadd(MONTH, datediff(MONTH, 0, getdate()) - 1, 0))
      		CASE WHEN TO_DATE(CONCAT(GOWN.ENDPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
      			THEN CONCAT(TRIM(GOWN.OWNERID),' Ownership Ends ', GOWN.ENDPD, (CASE WHEN NOWN.NEXT_OWNER <> 'na' THEN CONCAT(' (Next Owner: ', NOWN.NEXT_OWNER, ')') END) ) END,
      		CASE WHEN TO_DATE(ENTITY.ACQUIRED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
      			THEN 'Newly Acquired' END,
      		CASE WHEN TO_DATE(ENTITY.DISPOSED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE))
      			THEN CONCAT('Disposed ', TO_CHAR(ENTITY.DISPOSED)) END
      		)
      	, '; ') AS CHANGE
      ,	RTRIM(GOWN.OWNERID) AS OWNER_ID
      ,	CASE WHEN BLDG.INACTIVE <> 'Y' or BLDG.INACTIVE IS NULL THEN 'Active' ELSE 'Inactive' END as BLDG_ACTIVE
      ,	CASE WHEN GOWN.BEGPD IS NULL THEN 'NA' ELSE GOWN.BEGPD END AS BEGINNING_MONTH
      ,	CASE WHEN GOWN.ENDPD IS NULL THEN 'NA' ELSE GOWN.ENDPD END AS ENDING_MONTH
      ,	GOWN.PERCENT
      ,	GOWN.PRIMARYOWN AS PRIMARY_OWNER_CHECKED
      ,	TO_DATE(ENTITY.ACQUIRED) AS ACQUIRED_DATE
      ,	TO_DATE(ENTITY.DISPOSED) AS DISPOSED_DATE
      ,	TO_DATE(GOWN.LASTDATE) AS LAST_UPDATE_ENTERED
      ,	GOWN.USERID AS UPDATED_BY
      FROM MRI.GOWN
      LEFT JOIN MRI.BLDG
      ON GOWN.ENTITYID = BLDG.ENTITYID
      LEFT JOIN MRI.ENTITY
      ON GOWN.ENTITYID = ENTITY.ENTITYID
      LEFT JOIN
      (
      	SELECT
      	O.ENTITYID
      	,	trim(O.OWNERID) AS OWNERID
      	,	O.BEGPD
      	,	O.ENDPD
      	,	LAG(TRIM(O.OWNERID), 1, 'na') OVER (PARTITION BY O.ENTITYID ORDER BY O.BEGPD, O.ENDPD) AS PREV_OWNER
      	FROM MRI.GOWN O
      ) POWN
      ON GOWN.ENTITYID = POWN.ENTITYID
      	AND ((GOWN.BEGPD = POWN.BEGPD) or (GOWN.BEGPD IS NULL AND POWN.BEGPD IS NULL))
      LEFT JOIN
      (
      	SELECT
      	O.ENTITYID
      	,	trim(O.OWNERID) as OWNERID
      	,	O.BEGPD
      	,	O.ENDPD
      	,	LEAD(TRIM(O.OWNERID), 1, 'na') OVER (PARTITION BY O.ENTITYID ORDER BY O.BEGPD, O.ENDPD) AS NEXT_OWNER
      	FROM MRI.GOWN O
      ) NOWN
      ON GOWN.ENTITYID = NOWN.ENTITYID
      	AND ((GOWN.ENDPD = NOWN.ENDPD) or (GOWN.ENDPD IS NULL AND NOWN.ENDPD IS NULL))
      WHERE 
      	(
      		TO_DATE(CONCAT(GOWN.BEGPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* BEGPD since start of previous month */
      		OR TO_DATE(CONCAT(GOWN.ENDPD,'01'), 'YYYYMMDD') >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* ENDPD since start of previous month */
      		OR TO_DATE(ENTITY.ACQUIRED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* Start of previous month */
      		OR TO_DATE(ENTITY.DISPOSED) >= DATEADD('MONTH',-1,DATE_TRUNC('MONTH',CURRENT_DATE)) /* Start of previous month */
      	)
      	AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
      order by BLDG.BLDGID, LAST_UPDATE_ENTERED DESC, BEGINNING_MONTH ASC
    "
  )
  #********: mydata <- sqlQuery(mySSdb, myquery_exceptions, stringsAsFactors = FALSE)
  mydata <- dbGetQuery(mySfDB, myquery_exceptions)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
    mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
    #replace underscores in column names with space
    names(mydata) <- gsub("_", " ", names(mydata))
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("BLDGID",
                                                "CHANGE",
                                                "OWNER ID",
                                                "BLDG ACTIVE",
                                                "BEGINNING MONTH",
                                                "ENDING MONTH",
                                                "PERCENT",
                                                "PRIMARY OWNER CHECKED",
                                                "ACQUIRED DATE",
                                                "DISPOSED DATE",
                                                "LAST UPDATE",
                                                "UPDATED BY"
                                                #"",
                                                )
                                   ,
                                   width = c(8,
                                             if(max(nchar(mydata[,2])) >= 40){min(75, max(nchar(mydata[,2])))}else{35},
                                             10.5,
                                             10.5,
                                             11,
                                             10,
                                             10,
                                             10,
                                             11,
                                             11,
                                             11,
                                             9.5
                                             )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    myemailbodycols <- c(1,2,9,10)
    mydata_emailbody <- mydata[,myemailbodycols]
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata_emailbody)<20){
      bodytable <- paste0("<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)
                                ),
                                #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                                html.table.attributes = "border=2 cellspacing=1",
                                type = "html",
                                caption.placement = "top",
                                include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</p>"
      )
    }
    bodytext <- paste0("<p><b>REPORT: ", this_ReportName, "</b>",
                       "</p>",
                       myReportCriteria,
                       "<p>The info below contains ownership changes in the last month as noted in MRI. ",
                       "MRI ownership periods are noted as YYYYMM. <b>See attached Excel file for more details.</b> ",
                       "</p>",
                       bodytable,
                       "<br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   inline = TRUE,
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}


#********: odbcCloseAll()
DBI::dbDisconnect(mySfDB)




