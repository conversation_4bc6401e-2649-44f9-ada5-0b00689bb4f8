library(RODBC)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(stringr)
library(keyring)
library(openxlsx)
library(readr)


testing_emails <- FALSE  #NORMAL, next line over-rides this one, should be commented out in production
#testing_emails <- TRUE

# Version 20240926

### 20240926 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced Signature logo from local file to published image URL (to avoid inline image attachment)

### 20220606 change:
### updated mailsend to use keyring

### 20220428 change:
### enabled RM send option. Updated store list Oracle query to use hr_locations_all table instead of store # range
### Updated to use keyring package

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)

#macFn <- "Marcos Plan creator.xlsm"
#macpath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars", macFn)
#googdrive_calsave <- file.path("G:", "My Drive", "Hoogland Restaurant Group", "Hoogland Foods - Marketing", "01 - Marketing Plans")
macdrive_calsave <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars")
CalTmpFn <- "MP_StoreCal_Tmp.csv"
CalLogPath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars")
CalRptFN <- "MP_StoreCal_Log.csv"
#origpath <- getwd()
OthYear_NextorLast <- "None"

myReportName <- "Marco's Marketing Plans"

logname <- "MyStoreCals-Email-Log.csv"
logname_create <- "MyStoreCals-Create-Log.csv"
#logpath <- getwd()
logpath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars")
query.date <- format(Sys.Date(), "%d-%b-%y")
report.date <- format(as.Date(query.date, "%d-%b-%y"), "%Y%m%d")  # YYYYMMDD format of date for saves of report files
report.time <- format(Sys.time(), "%H%M%S")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/>Highland Ventures Ltd.<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_st_from <- paste0("<b>Direct any questions about plans to your RM/DM. </b><br/><br/>",
                       " Steve Olson<br/> Purchasing Analyst<br/> Highland Ventures Ltd.<br/> <EMAIL><br/> (847)904-9043<br/>")

norm_RM_DM_from <- paste0("<b>Direct any questions about store plans to <a href=\"mailto:<EMAIL>\">Deanna Flynn</a>. </b><br/><br/>",
                          " Steve Olson<br/> Purchasing Analyst<br/> Highland Ventures Ltd.<br/> <EMAIL><br/> (847)904-9043<br/>")
#append signature logo
#(HVLTD Corp with Brands)
sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
if(exists("norm_st_from")){norm_st_from <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_sig")){norm_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_RM_DM_from")){norm_RM_DM_from <- paste0(norm_RM_DM_from, "<br/>", sig_image_src)}

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}




# define some functions #

writelog <- function(LogTable){
  fn <- file.path(CalLogPath, logname)
  write.csv(LogTable, file = fn, row.names=FALSE)
}


st.emailaddy <- function(STORE_NUMBER){
  # Return the store email address based on current naming convention, default to warning email address if not found
  #print(paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"))
  addy <- case_when(
    STORE_NUMBER %in% seq(2, 888) ~ paste0("fv", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    STORE_NUMBER %in% seq(2100, 2999) ~ paste0("vt", str_pad(STORE_NUMBER, 4, pad = 0), "@familyvetgroup.com"),
    STORE_NUMBER %in% seq(3500, 3999) ~ paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    #(STORE_NUMBER >= 3500 & STORE_NUMBER <= 3999) ~ paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    STORE_NUMBER %in% seq(6500, 6999) ~ paste0("sf", str_pad(STORE_NUMBER, 4, pad = 0), "@stayfit24.com")
  )
  if(is.na(addy)){addy <- warn_recip}
  return(addy)
}


file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}


### check if calendar create routine has finished ###
#logname_create
CalCreateFinished <- FALSE
if(file.exists(file.path(logpath, logname_create)) ){
  MyCreateErrorLog <- read.csv(file = file.path(logpath, logname_create), sep=",", stringsAsFactors = FALSE)
  #check if status is complete
  #query.enddate %>% as.Date("%d-%b-%y")
  if( as.Date(MyCreateErrorLog[1,"QUERY_DATE"],"%d-%b-%y") - as.Date(query.date, "%d-%b-%y") <= 1 &
      MyCreateErrorLog[1,"PROGRESS"] == 'COMPLETE'){
    # log from create routine is within acceptable date range and has completed
    CalCreateFinished <- TRUE
  }
}


### check if log present/up-to-date ###
NewErrorLog <- FALSE
if(CalCreateFinished & file.exists(file.path(logpath, logname)) ) {
  MyErrorLog <- read.csv(file = file.path(logpath, logname), sep=",", stringsAsFactors = FALSE)
  #check if log is from prior run, if so replace values with default starting values
  if( MyErrorLog[1,"QUERY_DATE"] != query.date ){
    #log is from previous date, replace with new default values
    NewErrorLog <- TRUE
  }
} else {
  # log not found, create new log values
  NewErrorLog <- TRUE
}

if( NewErrorLog & CalCreateFinished ) {
  MyErrorLog <- data.frame(QUERY_DATE = query.date, 
                           #CAL_STATUS = 'NO LOG FILE',
                           CAL_STATUS = MyCreateErrorLog[1,"CAL_STATUS"],
                           ST_EMAIL_STATUS = 'NO LOG FILE',
                           DM_EMAIL_STATUS = 'NO LOG FILE',
                           RM_EMAIL_STATUS = 'NO LOG FILE',
                           PROGRESS = 'NO LOG FILE',
                           stringsAsFactors = FALSE)
  writelog(MyErrorLog)
  OthYear_NextorLast <- MyCreateErrorLog[1,"OTHYEAR_NEXTORLAST"]
}else{
  if( NewErrorLog ){
    MyErrorLog <- data.frame(QUERY_DATE = query.date, 
                             CAL_STATUS = 'NOT CREATED',
                             ST_EMAIL_STATUS = 'NO LOG FILE',
                             DM_EMAIL_STATUS = 'NO LOG FILE',
                             RM_EMAIL_STATUS = 'NO LOG FILE',
                             PROGRESS = 'ABORT CAL STATUS',
                             stringsAsFactors = FALSE)
    writelog(MyErrorLog)
  }
}

if(CalCreateFinished != TRUE){
  # send error email that calendars have NOT been created as expected yet
  bodytext <- paste0("This is an automated email to inform you that it appears the <b>Marco's Store Plan Calendar EMAILING</b> routine ",
                     "has encountered the following issue:<br/>",
                     "<b>CALENDAR CREATION ROUTINE HAS NOT COMPLETED!</b>",
                     "<br/><br/>",
                     "Things to check... ",
                     "<ul>",
                     "<li>Does the MARCOS_YoY_SALES.csv file contains sales from the most recent week? ",
                     "If it doesn't, ALL stores are considered 'closed' which would cause an error in calendar creation.</li>",
                     "<li>If the Google Drive desktop application up and running? That would also cause issue with calendar creation.</li>",
                     "<li>If it's around New Years, did the new mp_calendar.csv and mp_weekly_calendar.csv get updated in /public/steveo/Monday_Reports? ",
                     "If not, check the HV_csv_Calendars.R routine.</li>",
                     "<li>Is the plan named as expected and in the expected path? </li>",
                     "</ul>",
                     "<br/>",
                     warn_sig
  )
  #send mail
  mailsend(recipient = warn_recip,
           subject = "Marco's Store Plan Calendar EMAILING Issue: CREATION NOT COMPLETE",
           body = bodytext,
           test = testing_emails, 
           testrecipient = test_recip
  )
}

try(if(CalCreateFinished == FALSE) stop("Calendars not created yet"))

#check if previous mailing of STORES failed part way through and set parameters to pick-up from where it left off
store.mail_from <- 1
store.mail_send <- TRUE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"ST_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed store that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    store.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"ST_EMAIL_STATUS"] == 'COMPLETE' ) {
    store.mail_send <- FALSE
  }
}

#check if previous mailing of DMS failed part way through and set parameters to pick-up from where it left off
dm.mail_from <- 1
dm.mail_send <- TRUE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"DM_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed DM that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    dm.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"DM_EMAIL_STATUS"] == 'COMPLETE' ) {
    dm.mail_send <- FALSE
  }
}

#check if previous mailing of RMS failed part way through and set parameters to pick-up from where it left off
rm.mail_from <- 1
rm.mail_send <- TRUE
#### disable RM sends with next line, RMs will access calendars via Google Drive#####
# if RM sends are desired, comment out the next line
#rm.mail_send <- FALSE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"RM_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed RM that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    rm.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"RM_EMAIL_STATUS"] == 'COMPLETE' ) {
    rm.mail_send <- FALSE
  }
}


#determine 



#read in .csv file with calendars created that need to be emailed
if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] == "READY" ){
  MyErrorLog[1,"PROGRESS"] <- "LOAD CREATE REPORT"
  writelog(MyErrorLog)
  myFN <- file.path(CalLogPath, CalRptFN)
  #Calendars previously generated, read in
  #read log
  caldata <- read.csv(file = file.path(CalLogPath, CalRptFN), sep=",", stringsAsFactors = FALSE)
  colnames(caldata)[1] <- "St_Num"
}


# get RMs and DMs
if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] == "READY" ){
  MyErrorLog[1,"PROGRESS"] <- "QUERY MGMT INFO"
  writelog(MyErrorLog)
  #get RMs and DMs
  mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve") )
  myquery <- paste0(
    "select
    rdm.STORE,
    nvl(rdm.mgr_fname, 'No') as MANAGER_FNAME,
    nvl(rdm.mgr_lname, 'Mgr') as MANAGER_LNAME,
    rdm.dm_fname as DM_FNAME,
    rdm.dm_lname as DM_LNAME,
    rdm.dm_email AS DM_EMAIL,
    rdm.rm_fname as RM_FNAME,
    rdm.rm_lname as RM_LNAME,
    rdm.rm_email AS RM_EMAIL,
    ab_store.o_date as OPEN_DATE,
    (case --when ab_store.st BETWEEN 2 and 888
      when UPPER(hrloc.LOC_INFORMATION15) LIKE 'FVMC%'
        then 'fv0'||lpad(ab_store.st,3,0)||'@fvmc.com'
      --when ab_store.st BETWEEN 2100 and 2500
      when UPPER(hrloc.LOC_INFORMATION15) LIKE 'VET%'
        then 'vt'||lpad(ab_store.st,4,0)||'@familyvetgroup.com'
      --when ab_store.st BETWEEN 3500 and 3999
      when UPPER(hrloc.LOC_INFORMATION15) LIKE 'HF%'
        then 'mp'||lpad(ab_store.st,4,0)||'@fvmc.com'
      --when ab_store.st BETWEEN 6500 and 6999
      when UPPER(hrloc.LOC_INFORMATION15) LIKE 'STFIT%'
        then 'sf'||lpad(ab_store.st,4,0)||'@stayfit24.com'
      end
    ) as STORE_EMAIL
    from steve.so_rm_dm_mgr rdm
    left join ab_store
    on rdm.store = ab_store.st
    left join HR.hr_locations_all hrloc
    on lpad(ab_store.st,4,0) = hrloc.location_code
    where ab_store.st >= 2
      and ab_store.st < 9000
    order by rdm.store
    "
  )
  stlist <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  
}


# create final data lists
if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] == "READY" ){
  MyErrorLog[1,"PROGRESS"] <- "PREP FINAL LISTS"
  writelog(MyErrorLog)
  
  finaldata <- caldata
  finaldata$MGR_FNAME <- with(stlist, MANAGER_FNAME[match(finaldata$St_Num, STORE)])
  finaldata$MGR_LNAME <- with(stlist, MANAGER_LNAME[match(finaldata$St_Num, STORE)])
  finaldata$DM_FNAME <- with(stlist, DM_FNAME[match(finaldata$St_Num, STORE)])
  finaldata$DM_LNAME <- with(stlist, DM_LNAME[match(finaldata$St_Num, STORE)])
  finaldata$DM_EMAIL <- with(stlist, DM_EMAIL[match(finaldata$St_Num, STORE)])
  finaldata$RM_FNAME <- with(stlist, RM_FNAME[match(finaldata$St_Num, STORE)])
  finaldata$RM_LNAME <- with(stlist, RM_LNAME[match(finaldata$St_Num, STORE)])
  finaldata$RM_EMAIL <- with(stlist, RM_EMAIL[match(finaldata$St_Num, STORE)])
  
  #change windows backslash to forward slash (non-escape character)
  #finaldata$File_Path <- as.character(with(finaldata, file.path(File_Path) ))
  finaldata$File_Path <- str_replace_all(finaldata$File_Path, "\\\\", "/")
  
  #create de-duped list of stores in finaldata
  my.data <- finaldata[,c('St_Num','St_Email','File_Path')]
  #t1.stores <- my.data[complete.cases(my.data[,c('T1_ST','LAST4DAYS')]),c('T1_ST','T1_KDOLLARS')]
  final.stores <- data.frame(my.data[complete.cases(my.data[,c('St_Num','St_Email','File_Path')]),'St_Num'], stringsAsFactors = FALSE)
  final.stores <- unique(final.stores)
  colnames(final.stores) <- c("STORE")
  
  #create de-duped list of DM Emails in finaldata
  my.data <- finaldata[,c('DM_EMAIL','File_Path')]
  final.DMs <- data.frame(my.data[complete.cases(my.data[,c('DM_EMAIL','File_Path')]),'DM_EMAIL'], stringsAsFactors = FALSE)
  final.DMs <- unique(final.DMs)
  colnames(final.DMs) <- c("DM")
  
  #create de-duped list of RM Emails in finaldata
  my.data <- finaldata[,c('RM_EMAIL','File_Path')]
  final.RMs <- data.frame(my.data[complete.cases(my.data[,c('RM_EMAIL','File_Path')]),'RM_EMAIL'], stringsAsFactors = FALSE)
  final.RMs <- unique(final.RMs)
  colnames(final.RMs) <- c("RM")
  
  MyErrorLog[1,"ST_EMAIL_STATUS"] <- "EMAILING PREPPED"
  MyErrorLog[1,"PROGRESS"] <- "ST_EMAIL_STATUS"
  writelog(MyErrorLog)
  
}


#proceed with emailing results
if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] == "READY" ){
  if( store.mail_send ) {
    store.mail_to <- nrow(final.stores)
    #EMAILING STARTED
    #st_email.status <- data.frame(ST_EMAIL_STATUS = 'EMAILING STARTED', stringsAsFactors = FALSE)
    #progress.status <- data.frame(PROGRESS = 'COMPLETE', stringsAsFactors = FALSE)
    
    for(i in store.mail_from:store.mail_to ){  # normal, disable next TEST line
      #for(i in 1:3 ){   # test, disable above NORMAL line
      
      currStNum <- as.integer(final.stores[i, "STORE"])
      store_email <- st.emailaddy(currStNum)
      # create table of calendars for this stores email
      #st_cals.table <- select(dplyr::filter(finaldata, St_Num == currStNum, File_Path != ""), QTY, PC, STOCKNUM, TITLE, ASD_PICK_DATE)
      st_cals.table <- dplyr::filter(finaldata, St_Num == currStNum, File_Path != "")
      
      bodytext <- paste0("<p> <strong><h1>Store ", currStNum," - Marketing Plan Calendar</h1></span></strong></p>",
                         "<p>Store Manager: ", st_cals.table[1, "MGR_FNAME"], " ", st_cals.table[1, "MGR_LNAME"], "</p>",
                         "<p>The attached .pdf file(s) contains monthly calendars with the most recent ",
                         "summarized corporate marketing plans for your store. This replaces any previous ",
                         "versions.</p>",
                         if(OthYear_NextorLast == "Next"){paste0("<p>The calendar for NEXT year is also attached ",
                                                                 "for early year planning.</p>")
                         },
                         if(OthYear_NextorLast == "Last"){paste0("<p>The final completed calendar for LAST year is ",
                                                                 "also attached. You can retain this to see how previous advertising and events affected sales.</p>")
                         },
                         "<p>Future items are subject to change, though not likely for items scheduled in the next 6 weeks or so. </p>",
                         norm_st_from,
                         sep = ""
      )
      
      CalAttach <- dplyr::select(dplyr::filter(st_cals.table, St_Num == currStNum, File_Path != ""), "File_Path")
      #CalAttach <- as.list(dplyr::select(dplyr::filter(finaldata, File_Path != ""), "File_Path"))
      #CalAttach <- as.character(dplyr::select(dplyr::filter(finaldata, File_Path != ""), "File_Path"))
      #CalAttach <- dplyr::select(dplyr::filter(finaldata, File_Path != ""), "File_Path")
      #send store calendar mail
      #20240926: if(testing_emails){store_email <- test_recip}
      gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email
      mailsend(recipient = store_email,
               subject = paste0("Marco's Marketing Plans: Store ", currStNum),
               body = bodytext,
               attachment = CalAttach[ ,"File_Path"],
               inline = TRUE,
               test = testing_emails, 
               testrecipient = test_recip
      )
      # sleep-gmail needs gaps ~3 seconds between msgs to prevent exceeding Google API quota limits, wait an extra 42 seconds every 20 (Google allows short bursts)
      Sys.sleep(3)
      MyErrorLog[1,"ST_EMAIL_STATUS"] <- "EMAILING STARTED"
      MyErrorLog[1,"PROGRESS"] <- paste0(i," of ", store.mail_to)
      writelog(MyErrorLog)
      if(i %% 20 == 0) {Sys.sleep(42)}
      
    }
    
    #"ST_EMAIL_STATUS"] == 'COMPLETE'
    MyErrorLog[1,"ST_EMAIL_STATUS"] <- "COMPLETE"
    MyErrorLog[1,"PROGRESS"] <- "ST_EMAIL_STATUS"
    writelog(MyErrorLog)
    
  } #end of store.mail_send
  
  #email DMs
  if( dm.mail_send ) {
    dm.mail_to <- nrow(final.DMs)
    #EMAILING STARTED
    #st_email.status <- data.frame(ST_EMAIL_STATUS = 'EMAILING STARTED', stringsAsFactors = FALSE)
    #progress.status <- data.frame(PROGRESS = 'COMPLETE', stringsAsFactors = FALSE)
    
    for(i in dm.mail_from:dm.mail_to ){  # normal, disable next TEST line
      #for(i in 1:3 ){   # test, disable above NORMAL line
      
      dm_email <- final.DMs[i, "DM"]
      # create table of calendars for this stores email
      #st_cals.table <- select(dplyr::filter(finaldata, St_Num == currStNum, File_Path != ""), QTY, PC, STOCKNUM, TITLE, ASD_PICK_DATE)
      st_cals.table <- dplyr::filter(finaldata, DM_EMAIL == dm_email, File_Path != "")
      fname <- unique(na.omit(st_cals.table[1 , "DM_FNAME"]))
      lname <- unique(na.omit(st_cals.table[1 , "DM_LNAME"]))
      
      bodytext <- paste0("<p> <strong><h1>", fname, " ", lname," District - Marketing Plan Calendars</h1></span></strong></p>",
                         "<p>", fname, ", </p>",
                         "<p>The attached .pdf files contain monthly calendars with the most recent ",
                         "summarized corporate marketing plans for your stores. These replace any previous ",
                         "versions.</p>",
                         if(OthYear_NextorLast == "Next"){paste0("<p>The calendars for NEXT year are also attached ",
                                                                 "for early year planning.</p>")
                         },
                         if(OthYear_NextorLast == "Last"){paste0("<p>The final completed calendars for LAST year are also ",
                                                                 "attached. You can retain these to see how previous advertising and events affected sales.</p>")
                         },
                         "<p>Future items are subject to change, though not likely for items scheduled in the next 6 weeks or so. </p>",
                         norm_RM_DM_from,
                         sep = ""
      )
      
      CalAttach <- dplyr::select(dplyr::filter(st_cals.table, DM_EMAIL == dm_email, File_Path != ""), "File_Path")
      #send DM calendar mail
      #20240926: if(testing_emails){dm_email <- test_recip}
      gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email
      mailsend(recipient = dm_email,
               subject = paste0("Marco's Marketing Plans: ", fname, " ", lname),
               body = bodytext,
               attachment = CalAttach[ ,"File_Path"],
               inline = TRUE,
               test = testing_emails, 
               testrecipient = test_recip
      )
      # sleep-gmail needs gaps ~3 seconds between msgs to prevent exceeding Google API quota limits, wait an extra 42 seconds every 20 (to be safe as Google allows short bursts)
      Sys.sleep(3)
      MyErrorLog[1,"DM_EMAIL_STATUS"] <- "EMAILING STARTED"
      MyErrorLog[1,"PROGRESS"] <- paste0(i," of ", store.mail_to)
      writelog(MyErrorLog)
      if(i %% 20 == 0) {Sys.sleep(42)}
      
    }
    
    #"DM_EMAIL_STATUS"] == 'COMPLETE'
    MyErrorLog[1,"DM_EMAIL_STATUS"] <- "COMPLETE"
    MyErrorLog[1,"PROGRESS"] <- "DM_EMAIL_STATUS"
    writelog(MyErrorLog)
    
  } #end of dm.mail_send
  
  #email RMs
  if( rm.mail_send ) {
    rm.mail_to <- nrow(final.RMs)
    #EMAILING STARTED
    #st_email.status <- data.frame(ST_EMAIL_STATUS = 'EMAILING STARTED', stringsAsFactors = FALSE)
    #progress.status <- data.frame(PROGRESS = 'COMPLETE', stringsAsFactors = FALSE)
    
    for(i in rm.mail_from:rm.mail_to ){  # normal, disable next TEST line
      #for(i in 1:3 ){   # test, disable above NORMAL line
      
      rm_email <- final.RMs[i, "RM"]
      # create table of calendars for this stores email
      #st_cals.table <- select(dplyr::filter(finaldata, St_Num == currStNum, File_Path != ""), QTY, PC, STOCKNUM, TITLE, ASD_PICK_DATE)
      st_cals.table <- dplyr::filter(finaldata, RM_EMAIL == rm_email, File_Path != "")
      fname <- unique(na.omit(st_cals.table[1 , "RM_FNAME"]))
      lname <- unique(na.omit(st_cals.table[1 , "RM_LNAME"]))
      
      bodytext <- paste0("<p> <strong><h1>", fname, " ", lname," Region - Marketing Plan Calendars</h1></span></strong></p>",
                         "<p>", fname, ", </p>",
                         "<p>The attached .pdf files contain monthly calendars with the most recent ",
                         "summarized corporate marketing plans for your stores. These replace any previous ",
                         "versions.</p>",
                         if(OthYear_NextorLast == "Next"){paste0("<p>The calendars for NEXT year are also attached ",
                                                                 "for early year planning.</p>")
                         },
                         if(OthYear_NextorLast == "Last"){paste0("<p>The final completed calendars for LAST year are also ",
                                                                 "attached. You can retain these to see how previous advertising and events affected sales.</p>")
                         },
                         "<p>Future items are subject to change, though not likely for items scheduled in the next 6 weeks or so. </p>",
                         norm_RM_DM_from,
                         sep = ""
      )
      
      CalAttach <- dplyr::select(dplyr::filter(st_cals.table, RM_EMAIL == rm_email, File_Path != ""), "File_Path")
      #send RM Calendar mail
      #20240926: if(testing_emails){rm_email <- test_recip}
      gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email
      mailsend(recipient = rm_email,
               subject = paste0("Marco's Marketing Plans: ", fname, " ", lname),
               body = bodytext,
               attachment = CalAttach[ ,"File_Path"],
               inline = TRUE,
               test = testing_emails, 
               testrecipient = test_recip
      )
      # leep-gmail needs gaps ~3 seconds between msgs to prevent exceeding Google API quota limits, wait an extra 42 seconds every 20 (to be safe as Google allows short bursts)
      Sys.sleep(3)
      MyErrorLog[1,"RM_EMAIL_STATUS"] <- "EMAILING STARTED"
      MyErrorLog[1,"PROGRESS"] <- paste0(i," of ", store.mail_to)
      writelog(MyErrorLog)
      if(i %% 20 == 0) {Sys.sleep(42)}
      
    }
    
    #"RM_EMAIL_STATUS"] == 'COMPLETE'
    MyErrorLog[1,"RM_EMAIL_STATUS"] <- "COMPLETE"
    MyErrorLog[1,"PROGRESS"] <- "RM_EMAIL_STATUS"
    writelog(MyErrorLog)
    
  } #end of rm.mail_send
  
  
  #update log with completion
  MyErrorLog[1,"PROGRESS"] <- "COMPLETE"
  Sys.sleep(2)
  writelog(MyErrorLog)
  
  # Send routine complete email
  bodytext <- paste0("This is an automated email to inform you that it appears the <b>Marco's Store Plan Calendar EMAILING</b> routine ",
                     "has completed.<br/>",
                     if(store.mail_send){paste0("Stores emailed: ", store.mail_to, "<br/>")},
                     if(dm.mail_send){paste0("DMs emailed: ", dm.mail_to, "<br/>")},
                     if(rm.mail_send){paste0("RMs emailed: ", rm.mail_to, "<br/><br/>")},
                     print(xtable(MyErrorLog, 
                                  caption = paste0("MP Store Calendars (", report.time.txt, ")"), 
                                  align = rep("c", ncol(MyErrorLog) + 1)),
                           type = "html", 
                           caption.placement = "top", 
                           include.rownames=FALSE),
                     "<br/>",
                     warn_sig
  )
  #send mail
  gMail_reply_to <- gMail_auth_email #use if you want alternate reply-to email address, comment out to use gMail_auth_email
  mailsend(recipient = warn_recip,
           subject = "Marco's Store Plan Calendar EMAILING Status: COMPLETE",
           body = bodytext,
           test = testing_emails, 
           testrecipient = test_recip
  )
}



