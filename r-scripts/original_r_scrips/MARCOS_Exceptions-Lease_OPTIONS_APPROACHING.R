library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(dbplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
testing_emails <- TRUE

# Version 20250321

### 20250321 change:
### updated to use gmailr email package
### updated to use Snowflake DB

### 20230823 change:
### new file



# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE
scriptfolder <- "MARCOS_Exceptions-Leases"
myReportName <- "<PERSON>'s Lease Options Approaching"
rptFN <- paste0("MARCOS_Lease_Options", ".xlsx")

myNoticeDays <- '180, 60, 10, 1'

myReportCriteria <- paste0(
  "<p><b>Criteria for inclusion in the report:</b><ul>",
  "<li>Rented location with a lease renewal option notice coming up AND no renewal lease found in MRI</li>",
  "</li>",
  "</ul></p>",
  "<p><em>(notifications generated at ", myNoticeDays, " days out)</em></p>"
)


#logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)


#SSMS connection
mySSdb <- dbConnect(odbc(),
                 Driver = "ODBC Driver 17 for SQL Server",
                 Server = "************",
                 Database = "HIGHLANDPROD",
                 UID = "SteveO_ro",
                 PWD = key_get("MRI_bak", "SteveO_ro"),
                 Port = 1433)


# email parameters: recipient(s) of warning emails and signatures
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath, scriptfolder)
rptpath <- logpath
myReportPath <- file.path(logpath, scriptfolder)
sig_logo <- FALSE





### define some functions ###

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("Steve Olson<<EMAIL>>")
test_cc_recip <- c("<EMAIL>")

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}



check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  #this function checks the number of rows in the data frame and returns a list of
  #several values depending on whether it does or not
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
}





### Find Exceptions and email results
if(okaytocontinue){
  
  myReportPath <- rptpath
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myNoticeDays
  
  myquery_exceptions <- paste0(
    '
        SELECT /* SNOWFLAKE version */
        LEAS.BLDGID as "Bldg"
        , LEAS.LEASID as "Lease ID"
        , LEAS.SUITID as "Suite"
        , LEAS.OCCPNAME as "Name"
        , leas.GENERATION AS "Curr Lease Gen"
        , OPTNTYPE.DESCRPN AS "Type"
        , CONCAT(LEASOPTS.OPTNNUM, \' of \', max_opt.MAX_OPTNUM) AS "Option"
        , DATEDIFF(day, CURRENT_DATE(), leasopts.noticedate) as "Days Until Notice Due"
        , TO_DATE(LEASOPTS.NOTICEDATE) AS "Notice By Date"
        , TO_DATE(LEASOPTS.OPTNDATE) AS "Renewal Start Date"
        , LEASOPTS.SQFT
        , LEASOPTS.TERM as "Term"
        , LEASOPTS.RATE as "Rate"
        , LEASOPTS.RATEDESC AS "Desc"
        , LEASOPTS.NOTES as "Option Notes"
        , LEAS.CONTNAME as "Contact Name"
        , LEAS.PHONENO1 as "Contact Ph 1"
        , LEAS.PHONENO2 AS "Contact Ph 2"
      ',"
        from MRI.LEAS
        LEFT JOIN MRI.SUIT
        	ON LEAS.BLDGID = SUIT.BLDGID
        	AND LEAS.SUITID = SUIT.SUITID
        LEFT JOIN MRI.LEASOPTS
        ON LEAS.BLDGID = LEASOPTS.BLDGID AND LEAS.LEASID = LEASOPTS.LEASID
        LEFT JOIN
        (
        	select
        	  LEASOPTS.BLDGID
        	, LEASOPTS.LEASID
        	, max(LEASOPTS.OPTNNUM) as MAX_OPTNUM
        	from MRI.LEASOPTS
        	group by LEASOPTS.BLDGID
        	, LEASOPTS.LEASID
        ) max_opt
        on LEAS.BLDGID = max_opt.BLDGID AND LEAS.LEASID = max_opt.LEASID
        LEFT JOIN MRI.OPTNTYPE
        ON OPTNTYPE.OPTNTYPE = LEASOPTS.OPTNTYPE
        LEFT JOIN
        ( /* check for new lease where bldgid, suitid, moccpid are the same and generation > existing lease */
        	select
        	  leas.BLDGID
        	, leas.SUITID
        	, leas.LEASID
        	, leas.MOCCPID
        	, leas.EXPIR
        	, leas.GENCODE
        	, leas.GENERATION
        	, leas.OCCPSTAT
        	from MRI.leas
        	LEFT JOIN MRI.SUIT
        	ON LEAS.BLDGID = SUIT.BLDGID
        	AND LEAS.SUITID = SUIT.SUITID
        	where 
        	(
        		(
	 			UPPER(LEAS.OCCPNAME) LIKE '%MARCO%' 
	 			AND 
	 			(LEAS.COMPANYGRPID = 8 OR SUIT.BLDGID LIKE 'RNT%')
		 		)
				OR 
				UPPER(LEAS.TENTID) = 'MARCO'
        	)
        	and leas.OCCPSTAT <> 'I'
        	and try_cast(leas.BLDGID AS INT) is null
        ) exist
        on LEAS.BLDGID = exist.BLDGID
        AND LEAS.SUITID = exist.SUITID
        AND LEAS.MOCCPID = exist.MOCCPID
        AND LEAS.GENERATION < exist.GENERATION
        where 
    	(
    		(
	 			UPPER(LEAS.OCCPNAME) LIKE '%MARCO%' 
	 			AND 
	 			(LEAS.COMPANYGRPID = 8 OR SUIT.BLDGID LIKE 'RNT%')
	 		)
			OR 
			UPPER(LEAS.TENTID) = 'MARCO'
    	)
        and leas.OCCPSTAT = 'C'
        and try_cast(leas.BLDGID AS INT) is null
        AND DATEDIFF(day, CURRENT_DATE(), leasopts.noticedate) in (", myNoticeDays, ") /* R line with substitution variable, test line below */
        --AND DATEDIFF(day, CURRENT_DATE(), leasopts.noticedate) BETWEEN 60 AND 1000 /* test line only, not for PROD */
        and exist.GENERATION is NULL
        order by \"Days Until Notice Due\"
    "
  )
  #20250321: mydata <- dbGetQuery(mySSdb, myquery_exceptions)
  mydata <- dbGetQuery(mySfDB, myquery_exceptions)

  #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
  mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("Bldg",
                                                "Lease ID",
                                                "Suite",
                                                "Name",
                                                "Curr Lease Gen",
                                                "Type",
                                                "Option",
                                                "Days Until Notice Due",
                                                "Notice By Date",
                                                "Renewal Start Date",
                                                "SQFT",
                                                "Term",
                                                "Rate",
                                                "Desc",
                                                "Option Notes",
                                                "Contact Name",
                                                "Contact Ph 1",
                                                "Contact Ph 2"
                                                #"",
                                    )
                                   ,
                                   width = c(8.5,
                                             8.5,
                                             8,
                                             if(max(nchar(na.omit(mydata[,"Name"]))) > 30){min(75, max(nchar(na.omit(mydata[,"Name"]))))}else{31},
                                             7.5,
                                             16,
                                             9,
                                             9.25,
                                             11,
                                             11,
                                             7,
                                             7,
                                             8.5,
                                             8.5,
                                             if(max(nchar(na.omit(mydata[,"Option Notes"]))) > 39){min(110, max(nchar(na.omit(mydata[,"Option Notes"]))))}else{41},
                                             if(max(nchar(na.omit(mydata[,"Contact Name"]))) > 25){min(60, max(nchar(na.omit(mydata[,"Contact Name"]))))}else{26},
                                             12,
                                             12
                                    )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    myemailbodycols <- c(3,4,8,9,10)
    mydata_emailbody <- mydata[,myemailbodycols]
    #DEDUP mydata_emailbody to get summary vs details of full dataframe
    mydata_emailbody <- mydata_emailbody %>% distinct()
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata_emailbody)<31){
      bodytable <- paste0("<p>The info below contains MRI data (from yesterday) that ",
                          "indicates the following leases have an upcoming renewal ",
                          "notice date. <b>See attached Excel file for full details.</b> ",
                          "</p>",
                          "<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1),
                                       align = c(rep("l",3), rep("c", ncol(mydata_emailbody) - 2)),
                                ),
                                html.table.attributes = "border=2 cellspacing=1",
                                type = "html",
                                caption.placement = "top",
                                include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p><strong><em>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</em></strong></p>"
      )
    }
    bodytext <- paste0("<p><h2>", this_ReportName, "</h2>",
                       "</p>",
                       myReportCriteria,
                       bodytable,
                       "<br/><br/>",
                       norm_sig,
                       "<br/><br/>This script located in the Tableau/R computer at: ",
                       logpath
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}


DBI::dbDisconnect(mySfDB)




