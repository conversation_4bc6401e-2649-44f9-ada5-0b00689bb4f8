library(xtable)
library(reshape2)
library(dplyr)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(DBI)
library(odbc)
#library(RO<PERSON>le)
library(googledrive)
library(googlesheets4)
library(keyring)

# written by <PERSON> August 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241008

### 20241008 change:
### converted SQL queries to Snowflake DBs

### 20240917 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### updated email signature to use latest format provided by <PERSON> earlier in 2024

### 20230605 change:
### replaced missing HVSigLogopath path

### 20230518 change:
### Joined Legacy Assignments to ab_employees table on fname & lname (not ideal)

### 20230517 change:
### added Lot Size, Acreage and Legacy Assignments to data

### 20220831 change:
### new file


# Parameters

logpath <- file.path("C:","Users","table","Documents","ReportFiles","LEGACY_Portfolio_to_Google")
okaytocontinue <- TRUE

myReportName <- "LEGACY Portfolio to Google"
gSht_id <- "1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8" #PROD version
#gSht_id <- "16QUYf48WFORTzDW3ihR8C-UP0-JwAwy39QcKjBngMIY" #20241008 TEST version
mySheets <- c("PORTFOLIO")
gSht_auth_email <- "<EMAIL>"

msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="GMT")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)




# email parameters: recipient(s) of warning emails and signatures (can be replaced later with sig template call)
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>","<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")

report.date.text <- format(Sys.Date(), "%m-%d-%Y")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))

if(Sys.getenv("COMPUTERNAME") != "DESKTOP-TABLEAU"){
  testing_pc <- TRUE  #TESTING, changes some paths to network share instead of R/Tableau PC
  testing_pc_location <- "Office"
  #testing_pc_location <- "Laptop"
}else{testing_pc <- FALSE}

if(testing_pc){
  if(testing_pc_location == "Office"){
    # Steve PC testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_PNL_Line_Structure")
    HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
  }
  if(testing_pc_location == "Laptop"){
    # Steve HOME laptop testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("E:","Steve","Documents","R Scripts","TestScripts","HV_PNL_Line_Structure")
    rptpath_MARCOS_emails <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_PNL_Line_Structure","Email_Addys")
    HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
  }
}


### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


###Get email signature###
get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
  warn_sig <- norm_sig
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




# check google sheet status
if(okaytocontinue){
  gs4_auth(email = gSht_auth_email)
  if (gs4_has_token()) {
    gSht_get <- gs4_get(as.character(gSht_id))
  }else{
    #token not available
    gSht_get <- c("")
  }
  
  if(length(gSht_get) > 2){
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_URL <- gSht_get$spreadsheet_url[[1]]
    gSht_Sheets_num <- length(gSht_Sheets)
    test_compare <- mySheets %in% gSht_Sheets
    mySheets_present <- mySheets[test_compare]
    mySheets_notpresent <- mySheets[!test_compare]
    
    #check that at least ONE sheet found
    if(length(mySheets_notpresent) == length(mySheets)){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>The following sheets where expected but NOT found ",
                         "in the '", gSht_get$name, "' workbook:<br><b>", 
                         paste(mySheets_notpresent, collapse = "<br> "),
                         "</b><p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }else{
      if(length(mySheets_notpresent) > 0){
        bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                           "an error in the ", myReportName, " routine!</p>",
                           #"<p>There weren't any sheets named like '", mySheets, "' ",
                           "<p>The following sheets where expected but NOT found ",
                           "in the '", gSht_get$name, "' workbook:<br>", 
                           paste(mySheets_notpresent, collapse = "<br> "),
                           "<p>The routine will continue <B>BUT WILL NOT UPDATE ",
                           "THE SHEETS LISTED ABOVE.</B></p> ",
                           warn_sig
        )
        #send mail
        mailsend(warn_recip,
                 paste0(myReportName, " Issue: Missing one or more expected sheets"),
                 bodytext,
                 attachment = NULL,
                 test = testing_emails, testrecipient = test_recip
        )
      }
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}



### Populate sheets in Google

if(okaytocontinue){
  for(i in 1:length(mySheets_present)){
    gSht_Sheet_curr <- mySheets_present[[i]]
    myquery <- case_when(
      gSht_Sheet_curr == "PORTFOLIO" ~ paste0(
          "
            SELECT
            	TRY_CAST(B.BLDGID AS INT) AS \"Bldg\"
            ,	CASE WHEN B.INACTIVE <> 'Y' or B.INACTIVE IS NULL THEN 'Active' ELSE 'Inactive' END as \"Active\"
            ,	CONCAT(trim(B.ADDRESS1), (CASE WHEN B.ADDRESS2 IS NOT NULL THEN CONCAT('; ',TRIM(B.ADDRESS2)) ELSE '' END)) as \"Address\"
            ,	IFNULL(B.CITY,'') AS \"City\"
            ,	IFNULL(B.STATE,'') AS \"State\"
            ,	IFNULL(B.ZIPCODE,'') AS \"Postal Code\"
            ,	CAST(ROUND(SQFT.\"TOTAL_SQFT\",0) AS INT) AS \"Total Bldg Size\"
            ,	POWNER.OWNERID AS \"Owner\"
            ,	TO_DATE(E.DISPOSED) AS \"Disposition Date\"
            ,	CASE WHEN E.DISPOSED IS NOT NULL THEN 'SOLD' ELSE IFNULL(PTYP.DESCRPN,'') END AS \"Prop Type\"
            ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE trim(B.MAP) END,'') AS \"Hover for Google Map Link\"
            ,	CAST(LOTSZ.NOTE_VALUE AS INTEGER) AS \"Lot Size (sqft)\"
            ,	ACREAGE.NOTE_VALUE AS \"Acreage\"
            ,	CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE trim(MNGR.MNGRNAME) END AS \"Corporate PM\"
            ,	CASE WHEN B.INACTIVE = 'Y' THEN NULL 
            		ELSE trim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS \"PM_Email\"
            FROM MRI.BLDG B
            INNER JOIN MRI.ENTITY E
            ON B.ENTITYID = E.ENTITYID
            INNER JOIN MRI.PROJ P
            ON E.PROJID = P.PROJID
            LEFT JOIN MRI.PTYP
            ON E.PROPTYPE = PTYP.PROPTYPE
            LEFT JOIN MRI.LLRD 
            ON B.LLRDID = LLRD.LLRDID
            LEFT JOIN MRI.MNGR
            ON B.MNGRID = MNGR.MNGRID
            LEFT JOIN
            (
            	SELECT
            		SANDL.BLDGID
            	,	SUM(CASE WHEN SANDL.SQFTTYPE <> 'BTS' THEN SANDL.\"LEASABLE SQFT\" END) AS TOTAL_SQFT
            	FROM
            	( /* Suites and Leases */
            		select 
            			S.BLDGID
            		,	S.SUITID
            		,	S.SUITETYPE_MRI
            		,	S.SUITSQFT AS \"SUITE SQ FEET\"
            		,	CONCAT(TB_CM_SUITETYPE.SUITETYPEUSAGE,CASE WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'I' THEN ' (Include)' 
            							WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' THEN ' (Include Not Counted)' 
            							WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'E' THEN ' (Exclude)' END) AS \"SUITE TYPE AREA USAGE\"
            		,	SSQF_TYPE.SQFTTYPE
            		,	(CASE WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'I' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL THEN 1 END) AS \"COUNT AS SUITE\" /* Exclude N and E, sometimes Legacy counts NULL sometimes not */
            		,	(CASE WHEN LEASED.OCCPSTAT ='C' AND LEASED.GENCODE <> 'SNC' AND (S.SUITETYPE_MRI <> 'KIOSK' OR S.SUITETYPE_MRI IS NULL) THEN 1
            				WHEN LEASED.OCCPSTAT ='C' AND LEASED.GENCODE = 'SNC' AND (S.SUITETYPE_MRI <> 'KIOSK' OR S.SUITETYPE_MRI IS NULL) AND LEASED.\"Rent Start\" <= Cast(GetDate() AS date) AND (LEASED.ACTIVE_CONTINGENCY IS NULL OR LEASED.ACTIVE_CONTINGENCY = 'N')  THEN 1 END) AS \"COUNT AS OCCUPIED\" /* Steve best guess. Starting 10/13/2021 per Patrick M and Shaunti A meeting */
            		,	IFNULL(SSQF_TYPE.SQFT,0) AS \"LEASABLE SQFT\"
            		,	IFNULL((CASE WHEN LEASED.ACTIVE_CONTINGENCY = 'Y' THEN 0 ELSE SSQF_TYPE.LSDSQFT END),0) AS \"LEASED SQFT\"
            		,	TO_DATE(SSQF_TYPE.LASTDATE) AS \"SQFTLASTDATE\"
            		,	LEASED.LEASID
            		,	LEASED.ACTIVE_CONTINGENCY
            		,	LEASED.GENCODE AS \"GENERATION CODE\"
            		,	LEASED.OCCPNAME AS \"TENANT NAME\"
            		,	LEASED.\"Occupant Status\" AS \"OCCUPANT STATUS\"
            		,	LEASED.\"Rent Start\" AS \"RENT START\"
            		,	LEASED.STOPBILLDATE AS \"STOP BILL DATE\"
            		,	LEASED.VACATEDATE AS \"VACATE DATE\"
            		,	LEASED.EXPIRDATE AS \"EXPIRE DATE\"
            		,	LEASED.\"End Date\" AS \"END DATE\"
            		,	LEASED.EFFDATE
            		,	LEASED.\"Last Bill Date\" AS \"LAST BILL DATE\"
            		,	LEASED.\"3RD PARTY RENT\"
            		,	LEASED.\"INTERNAL RENT\"
            		FROM MRI.SUIT S
            		JOIN MRI.BLDG B
            		ON B.BLDGID = S.BLDGID
            		LEFT JOIN MRI.TB_CM_SUITETYPE
            		ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            		LEFT JOIN 
            		(
            			SELECT *
            			FROM MRI.SSQF
            			WHERE SSQF.EFFDATE = (
            				SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND TO_DATE(I.EFFDATE) <= CURRENT_DATE
            				)
            		) SSQF_TYPE
            		ON S.SUITID = SSQF_TYPE.SUITID
            		 AND S.BLDGID = SSQF_TYPE.BLDGID
            		LEFT JOIN
            		(
            			SELECT 								
            				CMRECC.BLDGID,							
            				RTRIM(P.PORTID) AS \"Portfolio\",
            				L.SUITID,
            				(CASE WHEN CMRECC.INCCAT = 'RNT' THEN '3rd Party Base Rent' ELSE INCH.DESCRPTN END) AS \"RENT TYPE\",							
            				CMRECC.LEASID,							
            				L.OCCPNAME,							
            				TO_DATE(L.RENTSTRT) AS \"Rent Start\",							
            				CAST(S.SUITSQFT AS INTEGER) AS \"Suite SqFt\", /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
            				CAST(IFNULL(SQF.SQFT,0) AS INTEGER) AS \"SSQF SqFt\",							
            				TRIM(SQF.SQFTTYPE) AS \"SqFt Type\",														
            				L.OCCPSTAT,
            				CL.CODEDESC AS \"Occupant Status\",							
            				TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
            				TO_CHAR(L.VACATE, 'MM/dd/yyyy') as VACATEDATE,							
            				TO_CHAR(L.EXPIR,'MM/dd/yyyy') as EXPIRDATE,							
            				TO_CHAR(CMRECC.ENDDATE,'MM/dd/yyyy') as \"End Date\",							
            				TO_DATE(CMRECC.EFFDATE) AS EFFDATE,							
            				CMRECC.LASTBILL AS \"Last Bill Date\",														
            				(CASE WHEN CMRECC.INCCAT = 'RNT' THEN (IFNULL(CMRECC.AMOUNT,0) + IFNULL(RN2.AMOUNT,0)) ELSE 0 END) AS \"3RD PARTY RENT\",							
            				(CASE WHEN CMRECC.INCCAT = 'RNT' THEN 0 ELSE (IFNULL(CMRECC.AMOUNT,0) + IFNULL(RN2.AMOUNT,0)) END) AS \"INTERNAL RENT\",							
            				S.SUITETYPE_MRI,							
            				TB_CM_SUITETYPE.SUITETYPEUSAGE,							
            				TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
            				L.GENCODE,
            				L.CONTINGENT,
            				L.CONTINGENTDT,
            				case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= CURRENT_DATE THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY				
            			FROM MRI.CMRECC								
            			left join								
            			(								
            				SELECT AMOUNT, 							
            					I.LEASID						
            				FROM MRI.CMRECC I							
            				WHERE I.INEFFECT = 'Y'							
            					AND I.INCCAT = 'RN2' /* count 'RN2' (Additional Base Rent) as Base Rent) */						
            			) RN2								
            			ON CMRECC.LEASID = RN2.LEASID								
            			INNER JOIN MRI.LEAS L								
            			ON CMRECC.LEASID = L.LEASID								
            			INNER JOIN MRI.BLDG B								
            			ON CMRECC.BLDGID = B.BLDGID								
            			INNER JOIN MRI.SUIT S								
            			ON L.SUITID = S.SUITID								
            			 AND B.BLDGID = S.BLDGID								
            			LEFT JOIN (								
            				SELECT * 							
            				FROM MRI.SSQF 							
            				WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND TO_DATE(I.EFFDATE) <= CURRENT_DATE )							
            			) SQF								
            			ON S.BLDGID = SQF.BLDGID								
            				AND S.SUITID = SQF.SUITID							
            			LEFT JOIN MRI.TB_CM_SUITETYPE								
            			ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
            			LEFT JOIN MRI.ENTITY E								
            			ON B.ENTITYID = E.ENTITYID								
            			LEFT JOIN MRI.PROJ P								
            			ON E.PROJID = P.PROJID								
            			LEFT JOIN MRI.INCH								
            			ON CMRECC.INCCAT = INCH.INCCAT								
            			LEFT JOIN MRI.CODELIST CL								
            			ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'	
            			LEFT JOIN MRI.INCH_LCP_REPORTS
            				ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
            			WHERE CMRECC.INEFFECT = 'Y'								
            				--AND CMRECC.INCCAT = 'RNT'  /* 3rd party tenants ONLY, HVLTD entities use other INCCAT rents */							
            				--AND CMRECC.INCCAT IN ('RNT','PPR','PRK','RFV','RHW','RMP','RSF','RVT')	/*REPLACED BY LINE BELOW 2024-10-08*/
            				AND (INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT') OR CMRECC.INCCAT IN ('PPR') )
            				AND CMRECC.EFFDATE = (SELECT MAX(IC.EFFDATE) FROM MRI.CMRECC IC WHERE IC.BLDGID=CMRECC.BLDGID AND IC.LEASID=CMRECC.LEASID AND IC.INCCAT=CMRECC.INCCAT AND TO_DATE(EFFDATE) <= CURRENT_DATE) /* can be multiple charges for same INCCAT, this limits to latest effective date */							
            				AND (CMRECC.ENDDATE IS NULL OR CMRECC.ENDDATE >= CURRENT_DATE)													
            				AND L.OCCPSTAT NOT IN ('P', 'I') 							
            				AND (							
            						(					
            							TO_DATE(L.RENTSTRT) <= CURRENT_DATE				
            							AND (				
            									L.STOPBILLDATE IS NULL 		
            									OR TO_DATE(L.STOPBILLDATE) >= CURRENT_DATE		
            									OR (		
            											TO_DATE(L.STOPBILLDATE) < CURRENT_DATE 
            											AND COALESCE(L.VACATE,L.EXPIR) >= CURRENT_DATE
            										)	
            								)			
            							AND COALESCE(L.VACATE,L.EXPIR) >= CURRENT_DATE 				
            						)					
            						OR 					
            						(((TO_DATE(L.EXPIR) <= CURRENT_DATE OR L.EXPIR IS NULL) AND (TO_DATE(L.VACATE) >= CURRENT_DATE OR L.VACATE IS NULL))) 					
            						AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')					
            					) 						
            		)LEASED
            		ON B.BLDGID = LEASED.BLDGID
            			AND S.SUITID = LEASED.SUITID
            		WHERE (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
            	) SANDL
            	WHERE SANDL.SQFTTYPE <> 'BTS'
            	GROUP BY SANDL.BLDGID
            ) SQFT
            ON B.BLDGID = SQFT.BLDGID
            LEFT JOIN
            (/* PRIMARY OWNER */
            	SELECT ENTITYID
            	,	OWNERID
            	,	\"Owned / Rented Property\"	
            	FROM
            	(
            	  SELECT O.ENTITYID
            	  ,	O.OWNERID
            	  ,	CASE WHEN UPPER(O.OWNERID) = 'RENTED' THEN 'Rented' ELSE 'Owned' END AS \"Owned / Rented Property\"
            	  ,	O.BEGPD
            	  ,	O.PRIMARYOWN
            	  ,	O.\"PERCENT\"
            	  ,	O.LASTDATE
            	  ,	RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.\"PERCENT\" DESC, O.BEGPD, O.LASTDATE DESC) AS RANK /* RANK OWNERSHIP INTERESTS SINCE MRI DOESN'T FULLY VALIDATE ENTRIES */
            	  FROM MRI.GOWN O
            	  WHERE (O.BEGPD = 
            		  (
            			  SELECT MAX(I.BEGPD)
            			  FROM MRI.GOWN I
            			  WHERE I.ENTITYID = O.ENTITYID
            				  AND I.BEGPD <= to_char(GETDATE(), 'yyyyMM')
            		  )
            	  OR (
            			  SELECT MAX(I.BEGPD)
            			  FROM MRI.GOWN I
            			  WHERE I.ENTITYID = O.ENTITYID
            		  ) IS NULL
            	  )
            	  AND (O.ENDPD IS NULL OR O.ENDPD >= to_char(GETDATE(), 'yyyyMM'))
            	) RANKED
            	WHERE RANK = 1
            ) POWNER
            ON E.ENTITYID = POWNER.ENTITYID
            LEFT JOIN MRI.GNAM 
            ON POWNER.OWNERID = GNAM.OWNERID
            LEFT JOIN
            (
            	select *
            	from
            	(
            		SELECT /* NOTEB building LOT SIZE notes, use RANK() to limit returned value to most recent note date */
            				NOTB.BLDGID
            			,	TO_DATE(NOTB.NOTEDATE) AS NOTE_DATE
            			,	NOTB.NOTETEXT as FULL_NOTETEXT
            			,	(SELECT RTYP.DESCRPTN FROM MRI.RTYP WHERE RTYPID = 'ACLOTSZ') AS \"Reference\"
            			,	TRY_CAST(REPLACE(REGEXP_SUBSTR(NOTB.NOTETEXT, '[0-9,.-]+'),',','') AS NUMBER(38,4)) as NOTE_VALUE
            			,	RANK() OVER (PARTITION BY NOTB.BLDGID ORDER BY NOTB.NOTEDATE DESC, NOTB.LASTDATE DESC) AS RANK /* RANK NOTES BY NOTE DATE THEN BY UPDATE DATE */
            			FROM MRI.NOTB
            			JOIN MRI.BLDG
            			ON NOTB.BLDGID = BLDG.BLDGID
            			WHERE (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
            				AND (NOTB.REF1 = 'ACLOTSZ' OR NOTB.REF2 = 'ACLOTSZ')
            	) ILOTSZ
            	WHERE ILOTSZ.RANK = 1
            ) LOTSZ
            ON B.BLDGID = LOTSZ.BLDGID
            LEFT JOIN
            (
            	select *
            	from
            	(
            		SELECT /* NOTEB building LOT SIZE notes, use RANK() to limit returned value to most recent note date */
            				NOTB.BLDGID
            			,	TO_DATE(NOTB.NOTEDATE) AS NOTE_DATE
            			,	NOTB.NOTETEXT as FULL_NOTETEXT
            			,	(SELECT RTYP.DESCRPTN FROM MRI.RTYP WHERE RTYPID = 'ACLOTSZ') AS \"Reference\"
            			,	TRY_CAST(REPLACE(REGEXP_SUBSTR(NOTB.NOTETEXT, '[0-9,.-]+'),',','') AS NUMBER(38,4)) as \"NOTE_VALUE\"
            			,	RANK() OVER (PARTITION BY NOTB.BLDGID ORDER BY NOTB.NOTEDATE DESC, NOTB.LASTDATE DESC) AS RANK /* RANK NOTES BY NOTE DATE THEN BY UPDATE DATE */
            			FROM MRI.NOTB 
            			JOIN MRI.BLDG
            			ON NOTB.BLDGID = BLDG.BLDGID
            			WHERE (BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)
            				AND (NOTB.REF1 = 'ACREAGE' OR NOTB.REF2 = 'ACREAGE')
            	) IACREAGE
            	WHERE IACREAGE.RANK = 1
            ) ACREAGE
            ON B.BLDGID = ACREAGE.BLDGID
            WHERE TRY_CAST(B.BLDGID AS INT) IS NOT NULL
            ORDER BY B.BLDGID
          "
        ),
      TRUE ~ "skip" #unknown
    )
    
    
    myquery_LCPAssign <- case_when(
      gSht_Sheet_curr == "PORTFOLIO" ~ paste0(
        "
          select  /* SNOWFLAKE version */
          a.BLDG AS \"Bldg\"
          , a.RM
          , case when rm.phone is NULL then NULL
              else '('||substr(rm.phone,1,3)||') '||substr(rm.phone,4,3)||'-'||substr(rm.phone,7,4) end as \"RM Phone\"
          , rm.email as \"RM Email\"
          , a.SUPPORT_LEASING AS \"Support Leasing\"
          , case when a.SUPPORT_LEASING = 'BRH' then '(*************' 
              when sl.phone is NULL then NULL
              else '('||substr(sl.phone,1,3)||') '||substr(sl.phone,4,3)||'-'||substr(sl.phone,7,4) end as \"SL Phone\"
          , case when a.SUPPORT_LEASING = 'BRH' then '<EMAIL>' else sl.email end as \"SL Email\"
          , a.SUPPORT_PROP_MGMT AS \"Support Property Management\"
          , case when rm.phone is NULL then NULL
              else '('||substr(spm.phone,1,3)||') '||substr(spm.phone,4,3)||'-'||substr(spm.phone,7,4) end as \"SPM Phone\"
          , spm.email as \"SPM Email\"
          from CORPORATE.lcp_assignments_sean a
          left join CORPORATE.ab_employees rm
          on upper(a.rm) = upper(rm.fname)||' '||upper(rm.lname)
          and rm.status not in ('T','R','D')
          left join CORPORATE.ab_employees sl
          on upper(a.SUPPORT_LEASING) = upper(sl.fname)||' '||upper(sl.lname)
          and sl.status not in ('T','R','D')
          left join CORPORATE.ab_employees spm
          on upper(a.SUPPORT_PROP_MGMT) = upper(spm.fname)||' '||upper(spm.lname)
          and spm.status not in ('T','R','D')
        "
      ),
      TRUE ~ "skip" #unknown
    )
    
    
    myquery_PMPhone <- case_when(
      gSht_Sheet_curr == "PORTFOLIO" ~ paste0(
        "
          select /* SNOWFLAKE version */
            email as \"PM_Email\"
          , '(847) 904-'||extension as \"PM_Phone\"
          from CORPORATE.ab_employees
          where status not in ('R','T')
          and extension is not null
          and email is not null
        "
      ),
      TRUE ~ "skip" #unknown
    )
    
    
    
    if(myquery != "skip"){
      #20241008: mydata <- dbGetQuery(mySSdb, myquery)
      mydata <- dbGetQuery(mySfDB, myquery)
      mydata_status <- check_mydf_rows(mydf = mydata, MinNumRows = 1, ReportName = myReportName)
      #20241008: mydata_PMPhone <- dbGetQuery(myOracleDB, myquery_PMPhone)
      mydata_PMPhone <- dbGetQuery(mySfDB, myquery_PMPhone)
      mydata_status_PMPhone <- check_mydf_rows(mydf = mydata_PMPhone, MinNumRows = 1, ReportName = myReportName)
      #20241008: mydata_LCPAssign <- dbGetQuery(myOracleDB, myquery_LCPAssign)
      mydata_LCPAssign <- dbGetQuery(mySfDB, myquery_LCPAssign)
      mydata_status_LCPAssign <- check_mydf_rows(mydf = mydata_LCPAssign, MinNumRows = 1, ReportName = myReportName)
      
      if(mydata_status[[1]] == TRUE && mydata_status_PMPhone[[1]] == TRUE && mydata_status_LCPAssign[[1]] == TRUE){
        #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
        mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
        #join PM phone to MRI data
        mydata <- mydata %>%
          dplyr::left_join(mydata_PMPhone, by = "PM_Email")
          #dplyr::left_join(mydata_PMPhone, by = join_by("PM_Email", "EMAIL"))
        mydata <- mydata %>% relocate(PM_Phone, .before = PM_Email)
        #replace headers to replace underscores with spaces
        names(mydata) <- gsub("_"," ",names(mydata))
        
        #add LCP Assignments to mydata
        #myLoadStores_failed <- myLoadStores_expect %>% 
        #  dplyr::left_join(myLoadStores_curr, by = "STORE") %>% .[which(.$LOADED != .$n || is.na(.$LOADED)), ]
        mydata <- mydata %>%
          dplyr::left_join(mydata_LCPAssign, by = 'Bldg')
        
        # add 'Updated' column at end of mydata
        mydata[, date.header.text] <- NA
        
        #convert to dates
        #convert POSIXct dates to match previous Oracle output
        mydata[] <- lapply(mydata[], function(x) if(inherits(x, "POSIXct")) as.Date(x) else x)
        #UPDATE sheet
        #clear existing data in sheet
        range_clear(gSht_get$spreadsheet_id, sheet = mySheets_present[[i]], range = NULL, reformat = FALSE)
        #write new data
        #sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = mySheets_present[[i]])
        range_write(ss = gSht_get$spreadsheet_id, 
                    data = mydata, 
                    sheet = mySheets_present[[i]], 
                    #range = cell_rows(), 
                    col_names = TRUE,
                    reformat = FALSE
        )
        Sys.sleep(2)
      }
    }else{
      #query not specified for the sheetname
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>The routine didn't have a query associated with the ",
                         "sheet named '", mySheets_present[[i]], "' ",
                         "in the '", gSht_get$name, "' workbook.</p>",
                         "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Unknown Query"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
}




