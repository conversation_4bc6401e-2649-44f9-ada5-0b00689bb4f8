library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#20241111: library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
#20241111: library(RODBC) #replaced by odbc and DBI packages for Snowflake conn
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241120

### 20241120 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### Updated signature for emails to standard requested by <PERSON> in March 2024
### Routine now saves a daily copy of SUIT table to compare against (previously only had prior day copy saved in SQL Server table)

### 20230926 change:
### removed Haylea as recipient

### 20220912 change:
### Added Bobbi, Haylea and cc Rachael to recipients

### 20220809 change:
### added <EMAIL> to recipients

### 20220606 change:
### revised mailsend to use keyring

### 20220427 change:
### added keyring

### 20220426 change:
### minor bug fixes

### 20220422 change:
### add HTML table to body of email when 30 or less results

### 20220411 change:
### Added a sentence to the results email to clarify sort order.

### 20220408 change:
### new script based on IDEXX_SUMINVSTAFF_WEEKLY_update script version 20220407
### this script captures a snapshot of the MRI SUIT table each day so that 
### changes can be spotted from day to day and reported


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

#********: myTableName <- "test.dbo.SUIT"
myReportName <- "MRI Suite Changes"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
Sys.sleep(1.5)
scriptfolder <- "LEGACY_MRI_Exceptions-Suites"
rptfolder <- "reports"
save_to_shared <- TRUE #If set to TRUE, SUIT table copy files will also be saved to myReportPath_shared path

#mySheets <- " TESTFAIL"
# NOTE myColNames order dictates column order in resulting insert statement
myColNames <- c("BLDGID",
                "SUITID",
                "ADDRESS",
                "SUITENO",
                "FLOORNO",
                "TAXFID",
                "SUITSQFT",
                "MRKTRATE",
                "LASTDATE",
                "USERID",
                "INCCAT",
                "VACPERIOD",
                "TAXEXEMPT",
                "ANNINCOME",
                "SUITETYPE_MRI"
                )
mySelectColNames <- paste("S.", myColNames, collapse = ', ', sep = "")
myColNames_df <- c(myColNames, "AS_OF")
myInsertColNames <- paste(myColNames_df, collapse = ', ', sep = "")


#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "sa", key_get("MRI_bak", "sa"))

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>","<EMAIL>")
norm_recip <- c("Sean Coyle <<EMAIL>>",
                "<EMAIL>",
                #"Haylea Peterson <<EMAIL>>",
                "Bobbi Steiner <<EMAIL>>",
                "Rachael Heironimus <<EMAIL>>"
                )
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report_time <- format(Sys.time(), "%H%M%S%Z")
report_date <- format(Sys.Date(),"%Y%m%d")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
logname <- paste0(myReportName," - LOG.csv")
report.loglimit <- format(floor_date(Sys.Date(), "month") - months(2), "%Y%m%d 000000") #only log rows >= this are retained to prevent log bloat
report.logruntime <- format(Sys.time(), "%Y%m%d %H%M%S")
old_dates_delete <- seq(Sys.Date() - 366, Sys.Date() - 181, by = "1 days") %>% format("%Y%m%d") #delete old files in this seq range
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))

myReportPath <- file.path(logpath, rptfolder)
myReportPath_shared <- file.path(centralPath, scriptfolder, rptfolder)
if(!dir.exists(myReportPath_shared)){
  #shared path not found
  save_to_shared <- FALSE
}
suit_file_prefix <- "MRI_SUIT_data_as_of_"
suit_file_ext <- ".csv"
#get files in myReportPath
existing_files <- list.files(
  path = myReportPath,
  pattern = paste0('\\',suit_file_ext),
  full.names = FALSE
)
if(save_to_shared & myReportPath != myReportPath_shared){
  #check SHARED PATH files in addition to files above (if not same DIR)
  existing_files_shared <- list.files(
    path = myReportPath_shared,
    pattern = paste0('\\',suit_file_ext),
    full.names = FALSE
  )
}

### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load...log
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try appending report time to filename
    myNewFN <- paste0(report_time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = "MRI Suite Changes Exceptions : REPORT FILE SAVING ERROR",
             body = bodytext
    )
  }
}

`%notin%` <- Negate(`%in%`)


### get and save yesterday's suite info
if(okaytocontinue){
  base::message("Query and save current SUITE info (as existed yesterday)")
  my_query <- paste0(
    "
  		select
  			dateadd(DAY, -1, CURRENT_DATE) as AS_OF
  		,	S.*
  		,	CONCAT(T.SUITETYPEUSAGE,CASE WHEN T.SUITETYPEUSAGE = 'I' THEN ' (Include)' 
  				WHEN T.SUITETYPEUSAGE = 'N' THEN ' (Include Not Counted)' 
  				WHEN T.SUITETYPEUSAGE = 'E' THEN ' (Exclude)' END) AS SUITETYPEUSAGE
  		from MRI.SUIT S
  		LEFT JOIN MRI.TB_CM_SUITETYPE T ON S.SUITETYPE_MRI = T.SUITETYPEID
    "
  )
  mydata_curr <- DBI::dbGetQuery(mySfDB, my_query)
  mydata_curr_status <- check_mydf_rows(mydata_curr, 1, myReportName)
  if(mydata_curr_status[[1]] == TRUE){
    #save data to file
    myFN_curr <- paste0(suit_file_prefix, report_date, suit_file_ext)
    mySaveAs <- file.path(myReportPath, myFN_curr)
    rs <- write.table(
      x = mydata_curr, 
      file = mySaveAs,
      na = "",
      sep = ",", 
      quote = TRUE,
      row.names = FALSE, 
      col.names = TRUE
    )
    if(save_to_shared & myReportPath != myReportPath_shared){
      #current report path not same as shared, save to shared DIR
      mySaveAs <- file.path(myReportPath_shared, myFN_curr)
      rs <- write.table(
        x = mydata_curr, 
        file = mySaveAs,
        na = "",
        sep = ",", 
        quote = TRUE,
        row.names = FALSE, 
        col.names = TRUE
      )
    }
    #read data back into mydata_curr for like comparison to data from prev files
    mydata_curr <- read.table(file = mySaveAs, header = TRUE, sep = ",", dec = ".", colClasses = "character", stringsAsFactors = FALSE)
  }else{
    #send warning email that data from SUIT table not found
    okaytocontinue <- FALSE
    bodytext <- paste0(
      "<html><head></head><body>",
      "<h2>",myReportName, " - CURRENT data not found</h2>",
      "<p>",
      "The routine was unable to get current suite data for comparision ",
      "to previous data. Check database table and/or connection. </p>",
      "<br/>",
      norm_sig,
      "</body></html>"
    )
    rs <- mailsend(recipient = warn_recip,
                   subject = paste0(myReportName),
                   body = bodytext,
                   #if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   test = testing_emails, testrecipient = test_recip
    )
  }

}


### load previous suite info
if(okaytocontinue){
  base::message("Loading previous SUITE info")
  #find newest file (besides one saved above)
  prev_file <- setdiff(existing_files, myFN_curr) %>% max(.)
  if(length(prev_file) > 0){mySavedAs <- file.path(myReportPath, prev_file)}
  if((save_to_shared & myReportPath != myReportPath_shared) || length(prev_file) == 0){
    #report path not same as shared path or prev_file not found, get possible newer file from shared
    prev_file_shared <- setdiff(existing_files_shared, myFN_curr) %>% max(.)
    if(is.null(prev_file) || prev_file_shared > prev_file){
      mySavedAs <- file.path(myReportPath_shared, prev_file_shared)
    }
  }
  if(exists("mySavedAs")){
    #load file
    mydata_prev <- read.table(file = mySavedAs, header = TRUE, sep = ",", dec = ".", colClasses = "character", stringsAsFactors = FALSE)
    mydata_prev_status <- check_mydf_rows(mydata_prev, 1, myReportName)
    if(mydata_curr_status[[1]] != TRUE){
      #data NOT found in file, abort
      okaytocontinue <- FALSE
      bodytext <- paste0(
        "<html><head></head><body>",
        "<h2>",myReportName, " - no data in PREVIOUS file</h2>",
        "<p>",
        "The file containing previous suite data for comparision to the current ",
        "data was empty. The routine attempted to read in the file located ",
        "at:<br><strong>",
        mySavedAs,
        "</strong></p><br/>",
        norm_sig,
        "</body></html>"
      )
      rs <- mailsend(recipient = warn_recip,
                     subject = paste0(myReportName),
                     body = bodytext,
                     #if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                     test = testing_emails, testrecipient = test_recip
      )
    }
  }else{
    #previous file to compare not found, abort and send warning email
    okaytocontinue <- FALSE
    bodytext <- paste0(
      "<html><head></head><body>",
      "<h2>",myReportName, " - PREVIOUS data not found</h2>",
      "<p>",
      "The file containing previous suite data for comparision to the current ",
      "data was not found. The routine attempted to locate older files in the ",
      "following location(s):<br><strong>",
      paste0(c(myReportPath, myReportPath_shared), collapse = '<br>'),
      "</strong></p><br/>",
      norm_sig,
      "</body></html>"
    )
    rs <- mailsend(recipient = warn_recip,
                   subject = paste0(myReportName),
                   body = bodytext,
                   #if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   test = testing_emails, testrecipient = test_recip
    )
  }
}



### Find Suite changes (compare current to prior data)
if(okaytocontinue){
  base::message("Identifying SUITE changes")
  order_cols <- c(
    "AS_OF",
    "BLDGID",
    "SUITID",
    "SUITENO",
    "ADDRESS",
    "SUITETYPE_MRI",
    "SUITETYPEUSAGE"
  )
  as_of_col <- "AS_OF"
  suite_type_col <- "SUITETYPE_MRI"
  sort_cols <- c("BLDGID", "SUITID")
  #remove as_of_col and suite_type_col for diff comparison
  compare_cols <- order_cols[order_cols %notin% c(as_of_col,suite_type_col)]
  #subset mydata_curr and mydata_prev to columns for setdiff
  comp_curr <- mydata_curr[,compare_cols]
  comp_prev <- mydata_prev[,compare_cols]
  diffs_curr <- setdiff(comp_curr, comp_prev)
  #add current suite_type_col
  diffs_curr <- diffs_curr %>%
    dplyr::left_join(
      mydata_curr[,c(compare_cols, as_of_col, suite_type_col)],
      by = compare_cols
    )
  diffs_prev <- setdiff(comp_prev, comp_curr)
  #add previous suite_type_col
  diffs_prev <- diffs_prev %>%
    dplyr::left_join(
      mydata_prev[,c(compare_cols, as_of_col, suite_type_col)],
      by = compare_cols
    )
  #combined changes
  diffs_all <- rbind(diffs_curr, diffs_prev)
  #reorder columss to final desired order
  diffs_all <- diffs_all[,order_cols]
  #sort (as_of is already desc due to rbind above, so only sort on BLDGID and SUITID here)
  mydata <- diffs_all %>% dplyr::arrange(across(all_of(sort_cols)))
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (
      colname  = c(
        as_of_col,
        "BLDGID",
        "ADDRESS",
        "SUITETYPEUSAGE",
        "SUITETYPE_MRI"
      )
      ,                      
      width = c(
        10,
        9, 
        25,
        25,
        18
      )
      ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    myFN <- paste0("MRI_Suite_Changes", ".xlsx")
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    
    # create email
    # subset columns if desirable
    mydata_emailbody <- mydata
    mydata[] <- lapply(mydata[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata)<=30){
      bodytable <- paste0("<p>",
                          print(xtable(mydata, 
                                       #caption = paste0(myReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata)+1)
                          ),
                          #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                          html.table.attributes = "border=2 cellspacing=1",
                          type = "html",
                          caption.placement = "top",
                          include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</p>"
      )
    }
    
    bodytext <- paste0(
      "<html><head></head><body>",
      "The <b>", myReportName, "</b> data is attached. ",
      "The attached file contains ", myReportName,
      " from yesterday vs the most recent prior data (as indicated in the AS_OF column).",
      ".<br/><br/>",
      "The results are sorted by BLDG ID, SUITE ID and then by AS_OF date.<br/><br/>",
      bodytable,
      "<br/>",
      norm_sig,
      "</body></html>"
    )
    rs <- mailsend(recipient = norm_recip,
                   subject = paste0(myReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    rm(mydata)
    
  }
}


# Delete old 'existing' files no longer needed
if(okaytocontinue){
  #old_dates_delete
  #check files in main report directory
  if(length(existing_files)>0){
    for(i in 1:length(existing_files)){
      curr_fn <- existing_files[i]
      if(gsub(".*?([0-9]+).*", "\\1", curr_fn) %in% old_dates_delete){
        #remove old file
        mySavedAs <- file.path(myReportPath, curr_fn)
        unlink(mySaveAs, force = TRUE)
        Sys.sleep(1.5)
      }
    }
  }
  
  #check files in shared report directory
  if(save_to_shared & myReportPath != myReportPath_shared){
    if(length(existing_files_shared)>0){
      for(i in 1:length(existing_files_shared)){
        curr_fn <- existing_files_shared[i]
        if(gsub(".*?([0-9]+).*", "\\1", curr_fn) %in% old_dates_delete){
          #remove old file
          mySavedAs <- file.path(myReportPath_shared, curr_fn)
          unlink(mySaveAs, force = TRUE)
          Sys.sleep(1.5)
        }
      }
    }
  }
}



DBI::dbDisconnect(mySfDB)





