
library(DBI)
library(keyring)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(xtable)
library(lubridate)
library(formattable)
library(stringr)
library(tidyverse)
library(ggthemes)
library(grid)
library(gridExtra)
library(gtable)
library(scales)
library(mime)
library(googledrive)
library(googlesheets4)
library(openxlsx)
library(odbc)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241030

### 20241030 change
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### changed signature to corporate version specified by <PERSON> earlier this year

### ******** change
### moved to Snowflake DB SQL queries

### 20240702 change
### fixed unintentional regression of logpath in ******** version, want to write all to central not local directory

### ******** change
### commented out deletion of previous files in plot directory (per A.P. request to preserve)

### Version: ********
### changed logpath so that it's always saving plots to central (so Maria can access in one place)

### Version: ********
### beta version

### ******** 
### new file, alpha version


# Parameters
options(stringsAsFactors = FALSE)

#P:\steveo\R Stuff\ReportFiles\MARCOS_Monthly_Banking
query_date <- Sys.Date() #other variables adjust to only query month prior to this
#query_date <- floor_date(query_date, "month") - months(1)  #test or manual run only
myReportName <- "MARCOS Monthly Banking Trans To PDF"
scriptfolder <- "MARCOS_Monthly_Banking"
rptfolder <- "Plots"
logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
date.header.text <- paste0("Updated ", format(query_date, "%m-%d-%Y"))
report.monthyear.txt <- format(floor_date(query_date, "month") - months(1), "%B %Y")
report.yyyymm.txt <- format(floor_date(query_date, "month") - months(1), "%Y%m")
report.yymm.txt <- format(floor_date(query_date, "month") - months(1), "%y%m")
report.date.txt <- format(Sys.Date(), "%Y%m%d") #actual date routine runs
report.time.txt <- format(Sys.time(), "%H%M%S%Z") #time routine runs
sql_sdate <- format(floor_date(query_date, "month") - months(1),"%d-%b-%y")
sql_edate <- format(floor_date(query_date, "month"),"%d-%b-%y")
old_dates_delete <- seq(query_date - 56, query_date - 29, by = "1 days") %>% format("%Y%m%d")

okaytocontinue <- TRUE

store_log <- data.frame(Location = integer(), Time = character(), Uploaded = logical(), Issue=character()) #%>% 
  


# Google sheets/Drive parameters, create empty df then add rows with needed info
#gSht_auth_email <- "<EMAIL>"
gSht_auth_email <- "<EMAIL>"
#gDrv_mainURL <- 'https://drive.google.com/drive/folders/1eHkd3j2U8oo18ew68m81BSIQM2A0eNIP' #"Bank Rec's" folder
gDrv_mainURL <- 'https://drive.google.com/drive/folders/1C-859i8gzfkFAC7JUBkBKzaCmOAEkI7H' # Regionals Bank Recs
gDrv_destinationfoldername <- 'Bank Transaction Report' #sometimes it's plural for some locations, but API call is pattern match so that should be okay




# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("Maria Lotz<<EMAIL>>", "Steve Olson<<EMAIL>>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("Maria Lotz<<EMAIL>>", "Steve Olson<<EMAIL>>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("Steve Olson<<EMAIL>>")

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to shared drive instead of R/Tableau PC
  HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
}else{
  testing_pc <- FALSE
  HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")
}


#routine specific paths
plotpath <- file.path(logpath, rptfolder)
myReportPath <- file.path(logpath, rptfolder)



### define some functions ###

#********: 
###STAGE Snowflake versions###
#Sf_DB <- "STAGE_CSM_DB"
#Sf_schema <- "MOMS"
#Sf_wh <- "STAGE_DATA_ANA_WH"
##Sf_role <- "FR_STAGE_ANA_USERS"
#Sf_role <- "AR_STAGE_CONSUMPTION_RO"
#Sf_user <- key_get("SfHV", "tableau_ID_prod")
#Sf_pw <- key_get("SfHV", "tableau_PW_prod")
###PROD Snowflake versions###
Sf_DB <- "PROD_CSM_DB"
Sf_schema <- "CORPORATE"
Sf_wh <- "PROD_DATA_ANA_WH"
Sf_role <- "AR_PROD_CONSUMPTION_RW" #this should probably be AR_PROD_CONSUMPTION_RO (read-only), but that role isn't set up fully as of ********
Sf_user <- key_get("SfHV", "tableau_ID_prod")
Sf_pw <- key_get("SfHV", "tableau_PW_prod")

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role
                         #,authenticator = 'externalbrowser'
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ='America/Chicago')
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

mySchema <- Sf_schema

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}



#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}




mailsend_OLD20241030 <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, "<<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}



check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



curr_time <- function(){
  t <- Sys.time() %>% format("%m/%d/%Y %H:%M:%S %Z")
  return(t)
}


numeric_to_currency <- function(x, na.rm = FALSE) format(x, big.mark=',', nsmall = 2)


create_banktrans_PDF <- function(store_df, store_num, SaveAsFN){
  
  # create store .pdf, requires ggplot2 (part of tidyverse)
  
  pdf(file.path(plotpath, SaveAsFN), 
      height=11, width = 8.5, 
      paper = "letter")
  
  mytheme <- ttheme_minimal(
    core=list(
      bg_params = list(#fill = blues9[seq(1, 4, by = 3)],
        #fill = c("#F7FBFF", "#D6EAF8"),
        #fill = c("#F7FBFF", "#fdc200"),
        #fill = c("#F7FBFF", "#fadb6e"),
        fill = c("#F7FBFF", "#cccccc"),
        col=NA),
      fg_params = list(fontface=1)
      #fg_params = list(hjust=1, x=0.95, fontface=1) #right justify all columns
    ),
    colhead=list(
      #bg_params = list(fill = blues9[8]),
      bg_params = list(fill = "#c20031"),           
      fg_params = list(col="#fadb6e")
    ),
    rowhead = NULL,
    padding = unit(c(9, 2.5), "mm"),
    base_size = 9
  )
  
  #convert Amount and Balance columns to user friendly $ amounts
  
  
  myt <- gridExtra::tableGrob(store_df, theme = mytheme, rows = NULL)
  
  my_rows_page <- 50
  my_start_row = 1
  
  if(my_rows_page > nrow(store_df)){
    my_end_row = nrow(store_df)
  }else{
    my_end_row = my_rows_page
  }
  my_npages <- ceiling(nrow(store_df)/my_rows_page)
  title <- textGrob(paste0("Bank Transactions for #", store_num),gp=gpar(fontsize=11))
  #footnote <- textGrob("Will NOT reflect current-day transactions", x=0, hjust=0,
  #                     gp=gpar( fontface="italic"))
  padding <- unit(1.25,"line")
  
  for(i in 1:my_npages){
    my_multi <- tableGrob(store_df[my_start_row:my_end_row, ], theme = mytheme, rows = NULL)
    #edit widths so all pages are same
    my_multi$widths <- myt$widths
    
    
    my_multi <- gtable_add_rows(my_multi, 
                             heights = grobHeight(title) + padding,
                             pos = 0)
    #my_multi <- gtable_add_rows(my_multi, 
    #                         heights = grobHeight(footnote) + padding)
    #my_multi <- gtable_add_grob(my_multi, list(title, footnote),
    #                         t=c(1, nrow(my_multi)), l=c(1,2), 
    #                         r=ncol(my_multi))
    my_multi <- gtable_add_grob(my_multi, list(title),
                                t=c(1), l=c(1), 
                                r=ncol(my_multi))
    
    grid.newpage()
    grid.draw(my_multi)
    #recycle variables for next page
    my_start_row = my_end_row + 1
    if((my_rows_page + my_end_row) < nrow(store_df)){
      my_end_row = my_rows_page + my_end_row
    }else{
      my_end_row = nrow(store_df)
    }
  }
  
  dev.off()
}


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext
    )
  }
}







###Google OAuth and main folder verify
if(okaytocontinue){
  isFolder <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was for folder
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isFolder <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("folder")
    if(!isFolder){
      okaytocontinue <- FALSE
    }else{
      mySearch <- drive_ls(drv_get_main$id, type = "folder", recursive = FALSE) #dribble of drv_get_main folders
      if(nrow(mySearch)>0){
        #for(i in 1:nrow(mySearch)){
        #  temp_search <- drive_ls(path = mySearch$id[i], type = "folder", recursive = TRUE)
          #temp_search <- drive_ls(path = drv_get_main$id, type = "folder", pattern = '3701 - ', recursive = TRUE)
        #  if(i==1){
        #    myFolders <- temp_search
        #  }else{
        #    myFolders <- rbind(myFolders, temp_search)
        #  }
        #}
      }else{
        okaytocontinue <- FALSE
      }
    }
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file folder for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Folder URL resolved: ", isFolder, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Access Issue"),
             body = bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



if(okaytocontinue){
  # clear all previous LOCAL plots and report files
  if(dir.exists(plotpath)) {
    filestoremove <- dir(plotpath, full.names = TRUE)
    #REMOVE REPORT (EXCEL) FILES
    if(length(filestoremove) >  0){
      #unlink(filestoremove, recursive = FALSE, force = TRUE)
    }
  }
  if(dir.exists(plotpath)) {
    filestoremove <- dir(plotpath, full.names = TRUE)
    #REMOVE PLOT FILES
    if(length(filestoremove) >  0){
      #unlink(filestoremove, recursive = FALSE, force = TRUE)
    }
  }
}





###Get expected Bank Transactions and create .PDF for each store###
if(okaytocontinue){
  myquery <- paste0(
    "
      Select bank.id
      , bank.store_number as \"Location\"
      , to_char(bank.t_date, 'mm/dd/yy  hh12:mi AM') as \"Date\"
      , BANK.paynum as \"Emp #\"
      , upper(emp.fname)||' '||upper(emp.lname) as \"Employee\"
      , reason as \"Description\"
      , amount as \"Amount\"
      , r_date
      from ac_bank bank
      inner join corporate.hr_locations_all hla
      on lpad(bank.store_number,4,'0') = hla.location_code
      left join ab_employees emp on bank.paynum = emp.paynum
      where BANK.T_DATE >= to_date('", sql_sdate, "','dd-mon-yy')
      and BANK.T_DATE < to_date('", sql_edate, "','dd-mon-yy')
      and (hla.inactive_date is null or hla.inactive_date >= to_date('", sql_sdate, "','dd-mon-yy'))
      
      --and bank.store_number < 3510          /*TEST ONLY*/
      
      order by bank.store_number, bank.t_date
    "
  )
  #********: bank_trans <- dbGetQuery(myOracleDB, myquery)
  bank_trans <- dbGetQuery(mySfDB, myquery)
  
  curr_stores <- bank_trans$Location %>% unique()
  
  for(store in curr_stores){
    #store_email <- st.emailaddy(Location = currStNum)
    store_trans <- bank_trans[which(bank_trans$Location == store), c("Date","Employee","Description","Amount")]
    #create "Balance" column
    store_trans$Balance <- cumsum(store_trans$Amount)
    store_trans$Balance <- numeric_to_currency(store_trans$Balance)
    store_trans$Amount <- numeric_to_currency(store_trans$Amount)
    curr_FN <- paste0(report.yymm.txt, store, " Bank Transaction Report.pdf")
    store_saved <- FALSE
    store_issue <- ""
    
    create_banktrans_PDF(store_df = store_trans, store_num = store, curr_FN)
    myPDFFile <- file.path(plotpath, curr_FN)
    #push file to location Google folder
    if(file.exists(myPDFFile)){
      
      
      #check if store report folder present
      find_pattern <- paste0("name contains '", store, " - '")
      store_folder <- drive_find(q = find_pattern, q = "mimeType = 'application/vnd.google-apps.folder'", q = "trashed = false")
      mydata_status <- check_mydf_rows(store_folder, MinNumRows = 1, ReportName = myReportName)
      
      if(mydata_status[[1]]){
        #folder found, check if just one
        if(nrow(store_folder) > 1){
          #more than one folder found, log issue
          store_issue <- paste0(nrow(store_folder), " folders beginning with '", store, " - ' were found. File NOT saved, unsure which is correct.")
        }else{
          #just one folder identified, proceed
          report_folder <- drive_ls(path = store_folder$id, type = "folder", pattern = gDrv_destinationfoldername)
          mydata_status_rptfolder <- check_mydf_rows(report_folder, MinNumRows = 1, ReportName = myReportName)
          if(mydata_status_rptfolder[[1]]){
            rs_upload <- drive_put(myPDFFile, path = report_folder$id[[1]], name = curr_FN)
            mydata_status_upload <- check_mydf_rows(rs_upload, MinNumRows = 1, ReportName = myReportName)
            if(mydata_status_upload[[1]]){
              #file apparently saved
              store_saved <- TRUE
              store_issue <- rs_upload$drive_resource[[1]]$webViewLink
            }else{
              #issue saving file, log issue
              store_issue <- "Issue uploading file: Destination folder was found, but upload failed"
            }
          }else{
            #destination folder not found, log issue
            store_issue <- paste0("Issue uploading file: '", gDrv_destinationfoldername, "(s)' folder NOT found! Check for proper case, spaces. Store folder: ",store_folder$drive_resource[[1]]$webViewLink)
          }
        }
      }else{
        #folder not found
        store_issue <- paste0("Unable to locate folder beginning with '", store, " - ', check for proper case, spaces. File NOT saved.")
        okaytocontinue <- FALSE
      }
    }else{
      #local file does not exist, add to exception report
      store_issue <- "Error creating/saving .PDF (prior to upload)"
    }
    #log store results
    store_log <- store_log %>% add_row(Location = as.integer(store), Time = curr_time(), Uploaded = store_saved, Issue = store_issue)
  }

  #Save log file and email results
  #logpath
  #specify report column widths where alternate width desired
  myXLSXColWidths <- data.frame (
    colname  = c(
      "Location",
      "Time",
      "Uploaded",
      "Issue"
      #"",
    )
    ,
    width = c(
      10,
      23,
      10.5,
      if(max(nchar(na.omit(store_log[,"Issue"]))) > 78){min(120, max(nchar(na.omit(store_log[,"Issue"]))))}else{83}
    )
    ,
    stringsAsFactors = FALSE
  ) #myXLSXColWidths
  myFN <- paste0(myReportName, " - ", report.yyyymm.txt, " RESULTS.xlsx")
  mySN <- paste0(report.date.txt, ' ',report.time.txt)
  writeXLSX(dirpath = logpath, fname = myFN, sname = mySN,  RptDF = store_log, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
  myemailfiles <- file.path(logpath, myFN)
  
  result_cnt_success <- sum(store_log$Uploaded, na.rm = TRUE)
  result_cnt_fail <- length(curr_stores) - result_cnt_success
  
  
  
  
  if(result_cnt_fail > 0){
    if(result_cnt_fail > 1){
      bodytext_fail <- paste0(
        '<p><font color="red"><strong>These ', result_cnt_fail, " locations"," DID NOT upload properly:</strong><br>",
        paste0(store_log$Location[which(store_log$Uploaded == FALSE)], collapse = ", "),
        "<br>See attached file for details.</font></p>"
      )
    }else{
      bodytext_fail <- paste0(
        '<p><font color="red"><strong>Location #', 
        paste0(store_log$Location[which(store_log$Uploaded == FALSE)], collapse = ", "),
        " DID NOT upload properly! See attached file for details.</strong></font></p>"
      )
    }
    
  }else{
    bodytext_fail <- ""
  }
  
  bodytext <- paste0("<h2>", myReportName, "</h2>",
                     "<h3>", report.monthyear.txt, " Transactions</h3>",
                     bodytext_fail,
                     "</p>",
                     result_cnt_success,
                     " locations were uploaded successfully. See attached file for links to each.",
                     "</p>",
                     "<br><br>",
                     norm_sig,
                     "<br/><br/>This script located in the Tableau/R computer at: ",
                     logpath
  )
  rs <- mailsend(recipient = norm_recip,
                 subject = paste0(myReportName),
                 body = bodytext,
                 if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                 test = testing_emails, testrecipient = test_recip
  )
  myemailfiles <- NA
  
  
}







