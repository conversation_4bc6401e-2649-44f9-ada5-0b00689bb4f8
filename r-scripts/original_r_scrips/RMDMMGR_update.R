#library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(keyring)
library(DBI)
library(ROracle)

# written by <PERSON> June 2020
# this script queries the employee/store tables for
# RM, DM and Mgr info.  The results are then uploaded
# into an Oracle table for faster/easier joins.


# Version 20240925

### 20240925 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### this includes new mailsend function and parameters
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present


### 20240325 change:
### added replace to trap when email gets entered into AC_EMAIL with a space or lf character

### 20240111 change:
### switched from odbc to R<PERSON><PERSON><PERSON> with associated data checks
### added query for new columns CIT<PERSON>, LOC_NAME, LOC_INFO15_ALPHA and ACQUIRED

### 20220606 change:
### updated mailsend to use keyring

### 20220225 change:
### added join to try ac_email table for email for new promotions
### previously those would populate a day later when payroll
### processes were run again and email pulled into ab_employees table

### 20210819 change:
### added lat (latitude) and lon (longitude) columns

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)


# Parameters
myReportName <- "RM_DM_MGR Update Oracle"
logpath <- file.path("C:","Users","table","Documents","ReportFiles","HV_RMDMMGR")
query.date <- format(Sys.Date(), "%d-%b-%y")
#next line is for testing or loading previous time-frames only, normally should be commented out
#query.date <- '11-OCT-19'
#query.daynum <- as.integer(format(as.Date(query.date, "%d-%b-%y"),"%w"))
okaytocontinue <- TRUE

#myTableName <- "STEVE.SO_RM_DM_MGR"

#mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve"))

#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ="GMT")
#Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
myOracleDB_deanna <- dbConnect(drv, username = "deanna", password =  key_get("Oracle", "deanna"), dbname = connect.string)
mySchema <- "STEVE"
myTable <- "SO_RM_DM_MGR"
myTableName <- paste(mySchema, myTable, sep = ".")



# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

#20240925: new email function and parameters
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}



#--Query existing table data to compare to new run--#
if(okaytocontinue){
  myquery <- paste0("select 
                      *
                    from ", myTableName
  )
  #priordata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  priordata <- dbGetQuery(myOracleDB, myquery)
}


#--Query new data--#
if(okaytocontinue){
  myquery <- paste0(" 
                    select 
                    stemp.store,
                    min(case when pos.sub_categories like '%,REG_RPT_INC,%' then
                    initcap(emp.fname)
                    end) as RM_Fname,
                    min(case when pos.sub_categories like '%,REG_RPT_INC,%' then
                    initcap(emp.lname)
                    end) as RM_Lname,
                    min(case when pos.sub_categories like '%,REG_RPT_INC,%' then
                    case when emp.fname is not NULL
                    then initcap(emp.fname)||' '||initcap(emp.lname)
                    end
                    end) as RM_Fullname,
                    min(case when pos.sub_categories like '%,REG_RPT_INC,%' then
                    replace(replace(nvl(emp.email, ac_email.email), chr(32)), chr(10))
                    end) as RM_Email,
                    min(case when pos.sub_categories like '%,REG_RPT_INC,%' then
                    emp.phone
                    end) as RM_Phone,
                    min(case when pos.sub_categories like '%,DIST_RPT_INC,%' then
                    initcap(emp.fname)
                    end) as DM_Fname,
                    min(case when pos.sub_categories like '%,DIST_RPT_INC,%' then
                    initcap(emp.lname)
                    end) as DM_Lname,
                    Min(case when pos.sub_categories like '%,DIST_RPT_INC,%' then
                    case when emp.fname is not NULL
                    then initcap(emp.fname)||' '||initcap(emp.lname)
                    end
                    end) as DM_Fullname,
                    min(case when pos.sub_categories like '%,DIST_RPT_INC,%' then
                    replace(replace(nvl(emp.email, ac_email.email), chr(32)), chr(10))
                    end) as DM_Email,
                    min(case when pos.sub_categories like '%,DIST_RPT_INC,%' then
                    emp.phone
                    end) as DM_Phone,  
                    min(case when pos.sub_categories like '%,MGR_HISTORY,%' then
                    initcap(emp.fname)
                    end) as MGR_Fname,
                    min(case when pos.sub_categories like '%,MGR_HISTORY,%' then
                    initcap(emp.lname)
                    end) as MGR_Lname,
                    Min(case when pos.sub_categories like '%,MGR_HISTORY,%' then
                    case when emp.fname is not NULL
                    then initcap(emp.fname)||' '||initcap(emp.lname)
                    end
                    end) as MGR_Fullname,
                    min(case when pos.sub_categories like '%,MGR_HISTORY,%' then
                    replace(replace(nvl(emp.email, ac_email.email), chr(32)), chr(10))
                    end) as MGR_Email,
                    min(case when pos.sub_categories like '%,MGR_HISTORY,%' then
                    emp.phone
                    end) as MGR_Phone,
                    min(case when pos.sub_categories like '%,REG_RPT_INC,%' then
                    emp.paynum
                    end) as RM_PAYNUM,
                    min(case when pos.sub_categories like '%,DIST_RPT_INC,%' then
                    emp.paynum
                    end) as DM_PAYNUM,
                    min(case when pos.sub_categories like '%,MGR_HISTORY,%' then
                    emp.paynum
                    end) as MGR_PAYNUM,
                    ll.lat as LAT,
                    ll.lon as LON,
                    hla.TOWN_OR_CITY AS CITY,
                    hla.DESCRIPTION AS LOC_NAME,
                    REGEXP_SUBSTR(hla.LOC_INFORMATION15, '[[:alpha:]]+') as LOC_INFO15_ALPHA,
                    case
                        when REGEXP_SUBSTR(hla.LOC_INFORMATION15, '[[:alpha:]]+') = 'HF' then COALESCE(HF_MATCH.ACQUIRED,0)
                    end as ACQUIRED
                    
                    
                    from ab_store_employees stemp
                    inner join ab_employees emp
                    on stemp.paynum = emp.paynum
                    inner join famv_payroll_position pos
                    on emp.position = pos.position
                    and (pos.sub_categories like '%,REG_RPT_INC,%'
                    or pos.sub_categories like '%,DIST_RPT_INC,%'
                    or pos.sub_categories like '%,MGR_HISTORY,%')
                    left join steve.lat_lon ll
                    on stemp.store = ll.store
                    left join ac_email
                    on emp.paynum = ac_email.paynum
                    left join hr.hr_locations_all hla
                    on lpad(stemp.store, 4, '0') = hla.location_code
                    
                    left join
                    (
                        select 
                            hf as STORE
                        ,   1 as ACQUIRED
                        from ac_store_match 
                        where hf != mp and hf is not null
                    ) HF_MATCH
                    on stemp.STORE = HF_MATCH.STORE
                    
                    where emp.status = 'A'
                    group by stemp.store,
                        ll.lat,
                        ll.lon,
                        hla.TOWN_OR_CITY,
                        hla.DESCRIPTION,
                        hla.LOC_INFORMATION15,
                        REGEXP_SUBSTR(hla.LOC_INFORMATION15, '[[:alpha:]]+'),
                        case
                            when REGEXP_SUBSTR(hla.LOC_INFORMATION15, '[[:alpha:]]+') = 'HF' then COALESCE(HF_MATCH.ACQUIRED,0)
                        end
                    order by stemp.store
                    "
                    )
  #mydata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  mydata <- dbGetQuery(myOracleDB, myquery)
}



if(okaytocontinue){
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName, "
    "
  )
  rs_sel <- dbSendQuery(myOracleDB, myquery_select)
  select_cnt <- dbFetch(rs_sel, n = -1)
  dbClearResult(rs_sel)
  if(nrow(mydata) <= 5){
    #not many rows, probable error, email warning and don't populate Oracle
    # create body of warning email
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       "query results. The query only returned the following: <br/><br/>",
                       print(mydata),
                       "<br/><b>The routine should attempt to run again if this is a temporary ",
                       " expected result that will resolve on it's own. ",
                       "Otherwise, make adjustments to the RMDMMGR_update.r script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             "RM_DM_MGR Update Issue: Oracle Query Rows",
             bodytext
    )
  }else{
    # Trunc Oracle table
    myquery_delete <- paste0("delete from ", myTableName)
    
    rs_del <- dbSendQuery(myOracleDB, myquery_delete)
    if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
      #delete failed
      warning("dubious deletion -- rolling back transaction")
      dbRollback(myOracleDB)
      
    }else{
      
      
      rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
      
      #compare sum of the STORE column of the data frame to Oracle to ensure all rows inserted
      myquery <- paste0(
        "select sum(STORE)
      from ", myTableName
      )
      oracle_checksum <- dbGetQuery(myOracleDB, myquery, stringsAsFactors = FALSE)
      
      mydata_checksum <- sum(as.numeric(mydata$STORE),na.rm=TRUE)
      
      if(round(oracle_checksum[[1]],2) != round(mydata_checksum,2)){
        #send warning that sums are different
        # create body of warning email
        bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                           "have been an error in the ", myTableName,
                           " Oracle load. The sum of the query STORE column was: <br/>",
                           mydata_netsales, "<br/><br/>",
                           "The sum of the ", myTableName, " table is: <br/>",
                           oracle_checksum, "<br/><br/>",
                           "The query is contained in the RMDMMGR_update.r script on the ",
                           "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                           warn_sig,
                           sep = ""
        )
        #send mail
        mailsend(warn_recip,
                 "RM_DM_MGR Update Issue: Mis-match of query sum vs. Oracle load",
                 bodytext
        )
        
      }else{
        #send completion email
        bodytext <- paste0("This is an automated email to inform you that it appears the ",
                           "<b>RM_DM_MGR update</b> routine has ",
                           "completed and results should now be available in the ", 
                           myTableName, " table.<br/> <br/>",
                           warn_sig)
        #send mail
        #mailsend(norm_recip,
        #         "RM_DM_MGR Update Status: COMPLETE",
        #         bodytext
        #)
      }
    }
  }
}

DBI::dbDisconnect(myOracleDB)