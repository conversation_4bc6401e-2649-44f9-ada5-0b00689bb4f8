#library(rJava) #********: not using mailR any longer
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #********: replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(keyring)
library(utils)
#library(RODBC) #********: replaced by DBI and odbc
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20240110

### 20240110 change:
### excepted 'HV3RD' and 'HVCORP' building IDs from reporting per Rachael H. request

### 20221006 change:
### added clause to SQL to exclude 'ACQUIS' bldgid from this check.

### 20220606 change:
### updated mailsend to use keyring

### 20220504 change:
### added Excel filename variable to beginning of script, added exclusion for ROVER and OHCANN building IDs

### 20220429 change:
### added keyring package and added concat_ws to report ALL issues for a building

### 20220414 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

scriptfolder <- "LEGACY_MRI_Exceptions-Owner-Landlord"
myReportName <- "MRI Owner-Landlord Exceptions"
msg_text <- paste0("Routine Starting: ", myReportName)
base::message(msg_text)
rptFN <- paste0("MRI_Ownership-Landlord_Exceptions", ".xlsx")
myReportCriteria <- paste0("<p><b>Criteria for exceptions:</b><ul>",
                           "<li>Owner ID is NULL (missing)</li>",
                           "<li>Landlord ID is NULL</li>",
                           "<li>Landlord ID <> Owner ID</li>",
                           "<li>Owner NAME is NULL</li>",
                           "<li>Landlord NAME is NULL</li>",
                           "<li>Landlord NAME <> Owner NAME</li>",
                           "<li>Rented location where Owner ID <> \"RENTED\"</li>",
                           "<li>Ownership PCT > 100%</li>",
                           "<li>Building ID is <b>NOT</b> 'ACQUIS'</li>",
                           "</ul></p>"
                           )


#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>","<EMAIL>")
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- logpath

### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load...log
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try appending report time to filename
    myNewFN <- gsub(report.startdate, paste0(report.startdate,"-",report.time), myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
}






### Find Insurance Exceptions and email Excel file with results if applicable
if(okaytocontinue){
  
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery_exceptions <- paste0(
    "
      SELECT /* Snowflake version */
      	array_to_string(
      		array_construct_compact(
      			CASE WHEN (rtrim(LLRD.LLRDID) IS NOT NULL AND trim(MYGOWN.OWNERID) IS NULL) THEN 'Owner ID is NULL' END,
	        	CASE WHEN (rtrim(LLRD.LLRDID) IS NULL AND trim(MYGOWN.OWNERID) IS NOT NULL) THEN 'Landlord ID is NULL' END,
	        	CASE WHEN (rtrim(LLRD.LLRDID) <> trim(MYGOWN.OWNERID)) THEN 'Landlord ID <> Owner ID' END,
	        	CASE WHEN (rtrim(LLRD.LLRDNAME) IS NOT NULL AND trim(GNAM.NAME) IS NULL) THEN 'Owner NAME is NULL' END,
	            CASE WHEN (rtrim(LLRD.LLRDNAME) IS NULL AND trim(GNAM.NAME) IS NOT NULL) THEN 'Landlord NAME is NULL' END,
	        	CASE WHEN (rtrim(LLRD.LLRDNAME) <> trim(GNAM.NAME)) THEN 'Landlord NAME <> Owner NAME' END,
	        	CASE WHEN (TRY_CAST(BLDG.BLDGID AS INT) IS NULL AND (Rtrim(MYGOWN.OWNERID) IS NULL OR Rtrim(MYGOWN.OWNERID) <> 'RENTED') AND BLDG.BLDGID <> 'ROVER' )
	        		THEN 'Rented location where Owner ID <> \"RENTED\"' END, /* Escape double quote in R using \" */
	        	CASE WHEN (MYGOWN.PCT + IFNULL(ALTGOWN.ALT_PCT,0) > 100) THEN 'Ownership PCT > 100%' END
      		)
      		, '; ') AS ISSUE
      ,	BLDG.BLDGID AS BLDGID
      ,	CASE 
      		WHEN Rtrim(LLRD.LLRDID) IS NOT NULL AND Rtrim(MYGOWN.OWNERID) IS NULL THEN 'Yes'
      		WHEN Rtrim(LLRD.LLRDID) IS NULL AND Rtrim(MYGOWN.OWNERID) IS NOT NULL THEN 'Yes'
      		WHEN Rtrim(LLRD.LLRDID) <> Rtrim(MYGOWN.OWNERID) THEN 'Yes'
      		ELSE 'No' END AS \"Landlord ID different than Owner ID\"
      ,	CASE 
      		WHEN Rtrim(LLRD.LLRDNAME) IS NOT NULL AND Rtrim(GNAM.NAME) IS NULL THEN 'Yes'
      		WHEN Rtrim(LLRD.LLRDNAME) IS NULL AND Rtrim(GNAM.NAME) IS NOT NULL THEN 'Yes'
      		WHEN Rtrim(LLRD.LLRDNAME) <> Rtrim(GNAM.NAME) THEN 'Yes'
      		ELSE 'No' END AS \"Landlord NAME different than Owner NAME\"
      ,	LLRD.LLRDID AS \"Landlord ID\"
      ,	Rtrim(LLRD.LLRDNAME) AS \"Landlord NAME\"
      ,	Rtrim(LLRD.CMPYID) AS \"Landlord CMPYID\"
      ,	Rtrim(MYGOWN.OWNERID) AS \"Owner ID (Entity)\"
      ,	Rtrim(GNAM.NAME) AS \"Owner NAME\"
      ,	MYGOWN.PCT AS \"Owner PCT\"
      ,	MYGOWN.PRIMARYOWN AS \"Owner PRIMARY\"
      ,	MYGOWN.BEGPD AS \"Owner BEGPD\"
      ,	MYGOWN.ENDPD AS \"Owner ENDPD\"
      ,	ALTGOWN.MULTI_OWN_PCT AS \"Multiple Owners-PCT\"
      ,	MYGOWN.PCT + IFNULL(ALTGOWN.ALT_PCT,0) AS \"Total Owned PCT\"
      FROM MRI.BLDG
      LEFT JOIN MRI.LLRD ON BLDG.LLRDID = LLRD.LLRDID
      LEFT JOIN MRI.ENTITY ON BLDG.ENTITYID = ENTITY.ENTITYID
      
      LEFT JOIN
      (/* PRIMARY OWNER */
      	SELECT ENTITYID
      	,	OWNERID
      	,	BEGPD
      	,	ENDPD
      	,	PRIMARYOWN
      	,	PCT
      	,	LASTDATE
      	FROM
      	(
      	  SELECT O.ENTITYID
      	  ,	O.OWNERID
      	  ,	O.BEGPD
      	  ,	O.ENDPD
      	  ,	O.PRIMARYOWN
      	  ,	O.PERCENT AS PCT
      	  ,	O.LASTDATE
      	  ,	RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.BEGPD, O.LASTDATE DESC) AS RANK /* RANK OWNERSHIP INTERESTS SINCE MRI DOESN'T FULLY VALIDATE ENTRIES */
      	  FROM MRI.GOWN O
      	  WHERE (O.BEGPD = 
      		  (
      			  SELECT MAX(I.BEGPD)
      			  FROM MRI.GOWN I
      			  WHERE I.ENTITYID = O.ENTITYID
      				  AND I.BEGPD <= TO_CHAR(CURRENT_DATE, 'yyyyMM')
      
      		  )
      	  OR (
      			  SELECT MAX(I.BEGPD)
      			  FROM MRI.GOWN I
      			  WHERE I.ENTITYID = O.ENTITYID
      		  ) IS NULL
      	  )
      	  AND (O.ENDPD IS NULL OR O.ENDPD >= TO_CHAR(CURRENT_DATE, 'yyyyMM'))
      	) RANKED
      	WHERE RANK = 1
      ) MYGOWN
      ON ENTITY.ENTITYID = MYGOWN.ENTITYID
      LEFT JOIN MRI.GNAM ON MYGOWN.OWNERID = GNAM.OWNERID
      
      LEFT JOIN
      (/* ALTERNATE MULTIPLE OWNERS */
      	SELECT ENTITYID
      	,	LISTAGG( CONCAT(RANKED.OWNERID,' - ',RANKED.PCT,'PCT'),'; ') AS MULTI_OWN_PCT
      	,	SUM(RANKED.PCT) as ALT_PCT
      	FROM
      	(
      	  SELECT O.ENTITYID
      	  ,	O.OWNERID
      	  ,	O.BEGPD
      	  ,	O.ENDPD
      	  ,	O.PRIMARYOWN
      	  ,	O.PERCENT AS PCT
      	  ,	O.LASTDATE
      	  ,	RANK() OVER (PARTITION BY O.ENTITYID ORDER BY O.PRIMARYOWN DESC, O.PERCENT DESC, O.BEGPD, O.LASTDATE DESC) AS RANK /* RANK OWNERSHIP INTERESTS SINCE MRI DOESN'T FULLY VALIDATE ENTRIES */
      	  FROM MRI.GOWN O
      	  WHERE (O.BEGPD = 
      		  (
      			  SELECT MAX(I.BEGPD)
      			  FROM MRI.GOWN I
      			  WHERE I.ENTITYID = O.ENTITYID
      				  AND I.BEGPD <= TO_CHAR(CURRENT_DATE, 'yyyyMM')
      
      		  )
      	  OR (
      			  SELECT MAX(I.BEGPD)
      			  FROM MRI.GOWN I
      			  WHERE I.ENTITYID = O.ENTITYID
      		  ) IS NULL
      	  )
      	  AND (O.ENDPD IS NULL OR O.ENDPD >= TO_CHAR(CURRENT_DATE, 'yyyyMM'))
      	) RANKED
      	WHERE RANK >= 1
      	GROUP BY ENTITYID
      	HAVING COUNT(RANK) > 1
      ) ALTGOWN
      ON ENTITY.ENTITYID = ALTGOWN.ENTITYID
      
      WHERE (BLDG.INACTIVE <> 'Y' OR BLDG.INACTIVE IS NULL)
        AND RTRIM(BLDG.BLDGID) not in ('ACQUIS','HV3RD','HVCORP')
      	AND
      	( /* ID or NAME mismatches */
      		(Rtrim(LLRD.LLRDID) IS NOT NULL AND trim(MYGOWN.OWNERID) IS NULL)
      		OR
      		(Rtrim(LLRD.LLRDID) IS NULL AND trim(MYGOWN.OWNERID) IS NOT NULL)
      		OR
      		(Rtrim(LLRD.LLRDID) <> trim(MYGOWN.OWNERID))
      		OR
      		(Rtrim(LLRD.LLRDNAME) IS NOT NULL AND trim(GNAM.NAME) IS NULL)
      		OR
      		(Rtrim(LLRD.LLRDNAME) IS NULL AND trim(GNAM.NAME) IS NOT NULL)
      		OR
      		(Rtrim(LLRD.LLRDNAME) <> trim(GNAM.NAME))
      		OR
      		/* ALPHA BLDGID and Owner <> 'RENTED' */
      		(TRY_CAST(BLDG.BLDGID AS INT) IS NULL AND (Rtrim(MYGOWN.OWNERID) IS NULL OR Rtrim(MYGOWN.OWNERID) <> 'RENTED') AND BLDG.BLDGID <> 'ROVER' )
      		OR
      		(MYGOWN.PCT + IFNULL(ALTGOWN.ALT_PCT,0) > 100)
      	)
      	AND UPPER(BLDG.BLDGID) NOT IN ('ROVER','OHCANN')
      ORDER BY ISSUE, BLDGID
    "
  )
  #********: mydata <- sqlQuery(mySSdb, myquery_exceptions, stringsAsFactors = FALSE)
  mydata <- dbGetQuery(mySfDB, myquery_exceptions)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("ISSUE",
                                                "BLDGID",
                                                "Landlord NAME",
                                                "Landlord CMPYID",
                                                "Owner NAME",
                                                "Owner PCT",
                                                "Owner PRIMARY",
                                                "Owner BEGPD",
                                                "Owner ENDPD",
                                                "Multiple Owners-PCT",
                                                "Total Owned PCT"
                                                )
                                   ,
                                   width = c(31,
                                             8.5,
                                             34,
                                             8.5,
                                             34,
                                             7.5,
                                             9.5,
                                             8.5,
                                             8.5,
                                             17.5,
                                             7.5
                                             )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    mydata_emailbody <- mydata[,1:2]
    if(nrow(mydata_emailbody)<=30){
      bodytable <- paste0("<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)),
                                #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                                html.table.attributes = "border=2 cellspacing=1",
                                type = "html",
                                caption.placement = "top",
                                include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</p>"
      )
    }
    bodytext <- paste0("<p><b>", this_ReportName, "</b>",
                       "</p>",
                       myReportCriteria,
                       "<p>The info below contains MRI data from yesterday that may need updating. <b>See attached Excel file for more details.</b> ",
                       "</p>",
                       bodytable,
                       "<br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   inline = TRUE,
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}




