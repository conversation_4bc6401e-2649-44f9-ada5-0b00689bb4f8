library(RODBC)
library(xtable)
library(data.table)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


#Written 2/3/2021 by <PERSON>
#(based on VET_Daily_Reports.r script)
#this routine queries Oracle and creates needed Monday Reporting
#files each week.
#

# Version 20241001

### 20241001 change:
### added test parameters for mailsend() calls and myReportName to beginning of script

### 20240916 change:
### changed signature HTML "img src" to use published HVLTD logos instead of local inline file, this IS NOT a full signature switch
### converted from mailR package (SMTP), to gmailr (OAuth-GMail API) ahead of 20240930 SMTP deprecation in GMail
### converted message() (base function) to explicit base::message() to avoid conflict with gmailr

### 20240111 change:
### added LOC_NAME and ACQUIRED columns to 'RMDMMGR' Google sheet

### 20230612 change: added Sean to warn_recip to alert him to possible reporting issues

### 20230508 change:
### revised method of compiling mgr changes data


### 20230420 change:
### fixed bug where paynum for new GM wasn't being included in Mgr Changes

### 20230414 change:
### add GM paynum to RMDMMGR Google sheet and manager changes emails per Sean Coyle request

### 20221229 change:
### changed daily managers changes recipient from Sean Coyle <NAME_EMAIL> group
### and removed email references to Family Vet

### 20220606 change:
### updated mailsend to use keyring

### 20220218 chenage:
### change Marco's Mgr Moves recip to sean.coyle for FMX changes at Mgr level

### 20210819 change:
### bug fix when RMDMMGR sheet previously empty

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)

### 20210713 change
### prevent decimals in store number in the changes email by use of digits parameter

### 20210707 change:
### added temp email of Mgr change results and scheduled in Task Scheduler

### 20210219 change:
### modified body of emails to add email

### 20210209 change:
### minor bug fixes to wording/emails

### 20210203 change:
### converted Ecomm PPC to VET reports
### staff added and staff terminated

### 20210111 changes:
### altered for a daily report of PPC collectible sales
### that will send a list of those to Derek Dye for fulfillment

### 20201026 changes:
### updated e-comm T-Shirt routine to include item master names
### when product_id != 0

### 20200915 changes:
### added e-com T-Shirt order report (with email to Derek Dye)

### 20200707 changes:
### slight change to inven_status check

query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- query.date <- format(as.Date("22-Jun-20","%d-%b-%y"),"%d-%b-%y")
#make .startdate and .enddate to always be previous Monday-Sunday
#query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y") - 7, "week", start.on.monday = TRUE)), "%d-%b-%y")
#query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)), "%d-%b-%y")
#20210111 two line above replaced with new lines below
query.startdate <- query.date %>% as.Date("%d-%b-%y") %>% -1 %>% format("%d-%b-%y")
query.enddate <- query.date %>% as.Date("%d-%b-%y") %>% -0 %>% format("%d-%b-%y")

email.enddate <- query.enddate %>% as.Date("%d-%b-%y") %>% -1 %>% format("%m/%d/%y")
report.startdate <- query.startdate %>% as.Date("%d-%b-%y") %>% format("%y%m%d")  # YYMMDD format of date for saves of report files
report.enddate <- query.enddate %>% as.Date("%d-%b-%y") %>% format("%y%m%d")  # YYMMDD format of date for saves of report files
report.enddateCBD <- query.enddate %>% as.Date("%d-%b-%y") %>% -1 %>% format("%y%m%d")  # YYMMDD format of date for saves of report files
report.currWE <- paste0("WE ",
                        format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) + 7, "%Y-%m-%d")
)# WE YYYY-MM-DD format of date for saves of CURRENT week ending report files
report.removestart <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 49, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
report.removeend <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 35, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
report.time <- format(Sys.time(), "%H%M%S")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
report.hour <- lubridate::hour(Sys.time())



emailnote_Corp.querydate <- "02-JUN-20"
emailnote_Corp <- paste0(
  '<p><span style="color: #0000ff;">',
  "Desired message here. ",
  "</span></p>"
)
# Check if email note period matches report period
if(toupper(emailnote_Corp.querydate) == toupper(query.date)){
  attachemailnote_Corp <- TRUE
}else{
  attachemailnote_Corp <- FALSE
}



myReportName <- "MARCOS Dail yReports"
logname <- "MARCOSDailyReports-Log.csv"

logpath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Daily_Reports")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")

rptpath_central_STEVE <- file.path("//*************","public","steveo")
#rptpath_central_STEVE <- file.path("//*************","public","steveo","monday_reports","test")

rptpath_central_MONDAY <- file.path("//*************","public","steveo","monday_reports")
#rptpath_central_MONDAY <- file.path("//*************","public","steveo","monday_reports","test")

rptpath_central_STORESUM <- file.path("//*************","public","steveo","Store_Summaries_Weekly")
#rptpath_central_STORESUM <- file.path("//*************","public","steveo","Store_Summaries_Weekly","test")

rptpath_central_CBD <- file.path("//*************","sales_marketing","Tableau Data Files","CBD","CBD Conversant")
#rptpath_central_CBD <- file.path("//*************","sales_marketing","Tableau Data Files","CBD","CBD Conversant","test")
#W:\Tableau Data Files\CBD\CBD Conversant

rptpath_MARCOS_emails <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Daily_Reports","Email_Addys")

#gsheet.tokenpath  <- file.path("C:","Users","table",".R","gargle","gargle-oauth")


test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){

  # Steve PC testing paths, replace above when testing_pc is TRUE
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","MARCOS_Daily_Reports")
  rptpath_MARCOS_emails <- file.path("//*************","public","steveo","R Stuff","ReportFiles","MARCOS_Daily_Reports","Email_Addys")
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  #gsheet.tokenpath  <- file.path("C:","Users","Steve",".R","gargle","gargle-oauth")
  
}

okaytocontinue <- TRUE
myfilescreated <- c('Files Created:')
orig_wd <- getwd()
mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve"))

# email parameters: recipient(s) of warning emails and signatures

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

#warn_recip <- c("<EMAIL>","<EMAIL>")
warn_recip <- c("<EMAIL>","<EMAIL>")
norm_recip <- c("<EMAIL>")
corp_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Sr. Analytics Manager<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_st_from <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                       "Sr. Analytics Mgr.<br/>",
                       "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                       "2500 Lehigh Ave.<br/>",
                       "Glenview, IL 60026<br/>",
                       "Ph: 847/904-9043<br/></span></font>")

test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>","<EMAIL>")

if(file.exists(HVSigLogopath)){
  #append signature logo
  #(HVLTD Corp with Brands)
  sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
  if(exists("norm_st_from")){norm_st_from <- paste0(warn_sig, "<br/>", sig_image_src)}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
}


### define some functions ###

mailsend <- function(
    recipient, subject, body, attachment = NULL, inline = FALSE, 
    sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  #attach inline images
  
  gmailr::gm_send_message(msg)
}

writelog <- function(LogTable){
  fn <- file.path(logpath, logname)
  write.csv(LogTable, file = fn, row.names=FALSE)
}


writeCSVreport <- function(dirpath, fname, RptDF, colnames = TRUE){
  myFN <- file.path(dirpath, fname)
  mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(RptDF, myFN, sheetName=mySN, row.names=FALSE, showNA=FALSE)
  }else{
    #try appending report time to filename
    myNewFN <- gsub(report.startdate, paste0(report.startdate,"-",report.time), myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      #write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>MARCOS Daily Reports</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>MARCOS Daily Reports</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             "MARCOS Daily Reports Issue: REPORT FILE SAVING ERROR",
             bodytext,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


writeTXTreport <- function(dirpath, fname, RptDF, delim, colnames = TRUE){
  myFN <- file.path(dirpath, fname)
  mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath) & file.opened(myFN) == FALSE) {
    #save file
    write_delim(RptDF, path = myFN, delim = delim, na="", col_names = colnames)
    #write_excel_csv(RptDF, myFN, na="")
    #write.xlsx(RptDF, myFN, sheetName=mySN, row.names=FALSE, showNA=FALSE)
  }else{
    #try appending report time to filename
    myNewFN <- gsub(report.startdate, paste0(report.startdate,"-",report.time), myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      write_delim(RptDF, path = myFN, delim = delim, na="", col_names = colnames)
      #write_excel_csv(RptDF, myNewFN, na="")
      #write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>MARCOS Daily Reports</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>MARCOS Daily Reports</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             "MARCOS Daily Reports Issue: REPORT FILE SAVING ERROR",
             bodytext,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



moveme <- function (invec, movecommand) {
  movecommand <- lapply(strsplit(strsplit(movecommand, ";")[[1]], 
                                 ",|\\s+"), function(x) x[x != ""])
  movelist <- lapply(movecommand, function(x) {
    Where <- x[which(x %in% c("before", "after", "first", 
                              "last")):length(x)]
    ToMove <- setdiff(x, Where)
    list(ToMove, Where)
  })
  myVec <- invec
  for (i in seq_along(movelist)) {
    temp <- setdiff(myVec, movelist[[i]][[1]])
    A <- movelist[[i]][[2]][1]
    if (A %in% c("before", "after")) {
      ba <- movelist[[i]][[2]][2]
      if (A == "before") {
        after <- match(ba, temp) - 1
      }
      else if (A == "after") {
        after <- match(ba, temp)
      }
    }
    else if (A == "first") {
      after <- 0
    }
    else if (A == "last") {
      after <- length(myVec)
    }
    myVec <- append(temp, values = movelist[[i]][[1]], after = after)
  }
  myVec
}



file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}


queryfailure.mailsend <- function(recip, reportname){
  #send warning email
  
  # create body of warning email
  bodytext <- paste0("This is an automated email to inform you that it appears the main Oracle query ",
                     "needed for the <b>", reportname, "</b> report failed in the MARCOS Daily Reports script.<br/><br/>",
                     print(xtable(MyErrorLog, 
                                  caption = paste0("MARCOS Daily Reports Log (", report.time.txt, ")")),
                           align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                           html.table.attributes = "border=2 cellspacing=1",
                           type = "html",
                           caption.placement = "top",
                           include.rownames=FALSE),
                     warn_sig
  )
  mailsend(recip,
           paste0("MARCOS Daily Reports Issue: ", toupper(reportname), " QUERY FAILURE"),
           bodytext,
           inline = TRUE,
           test = testing_emails, testrecipient = test_recip
  )
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




### check if log present/up-to-date ###
NewErrorLog <- FALSE
MARCOS_Sub_Run <- FALSE
if(file.exists(file.path(logpath, logname)) ) {
  MyErrorLog <- read.csv(file = file.path(logpath, logname), sep=",", stringsAsFactors = FALSE)
  if(file.opened(file.path(logpath, logname))){
    okaytocontinue <- FALSE
    base::message("Log file open, ABORTING!")
  }else{
    #not open, but file.opened() wrote over file, rewrite
    writelog(MyErrorLog)
  }
  #check if log is from prior day, is so replace values with default starting values
  #if( MyErrorLog[1,"QUERY_DATE"] != query.date ){
  #if( as.Date(cut(as.Date(MyErrorLog[1,"QUERY_DATE"], "%d-%b-%y"), "week", start.on.monday = TRUE)) != 
  #    as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) ){
  # 20210111 two lines above replaced with lines below
  if( as.Date(MyErrorLog[1,"QUERY_DATE"], "%d-%b-%y") != as.Date(query.date, "%d-%b-%y") ){
      #log is from previous DAY, replace with new default values
    NewErrorLog <- TRUE
  }else{
    if(MyErrorLog[1,"PROGRESS"] == 'COMPLETE'){
      # previous run complete, log orders found for check for new orders
      okaytocontinue <- FALSE
      base::message("Previous run completed, aborting.")
      MARCOS_Sub_Run <- TRUE
      MARCOS_Prev_ORDERCNT <- as.integer(MyErrorLog[1,"MARCOS_ORDERCNT"])
    }
  }
} else {
  # log not found, create new log values
  NewErrorLog <- TRUE
}
if( NewErrorLog ) {
  MyErrorLog <- data.frame(QUERY_DATE = query.date, 
                           GSHT_STATUS = 'NO LOG FILE',
                           #DS_STATUS = 'NO LOG FILE',
                           QUERY_STATUS = 'NO LOG FILE',
                           PROCESSING_STATUS = 'NA',
                           MARCOS_ORDERCNT = '0',
                           PROGRESS = 'NO LOG FILE',
                           stringsAsFactors = FALSE)
}


# check google sheet status
if(okaytocontinue){
  
  MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  writelog(MyErrorLog)
  
  #sheets_auth(email = "<EMAIL>")
  #above was deprecated as of googlesheets4 0.2.0
  
  
  #drive_auth(email = "<EMAIL>")
  #x <- drive_get(as_id("1BSF8VP_v0b_DSh-_EncxSC9_bUKxOtxS5mJtGi7vy60"))
  
  
  gs4_auth(email = "<EMAIL>")
  
  
  gSht_get <- gs4_get('1BSF8VP_v0b_DSh-_EncxSC9_bUKxOtxS5mJtGi7vy60')
  #test copy: https://docs.google.com/spreadsheets/d/1mGXz-pnULDHjRbBvJURuOzE3rogxOHQCpMeJY1_fgXU/edit#gid=0
  #gSht_get <- gs4_get('1mGXz-pnULDHjRbBvJURuOzE3rogxOHQCpMeJY1_fgXU')
  
  
  
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH OKAY")
    MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    writelog(MyErrorLog)
    #read data in from desired sheet
    #gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RMDMMGR")
  }else{
    MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    writelog(MyErrorLog)
    okaytocontinue <- FALSE
  }
  
}



# RM, DM, Mgr Oracle to Google
### Download prior version of Google sheet (for checking changes later) and update to current Oracle info
if(okaytocontinue & MyErrorLog[1,"GSHT_STATUS"] == "OAUTH OKAY" ){
  
  #report specific parameters
  myReportName <- "RMDMMGR Update"
  MyErrorLog[1,"PROGRESS"] <- "QUERY STATUS"
  MyErrorLog[1,"QUERY_STATUS"] <- paste0("STARTING ", myReportName)
  myemailfiles <- NA
  
  gSht_RMDMMGR_get <- gs4_get('1BSF8VP_v0b_DSh-_EncxSC9_bUKxOtxS5mJtGi7vy60')
  #test copy: https://docs.google.com/spreadsheets/d/1mGXz-pnULDHjRbBvJURuOzE3rogxOHQCpMeJY1_fgXU/edit#gid=0
  #gSht_RMDMMGR_get <- gs4_get('1mGXz-pnULDHjRbBvJURuOzE3rogxOHQCpMeJY1_fgXU')
  
  
  #read existing data in from desired sheet
  gSht_RMDMMGR_Orig <- read_sheet(gSht_RMDMMGR_get$spreadsheet_id, sheet = "RMDMMGR")
  
  #query Oracle for current data
  myquery <- paste0(
    "
    select 
      STORE,
      RM_FNAME,
      RM_LNAME,
      RM_FULLNAME,
      RM_EMAIL,
      RM_PHONE,
      DM_FNAME,
      DM_LNAME,
      DM_FULLNAME,
      DM_EMAIL,
      DM_PHONE,
      MGR_FNAME,
      MGR_LNAME,
      MGR_FULLNAME,
      MGR_EMAIL,
      MGR_PHONE,
      MGR_PAYNUM,
      LOC_NAME,
      ACQUIRED
    from steve.so_rm_dm_mgr
    order by STORE
    "
  )
  
  mydata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  mydata_status <- check_mydata_rows(MinNumRows = 0, ReportName = myReportName)
  okaytocontinue <- mydata_status[[1]]
  MyErrorLog[1,"QUERY_STATUS"] <- mydata_status[[2]]
  
  if(!okaytocontinue){
    #send warning email
    print(MyErrorLog[1,"QUERY_STATUS"])
    queryfailure.mailsend(recip = warn_recip, reportname = myReportName)
  }else{
    ###update sheet with current Oracle data
    
    #clear existing data in sheet
    range_clear(gSht_RMDMMGR_get$spreadsheet_id, sheet = "RMDMMGR", range = NULL, reformat = FALSE)
    
    #write new data
    sheet_write(mydata, ss = gSht_RMDMMGR_get$spreadsheet_id, sheet = "RMDMMGR")
    #replace headers to replace underscores with spaces
    #my_headers <- data.frame(gsub("_"," ",mydata[0,]))
    Sys.sleep(2)
    my_headers <- mydata[0,]
    colnames(mydata) <- gsub("_"," ",colnames(my_headers))
    range_write(gSht_RMDMMGR_get$spreadsheet_id, mydata[0,], range = "RMDMMGR!A1", col_names = TRUE, reformat = FALSE)
    Sys.sleep(2)
    if(length(gSht_RMDMMGR_Orig)>0){
      ###compare new Marco's store-manager list to previous data and store diffs
      my_headers <- gSht_RMDMMGR_Orig[0,]
      colnames(gSht_RMDMMGR_Orig) <- gsub("_"," ",colnames(my_headers))
      mp_mgrs_colnames <- c("STORE", "MGR FULLNAME", "MGR PAYNUM", "MGR EMAIL")
      #mp_mgrs_prev <- gSht_RMDMMGR_Orig[which(between(gSht_RMDMMGR_Orig$STORE, 3500, 3999)), mp_mgrs_colnames]
      #mp_mgrs_curr <- mydata[which(between(mydata$STORE, 3500, 3999)), mp_mgrs_colnames]
      mp_mgrs_prev <- gSht_RMDMMGR_Orig[which(between(gSht_RMDMMGR_Orig$STORE, 3500, 3999) | between(gSht_RMDMMGR_Orig$STORE, 2101, 2999) ), mp_mgrs_colnames]
      mp_mgrs_curr <- mydata[which(between(mydata$STORE, 3500, 3999) | between(mydata$STORE, 2100, 2999) ), mp_mgrs_colnames]
      
      mp_mgrs_prev_diff <- copy(setdiff(mp_mgrs_prev, mp_mgrs_curr))
      setnames(mp_mgrs_prev_diff, 
               old = c("MGR FULLNAME", "MGR PAYNUM", "MGR EMAIL"), 
               new = c("PREV MGR FULLNAME", "PREV EMP ID", "PREV MGR EMAIL"))
      
      mp_mgrs_prev_curr <- copy(setdiff(mp_mgrs_curr, mp_mgrs_prev))
      setnames(mp_mgrs_prev_curr, 
               old = c("MGR FULLNAME", "MGR PAYNUM", "MGR EMAIL"), 
               new = c("NEW MGR FULLNAME","NEW EMP ID","NEW MGR EMAIL"))
      
      #create df with all store rows
      STORE <- unique(c(mp_mgrs_prev_diff$STORE, mp_mgrs_prev_curr$STORE))
      mp_mgrs_changes <- as.data.frame(STORE)
      #mp_mgrs_changes[,c("PREV MGR FULLNAME", "PREV EMP ID", "PREV MGR EMAIL","NEW MGR FULLNAME","NEW EMP ID","NEW MGR EMAIL")] <- NA
      
      mp_mgrs_changes <- mp_mgrs_changes %>%
        dplyr::left_join(
          mp_mgrs_prev_diff,
          by = c("STORE")
        )
      
      mp_mgrs_changes <- mp_mgrs_changes %>%
        dplyr::left_join(
          mp_mgrs_prev_curr,
          by = c("STORE")
        )
      #remove rows with all mgr cols are NA (new or closing stores without prev or curr mgr)
      mp_mgrs_changes <- mp_mgrs_changes[rowSums(is.na(mp_mgrs_changes[, 2:7])) != 6, ]

      
      ###notify email group of Mgr changes
      if(nrow(mp_mgrs_changes) >= 1){
        # maybe add column for previous store where a manager moved to diff location?
        
        
        #Body of email...include list of files generated
        
        bodytext <- paste0("The Marco's manager changes from the <b>", myReportName, "</b> routine are below: <br/><br/>",
                           #paste(mydata, collapse = "\n"),
                           print(xtable(mp_mgrs_changes, 
                                        caption = paste0("Marco's Manager Changes (", query.date, ")"),
                                        digits = rep(0,ncol(mp_mgrs_changes)+1)),
                                 align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                                 html.table.attributes = "border=2 cellspacing=1",
                                 type = "html",
                                 caption.placement = "top",
                                 include.rownames=FALSE),
                           "<br/><br/>",
                           norm_st_from
        )
        this_recip <- c('<EMAIL>')
        #this_recip <- c('<EMAIL>')
        if(testing_emails){
          bodytext <- paste0("<p><b>TEST SEND (normal recipient: ",
                             paste(this_recip, collapse = "; "), ")</b></p>",
                             bodytext)
          this_recip <- test_recip
        }
        mailsend(recipient = this_recip,
           subject = paste0('Manager changes from ', myReportName, ' routine'),
           body = bodytext,
           if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
           inline = TRUE,
           test = testing_emails, testrecipient = test_recip
        )
        
        
        
      }
    }else{
      #existing data in google sheet missing, send warning()
      okaytocontinue <- FALSE
      MyErrorLog[1,"PROGRESS"] <- "FAILURE"
      MyErrorLog[1,"QUERY_STATUS"] <- paste0("GSHT was previously empty")
      writelog(MyErrorLog)
    }
  }
  
  
  
  
  
}






#--END, send completion email--#
#if(okaytocontinue & 
#   MyErrorLog[1,"INVEN_STATUS"] == "READY" & 
#   MyErrorLog[1,"DS_STATUS"] == "READY"){
if(okaytocontinue){
  
  #send SUCCESS EMAIL
  MyErrorLog[1,"PROGRESS"] <- "COMPLETE"
  MyErrorLog[1,"QUERY_STATUS"] <- paste0("COMPLETE")
  writelog(MyErrorLog)
  
  #Body of email...include list of files generated
  bodytext <- paste0(
    "<p><h1>MARCOS Daily Reports</h1></span></p>",
    if(attachemailnote_Corp){emailnote_Corp},
    "<p>The latest MARCOS daily report files have been generated.</p> ",
    "<p>The MARCOS daily report files and previous ones can be found in the following folders:<br/>",
    #gsub("//*************","",rptpath_central_STEVE), "/<br/>",
    #gsub("//*************","",rptpath_central_MONDAY), "/<br/>",
    #gsub("//*************","",rptpath_central_STORESUM), "/</p>",
    gsub("//*************","",rptpath_MARCOS_emails), "/</p>",
    paste(myfilescreated, collapse="<br/>"),
    "<br/><br/>",
    norm_st_from
  )
  
  if(testing_emails){
    #test send
    mailsend(recipient =  test_recip,
             subject =  paste0("TEST: MARCOS Daily Reports: ", query.enddate),
             body = paste0("<p><b>TEST SEND (normal recipient: ",
                           #paste(corp_recip, sep = "; "), ")</b></p>",
                           paste(corp_recip, collapse = "; "), ")</b></p>",
                           bodytext),
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }else{
    #production send
    #mailsend(recipient =  corp_recip,
    #         subject = paste0("MARCOS Daily Reports: ", query.enddate),
    #         body =   bodytext,
    #         inline = TRUE
    #)
  }
  
}else{ 
  #if(MyErrorLog[1,"INVEN_STATUS"] == "READY" & 
  #        MyErrorLog[1,"DS_STATUS"] == "READY" & 
  #        MyErrorLog[1,"PROGRESS"] != "COMPLETE"){
  if(MyErrorLog[1,"PROGRESS"] != "COMPLETE"){  
    #send FAILURE EMAIL
    writelog(MyErrorLog)
    
    #Body of email...include list of files generated
    bodytext <- paste0(
      "<p><h1>MARCOS Daily Reports FAILURE</h1></span></p>",
      print(xtable(MyErrorLog, 
                   caption = paste0("MARCOS Daily Reports Log (", report.time.txt, ")")),
            align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
            html.table.attributes = "border=2 cellspacing=1",
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE),
      "<br/>",
      "<p>There was a failure while creating the latest MARCOS Daily report files. Only the files ",
      "listed below were generated. See the 'MARCOS_Daily_Reports.R' script in the ",
      orig_wd,
      " directory of the Tableau desktop.</p> ",
      
      "<p>The MARCOS Daily report files and previous ones can be found in the following folders:<br/>",
      #gsub("//*************","",rptpath_central_STEVE), "/<br/>",
      #gsub("//*************","",rptpath_central_MONDAY), "/<br/>",
      #gsub("//*************","",rptpath_central_STORESUM), "/</p>",
      gsub("//*************","",rptpath_MARCOS_emails), "/</p>",
      paste(myfilescreated, collapse="<br/>"),
      "<br/>",
      norm_st_from
    )
    
    if(testing_emails){
      #test send
      mailsend(recipient =  test_recip,
               subject =  paste0("TEST: FAILURE DURING MARCOS Daily Reports: ", query.enddate),
               body = paste0("<p><b>TEST SEND (normal recipient: ",
                             paste(corp_recip, collapse = "; "), ")</b></p>",
                             bodytext),
               inline = TRUE,
               attachment = file.path(logpath, logname),
               test = testing_emails, testrecipient = test_recip
               
      )
    }else{
      #production send
      mailsend(recipient =  corp_recip,
               subject = paste0("FAILURE DURING MARCOS Daily Reports: ", query.enddate),
               body =   bodytext,
               inline = TRUE,
               attachment = file.path(logpath, logname),
               test = testing_emails, testrecipient = test_recip
               
      )
    }
  }
}