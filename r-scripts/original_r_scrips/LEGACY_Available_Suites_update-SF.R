library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(tidyr)
library(tidyverse)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(keyring)
library(googledrive)
library(googlesheets4)
library(DBI)
library(odbc)
#library(ROracle)
library(jsonlite)

# written by <PERSON> March 2022
# based on HV_PNL_LINE_STRUCTURE loading script

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

###NOTE: after this routine runs, there are additional Google Apps Script functions that run
### to format columns, re-define filter ranges and populate the HRG interest column in certain sheets/buildings.
### Apps Script functions may include Availability_NumFormat, Avail_Prior_NumFormat, (ExpandFilterRng called by NumFormat scripts), HRG_Interest_Update
### and there may be more not included here.

# Version 20241203

### 20241203 change:
### fixed bug where routine still thought there might be Developement Availability data
### if column A of Google Sheet had 'ACQUIS' but other columns were empty
### also changed Availability Additions email recipients, replaced <NAME_EMAIL>

### 20241202 change:
### identify and email removals from 'Availability' to <EMAIL>

### 20241018 change:
### added 'EXCLUDE' building note reference to flag certain buildings as NOT available

### 20241011 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced check_mydata_rows() function with more universal check_mydf_rows()

### 20240702 change:
### added email notification to leasing agents and other office staff of additions to sheet

### 20240219 change:
### added 'HRG Interested' column to Available sheets (which will be
### populated by apps script)

### 20231103 change:
### added ability to add individual suites note in the 'Development Availability' sheet


### 20230525 change:
### commented out the Exec Date and Stop Billing Date in the Prior tenant data

### 20230522 change:
### added Property Mgr and field support staff for each location

### 20230421 change:
### added additional query to add in suites tagged with 'BYEBYE' note in MRI

### 20230419 change:
### added ability to get rows from 'Development Availability' tab and append it to results

### 20220920 change:
### added building GLA column
### changed to range_write (from sheet_write) to preserve formatting in the sheet which fixed hyperlinks of Google Map

### 20220606 change:
### updated mailsend to use keyring

### 20220504 change:
### added keyring package

### 20220318 change:
### Added in info for writing same info to another Google Sheet (Legacy Portfolio)

### 20220317 change:
### new script based on HV PNL LINE STRUCTURE script version 20220317
### added in section to populate 'MRI data' sheet of Google sheet with
### select MRI data to keep other sheets in that file up-to-date


# Parameters
#logpath <- file.path("C:","Users","table","Documents","ReportFiles","HV_PNL_Line_Structure")
okaytocontinue <- TRUE

myTableName <- "STEVE.NA"
myReportName <- "LCP_Available_Suites_update"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
mySheets <- c("Available")
mySheets_2 <- c("Availability")
myMRISheet <- "Available"
myMRISheet_2 <- "Availability"
myPriorSheet <- "Avail-Prior Tenant"

emoji_noentry <- "=?UTF-8?B?4puU?="

#Available Suite info workbook
#https://docs.google.com/spreadsheets/d/1GL-PMYybfT9iLYNLxyUP6zRWs8Cbl58o1rbkmYMacRM/edit#gid=0
gSht_key <- "1GL-PMYybfT9iLYNLxyUP6zRWs8Cbl58o1rbkmYMacRM"

#Portfolio Google Sheet (main)
#https://docs.google.com/spreadsheets/d/1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8/edit#gid=2021978956
gSht_key_2 <- "1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8"

###----------------------------------------------###
###dev testing key for 20241008 copy of portfolio###
###----------------------------------------------###
#gSht_key_2 <- "16QUYf48WFORTzDW3ihR8C-UP0-JwAwy39QcKjBngMIY"



###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="GMT")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)



# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>", "<EMAIL>", "Ben Hoogland<<EMAIL>>")
removals_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts


test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}


if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE
  #logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_PNL_Line_Structure")
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  mainpath <- centralPath
}else{
  mainpath <- tableauPath
}


### define some functions ###
Convert_to_ColClass <- function(mydata, names_class_df){
  #names_class_df should be a dataframe with two columns, ColName with DF columns and DataType with the expected class
  classes <- names_class_df$DataType %>% unique(.) %>% toupper(.)
  myresult <- mydata
  if(length(classes)>=1){
    myresult <- mydata
    for(i in 1:length(classes)){
      curr_class <- classes[i]
      convert_cols <- names_class_df$ColName[which(toupper(names_class_df$DataType) == curr_class)]
      if(curr_class == 'INTEGER'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.integer))}
      if(curr_class == 'NUMERIC'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.numeric))}
      if(curr_class == 'DATE'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.Date))}
      if(curr_class == 'CHARACTER'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.character))}
    }
  }
  return(myresult)
}

#determine if current external IP in whitelisted range for Google SMTP relay 
#(list maintained in Google admin settings https://admin.google.com/ac/apps/gmail/routing)
smtp_relay <- FALSE
CorpIPsPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HVLTD IP Ranges.csv")
convIP <- function(IP) {#from: https://stackoverflow.com/questions/********/convert-ip-address-ipv4-itno-an-integer-in-r
  vals <- read.table(text=as.character(IP), sep=".")
  return( Reduce("+", vals*256^(3:0))) 
}
if(file.exists(CorpIPsPath)) {
  corp_IPs <- read.csv(file = CorpIPsPath, sep=",", stringsAsFactors = FALSE)
  my_IPv4 <- fromJSON(readLines("http://api.hostip.info/get_json.php", warn=F))$ip
  my_IPint <- convIP(my_IPv4)
  my_IPrangerow <- which(corp_IPs$Min_int <= my_IPint & corp_IPs$Max_int >= my_IPint)
  smtp_relay <- !is_empty(my_IPrangerow)
}else{
  #guess according to computer running on
  smtp_relay <- !testing_pc || Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010"
}



#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signatures###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
sig_logo <- FALSE
if(file.exists(HVSigPath)){
  sigName <- 'NA'
  sigTitle <- 'NA'
  sigEmail <- 'NA'
  sigTemplate <- 'LCP Reporting' #LCP Reporting doesn't use any personal info substitutions
  sigPhone <- 'NA'
  #update_log(
  #  event = "Signature Template", 
  #  desc = paste0("Creating...PARAMS - Name: ", sigName, "; Email: ", sigEmail, "; Signature Template: ", sigTemplate)
  #)
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == sigTemplate)],
    Name = sigName,
    Title = sigTitle,
    Email = sigEmail,
    Phone = sigPhone
  )
}



check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load...log
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



# check google sheet status
if(okaytocontinue){
  
  #MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  #MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  #writelog(MyErrorLog)
  
  gs4_auth(email = "<EMAIL>")
  
  #Is it OK to cache OAuth access credentials in the folder 'C:/Users/<USER>/.R/gargle/gargle-oauth' between R sessions?
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive package first, then use the same token
  #in googlesheets4 something like this:
  #tk <- drive_auth()
  #gs4_auth(token = drive_token())
  #gSht_Closings <- sheets_get(gSht_key)
  #above was deprecated as of googlesheets4 0.2.0

  
  gSht_get <- gs4_get(gSht_key)
  gSht_get_2 <- gs4_get(gSht_key_2)
  
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2 & length(gSht_get_2) > 2){
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH OKAY")
    #MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    #writelog(MyErrorLog)
    #read data in from desired sheet
    #gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RE Taxes Data")
    
    #Get number of sheets like '% P&L Full Sort' sheets
    #gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %ilike% mySheetsLike)]
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    gSht_Sheets_2 <- gSht_get_2$sheets$name[which(gSht_get_2$sheets$name %in% mySheets_2)]
    gSht_Sheets_2_shtid <- gSht_get_2$sheets$id[which(gSht_get_2$sheets$name == gSht_Sheets_2)]
    "https://docs.google.com/spreadsheets/d/"
    gSht_Sheets_2_url <- paste0(
      "https://docs.google.com/spreadsheets/d/", 
      gSht_get_2$spreadsheet_id, 
      "/view?gid=",
      gSht_Sheets_2_shtid
    )
    gSht_Sheets_num_2 <- length(gSht_Sheets_2)
    
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>There weren't any sheets with the expected names (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}



### Populate 'Available' data sheet

if(okaytocontinue){
  # verify myMRISheet is present
  gSht_Sheet_MRI <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% myMRISheet)]
  gSht_Sheet_MRI_num <- length(gSht_Sheet_MRI)
  gSht_Sheet_MRI_2 <- gSht_get_2$sheets$name[which(gSht_get_2$sheets$name %in% myMRISheet_2)]
  gSht_Sheet_MRI_num_2 <- length(gSht_Sheet_MRI_2)
  if(gSht_Sheet_MRI_num > 0){
    #sheet found, continue
    
    #query MRI
    #get last update to BLDG, SUIT or LEAS tables
    myquery <- paste0(
      "
       SELECT /* SNOWFLAKE version */ 
       concat('Last change in MRI was made: ',to_char(MAX(A.LASTDATE), 'MON DD YYYY HH12:MIAM')) as LUPD
       from
       (
        	select max(bldg.LASTDATE) AS LASTDATE from MRI.BLDG
        	UNION ALL
        	select max(suit.LASTDATE) AS LASTDATE FROM MRI.SUIT
        	UNION ALL
        	select MAX(leas.LASTDATE) AS LASTDATE FROM MRI.LEAS
        ) A
      "
    )
    myupdated <- dbGetQuery(mySfDB, myquery)
    
    #get main 'AVAILABILITY' data
    myquery <- paste0(
      '
        select /* SNOWFLAKE version */
            BLDGID
          ,	BLDG AS "Bldg"
          , HRG as "HRG Interested"
          ,	BLDGADDRESS AS "Primary Address"
          ,	SUITADDRESS AS "Suite Address"
          ,	CITY AS "City"
          ,	STATE AS "State"
          ,	ZIPCODE AS "Zip Code"
          ,	SUITID AS "Suite ID"
          ,	SQFT AS "SQ FT"
          , BLDG_GLA as "BLDG GLA"
          ,	AVAILTYPE AS "Avail Type"
          ,	MAPURL AS "Google Map URL"
      	  ,	CORPPM AS "Corporate PM"
      	  ,	CORPPMEMAIL AS "PM_Email"
      ',"
        from
        (
          select
            B.BLDGID
          ,	CASE WHEN TRY_CAST(B.BLDGID AS INT) IS NULL THEN B.BLDGID ELSE TO_CHAR(TRY_CAST(B.BLDGID AS INT)) END AS BLDG
          , NULL as HRG
          ,	CONCAT(RTRIM(B.ADDRESS1), (CASE WHEN B.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(B.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
          ,	RTRIM(S.ADDRESS) AS SUITADDRESS
          ,	B.CITY
          ,	B.STATE
          ,	B.ZIPCODE
          ,	S.SUITID
          ,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
          , BLDG_SQFT.BLDG_GLA
          , 'Vacancy' AS AVAILTYPE
          ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE rtrim(B.MAP) END,'') AS MAPURL
      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) 
					  ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
          FROM MRI.SUIT S
          JOIN MRI.BLDG B
          ON B.BLDGID = S.BLDGID
		  left join MRI.MNGR
          on b.MNGRID = mngr.MNGRID
          LEFT JOIN MRI.TB_CM_SUITETYPE
          ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
          LEFT JOIN 
          (
          SELECT *
          FROM MRI.SSQF
          WHERE SSQF.EFFDATE = (
              SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
              )
          ) SSQF_TYPE
          ON S.SUITID = SSQF_TYPE.SUITID
          AND S.BLDGID = SSQF_TYPE.BLDGID
        	left join
        	(
            SELECT SSQF.BLDGID
        	,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
            FROM MRI.SSQF
        	join MRI.SUIT
        		ON SSQF.SUITID = SUIT.SUITID
        		AND SSQF.BLDGID = SUIT.BLDGID
        	LEFT JOIN MRI.TB_CM_SUITETYPE								
            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            WHERE SSQF.EFFDATE = (
                  	SELECT MAX(I.EFFDATE) 
        			FROM MRI.SSQF I 
        			WHERE I.BLDGID = SSQF.BLDGID 
        				AND I.SUITID = SSQF.SUITID 
        				AND I.EFFDATE <= CURRENT_DATE
                )
        		AND UPPER(SSQF.SQFTTYPE) = 'GLA'
        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
        	GROUP BY SSQF.BLDGID
            ) BLDG_SQFT
            ON S.BLDGID = BLDG_SQFT.BLDGID
        
            LEFT JOIN
            (
            SELECT 								
                L.BLDGID,							
                RTRIM(P.PORTID) AS Portfolio,
                L.SUITID,							
                L.LEASID,							
                L.OCCPNAME,							
                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
                L.OCCPSTAT,
                CL.CODEDESC AS OCCP_STATUS,							
                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
                TO_DATE(L.VACATE) as VACATEDATE,							
                TO_DATE(L.EXPIR) as EXPIRDATE,																											
                S.SUITETYPE_MRI,							
                TB_CM_SUITETYPE.SUITETYPEUSAGE,							
                TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
                L.GENCODE,
                L.CONTINGENT,
                L.CONTINGENTDT,
                case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= Cast(GetDate() AS date) THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY														
            FROM MRI.LEAS L								
            INNER JOIN MRI.BLDG B								
            ON L.BLDGID = B.BLDGID								
            INNER JOIN MRI.SUIT S								
            ON L.SUITID = S.SUITID								
                AND B.BLDGID = S.BLDGID								
            LEFT JOIN (								
                SELECT * 							
                FROM MRI.SSQF 							
                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
            ) SQF								
            ON S.BLDGID = SQF.BLDGID								
                AND S.SUITID = SQF.SUITID							
            LEFT JOIN MRI.TB_CM_SUITETYPE								
            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
            LEFT JOIN MRI.ENTITY E								
            ON B.ENTITYID = E.ENTITYID								
            LEFT JOIN MRI.PROJ P								
            ON E.PROJID = P.PROJID																
            LEFT JOIN MRI.CODELIST CL								
            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
            WHERE 		
                L.OCCPSTAT NOT IN ('P', 'I') 							
                AND (		/* Next section commented out to allow future tenants to appear in results (also comment out CMRECC.INEFFECT = 'Y' line above)  */
                  		(					
                  			(
                  				L.RENTSTRT <= GETDATE() 
                  				OR L.OCCUPNCY <= GETDATE()
                  				OR L.EXECDATE <= GETDATE()
                  			)
                  			AND (				
                  					L.STOPBILLDATE IS NULL 		
                  					OR L.STOPBILLDATE >= GETDATE() 		
                  					OR (		
                  							L.STOPBILLDATE < GETDATE() 
                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
                  						)	
                  			)			
                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE() 				
                  		)					
                  		OR 					
                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
                  				
                  	)
            )LEASED
            ON B.BLDGID = LEASED.BLDGID
            AND S.SUITID = LEASED.SUITID
            LEFT JOIN
            (
      				SELECT DISTINCT
      				NOTB.BLDGID
      				, NOTB.NOTEDATE
      				FROM MRI.NOTB
      				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
      				AND NOTB.NOTEDATE = (
      					SELECT MIN(I.NOTEDATE) 
      					FROM MRI.NOTB I 
      					WHERE I.BLDGID = NOTB.BLDGID  
      					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
      				)
      				AND NOTB.NOTEDATE <= CURRENT_DATE
            )EXCBLDGNOTE
            ON B.BLDGID = EXCBLDGNOTE.BLDGID
            WHERE (B.INACTIVE <> 'Y' or B.INACTIVE IS NULL)
            AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
            AND B.BLDGID IS NOT NULL
            AND LEASED.OCCP_STATUS IS NULL
            AND TRY_CAST(B.BLDGID AS INT) IS NOT NULL
            AND (CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 OR
                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND S.SUITETYPE_MRI = 'BTS')
        	)
        	AND EXCBLDGNOTE.BLDGID IS NULL /* EXCLUDES BUILDING when 'EXCLUDE' building note in effect */
        
        
        	union all
        
        
        	SELECT	/* BYEBYE note additions */
        	  NOTE.BLDGID
        	,	CASE WHEN TRY_CAST(NOTE.BLDGID AS INT) IS NULL THEN NOTE.BLDGID ELSE TO_CHAR(TRY_CAST(NOTE.BLDGID AS INT)) END AS BLDG
        	,	NULL as HRG
        	,	CONCAT(RTRIM(BLDG.ADDRESS1), (CASE WHEN BLDG.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(BLDG.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
        	,	rtrim(SUIT.ADDRESS) AS SUITADDRESS
            ,	BLDG.CITY
            ,	BLDG.STATE
            ,	BLDG.ZIPCODE
            ,	SUIT.SUITID
        	,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
        	,	BLDG_SQFT.BLDG_GLA
        	,	CONCAT('BYEBYE', ' (', RTRIM(NOTE.NOTETEXT), ') Avail ',TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yy')) AS AVAILTYPE
        	,	ifnull(CASE WHEN BLDG.INACTIVE = 'Y' THEN NULL ELSE rtrim(BLDG.MAP) END,'') AS MAPURL
        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  					ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
        	FROM MRI.NOTE			
        	LEFT JOIN MRI.BLDG			
        	ON NOTE.BLDGID = BLDG.BLDGID
        	left join MRI.MNGR
        	on bldg.MNGRID = mngr.MNGRID
        	LEFT JOIN MRI.LEAS
        	ON NOTE.LEASID = LEAS.LEASID
        		AND NOTE.BLDGID = LEAS.BLDGID
        	LEFT JOIN MRI.CODELIST CL								
        	ON LEAS.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
        	LEFT JOIN MRI.SUIT
        	ON LEAS.SUITID = SUIT.SUITID
        		AND LEAS.BLDGID = SUIT.BLDGID
        	LEFT JOIN 
            (
        		SELECT *
        		FROM MRI.SSQF
        		WHERE SSQF.EFFDATE = (
        			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE 
        			)
            ) SSQF_TYPE
            ON LEAS.SUITID = SSQF_TYPE.SUITID
        		AND LEAS.BLDGID = SSQF_TYPE.BLDGID
        	left join
        	(
        		SELECT SSQF.BLDGID
        		,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
        		FROM MRI.SSQF
        		join MRI.SUIT
        			ON SSQF.SUITID = SUIT.SUITID
        			AND SSQF.BLDGID = SUIT.BLDGID
        		LEFT JOIN MRI.TB_CM_SUITETYPE								
        		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
        		WHERE SSQF.EFFDATE = (
                  		SELECT MAX(I.EFFDATE) 
        				FROM MRI.SSQF I 
        				WHERE I.BLDGID = SSQF.BLDGID 
        					AND I.SUITID = SSQF.SUITID 
        					AND I.EFFDATE <= CURRENT_DATE 
        			)
        			AND UPPER(SSQF.SQFTTYPE) = 'GLA'
        			AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
        		GROUP BY SSQF.BLDGID
            ) BLDG_SQFT
            ON LEAS.BLDGID = BLDG_SQFT.BLDGID
        	LEFT JOIN MRI.TB_CM_SUITETYPE
            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
        
        	LEFT JOIN
            (
            SELECT 								
                L.BLDGID,							
                RTRIM(P.PORTID) AS Portfolio,
                L.SUITID,						
                L.LEASID,							
                L.OCCPNAME,							
                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
                L.OCCPSTAT,
                CL.CODEDESC AS OCCP_STATUS,							
                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
                TO_DATE(L.VACATE) as VACATEDATE,							
                TO_DATE(L.EXPIR) as EXPIRDATE																																						
            FROM MRI.LEAS L							
            INNER JOIN MRI.BLDG B								
            ON L.BLDGID = B.BLDGID								
            INNER JOIN MRI.SUIT S								
            ON L.SUITID = S.SUITID								
                AND L.BLDGID = S.BLDGID								
            LEFT JOIN (								
                SELECT * 							
                FROM MRI.SSQF 							
                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
            ) SQF								
            ON S.BLDGID = SQF.BLDGID								
                AND S.SUITID = SQF.SUITID							
            LEFT JOIN MRI.TB_CM_SUITETYPE								
            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
            LEFT JOIN MRI.ENTITY E								
            ON B.ENTITYID = E.ENTITYID								
            LEFT JOIN MRI.PROJ P								
            ON E.PROJID = P.PROJID																
            LEFT JOIN MRI.CODELIST CL								
            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
            WHERE 
        		L.OCCPSTAT NOT IN ('P', 'I') 							
                AND (
                  		(					
                  			(
                  				L.RENTSTRT <= GETDATE() 
                  				OR L.OCCUPNCY <= GETDATE()
                  				OR L.EXECDATE <= GETDATE()
                  			)
                  			AND (				
                  					L.STOPBILLDATE IS NULL 		
                  					OR L.STOPBILLDATE >= GETDATE()		
                  					OR (		
                  							L.STOPBILLDATE < GETDATE() 
                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
                  						)	
                  			)			
                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()				
                  		)					
                  		OR 					
                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
                  				
                  	)
            )LEASED
        	ON LEAS.BLDGID = LEASED.BLDGID
            AND LEAS.SUITID = LEASED.SUITID
        	AND LEAS.LEASID = LEASED.LEASID
        	LEFT JOIN
          (
    				SELECT DISTINCT
    				NOTE.BLDGID
    				, NOTE.LEASID
    				, NOTE.NOTEDATE
    				FROM MRI.NOTE
    				WHERE (NOTE.REF1 = 'EXCLUDE' OR NOTE.REF2 = 'EXCLUDE')
    				AND NOTE.NOTEDATE = (
    					SELECT MIN(I.NOTEDATE) 
    					FROM MRI.NOTE I 
    					WHERE I.BLDGID = NOTE.BLDGID 
    					AND I.LEASID = NOTE.LEASID 
    					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
    				)
    				AND NOTE.NOTEDATE <= CURRENT_DATE
          )EXCLEASNOTE
          ON NOTE.BLDGID = EXCLEASNOTE.BLDGID
          AND NOTE.LEASID = EXCLEASNOTE.LEASID
        	LEFT JOIN
          (
      			SELECT DISTINCT
      			NOTB.BLDGID
    				, NOTB.NOTEDATE
    				FROM MRI.NOTB
    				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
      			AND NOTB.NOTEDATE = (
      				SELECT MIN(I.NOTEDATE) 
      				FROM MRI.NOTB I 
      				WHERE I.BLDGID = NOTB.BLDGID  
    					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
    				)
    				AND NOTB.NOTEDATE <= CURRENT_DATE
          )EXCBLDGNOTE
          ON NOTE.BLDGID = EXCBLDGNOTE.BLDGID
        	WHERE 			
        		(BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)			
        		AND (NOTE.REF1 = 'BYEBYE' OR NOTE.REF2 = 'BYEBYE')
        		--AND CONVERT(DATE, NOTE.NOTEDATE) <= CONVERT(DATE, GETDATE()) /*20240628 notes now added as soon as entered */
        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)	
        		AND LEASED.OCCP_STATUS IS NOT NULL
        		AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
        		AND 
        		(
        			CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 
        			OR
                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND SUIT.SUITETYPE_MRI = 'BTS')
        		)
        		AND EXCLEASNOTE.LEASID IS NULL
        		AND EXCBLDGNOTE.BLDGID IS NULL
        ) combined
        order by combined.BLDGID, combined.SUITID, combined.AVAILTYPE
      "
    )
    
    
    #get prior occupant info
    myquery_Prior <- paste0(
      '
	        select /* SNOWFLAKE version, Prior Occupant query */
	          combined.BLDGID
	        , combined.BLDG AS "Bldg"
	        , combined.BLDGADDRESS AS "Primary Address"
	        , combined.SUITADDRESS AS "Suite Address"
	        , combined.CITY AS "City"
	        , combined.STATE AS "State"
	        , combined.ZIPCODE AS "Zip Code"
	        , combined.SUITID AS "Suite ID"
	        , combined.SQFT AS "SQ FT"
	        , combined.BLDG_GLA as "BLDG GLA"
	        , combined.AVAILTYPE AS "Avail Type"
	        , LASTTNT.LEASID as "Prior Lease ID"
	        , LASTTNT.OCCPNAME AS "Prior Occupant Name"
	        , LASTTNT.DBA as "Prior DBA"
	        , LASTTNT.RENTSTRT AS "Rent Start"
	        , LASTTNT.VACATEDATE as "Vacate Date"
	        , LASTTNT.EXPIRDATE as "Lease Expiration Date"
	        , combined.SUITETYPEDESC AS "Suite Type Desc"	          
	        , combined.MAPURL AS "Google Map URL"
	        , combined.CORPPM AS "Corporate PM"
	        , combined.CORPPMEMAIL AS "PM_Email"
	     ',"
	        from
	        (
	          select
	            B.BLDGID
	          ,	CASE WHEN TRY_CAST(B.BLDGID AS INT) IS NULL THEN B.BLDGID ELSE TO_CHAR(TRY_CAST(B.BLDGID AS INT)) END AS BLDG
	          , NULL as HRG
	          ,	CONCAT(RTRIM(B.ADDRESS1), (CASE WHEN B.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(B.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
	          ,	RTRIM(S.ADDRESS) AS SUITADDRESS
	          ,	B.CITY
	          ,	B.STATE
	          ,	B.ZIPCODE
	          ,	S.SUITID
	          ,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
	          , BLDG_SQFT.BLDG_GLA
	          , 'Vacancy' AS AVAILTYPE
	          ,	TB_CM_SUITETYPE.DESCRIPTION as SUITETYPEDESC
	          ,	ifnull(CASE WHEN B.INACTIVE = 'Y' THEN NULL ELSE rtrim(B.MAP) END,'') AS MAPURL
	      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
	      	  ,	CASE WHEN b.INACTIVE = 'Y' THEN to_char(NULL) 
						  ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
	          FROM MRI.SUIT S
	          JOIN MRI.BLDG B
	          ON B.BLDGID = S.BLDGID
	          left join MRI.MNGR
	          on b.MNGRID = mngr.MNGRID
	          LEFT JOIN MRI.TB_CM_SUITETYPE
	          ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	          LEFT JOIN 
	          (
	          SELECT *
	          FROM MRI.SSQF
	          WHERE SSQF.EFFDATE = (
	              SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
	              )
	          ) SSQF_TYPE
	          ON S.SUITID = SSQF_TYPE.SUITID
	          AND S.BLDGID = SSQF_TYPE.BLDGID
	        	left join
	        	(
	            SELECT SSQF.BLDGID
	        	,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
	            FROM MRI.SSQF
	        	join MRI.SUIT
	        		ON SSQF.SUITID = SUIT.SUITID
	        		AND SSQF.BLDGID = SUIT.BLDGID
	        	LEFT JOIN MRI.TB_CM_SUITETYPE								
	            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	            WHERE SSQF.EFFDATE = (
	                  	SELECT MAX(I.EFFDATE) 
	        			FROM MRI.SSQF I 
	        			WHERE I.BLDGID = SSQF.BLDGID 
	        				AND I.SUITID = SSQF.SUITID 
	        				AND I.EFFDATE <= CURRENT_DATE
	                )
	        		AND UPPER(SSQF.SQFTTYPE) = 'GLA'
	        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
	        		GROUP BY SSQF.BLDGID
	            ) BLDG_SQFT
	            ON S.BLDGID = BLDG_SQFT.BLDGID
	        
	            LEFT JOIN
	            (
	            SELECT 								
	                L.BLDGID,							
	                RTRIM(P.PORTID) AS Portfolio,
	                L.SUITID,							
	                L.LEASID,							
	                L.OCCPNAME,							
	                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
	                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
	                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
	                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
	                L.OCCPSTAT,
	                CL.CODEDESC AS OCCP_STATUS,							
	                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
	                TO_DATE(L.VACATE) as VACATEDATE,							
	                TO_DATE(L.EXPIR) as EXPIRDATE,																											
	                S.SUITETYPE_MRI,							
	                TB_CM_SUITETYPE.SUITETYPEUSAGE,							
	                TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
	                L.GENCODE,
	                L.CONTINGENT,
	                L.CONTINGENTDT,
	                case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= Cast(GetDate() AS date) THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY														
	            FROM MRI.LEAS L								
	            INNER JOIN MRI.BLDG B								
	            ON L.BLDGID = B.BLDGID								
	            INNER JOIN MRI.SUIT S								
	            ON L.SUITID = S.SUITID								
	                AND B.BLDGID = S.BLDGID								
	            LEFT JOIN (								
	                SELECT * 							
	                FROM MRI.SSQF 							
	                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
	            ) SQF								
	            ON S.BLDGID = SQF.BLDGID								
	                AND S.SUITID = SQF.SUITID							
	            LEFT JOIN MRI.TB_CM_SUITETYPE								
	            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
	            LEFT JOIN MRI.ENTITY E								
	            ON B.ENTITYID = E.ENTITYID								
	            LEFT JOIN MRI.PROJ P								
	            ON E.PROJID = P.PROJID																
	            LEFT JOIN MRI.CODELIST CL								
	            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	            WHERE 		
	                L.OCCPSTAT NOT IN ('P', 'I') 							
	                AND (		/* Next section commented out to allow future tenants to appear in results (also comment out CMRECC.INEFFECT = 'Y' line above)  */
	                  		(					
	                  			(
	                  				L.RENTSTRT <= GETDATE() 
	                  				OR L.OCCUPNCY <= GETDATE()
	                  				OR L.EXECDATE <= GETDATE()
	                  			)
	                  			AND (				
	                  					L.STOPBILLDATE IS NULL 		
	                  					OR L.STOPBILLDATE >= GETDATE() 		
	                  					OR (		
	                  							L.STOPBILLDATE < GETDATE() 
	                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
	                  						)	
	                  			)			
	                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE() 				
	                  		)					
	                  		OR 					
	                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
	                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
	                  				
	                  	)
	            )LEASED
	            ON B.BLDGID = LEASED.BLDGID
	            AND S.SUITID = LEASED.SUITID
	            LEFT JOIN
              (
        				SELECT DISTINCT
        				NOTB.BLDGID
        				, NOTB.NOTEDATE
        				FROM MRI.NOTB
        				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
        				AND NOTB.NOTEDATE = (
        					SELECT MIN(I.NOTEDATE) 
        					FROM MRI.NOTB I 
        					WHERE I.BLDGID = NOTB.BLDGID  
        					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
        				)
        				AND NOTB.NOTEDATE <= CURRENT_DATE
              )EXCBLDGNOTE
              ON B.BLDGID = EXCBLDGNOTE.BLDGID
	            WHERE (B.INACTIVE <> 'Y' or B.INACTIVE IS NULL)
	            AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
	            AND B.BLDGID IS NOT NULL
	            AND LEASED.OCCP_STATUS IS NULL
	            AND TRY_CAST(B.BLDGID AS INT) IS NOT NULL
	            AND (CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 OR
	                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND S.SUITETYPE_MRI = 'BTS')
	        	)
	        	AND EXCBLDGNOTE.BLDGID IS NULL /* EXCLUDES BUILDING when 'EXCLUDE' building note in effect */
	        
	        
	        	union all
	        
	        
	        	SELECT	/* BYEBYE note additions */
	        	  NOTE.BLDGID
	        	,	CASE WHEN TRY_CAST(NOTE.BLDGID AS INT) IS NULL THEN NOTE.BLDGID ELSE TO_CHAR(TRY_CAST(NOTE.BLDGID AS INT)) END AS BLDG
	        	,	NULL as HRG
	        	,	CONCAT(RTRIM(BLDG.ADDRESS1), (CASE WHEN BLDG.ADDRESS2 IS NOT NULL THEN CONCAT('; ',RTRIM(BLDG.ADDRESS2)) ELSE '' END)) AS BLDGADDRESS
	        	,	rtrim(SUIT.ADDRESS) AS SUITADDRESS
	            ,	BLDG.CITY
	            ,	BLDG.STATE
	            ,	BLDG.ZIPCODE
	            ,	SUIT.SUITID
	        	,	CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) AS SQFT
	        	,	BLDG_SQFT.BLDG_GLA
	        	,	CONCAT('BYEBYE', ' (', RTRIM(NOTE.NOTETEXT), ') Avail ',TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yy')) AS AVAILTYPE
	        	,	TB_CM_SUITETYPE.DESCRIPTION as SUITETYPEDESC
	        	,	ifnull(CASE WHEN BLDG.INACTIVE = 'Y' THEN NULL ELSE rtrim(BLDG.MAP) END,'') AS MAPURL
	        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE rtrim(MNGR.MNGRNAME) END AS CORPPM
	        	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
	  					ELSE trim(case when rtrim(mngr.email) like '%@legacypro.' then replace(rtrim(mngr.email), '@legacypro.', '@legacypro.com') else rtrim(mngr.email) end) END AS CORPPMEMAIL
	        	FROM MRI.NOTE			
	        	LEFT JOIN MRI.BLDG			
	        	ON NOTE.BLDGID = BLDG.BLDGID
	        	left join MRI.MNGR
	        	on bldg.MNGRID = mngr.MNGRID
	        	LEFT JOIN MRI.LEAS
	        	ON NOTE.LEASID = LEAS.LEASID
	        		AND NOTE.BLDGID = LEAS.BLDGID
	        	LEFT JOIN MRI.CODELIST CL								
	        	ON LEAS.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	        	LEFT JOIN MRI.SUIT
	        	ON LEAS.SUITID = SUIT.SUITID
	        		AND LEAS.BLDGID = SUIT.BLDGID
	        	LEFT JOIN 
	            (
	        		SELECT *
	        		FROM MRI.SSQF
	        		WHERE SSQF.EFFDATE = (
	        			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE 
	        			)
	            ) SSQF_TYPE
	            ON LEAS.SUITID = SSQF_TYPE.SUITID
	        		AND LEAS.BLDGID = SSQF_TYPE.BLDGID
	        	left join
	        	(
	        		SELECT SSQF.BLDGID
	        		,	CAST(ROUND(SUM(SQFT), 0) AS INT) AS BLDG_GLA
	        		FROM MRI.SSQF
	        		join MRI.SUIT
	        			ON SSQF.SUITID = SUIT.SUITID
	        			AND SSQF.BLDGID = SUIT.BLDGID
	        		LEFT JOIN MRI.TB_CM_SUITETYPE								
	        		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	        		WHERE SSQF.EFFDATE = (
	                  		SELECT MAX(I.EFFDATE) 
	        				FROM MRI.SSQF I 
	        				WHERE I.BLDGID = SSQF.BLDGID 
	        					AND I.SUITID = SSQF.SUITID 
	        					AND I.EFFDATE <= CURRENT_DATE 
	        			)
	        			AND UPPER(SSQF.SQFTTYPE) = 'GLA'
	        			AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)
	        		GROUP BY SSQF.BLDGID
	            ) BLDG_SQFT
	            ON LEAS.BLDGID = BLDG_SQFT.BLDGID
	        	LEFT JOIN MRI.TB_CM_SUITETYPE
	            ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
	        
	        	LEFT JOIN
	            (
	            SELECT 								
	                L.BLDGID,							
	                RTRIM(P.PORTID) AS Portfolio,
	                L.SUITID,						
	                L.LEASID,							
	                L.OCCPNAME,							
	                TO_DATE(L.RENTSTRT) AS RENTSTRT,							
	                CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
	                CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
	                RTRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
	                L.OCCPSTAT,
	                CL.CODEDESC AS OCCP_STATUS,							
	                TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
	                TO_DATE(L.VACATE) as VACATEDATE,							
	                TO_DATE(L.EXPIR) as EXPIRDATE																																						
	            FROM MRI.LEAS L							
	            INNER JOIN MRI.BLDG B								
	            ON L.BLDGID = B.BLDGID								
	            INNER JOIN MRI.SUIT S								
	            ON L.SUITID = S.SUITID								
	                AND L.BLDGID = S.BLDGID								
	            LEFT JOIN (								
	                SELECT * 							
	                FROM MRI.SSQF 							
	                WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
	            ) SQF								
	            ON S.BLDGID = SQF.BLDGID								
	                AND S.SUITID = SQF.SUITID							
	            LEFT JOIN MRI.TB_CM_SUITETYPE								
	            ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
	            LEFT JOIN MRI.ENTITY E								
	            ON B.ENTITYID = E.ENTITYID								
	            LEFT JOIN MRI.PROJ P								
	            ON E.PROJID = P.PROJID																
	            LEFT JOIN MRI.CODELIST CL								
	            ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	            WHERE 
	        		L.OCCPSTAT NOT IN ('P', 'I') 							
	                AND (
	                  		(					
	                  			(
	                  				L.RENTSTRT <= GETDATE() 
	                  				OR L.OCCUPNCY <= GETDATE()
	                  				OR L.EXECDATE <= GETDATE()
	                  			)
	                  			AND (				
	                  					L.STOPBILLDATE IS NULL 		
	                  					OR L.STOPBILLDATE >= GETDATE()		
	                  					OR (		
	                  							L.STOPBILLDATE < GETDATE() 
	                  							AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()
	                  						)	
	                  			)			
	                  			AND COALESCE(L.VACATE,L.EXPIR)>=GETDATE()				
	                  		)					
	                  		OR 					
	                  		(((L.EXPIR <= GETDATE() OR L.EXPIR IS NULL) AND (L.VACATE>=GETDATE() OR L.VACATE IS NULL))) 					
	                  		AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
	                  				
	                  	)
	            )LEASED
	        	ON LEAS.BLDGID = LEASED.BLDGID
	        	AND LEAS.SUITID = LEASED.SUITID
	        	AND LEAS.LEASID = LEASED.LEASID
          	LEFT JOIN
            (
      				SELECT DISTINCT
      				NOTE.BLDGID
      				, NOTE.LEASID
      				, NOTE.NOTEDATE
      				FROM MRI.NOTE
      				WHERE (NOTE.REF1 = 'EXCLUDE' OR NOTE.REF2 = 'EXCLUDE')
      				AND NOTE.NOTEDATE = (
      					SELECT MIN(I.NOTEDATE) 
      					FROM MRI.NOTE I 
      					WHERE I.BLDGID = NOTE.BLDGID 
      					AND I.LEASID = NOTE.LEASID 
      					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
      				)
      				AND NOTE.NOTEDATE <= CURRENT_DATE
            )EXCLEASNOTE
            ON NOTE.BLDGID = EXCLEASNOTE.BLDGID
            AND NOTE.LEASID = EXCLEASNOTE.LEASID
          	LEFT JOIN
            (
        			SELECT DISTINCT
        			NOTB.BLDGID
      				, NOTB.NOTEDATE
      				FROM MRI.NOTB
      				WHERE (NOTB.REF1 = 'EXCLUDE' OR NOTB.REF2 = 'EXCLUDE')
        			AND NOTB.NOTEDATE = (
        				SELECT MIN(I.NOTEDATE) 
        				FROM MRI.NOTB I 
        				WHERE I.BLDGID = NOTB.BLDGID  
      					AND (I.REF1 = 'EXCLUDE' OR I.REF2 = 'EXCLUDE')
      				)
      				AND NOTB.NOTEDATE <= CURRENT_DATE
            )EXCBLDGNOTE
            ON NOTE.BLDGID = EXCBLDGNOTE.BLDGID
	        	WHERE 			
	        		(BLDG.INACTIVE = 'N' OR BLDG.INACTIVE IS NULL)			
	        		AND (NOTE.REF1 = 'BYEBYE' OR NOTE.REF2 = 'BYEBYE')
	        		--AND CONVERT(DATE, NOTE.NOTEDATE) <= CONVERT(DATE, GETDATE()) /*20240628 notes now added as soon as entered */
	        		AND (TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL)	
	        		AND LEASED.OCCP_STATUS IS NOT NULL
	        		AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
	                AND 
	        		(
	        			CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) > 0 
	        			OR
	                  	(CAST(ROUND(IFNULL(SSQF_TYPE.SQFT,0),0) AS INT) = 0 AND SUIT.SUITETYPE_MRI = 'BTS')
	        		)
	        		AND EXCLEASNOTE.LEASID IS NULL
	        		AND EXCBLDGNOTE.BLDGID IS NULL
	        ) combined
	       LEFT JOIN
	        (
	        	SELECT 								
	        		L.BLDGID,							
	        		TRIM(P.PORTID) AS Portfolio,
	        		L.SUITID,							
	        		L.LEASID,							
	        		L.OCCPNAME,	
	        		L.DBA,
	        		to_date(L.EXECDATE) AS EXECDATE,
	        		to_date(L.RENTSTRT) AS RENTSTRT,							
	        		CAST(S.SUITSQFT AS INT) AS SUITSQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
	        		CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQFSQFT,							
	        		TRIM(SQF.SQFTTYPE) AS SQFTType,														
	        		L.OCCPSTAT,
	        		CL.CODEDESC AS OCCPSTATUS,							
	        		to_date(L.STOPBILLDATE) AS STOPBILLDATE,							
	        		to_date(L.VACATE) as VACATEDATE,							
	        		to_date(L.EXPIR) as EXPIRDATE,																											
	        		S.SUITETYPE_MRI,							
	        		--TB_CM_SUITETYPE.SUITETYPEUSAGE,							
	        		--TB_CM_SUITETYPE.DESCRIPTION AS [SUITETYPEDESCRIPTION],
	        		L.GENCODE,
	        		L.CONTINGENT,
	        		L.CONTINGENTDT,
	        		case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= CURRENT_DATE THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY
	        	FROM MRI.LEAS L
	        	INNER JOIN MRI.BLDG B								
	        	ON L.BLDGID = B.BLDGID								
	        	INNER JOIN MRI.SUIT S								
	        	ON L.SUITID = S.SUITID								
	        		AND B.BLDGID = S.BLDGID								
	        	LEFT JOIN (								
	        		SELECT * 							
	        		FROM MRI.SSQF 							
	        		WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE )							
	        	) SQF								
	        	ON S.BLDGID = SQF.BLDGID								
	        		AND S.SUITID = SQF.SUITID							
	        	--LEFT JOIN TB_CM_SUITETYPE								
	        	--ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
	        	LEFT JOIN MRI.ENTITY E								
	        	ON B.ENTITYID = E.ENTITYID								
	        	LEFT JOIN MRI.PROJ P								
	        	ON E.PROJID = P.PROJID																
	        	LEFT JOIN MRI.CODELIST CL								
	        	ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'
	        	WHERE 		
	        		L.RENTSTRT = (select max(i.RENTSTRT) from MRI.LEAS i where i.BLDGID = L.BLDGID and i.SUITID = L.SUITID and i.RENTSTRT < CURRENT_DATE)
	        )LASTTNT
	        ON combined.BLDGID = LASTTNT.BLDGID
	        AND combined.SUITID = LASTTNT.SUITID	        
	        order by combined.BLDGID, combined.SUITID, combined.AVAILTYPE
      "
    )
    
    #get field staff associated with each bldg
    myquery_LCPAssign <- paste0(
      "
          select  /* SNOWFLAKE version */
          to_char(a.BLDG) \"Bldg\"
          , a.RM
          , case when rm.phone is NULL then NULL
              else '('||substr(rm.phone,1,3)||') '||substr(rm.phone,4,3)||'-'||substr(rm.phone,7,4) end as \"RM Phone\"
          , rm.email as \"RM Email\"
          , a.SUPPORT_LEASING AS \"Support Leasing\"
          , case when a.SUPPORT_LEASING = 'BRH' then '(*************' 
              when sl.phone is NULL then NULL
              else '('||substr(sl.phone,1,3)||') '||substr(sl.phone,4,3)||'-'||substr(sl.phone,7,4) end as \"SL Phone\"
          , case when a.SUPPORT_LEASING = 'BRH' then '<EMAIL>' else sl.email end as \"SL Email\"
          , a.SUPPORT_PROP_MGMT AS \"Support Property Management\"
          , case when rm.phone is NULL then NULL
              else '('||substr(spm.phone,1,3)||') '||substr(spm.phone,4,3)||'-'||substr(spm.phone,7,4) end as \"SPM Phone\"
          , spm.email as \"SPM Email\"
          from CORPORATE.lcp_assignments_sean a
          left join CORPORATE.ab_employees rm
          on upper(a.rm) = upper(rm.fname)||' '||upper(rm.lname)
          and rm.status not in ('T','R','D')
          left join CORPORATE.ab_employees sl
          on upper(a.SUPPORT_LEASING) = upper(sl.fname)||' '||upper(sl.lname)
          and sl.status not in ('T','R','D')
          left join CORPORATE.ab_employees spm
          on upper(a.SUPPORT_PROP_MGMT) = upper(spm.fname)||' '||upper(spm.lname)
          and spm.status not in ('T','R','D')
      "
    )
    myquery_PMPhone <- paste0(
      "
        select /* SNOWFLAKE version */
          email as \"PM_Email\"
        , '(847) 904-'||extension as \"PM_Phone\"
        from CORPORATE.ab_employees
        where status not in ('T','R','D')
        and extension is not null
        and email is not null
      "
    )
    
    #20241011: mydata <- sqlQuery(mySSdb, myquery, stringsAsFactors = FALSE)
    mydata <- dbGetQuery(mySfDB, myquery)
    mydata_status <- check_mydf_rows(mydf = mydata, MinNumRows = 3, ReportName = myReportName)
    #20241011: mydata_Prior <- sqlQuery(mySSdb, myquery_Prior, stringsAsFactors = FALSE)
    mydata_Prior <- dbGetQuery(mySfDB, myquery_Prior)
    mydata_status_Prior <- check_mydf_rows(mydf = mydata_Prior, MinNumRows = 2, ReportName = myReportName)
    mydata_PMPhone <- dbGetQuery(mySfDB, myquery_PMPhone)
    mydata_status_PMPhone <- check_mydf_rows(mydf = mydata_PMPhone, MinNumRows = 1, ReportName = myReportName)
    mydata_LCPAssign <- dbGetQuery(mySfDB, myquery_LCPAssign)
    mydata_status_LCPAssign <- check_mydf_rows(mydf = mydata_LCPAssign, MinNumRows = 1, ReportName = myReportName)
    
    if(mydata_status[[1]] == TRUE && 
       mydata_status_Prior[[1]] == TRUE && 
       mydata_status_PMPhone[[1]] == TRUE && 
       mydata_status_LCPAssign[[1]] == TRUE){
      #drop BLDGID column (as-is MRI varchar column, BLDG column is 'numeric' entries converted to integer)
      mydata <- mydata[, names(mydata) != c("BLDGID") ]
      mydata_Prior <- mydata_Prior[, names(mydata_Prior) != c("BLDGID") ]
      
      #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
      mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
      mydata_Prior[] <- lapply(mydata_Prior[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
      
      #join PM phone to MRI data
      mydata <- mydata %>%
        dplyr::left_join(mydata_PMPhone, by = "PM_Email")
      mydata <- mydata %>% relocate(PM_Phone, .before = PM_Email)
      mydata_Prior <- mydata_Prior %>%
        dplyr::left_join(mydata_PMPhone, by = "PM_Email")
      mydata_Prior <- mydata_Prior %>% relocate(PM_Phone, .before = PM_Email)
      
      
      #add LCP Assignments to mydata
      mydata <- mydata %>%
        dplyr::left_join(mydata_LCPAssign, by = 'Bldg')
      mydata_Prior <- mydata_Prior %>%
        dplyr::left_join(mydata_LCPAssign, by = 'Bldg')
      
      
      #replace headers to replace underscores with spaces
      names(mydata) <- gsub("_"," ",names(mydata))
      names(mydata_Prior) <- gsub("_"," ",names(mydata_Prior))
      
      # add 'Updated' column at end of mydata
      mydata[, myupdated[1,1]] <- NA
      mydata_Prior[, myupdated[1,1]] <- NA
      
      
      #20240628 NEW, read existing 'Availability' data in Portfolio workbook
      #this will be used later to determine this loads additions to Google Sheet
      gSht_existing <- range_read(gSht_get_2$spreadsheet_id, sheet = myMRISheet_2)
      #20241011: gSht_existing_avail <- data.frame("Bldg" = integer(), 
      gSht_existing_avail <- data.frame("Bldg" = character(),
                                        "Suite ID" = character(),
                                        check.names=FALSE)
      gSht_existing_dev <- data.frame("Bldg" = character(),
                                      "Primary Address" = character(), #this is used to determine unique locations, but won't be sent in notification email
                                      "Suite ID" = character(), 
                                      check.names=FALSE)
      #divide general Available (Bldg are integers) and Development rows (Bldg are alpha)
      if(nrow(gSht_existing)>1){
        for(i in 1:nrow(gSht_existing)){
          if(class(gSht_existing$Bldg[[i]])=="numeric"){
            #Bldg is numeric, as of 20241011 version, convert Bldg to string
            gSht_existing_avail <- gSht_existing_avail %>%
              add_row(
                "Bldg" = as.character(gSht_existing$Bldg[[i]]), 
                "Suite ID" = gSht_existing$`Suite ID`[[i]]
              )
          }else if(class(gSht_existing$Bldg[[i]])=="character"){
            #
            gSht_existing_dev <- gSht_existing_dev %>%
              add_row(
                "Bldg" = gSht_existing$Bldg[[i]], 
                "Primary Address" = gSht_existing$`Primary Address`[[i]],
                "Suite ID" = gSht_existing$`Suite ID`[[i]]
              )
          }
        }
      }
      
      
      #UPDATE MRI sheet
      #clear existing data in MRI sheet
      range_clear(gSht_get$spreadsheet_id, sheet = myMRISheet, range = NULL, reformat = FALSE)
      #write new data
      myupdate_col <- ncol(mydata)
      #sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = myMRISheet)
      range_write(ss = gSht_get$spreadsheet_id, 
                  data = mydata, 
                  sheet = myMRISheet, 
                  #range = cell_rows(mypaste_row:(mypaste_row+nrow(mydata_paste_curr)-1) ), 
                  col_names = TRUE,
                  reformat = FALSE
      )
      range_autofit(ss = gSht_get$spreadsheet_id, sheet = myMRISheet, range = cell_cols(c(myupdate_col)) )
      Sys.sleep(2)
      

      #clear and write to second Availability Google sheet (Portfolio workbook)
      range_clear(gSht_get_2$spreadsheet_id, sheet = myMRISheet_2, range = NULL, reformat = FALSE)
      #sheet_write(mydata, ss = gSht_get_2$spreadsheet_id, sheet = myMRISheet_2)
      range_write(ss = gSht_get_2$spreadsheet_id, 
                  data = mydata, 
                  sheet = myMRISheet_2, 
                  #range = cell_rows(mypaste_row:(mypaste_row+nrow(mydata_paste_curr)-1) ), 
                  col_names = TRUE,
                  reformat = FALSE
      )
      range_autofit(ss = gSht_get_2$spreadsheet_id, sheet = myMRISheet_2,  range = cell_cols(c(myupdate_col)) )
      
      Sys.sleep(2)
      
      #write PRIOR tenant data
      #myPriorSheet mydata_Prior
      myupdate_col_prior <- ncol(mydata_Prior)
      range_clear(gSht_get_2$spreadsheet_id, sheet = myPriorSheet, range = NULL, reformat = FALSE)
      #write new data
      range_write(ss = gSht_get_2$spreadsheet_id, 
                  data = mydata_Prior, 
                  sheet = myPriorSheet, 
                  col_names = TRUE,
                  reformat = FALSE
      )
      range_autofit(ss = gSht_get_2$spreadsheet_id, sheet = myPriorSheet, range = cell_cols(c(myupdate_col_prior)) )
      Sys.sleep(2)
    }
    
    
  }else{
    #MRI sheet not found, warn
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine!</p>",
                       "<p>There weren't any sheets named like '", myMRISheet, "' ",
                       #"<p>There weren't any sheets with the expected names (", 
                       #paste(mySheets, collapse = "; "),
                       " found in the '", gSht_get$name, "' workbook.",
                       "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Missing expected sheet"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}



#Gather Development Availability data
if(okaytocontinue){
  #is "Development Availability" sheet is populated manually?
  mySheets_Dev <- "Development Availability"
  #Suite Name columns like
  SuiteName_colname <- "Available Suite ID"
  SuiteSqft_colname <- "Suite SQFT"
  dev_fnd <- FALSE
  
  if(mySheets_Dev %in% gSht_get_2$sheets$name){
    myColNames <- c(
      "BLDG",
      "Address",
      "City",
      "State",
      "Zip Code",
      "Sqft Avail",
      "Total SF",
      "Site Link",
      "Anticipated Close Date"
    )
    myColNames_New <- c(
      "Bldg",
      "Primary Address",
      "City",
      "State",
      "Zip Code",
      "SQ FT",
      "BLDG GLA",
      "Google Map URL",
      "Avail Date"
    )
    
    #read sheet into temp df and select only the needed columns
    gSht_Curr <- read_sheet(gSht_get_2$spreadsheet_id, sheet = mySheets_Dev)
    if(nrow(gSht_Curr)>0){
      #get main BLDG info
      gSht_Dev <- gSht_Curr[,myColNames]
      #find last 'probable' row (more than 2 columns populated)
      last_row_vector <- sapply(gSht_Dev, function(z) c(rev(which(!is.na(z))), 0)[1])
      last_row <- sort(table(last_row_vector),decreasing=TRUE)[1] %>% names() %>% as.numeric()
      if(last_row > 0){
        dev_fnd <- TRUE
        gSht_Dev <- gSht_Dev[1:last_row, ]
        #convert NULL in columns to NA so they don't populate 'NULL' in Google
        for(i in 1:ncol(gSht_Dev)){
          templist <- apply(gSht_Dev[,i][1], 2, as.list) %>% .[[1]]
          templist[sapply(templist, is.null)] <- NA
          gSht_Dev[,myColNames[i]] <- unlist(templist)
        }
        
        
        #convert `Anticipated Close Date` to a date (from character)
        names_class <- data.frame(ColName = "Anticipated Close Date", DataType = "date")
        gSht_Dev <- Convert_to_ColClass(gSht_Dev, names_class)
        #"Avail Type" column should be "Development Avail "||format(`Anticipated Close Date`,'%m/%d/%y')
        gSht_Dev$`Avail Type` <- paste0("Development Avail ", format(gSht_Dev$`Anticipated Close Date`,'%m/%d/%y'))
        
        
        #20231103 addition, join suites to Developement buildings
        gSht_Suites <- gSht_Curr[1:last_row, ]
        #find columns like SuiteName_colname, SuiteSqft_colname
        Suite_names <- grepl(SuiteName_colname, names(gSht_Suites))
        Suite_sqft <- grepl(SuiteSqft_colname, names(gSht_Suites))
        Suite_names_present <- which(!is.na(gSht_Suites[, Suite_names]), arr.ind = TRUE) %>%
          .[order(.[,1], decreasing = FALSE),]
        if(length(Suite_names_present) > 0){
          SuiteID_colname <- "Suite ID"
          SuiteSF_colname <- "Sqft Avail"
          #add Suite ID column
          gSht_Dev[,SuiteID_colname] <- as.character(NA)
          
            
          Suite_rows <- unique(Suite_names_present[,1])
          Suites <- gSht_Dev[0, ]
          for(ste_row in Suite_rows){
            #create new bldg rows where suites are present
            mycols <- Suite_names_present[which(Suite_names_present[,1] == ste_row), 2]
            ids <- gSht_Suites[, Suite_names][ste_row, mycols]
            sqft <- gSht_Suites[, Suite_sqft][ste_row, mycols]
            new_rows <- gSht_Dev[rep(ste_row, length(mycols)), ]
            new_rows[, SuiteID_colname] <- as.character(ids[1, ])
            new_rows[, SuiteSF_colname] <- as.character(sqft[1, ])
            Suites <- rbind(Suites, new_rows)
          }
          #remove old bldg rows which will be replaced by Suites data
          gSht_Dev <- gSht_Dev[-Suite_rows,]
          gSht_Dev <- rbind(gSht_Dev, Suites)
        }
        
        #rename columns
        setnames(gSht_Dev, 
                 old = myColNames, 
                 new = myColNames_New,
                 skip_absent = TRUE)
        #get Availability columns NOT present in Dev
        Avail_names <- names(mydata)
        Curr_names <- names(gSht_Dev)
        Missing_names <- setdiff(Avail_names, Curr_names)
        #add blank columns needed to match Availability and then reorder to match
        gSht_Dev[, Missing_names] <- NA
        gSht_Dev <- gSht_Dev[, Avail_names]
        #append rows to end of main Portfolio Availability sheet sheet_append()
        #first avail sheet
        sheet_append(
          ss = gSht_get$spreadsheet_id, 
          data = gSht_Dev, 
          sheet = myMRISheet
        )
        #second avail sheet
        sheet_append(
          ss = gSht_get_2$spreadsheet_id, 
          data = gSht_Dev, 
          sheet = myMRISheet_2
        )
      }
    }
  }
}



#Compare prior Availability suites to this update and email new additions and removals (each their own email)
if(okaytocontinue){
  additions_fnd <- FALSE
  removals_fnd <- FALSE
  avail_cols <- c("Bldg","City","State", "Suite ID", "SQ FT", "Avail Type")
  removal_cols <- c("Bldg","City","State", "Suite ID", "SQ FT", "Avail Type","Corporate PM")
  dev_cols <- avail_cols
  #TEST ALTER EXISTING TO REMOVE ONE OR MORE SUITES (to generate test 'additions')
  #rm(avail_additions, dev_additions)
  #gSht_existing_avail <- gSht_existing_avail[c(1:3,6:23,25:nrow(gSht_existing_avail)),]
  #gSht_existing_dev <- gSht_existing_dev[c(1,3),]
  
  if(nrow(mydata)>0){
    avail_additions <- setdiff(mydata[,c("Bldg","Suite ID")], gSht_existing_avail)
    if(nrow(avail_additions)>0){
      additions_fnd <- TRUE
      #get additional columns for each addition found ("Avail Type")
      avail_additions_email <- left_join(
        #avail_additions, mydata[, c("Bldg", "Suite ID", "Avail Type")],
        avail_additions, mydata[, avail_cols],
        by = join_by(Bldg, `Suite ID`)
      )
      #reorder columns
      additions_email <- avail_additions_email[,avail_cols]
    }
  }
  if(nrow(gSht_existing_avail)>0){
    avail_removals <- setdiff(gSht_existing_avail,mydata[,c("Bldg","Suite ID")])
    if(nrow(avail_removals)>0){
      removals_fnd <- TRUE
      #get additional columns for each removal found ("Avail Type")
      avail_removals_email <- left_join(
        avail_removals, gSht_existing[,removal_cols],
        by = join_by(Bldg, `Suite ID`)
      )
      #reorder columns
      removals_email <- avail_removals_email[,removal_cols]
    }
  }
  if(dev_fnd){
    dev_additions <- setdiff(gSht_Dev[,c("Bldg", "Primary Address", "Suite ID")], gSht_existing_dev)
    if(nrow(dev_additions)>0){
      additions_fnd <- TRUE
      dev_additions_email <- left_join(
        #dev_additions, gSht_Dev[, c("Bldg", "Primary Address", "Suite ID", "Avail Type")],
        dev_additions, gSht_Dev[, c("Primary Address", dev_cols)],
        by = join_by(Bldg, `Primary Address`, `Suite ID`)
      )
      #reorder columns (and drop any no longer needed...i.e. "Primary Address")
      dev_additions_email <- dev_additions_email[,dev_cols]
      if(exists("additions_email")){
        additions_email <- rbind(additions_email, dev_additions_email)
      }else{
        additions_email <- dev_additions_email
      }
      #dev_additions_text <- paste0(
      #  "<p>Development Acquisition Additions:",
      #  print(
      #    xtable(dev_additions_email, 
      #           digits = rep(0,ncol(dev_additions_email)+1)
      #    ),
      #    html.table.attributes = "border=2 cellspacing=1",
      #    type = "html",
      #    caption.placement = "top",
      #    include.rownames=FALSE
      #  ),
      #  "</p>"
      #)
    }
  }

  if(additions_fnd){
    avail_additions_text <- paste0(
      print(
        xtable(additions_email, 
               digits = rep(0,ncol(additions_email)+1)
        ),
        html.table.attributes = "border=2 cellspacing=1",
        type = "html",
        caption.placement = "top",
        include.rownames=FALSE
      ),
      "<br>"
    )
    body_html <- paste0(
      "<html><head></head><body>",
      "<h2>New Locations Added to 'Availability'!</h2>",
      "<p>The following locations were added to the ",
      "<a href=\"", gSht_Sheets_2_url, "\">", gSht_Sheets_2, "</a>",
      " sheet of the ", gSht_get_2$name, " workbook:</p>",
      #if(exists("avail_additions_text")){avail_additions_text},
      #if(exists("dev_additions_text")){dev_additions_text},
      avail_additions_text,
      "<br>",
      norm_sig,
      "</body></html>"
    )
    #send mail
    mailsend(recipient = norm_recip,
             subject = paste0("New 'Availability' Spaces"),
             body = body_html,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
  #send removals if applicable
  if(removals_fnd){
    avail_removals_text <- paste0(
      print(
        xtable(removals_email, 
               digits = rep(0,ncol(removals_email)+1)
        ),
        html.table.attributes = "border=2 cellspacing=1",
        type = "html",
        caption.placement = "top",
        include.rownames=FALSE
      ),
      "<br>"
    )
    body_html <- paste0(
      "<html><head></head><body>",
      "<h2>New Locations REMOVED FROM 'Availability'!</h2>",
      "<p>The following locations were <strong>removed</strong> from the ",
      "<a href=\"", gSht_Sheets_2_url, "\">", gSht_Sheets_2, "</a>",
      " sheet of the ", gSht_get_2$name, " workbook:</p>",
      avail_removals_text,
      "<br>",
      norm_sig,
      "</body></html>"
    )
    #send mail
    mailsend(recipient = removals_recip,
             subject = paste0("Removed 'Availability' Spaces ",emoji_noentry),
             body = body_html,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


#close connections
odbcCloseAll()



