library(xtable)
library(reshape2)
library(dplyr)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(tidyr)
library(DBI)
library(odbc)
library(keyring)
library(janitor)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20250206

### 20250206 change
### starting with FY 2025, pull out avg daily GM hours (for stores with GM of record)
### when calculating the IDEAL_LABOR_DOLLARS column. The hours are NOT taken out of the IDEAL_ADJ_HOURS.
### the parameter HoursWk_<PERSON> holds the weekly GM expected hours (50 at time of change).

### 20240916 change
### converted from mailR package (SMTP), to gmailr (OAuth-GMail API) ahead of 20240930 SMTP deprecation in GMail
### changed signature HTML img src to use published HVLTD logos

### 20240826 change
### moved to Tableau login so it can be run on that PC

### 20240806 change:
### new script
### modified 20240729 version of MARCOS_MOMS_ORDERS_SUMMARY_DAILY_Load.R script 
### to load MOMS.LABOR_SUMMARY_DAILY_IDEAL
### daily NET_SALES is dependent on MOMS.ORDERS_SUMMARY_DAILY table for historical AND MOM.ORDERSRESULT for last two weeks

# Parameters
okaytocontinue <- TRUE

myReportName <- "Marco's MOMS.LABOR_SUMMARY_DAILY_IDEAL-Snowflake Load"
scriptfolder <- "MARCOS_Daily_Reports"
rptfolder <- "reports"
#20240826: use network shared drive paths as default, but can be changed depending on environment further down
logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")

###STAGE Snowflake versions###
#Sf_DB <- "STAGE_CSM_DB"
#Sf_schema <- "CORPORATE"
#Sf_wh <- "STAGE_DATA_ANA_WH"
#Sf_role <- "AR_STAGE_CONSUMPTION_RW"
#Sf_user <- key_get("SfHV", "steve_ID")
#Sf_pw <- key_get("SfHV", "steve_PW")
###PROD Snowflake versions###
Sf_DB <- "PROD_CSM_DB"
Sf_schema <- "CORPORATE"
Sf_wh <- "PROD_DATA_ANA_WH"
Sf_role <- "AR_PROD_CONSUMPTION_RW"
Sf_user <- key_get("SfHV", "tableau_ID_prod")
Sf_pw <- key_get("SfHV", "tableau_PW_prod")

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role
)
rm(Sf_user,Sf_pw)

mySchema <- "CORPORATE"
myTable <- "MP_LABOR_SUMMARY_DAILY_IDEAL"
myTableName <- paste(mySchema, myTable, sep = ".")

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_recip <- c("<EMAIL>") #testing purposes, normal is line above
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

test_computers <- c("STEVEANDJENYOGA")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to shared/central drive instead of local paths
}else{
  testing_pc <- FALSE
}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  # default assigned paths above are now network paths
}else if(this_computer == "DESKTOP-TABLEAU"){
  logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")
}else if(this_computer == "STEVEO-PLEX7010"){
  logpath <- file.path("C:","Users","steve","Documents","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("C:","Users","steve","Documents","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("C:","Users","steve","Documents","ReportFiles","HTML_signatures.csv")
}


myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo
  #20240916: new sig image
  #(HVLTD Corp with Brands)
  sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
  if(exists("norm_sig")){norm_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
}

# date and time variables
query.date <- format(Sys.Date(), "%Y-%m-%d")
#query.date <- "2024-02-26" # 1/1/24:3/24/24 testing or re-load prior date line only
#query.date <- "2024-05-20" # 3/25/24:6/16/24 testing or re-load prior date line only
#query.date <- "2024-08-12" #6/17/24:8/05/24 testing or re-load prior date line only

query.periods <- 2 #total number of periods to cover in update query
query.period_offset <- 0 #trailing period(s) for END of update query (start is s_date for query.periods + query.period_offset)
rpt.date <- as.Date(query.date, "%Y-%m-%d")



### define some functions ###
mailsend <- function(
    recipient, subject, body, attachment = NULL, inline = FALSE, 
    sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  #attach inline images
  
  gmailr::gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



# Query Snowflake for recent LABOR summary data, 
if(okaytocontinue){
  #Get dates from MP_CALENDAR for main query
  myquery <- paste0(
    "
      select
      to_char(to_date(min(s_date))) as s_date
      , to_char(to_date(max(case when e_date > (TRUNC(CURRENT_DATE, 'week') - 1) then (TRUNC(CURRENT_DATE, 'day') - 1) else e_date end))) as e_date
      from CORPORATE.mp_calendar
      where cal_id >= (
        select i.cal_id - ", query.periods + query.period_offset, "
        from CORPORATE.mp_calendar i
        where to_date('", query.date, "') between i.s_date and i.e_date
      )
      and cal_id <= (
        select i.cal_id - ", query.period_offset, "
	      from CORPORATE.mp_calendar i
	      where to_date('", query.date, "') between i.s_date and i.e_date
      )
    "
  )
  mydates <- dbGetQuery(mySfDB, myquery)
  
  #Main Data Query
  HoursWk_GM <- 50 #Avg GM hours per week = 50 as of start of FY 2025 (2024-12-30)
  myquery <- paste0(
    "
      SELECT 
      	ORDERS.STORE_NUMBER,
      	DATE_TRUNC(\"DAY\",ORDERS.BUSINESS_DATE) AS BUSINESS_DATE,
      	MCW.PERIOD_YEAR,
      	MCW.PERIOD_NUM,
      	MCW.WEEK_NUM,
      	DATE_TRUNC(\"DAY\",MCW.E_DATE) AS WEEK_E_DATE,
      	ORDERS.NET_SALES,
      	MPT.TIER,
      	CASE WHEN NVL(MLTA.HOURS_MIN,0) > MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) 
              THEN MLTA.HOURS_MIN 
              ELSE MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1)
              END 
              AS IDEAL_ADJ_HOURS, /* Starting with FY 2025, this is HOURS without avg GM hours */
      	CASE WHEN NVL(MLTA.HOURS_MIN,0) > MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) 
              THEN MLTA.HOURS_MIN 
              --ELSE MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) - (50/7 * IFNULL(MGR.CNT, 0)) /* TESTING LINE, assumes GM 50 hour week */
              ELSE MLDI.HOURS * NVL(MLTA.HOURS_FACTOR,1) - (", HoursWk_GM , "/7 * IFNULL(MGR.CNT, 0))
              END * MLWR.WAGE_RATE_GOAL 
              AS IDEAL_LABOR_DOLLARS /* Starting with FY 2025, this is $ without avg GM hours */
      FROM 
      (
      	SELECT /* LIVE orders info */
      		ORD.STORE_NUMBER
      	,    DATE_TRUNC(\"DAY\",ORD.BUSINESS_DATE) AS BUSINESS_DATE
      	,    round(sum(ORD.net),2) as NET_SALES
      	FROM MOMS.ORDERSRESULT ORD
      	WHERE VOID = 0
      	--	AND ORD.BUSINESS_DATE >= TRUNC(TO_DATE('2024-08-06', 'YYYY-MM-DD'), 'week') - 7 /* TESTING LINE */
      	--	AND ORD.BUSINESS_DATE <= TO_DATE('2024-08-06', 'YYYY-MM-DD') /* TESTING LINE */
      		AND ORD.BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "') /* report start date */
      		AND ORD.BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "') /* report end date */
      	GROUP BY ORD.STORE_NUMBER
      	,	DATE_TRUNC(\"DAY\",ORD.BUSINESS_DATE)
      ) ORDERS
      LEFT JOIN CORPORATE.MP_CALENDAR_WEEKLY MCW
      ON ORDERS.BUSINESS_DATE >= MCW.S_DATE
      	AND ORDERS.BUSINESS_DATE <= MCW.E_DATE
      LEFT JOIN CORPORATE.MP_LABOR_DAILY_IDEAL mldi 
      ON TRUNC(ORDERS.NET_SALES) >= mldi.SALES_LOW 
      	AND TRUNC(ORDERS.NET_SALES) <= mldi.SALES_HIGH 
      	AND ORDERS.BUSINESS_DATE >= MLDI.S_DATE
      	AND (ORDERS.BUSINESS_DATE <= MLDI.E_DATE OR MLDI.E_DATE IS NULL)
      LEFT JOIN CORPORATE.MP_PRICING_TIER mpt 
      ON ORDERS.STORE_NUMBER = MPT.STORE_NUMBER 
      	AND ORDERS.BUSINESS_DATE >= MPT.S_DATE
      	AND (ORDERS.BUSINESS_DATE <= MPT.E_DATE OR MPT.E_DATE IS NULL)
      LEFT JOIN CORPORATE.MP_LABOR_TIER_ADJ mlta 
      ON ORDERS.BUSINESS_DATE >= MLTA.S_DATE
      	AND (ORDERS.BUSINESS_DATE <= MLTA.E_DATE OR MLTA.E_DATE IS NULL)
      	AND MPT.TIER = MLTA.TIER
      LEFT JOIN CORPORATE.MP_LABOR_WAGE_RATE_GOAL mlwr
      ON ORDERS.STORE_NUMBER = MLWR.STORE_NUMBER
      	AND ORDERS.BUSINESS_DATE >= MLWR.S_DATE
      	AND (ORDERS.BUSINESS_DATE <= MLWR.E_DATE OR MLWR.E_DATE IS NULL)
      --WHERE ORDERS.BUSINESS_DATE >= TO_DATE('2024-01-01', 'YYYY-MM-DD') /* TESTING LINE */
      LEFT JOIN
      ( /* added 20250206 per Deanna request to remove $ above equivalent to avg daily GM hours (50 hrs/7 days) */
      	SELECT DATEADD('DAY', 1, WEH.SUNDAY) AS S_DATE
      	, WEH.HOME_STORE AS STORE_NUMBER
      	, COUNT(WEH.*) AS CNT
      	FROM CORPORATE.FAMV_WEEKLY_EMPLOYEE_HISTORY WEH
      	LEFT JOIN CORPORATE.FAMV_PAYROLL_POSITION FPP
      	ON WEH.POSITION = FPP.POSITION
      	WHERE FPP.SUB_CATEGORIES LIKE '%MGR_HISTORY%'
      	--AND WEH.HOME_STORE = 3536 /* TESTING LINE ONLY */
      	AND DATEADD('DAY', 1, WEH.SUNDAY) >= '2024-12-30'::TIMESTAMPNTZ  /* START IN FY 2025 ONLY */
      	GROUP BY DATEADD('DAY', 1, WEH.SUNDAY)
      	, WEH.HOME_STORE
      ) MGR
      ON ORDERS.STORE_NUMBER = MGR.STORE_NUMBER  
      AND MCW.S_DATE = MGR.S_DATE 
      ORDER BY ORDERS.BUSINESS_DATE, ORDERS.STORE_NUMBER
    "
  )
  mydata <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = (28 * (query.periods - 1)), ReportName = myReportName)
  if(mydata_status[[1]]){
    #ok...do nothing
    
  }else{
    #apparent error, send warning email
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error querying the man-hours for the ", myReportName, " routine! ",
                       "The query failed or produced less than 24 rows of results.</p>",
                       "<br>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, missing data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}





#Load Snowflake
if(okaytocontinue){
  #Delete rows within expected date range and replace with current data
  myDeleteRows_failed <- FALSE
  myLoadRows_failed <- FALSE
  myDeleteError_text <- ""
  
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName, "
      where BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
      and BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
    "
  )
  select_cnt <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
  dbBegin(mySfDB)
  myquery_delete <- paste0(
    "
      delete from ", myTableName, "
      where BUSINESS_DATE >= to_date('", mydates$S_DATE[1], "')
      and BUSINESS_DATE <= to_date('", mydates$E_DATE[1], "')
      "
  )
  rs_del <- dbSendQuery(mySfDB, myquery_delete)
  delete_cnt <- dbGetRowsAffected(rs_del)
  if(delete_cnt != select_cnt){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(mySfDB)
    myDeleteRows_failed <- TRUE
    myDeleteError_text <- paste0(
      "<p>There was an unexpected issue deleting previous data that might ",
      "have been present for dates between ", mydates$S_DATE[1],
      " and ", mydates$E_DATE[1],". <b>The routine has ",
      "ABORTED without attempting to load the current results!</b></p>"
    )
    dbClearResult(rs_del)
    #do not load since deletion apparently failed
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    dbCommit(mySfDB)
    dbClearResult(rs_del)
    #Insert trans rows into myTable, count rows before and after to catch load issues
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName, "
        where BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
        and BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
      "
    )
    select_cnt_pre <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    rs_write <- dbWriteTable(mySfDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    #get new count of rows in table
    select_cnt_post <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    myload_numrows <- select_cnt_post - select_cnt_pre
    mydata_numrows <- nrow(mydata)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, get load counts by store
      myquery <- paste0(
        "
          select store_number
          ,   count(BUSINESS_DATE) as COUNT_LOADED
          from ", myTableName, "
          where BUSINESS_DATE >= to_date('", mydates$S_DATE[1], "')
            and BUSINESS_DATE <= to_date('", mydates$E_DATE[1], "')
          group by store_number
          order by store_number
          "
      )
      myLoadStores_curr <- dbGetQuery(mySfDB, myquery)
      #summarize dataframe to get counts by store
      myLoadStores_results_cnt <- mydata %>% select(STORE_NUMBER) %>% group_by(STORE_NUMBER) %>% summarize(QUERY_RESULTS = n())
      myLoad_failed <- myLoadStores_results_cnt %>% 
        dplyr::left_join(myLoadStores_curr, by = c("STORE_NUMBER")) %>%
        mutate_if(is.numeric,coalesce,0) %>%
        .[which(.$QUERY_RESULTS != .$COUNT_LOADED), ]
      
      
      if(nrow(myLoad_failed)>0){
        myLoadRows_failed <- TRUE
        myLoadError_text <- paste0(
          "<p>One or more stores failed to load the expected number or rows ",
          "into Snowflake for the dates between ", mydates$S_DATE[1],
          " and ", mydates$E_DATE[1],". <b>The Snowflake table (",
          myTableName, ")", " will have incomplete results for these dates!</b> ",
          "Investigate the stores shown below and re-run this routine when issue ",
          "has been addressed.<br>",
          print(
            xtable(myLoad_failed, 
                   digits = rep(0,ncol(myLoad_failed)+1),
                   align = c(rep("c", ncol(myLoad_failed) + 1))
            ),
            html.table.attributes = 'border=2 cellspacing=1 align="center"',
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          ),
          "</p>"
        )
      }
    }
    
    
    
  }
  if(myDeleteRows_failed || myLoadRows_failed){
    #email warning
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error populating Snowflake in the ", 
                       myReportName, " routine. </p>",
                       if(myDeleteRows_failed){myDeleteError_text},
                       if(myLoadRows_failed){myLoadError_text},
                       "<br>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Snowflake load error"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}

