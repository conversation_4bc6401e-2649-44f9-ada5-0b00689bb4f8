library(dplyr)
library(tidyverse)
library(googlesheets4)
library(RODBC)
library(mailR)
library(xtable)
library(lubridate)
library(formattable)
library(stringr)
library(readr)
library(data.table)
library(scales)
library(cellranger)
library(keyring)
library(DBI)
library(ROracle)


### Written November 2020 by <PERSON>
# This routine downloads Rachael <PERSON> google sheet that contains sqft of
# lease spaces and our stores and populates an Oracle table to ease
# use of the data in Tableau reporting (datasource "FV Piece count per SQFT")

# Version 20220606

### 20220606 change:
### updated mailsend to use keyring

### 20210820 change:
### updated log save location from fvmc folder to hv folder

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)

testing_emails <- FALSE  #NORMAL, next line over-rides & should be disabled in PRODUCTION instance
#testing_emails <- TRUE

testing_pc <- FALSE  #NORMAL, next line over-rides and should be disabled in PRODUCTION instance
#testing_pc <- TRUE
if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010"){
  testing_pc <- TRUE  #TESTING, changes some paths to <PERSON>'s PC instead of R/Tableau PC
}
testing_pc_location <- "Office"
#testing_pc_location <- "Laptop"


# Needed Dates
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replaces line above for test purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")

routine_name_main <- "Prop SqFt to Oracle"
report.date <- query.date %>% as.Date("%d-%b-%y") %>% format("%Y%m%d")  # YYYYMMDD format of date for saves of report files
myFN <- paste0(routine_name_main, "-", report.date, ".csv")
myVendorDate <- data.frame(QUERY_DATE = query.date %>% as.Date("%d-%b-%y") )

# insert a special note below the header in the message body of ALL RM or DM EMAILS if text is present below AND
# the emailnote.querydate is set to the QUERY DATE of the report being sent;
# '<span style="color: #0000ff;">' in the HTML is for BLUE text for the note
emailnote_DM.querydate <- "25-JUL-20"
emailnote_DM <- paste0(
  '<p><span style="color: #0000ff;">',
  "NOTE: ",
  "",
  "",
  "</span></p>"
)

emailnote_RM.querydate <- "25-JUL-20"
emailnote_RM <- paste0(
  '<p><span style="color: #0000ff;">',
  "NOTE: ",
  "",
  "",
  "</span></p>"
)
emailnote_Corp.querydate <- "25-JUL-20"
emailnote_Corp <- paste0(
  '<p><span style="color: #0000ff;">',
  "NOTE: ",
  "",
  "",
  "</span></p>"
)


# Check if email note period matches report period
if(toupper(emailnote_DM.querydate) == toupper(query.date)){
  attachemailnote_DM <- TRUE
}else{
  attachemailnote_DM <- FALSE
}
if(toupper(emailnote_RM.querydate) == toupper(query.date)){
  attachemailnote_RM <- TRUE
}else{
  attachemailnote_RM <- FALSE
}
if(toupper(emailnote_Corp.querydate) == toupper(query.date)){
  attachemailnote_Corp <- TRUE
}else{
  attachemailnote_Corp <- FALSE
}


#mydb <- odbcConnect("FVPA64", "deanna", key_get("Oracle", "deanna"))
#Oracle connection
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password = key_get("Oracle", "steve"), dbname = connect.string)
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')

#report.time <- format(Sys.time(), "%H:%M:%S")
report.time <- Sys.time()
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

logname <- "MyPropertySF-Log.csv"

logpath <- file.path("C:","Users","table","Documents","ReportFiles","HV_Prop_SqFt")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
gsheet.tokenpath  <- file.path("C:","Users","table",".R","gargle","gargle-oauth")
#myTableName <- "STEVE.SO_PROP_SQFT"
myTableName <- "SO_PROP_SQFT"

if(testing_pc){
  if(testing_pc_location == "Office"){
    # Steve PC testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_Prop_SqFt")
    HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
    gsheet.tokenpath  <- file.path("C:","Users","Steve",".R","gargle","gargle-oauth")
  }
  if(testing_pc_location == "Laptop"){
    # Steve HOME laptop testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("E:","Steve","Documents","R Scripts","TestScripts","HV_Prop_SqFt")
    HVSigLogopath <- file.path("E:","Steve","Documents","R Scripts","TestScripts","HV Logo Email Signature.png")
    gsheet.tokenpath  <- file.path("C:","Users","Steven",".R","gargle","gargle-oauth")
  }



}




orig_wd <- getwd()
gSht_URL <-  "https://docs.google.com/spreadsheets/d/1yYCzMX1uZ1lfAw0f1zZ7IJVT4F1j608AEVuGBaBFZ1g/edit#gid=1334170846"
gSht_Key <- '1yYCzMX1uZ1lfAw0f1zZ7IJVT4F1j608AEVuGBaBFZ1g'
gSht_BuildingSN <- "Building"
gSht_FVStColName <- ""
gSht_MPStColName <- "Marcos"
gSht_SFStColName <- "StayFit 24"
gSht_HPWIStColName <- "HPWI"
gSht_VETStColName <- "Family Vet"
gSht_LeaseColName <- "Lease Name"
gSht_SqFtColName <- "SFT"
gSht_BlkListColNames <- c()


# email parameters: recipient(s) of warning emails and signatures
corp_recip <- c("<EMAIL>")
#corp_recip <- c("<EMAIL>")
warn_recip <- c("<EMAIL>")
#warn_recip <- c("<EMAIL>", "<EMAIL>")
test_warn_recip <- c("<EMAIL>","<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_st_from <- paste0("<b><a href=\"mailto:<EMAIL>\">Steve Olson</a></b><br/>",
                       "Sr. Analytics Manager<br/>",
                       "<b>Highland Ventures, Ltd.</b><br/>",
                       "2500 Lehigh Ave.<br/>",
                       "Glenview, IL 60026<br/>",
                       "Ph: 847/904-9043<br/>")

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  norm_st_from <- paste0(norm_st_from,
                         '<img src="',HVSigLogopath,'" width="600"> ')
}

#norm_st_cc <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>","<EMAIL>")

okaytocontinue <- TRUE


### define some functions ###
mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE){
  library(mailR)
  sender <- paste0(routine_name_main, " <<EMAIL>>")
  recipients <- recipient
  send.mail(from = sender,
            to = recipients,
            replyTo = "<EMAIL>",
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = "<EMAIL>",            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


writelog <- function(LogTable){
  fn <- file.path(logpath, logname)
  write.csv(LogTable, file = fn, row.names=FALSE)
}

file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path,
               open = "w"),
          silent = TRUE
      )
    )
  )
}

### check if log present/up-to-date (allows one successful run per day) ###
NewErrorLog <- FALSE
if(file.exists(file.path(logpath, logname)) ) {
  MyErrorLog <- read.csv(file = file.path(logpath, logname), sep=",", stringsAsFactors = FALSE)
  if(file.opened(file.path(logpath, logname))){
    okaytocontinue <- FALSE
    message("Log file open, ABORTING!")
  }else{
    #not open, but file.opened() wrote over file, rewrite
    writelog(MyErrorLog)
  }
  #check if log is from prior week, is so replace values with default starting values
  if( MyErrorLog[1,"QUERY_DATE"] != query.date ){
    #if( MyErrorLog[1,"QUERY_PERIOD"] != report.period_name ){
    #log is from previous date, replace with new default values
    NewErrorLog <- TRUE
  }else{
    #log is from this reporting period, check if previously completed
    if(MyErrorLog[1,"PROGRESS"] == 'COMPLETE'){
      # previous run complete, abort this run
      okaytocontinue <- FALSE
      message("Previous run completed, aborting this attempt.")
    }
  }
} else {
  # log not found, create new log values
  NewErrorLog <- TRUE
}

if( NewErrorLog ) {
  MyErrorLog <- data.frame(QUERY_DATE = query.date,
                           GS_STATUS = 'NA',
                           ORACLE_STATUS = 'NA',
                           PROGRESS = 'NA',
                           stringsAsFactors = FALSE)
}



# get Google Sheet data and verify
if(okaytocontinue){
  MyErrorLog[1,"PROGRESS"] <- "GS STATUS"
  MyErrorLog[1,"GS_STATUS"] <- "LOADING GOOGLE SHEET"
  writelog(MyErrorLog)
  #method (googlesheets4)  (Is it OK to cache OAuth access credentials in the folder 'C:/Users/<USER>/.R/gargle/gargle-oauth' between R sessions?)
  #sheets_auth(email = "<EMAIL>")
  #above was deprecated as of googlesheets4 0.2.0
  gs4_auth(email = "<EMAIL>")
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive first, then use the same token
  #in googlesheets4 something like this:
  #drive_auth()
  #sheets_auth(token = drive_token())

  #gSht_ResponseShts <- sheets_get(gSht_Key)
  #above is deprecated as of googlesheets4 0.2.0. replaced by line below
  gSht_ResponseShts <- gs4_get(gSht_Key)
  # weekly sheet name gSht_RespSN
  #if(is.na(match(gSht_RespSN, gs_ws_ls(gSht_Responses)))){
  #gSht_Responses$sheets$name
  if(is.na(match(gSht_BuildingSN, gSht_ResponseShts$sheets$name))){
    # desired original response sheet NOT PRESENT
    # update log and quit
    MyErrorLog[1,"GS_STATUS"] <- paste0("'", gSht_BuildingSN, "' SHEET NOT FOUND")
    writelog(MyErrorLog)
    okaytocontinue <- FALSE

  }

  if( okaytocontinue){
    # continue to load google sheet

    gSht_Master <- read_sheet(gSht_Key, sheet = gSht_BuildingSN)

    #convert string dates to date values
    #gSht_Master$`Form Submit` <- as.Date(gSht_Master$`Form Submit`, format = "%m/%d/%Y")

    #orig, doesn't preserve original row numbers
    #gSht_Responses <- filter(gSht_Master, is.na(gSht_Master[[gSht_VendorColName]]),
    #                         !is.na(gSht_Master[[gSht_StColName]]) )

    #get first column name (missing in source Google Sheet)
    gSht_FVStColName <- colnames(gSht_Master[,1])
    gSht_WhiteListColNames <- c(gSht_FVStColName,
                              gSht_MPStColName,
                              gSht_SFStColName,
                              gSht_HPWIStColName,
                              gSht_VETStColName,
                              gSht_LeaseColName,
                              gSht_SqFtColName
    )
    gSht_StoreColNames <- c(gSht_FVStColName,
                            gSht_MPStColName,
                            gSht_SFStColName,
                            gSht_HPWIStColName,
                            gSht_VETStColName
    )
    
    #gSht_simple <- select(gSht_Master, c(gSht_WhiteListColNames))
    gSht_simple <- select(gSht_Master, all_of(gSht_WhiteListColNames))
    # replace NULL in store columns with na for consistency
    gSht_simple[[gSht_FVStColName]] <- gSht_simple[[gSht_FVStColName]] %>% replace(.=='NULL', NA)
    gSht_simple[[gSht_MPStColName]] <- gSht_simple[[gSht_MPStColName]] %>% replace(.=='NULL', NA)
    gSht_simple[[gSht_SFStColName]] <- gSht_simple[[gSht_SFStColName]] %>% replace(.=='NULL', NA)
    gSht_simple[[gSht_HPWIStColName]] <- gSht_simple[[gSht_HPWIStColName]] %>% replace(.=='NULL', NA)
    gSht_simple[[gSht_VETStColName]] <- gSht_simple[[gSht_VETStColName]] %>% replace(.=='NULL', NA)
    #rename columns to match Oracle
    #FVMC, MP, SF, HPWI, VET, LEASE_NAME, SQFT
    names(gSht_simple)[names(gSht_simple) == gSht_FVStColName] <- 'FVMC'
    names(gSht_simple)[names(gSht_simple) == gSht_MPStColName] <- 'MP'
    names(gSht_simple)[names(gSht_simple) == gSht_SFStColName] <- 'SF'
    names(gSht_simple)[names(gSht_simple) == gSht_HPWIStColName] <- 'HPWI'
    names(gSht_simple)[names(gSht_simple) == gSht_VETStColName] <- 'VET'
    names(gSht_simple)[names(gSht_simple) == gSht_LeaseColName] <- 'LEASE_NAME'
    names(gSht_simple)[names(gSht_simple) == gSht_SqFtColName] <- 'SQFT'

    gSht_simple <- gSht_simple[c('FVMC','MP','SF','HPWI','VET','LEASE_NAME','SQFT')]
    #clean data
    #gSht_simple$FVMC <- gsub("[[:punct:]]", "", gSht_simple$FVMC)
    gSht_simple$FVMC <- gsub("[^0-9.-]", "", gSht_simple$FVMC)
    gSht_simple$MP <- gsub("[^0-9.-]", "", gSht_simple$MP)
    gSht_simple$SF <- gsub("[^0-9.-]", "", gSht_simple$SF)
    gSht_simple$HPWI <- gsub("[^0-9.-]", "", gSht_simple$HPWI)
    gSht_simple$VET <- gsub("[^0-9.-]", "", gSht_simple$VET)
    gSht_simple <- transform(gSht_simple, FVMC = as.numeric(FVMC),
                   MP = as.numeric(MP),
                   SF = as.numeric(SF),
                   HPWI = as.numeric(HPWI),
                   VET = as.numeric(VET)
                   )
    #mydata <- gSht_simple %>% mutate_all(~gsub('[^ -~]', '', .))
    mydata <- gSht_simple[which(!is.na(gSht_simple$LEASE_NAME)), ]
    mydata <- mydata %>% mutate(SQFT = sapply(SQFT, toString))
    mynumdiff <- nrow(gSht_simple) - nrow(mydata)
    
    if(mynumdiff > 4 & nrow(mydata) > 1){
      #missing LEASE_NAME value, warn
      myfvmcmissing <- gSht_simple$FVMC[which(is.na(gSht_simple$LEASE_NAME))]
      bodytext <- paste0("This is an automated email to inform you that it appears that there were missing ",
                         "(null) Lease Names in the Google sheet for the <b>", routine_name_main, " Routine.</b> ", 
                         "The '", gSht_BuildingSN, "' sheet is present in the '", gSht_ResponseShts$name,
                         "' file, but ", mynumdiff, 
                         if(mynumdiff == 1){" row appears to be missing a lease name. "}else{" rows appear to be missing lease names. "},
                         if(mynumdiff < 20){paste0("<br><br>FVMC #s with missing lease name(s): ", paste(myfvmcmissing, collapse = "; "))},
                         "<br><br>",
                         "If you believe this to be an error, check the Google sheet data at ",
                         '<a href="', gSht_URL, '">', routine_name_main, '"</a>',
                         " or check the R code in the ", orig_wd, " directory of the Tableau Desktop.<br/><br/>",
                         print(xtable(MyErrorLog,
                                      caption = paste0(logname, " (", report.time.txt, ")")),
                               align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                               html.table.attributes = "border=2 cellspacing=1",
                               type = "html",
                               caption.placement = "top",
                               include.rownames=FALSE),
                         "<br>",
                         warn_sig
      )
      #send mail
      if(testing_emails){warn_recip <- test_warn_recip}
      mailsend(warn_recip,
               paste0(routine_name_main, " Routine: GOOGLE SHEET DATA"),
               bodytext
      )
    }
    
    #add column
    #gSht_Responses <- gSht_Responses %>% add_column(City = NA, .before = "DM")
    #gSht_Responses$City <- with(stlist, CITY[match(gSht_Responses$`Select your store number.`, STORE)])

    #update log (don't write)
    MyErrorLog[1,"PROGRESS"] <- "GS STATUS"
    #writelog(MyErrorLog)
  }
  if(okaytocontinue){
    if(nrow(mydata)>1){
      #okay, continue

      MyErrorLog[1,"GS_STATUS"] <- "LOADED"
    }else{
      #reponse sheet found but no rows, abort
      MyErrorLog[1,"GS_STATUS"] <- "No Results or Error"
      writelog(MyErrorLog)
      #email warning
      # create body of warning email
      bodytext <- paste0("This is an automated email to inform you that it appears that there weren't any rows ",
                         "found in the Google sheet for the <b>", routine_name_main, " Routine.</b> (", gSht_BuildingSN, "), ",
                         "the sheet was present but no rows were reported. ",
                         "<br>",
                         "If you believe this to be an error, check the Google sheet data at ",
                         '<a href="', gSht_URL, '">', routine_name_main, '"</a>',
                         " or check the R code in the ", orig_wd, " directory of the Tableau Desktop.<br/><br/>",
                         print(xtable(MyErrorLog,
                                      caption = paste0(logname, " (", report.time.txt, ")")),
                               align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                               html.table.attributes = "border=2 cellspacing=1",
                               type = "html",
                               caption.placement = "top",
                               include.rownames=FALSE),
                         "<br>",
                         warn_sig
      )
      #send mail
      if(testing_emails){warn_recip <- test_warn_recip}
      mailsend(warn_recip,
               paste0(routine_name_main, " Routine: GOOGLE SHEET DATA"),
               bodytext
      )
      
      writelog(MyErrorLog)
    }
  }

}


# remove old data in Oracle table and replace with current data
if(okaytocontinue){
  MyErrorLog[1,"PROGRESS"] <- "ORACLE STATUS"
  MyErrorLog[1,"ORACLE_STATUS"] <- "TRUNCATE ORACLE TABLE"
  writelog(MyErrorLog)
  #mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve"))
  # get column names and datatypes of Oracle table
  #tmpDBinfo <- sqlColumns(mydb, myTableName)
  #columnTypes <- as.character(tmpDBinfo$TYPE_NAME)
  #names(columnTypes) <- as.character(tmpDBinfo$COLUMN_NAME)
  
  
  
  #truncate table "STEVE"."SO_PROP_SQFT" drop storage

  #myquery <- paste0('truncate table ', myTableName, ' drop storage')
  #myTrucResults <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  myquery <- paste0('truncate table ', myTableName, ' drop storage')
  dbExecute(myOracleDB, myquery)
  
  MyErrorLog[1,"ORACLE_STATUS"] <- "LOADING ORACLE TABLE"
  writelog(MyErrorLog)
  
  #populate Oracle
  #sqlSave(mydb,
  #        mydata,
  #        tablename = myTableName,
  #        append = TRUE,
  #        rownames = FALSE,
  #        colnames = FALSE,
  #        safer = TRUE,
  #        fast = FALSE,
  #        addPK = FALSE,
  #        varTypes = columnTypes,
  #        nastring = NULL)
  
  #load Oracle table
  dbWriteTable(myOracleDB, myTableName, mydata,  append = TRUE, overwrite = FALSE)
  
  #check that all rows loaded
  myquery <- paste0("select count(*) as cnt from ", myTableName)
  myNumRowsResult <- dbGetQuery(myOracleDB, myquery)
  
  if(nrow(mydata) != myNumRowsResult[[1]]){
    #warn that there may have been a load error
    bodytext <- paste0("<p>This is an automated email to inform you that it appears ",
                       "there may have been a loading error in the '",
                       this_ReportName, "' routine. </p>",
                       "<p><strong>An attempt to load the '", myTableName, "' table was ",
                       "made, but the # of expected rows was off.</strong></p>",
                       "<p>The source data had ", nrow(mydata), " rows and the ",
                       "table ended up with ", myNumRowsResult[[1]], " rows.</p>",
                       "<p>The routine will still attempt other loads in this script.</p>",
                       warn_sig
    )
    mailsend(warn_recip,
             paste0(myReportName, " : LOADING ERROR"),
             bodytext
    )
    MyErrorLog[1,"ORACLE_STATUS"] <- "LOAD ERROR - DIFF IN ROW CNT"
    MyErrorLog[1,"PROGRESS"] <- "ORACLE_STATUS"
  }else{
    MyErrorLog[1,"ORACLE_STATUS"] <- "COMPLETE"
    MyErrorLog[1,"PROGRESS"] <- "COMPLETE"
  }
  
  writelog(MyErrorLog)
  
  
}


