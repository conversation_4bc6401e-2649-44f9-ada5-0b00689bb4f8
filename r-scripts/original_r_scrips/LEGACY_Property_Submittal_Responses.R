library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyr)
library(keyring)
library(janitor)


# written by <PERSON> August 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20240925

### 20240925 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present

### 20221228 change
### added in new laptop to test 

### 20220929 change
### file name change caused crash, changed to check file ID (from URL, ignoring file name) and then sheets present

### 20220920 change
### fixed bug where if no update needed, the range_write statement was removing formatting of the update range in the google sheet 

### 20220907 change
### added ability for subscribers get all their relevant sheets in the Excel attachment (instead of just ones with new additions)
### to enable, search for "test_compare <- gSht_email$Email_Sheet %in% gSht_info$sheets$name" and un-comment that line
### also added modified email username to Excel file name to create distinct file names in case users want to compare versions

### 20220906 change
### added email section to notify subscribers of new additions

### 20220831 change
### new file


# Parameters

okaytocontinue <- TRUE

myReportName <- "Legacy Property Submittals"
scriptfolder <- "LEGACY_Property_Submittal_Responses"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
rptfolder <- "reports"
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
gSht_auth_email <- "<EMAIL>"
gDrv_mainURL <-'https://docs.google.com/spreadsheets/d/1ne1dputHFfVUO1sy0FGuo8sV3GWnsldZDGaAoMZ39_Y/'
gSht_response_SN <- "Form Responses 1"
gSht_parameters_SN <- "Parameters"
gSht_update_rngname <- "Last_Update"
gSht_email_rngname <- "Email_Sheets"
gSht_colname_corpnames <- "Relevant Corporate Name(s)"
gSht_colname_maplink <- "Google Map Link"
gSht_colname_emailbody <- c("to_sheet","Leasing Agent Name","City","Timestamp","Property Address","State","Zip Code")
emailbody_oldnames <- c("to_sheet","Leasing Agent Name","Timestamp","Zip Code")
emailbody_newnames <- c("Corporate Name","Leasing Agent","Date Submitted","Zip")

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

#Email OAuth dance
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}

# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "18-AUG-22" #testing line only
query.days <- 14
query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - (query.days - 3), "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) + 2, "%d-%b-%y")# Oracle date format for end date of reporting

rpt.end <- format(as.Date(query.date, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
email.end <- format(as.Date(query.date, "%d-%b-%y"), "%m/%d/%Y")

report.time.txt <- format(Sys.time(), "%H%M%S %Z")
rptFN_noEXT <- paste0(myReportName)
rptFN <- paste0(rptFN_noEXT, ".xlsx")

last_request <- Sys.time() # initialize variable for timer that limits Google sheet requests


### define some functions ###

check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


invalid_cols <- function(testdf, col_list){
  my_results <- c("Data missing or no column names/indices supplied")
  if(is.data.frame(testdf) & length(col_list) > 0){
    my_results <- c()
    max_col_num <- ncol(testdf)
    col_names <- names(testdf)
    #col_list might include vectors so extra loop for each list item
    for(i in 1:length(col_list)){
      curr_issue <- NA
      for(x in 1:length(col_list[[i]])){
        if(!is.na(col_list[[i]][x])){
          if(is.numeric(col_list[[i]][x])){
            #test position
            curr_issue <- if(col_list[[i]][x] > 0 & col_list[[i]][x] <= max_col_num){NA}else{as.character(col_list[[i]][x])}
          }else{
            #test name
            curr_issue <- if(col_list[[i]][x] %in% col_names){NA}else{paste0('"', col_list[[i]][x], '"')}
          }
        }
        if(!is.na(curr_issue)){my_results <- c(my_results, curr_issue)}
      }
    }
    if(length(my_results) > 0){
      my_results <- paste(my_results, collapse = "; ") %>% paste0("Columns missing: ", .)
    }
  }
  
  return(my_results)
}



find_hdr_row <- function(mydf, hdr_colnames){
  output <- 0
  if(nrow(mydf) > 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames) %>% apply(., 1, sum)
    if(max(myhdr_matches) > 0){
      output <- myhdr_matches %>% which.max(.)
    }
  }
  if(nrow(mydf) == 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames)
    if(sum(myhdr_matches) > 0){output <- 1}
  }
  return(output)
}



writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  oldOpt <- options()
  options(xlsx.date.format="MM/dd/yyyy")
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(format(Sys.time(), "%H:%M:%S"), "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      openxlsx::saveWorkbook(myWB, file = myNewFN, overwrite = writeover)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext,
             inline = TRUE
    )
  }
  options(oldOpt)
}



# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isSheet <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was spreadsheet
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    if(exists("drv_get_main")){
      isSheet <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("spreadsheet")
      if(!isSheet){okaytocontinue <- FALSE}
    }else{
      isSheet <- "No"
      okaytocontinue <- FALSE
    }

  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Google Auth Email: ", gSht_auth_email, "</li>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Sheet URL resolved: ", isSheet, " (", gDrv_mainURL,
                       ")</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Access Issue"),
             body = bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


# check if desired file and response sheet is present
if(okaytocontinue){
  #set initial okaytocontinue flag to false and then only set it to true if file and response sheet(s) present
  okaytocontinue <- FALSE
  #check if sheet is present
  gSht_id <- drv_get_main$id
  gSht_info <- gs4_get(gSht_id)
  if(gSht_response_SN %in% c(gSht_info$sheets$name)){
    #sheetname present, okay to continue
    okaytocontinue <- TRUE
  }
  
  if(!okaytocontinue){
    #send warning email that file or response sheet not found
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error trying to find a sheet in the Google file ",
                       " for the ", myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Google Sheets Filename: ", paste(gSht_info$name, collapse = '; '), "</li>",
                       "<li>Sheet name expected for responses: ", gSht_response_SN, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Response Sheet Not Found"),
             body = bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}  






#find responses since last update  
if(okaytocontinue){ 
  #find A1 range of last update named range
  gSht_update_rngA1 <- gSht_info$named_ranges$A1_range[[which(gSht_info$named_ranges$name == gSht_update_rngname)]]
  #get form responses since last update and record current time which will overwrite this when done
  gSht_update <- range_read(ss = gSht_id, range = gSht_update_rngA1)
  gSht_update_readtime <- force_tz(Sys.time(), tz = "UTC")
  mydata_responses <- range_read(ss = gSht_id, sheet = gSht_response_SN) %>% filter(Timestamp >= gSht_update$Last_Update)
  if(nrow(mydata_responses) > 0){
    #separate rows to dup entries when response includes multiple businesses (delimited by ", ")
    ##dup the column with the corporate names response to a new 'to_sheet' column
    mydata_responses$to_sheet <- mydata_responses[,gSht_colname_corpnames]
    for(i in 1:nrow(mydata_responses)){
      curr_rows <- separate_rows(mydata_responses[i, ], to_sheet, sep = ", ")
      if(i==1){
        mydata_paste <- curr_rows
      }else{
        mydata_paste <- rbind(mydata_paste, curr_rows)
      }
    }
    rm(curr_rows)
    ##get unique destination sheets
    pasteSheets_expected <- unique(mydata_paste$to_sheet)
    ##verify needed sheets present
    test_compare <- pasteSheets_expected %in% c(gSht_info$sheets$name)
    pasteSheets_expected_present <- pasteSheets_expected[test_compare]
    pasteSheets_expected_notpresent <- pasteSheets_expected[!test_compare]
    
    if(length(pasteSheets_expected_notpresent) > 0){
      #Add needed sheets for corporations not present
      for(i in 1:length(pasteSheets_expected_notpresent)){
        sheet_add(gSht_id, sheet = pasteSheets_expected_notpresent[i], .before = "Parameters", )
      }
      #refresh gSht_info so row count for all sheets present
      gSht_info <- gs4_get(gSht_id)
    }
    
  }else{
    base::message("No new responses identified")
    okaytocontinue <- FALSE
    #write update time in Google sheet parameters to current time
    gSht_update$Last_Update <- gSht_update_readtime
    rs <- range_write(ss = gSht_id, data = gSht_update, range = gSht_update_rngA1, reformat = FALSE)
  } 
}







#all sheets should now exist, paste rows into each
if(okaytocontinue){
  
  myhdr_names_paste <- names(mydata_paste[,!names(mydata_paste) %in% c("to_sheet")])
  myhdr_names_cnt <- length(myhdr_names_paste)
  
  #convert map link to GS4 hyperlink formula
  mydata_paste$`Google Map Link` <- sapply(mydata_paste$`Google Map Link`, function(x) paste0("=HYPERLINK(\"", x, "\")") )
  mydata_paste$`Google Map Link` <- gs4_formula(mydata_paste$`Google Map Link`)
  
  #init default header to "Status" plus other names from responses in original order (only used if headers not found in existing sheets)
  myhdr_names_new <- c("Status", myhdr_names_paste)
  #attempt to copy headers in order from existing company sheet
  if(length(pasteSheets_expected_present) > 0){
    #copy headers from 1st sheet that is present
    for(i in 1:length(pasteSheets_expected_present)){
      gSht_tosheet <- read_sheet(ss = gSht_id, sheet = pasteSheets_expected_present[[i]], col_names = FALSE)
      #find header row (which has most matches to response headers)
      myhdr_row <- find_hdr_row(mydf = gSht_tosheet, hdr_colnames = colnames(mydata_paste))
      if(myhdr_row > 0){
        #copy headers from this sheet and exit for loop
        myhdr_names_new <- gSht_tosheet[myhdr_row,] %>% unlist(use.names = FALSE)
        break
      }
    }
  }
  
  
  
  #write data
  for(i in 1:length(pasteSheets_expected)){
    #read sheet without using first row as column names
    gSht_info_row <- match(pasteSheets_expected[[i]], gSht_info$sheets$name )
    gSht_lastrow <- gSht_info$sheets$grid_rows[[gSht_info_row]]
    #gSht_tosheet <- read_sheet(ss = gSht_id, sheet = pasteSheets_expected[[i]], col_names = FALSE, trim_ws = FALSE, skip = 0)
    gSht_tosheet <- range_read(ss = gSht_id, sheet = pasteSheets_expected[[i]], range = cell_rows(1:gSht_lastrow), col_names = FALSE, trim_ws = FALSE, skip = 0)
    
    #find header row
    #debugonce(find_hdr_row)
    myhdr_row <- find_hdr_row(mydf = gSht_tosheet, hdr_colnames = colnames(mydata_paste))
    if(myhdr_row > 0){
      #find last row
      #mypaste_row <- nrow(gSht_tosheet) + 1
      #mypaste_row <- rowSums(is.na(gSht_tosheet[myhdr_row:gSht_lastrow, ]) ) %>% match(max(.), .) %>% + myhdr_row - 1
      myTimestampcol <- match(gSht_colname_maplink, unlist(gSht_tosheet[myhdr_row, ]))
      mypaste_row <- rowSums(is.na(gSht_tosheet[myhdr_row:gSht_lastrow, myTimestampcol]) ) %>% match(max(.), .) %>% + myhdr_row - 1
      
      
      mydata_paste_curr <- mydata_paste %>% filter(to_sheet == pasteSheets_expected[[i]])
      #initialize empty df matching gSht_tosheet columns and mydata_paste_curr rows
      mypaste_df <- data.frame(matrix(nrow = nrow(mydata_paste_curr), ncol = ncol(gSht_tosheet)))
      names(mypaste_df) <- names(gSht_tosheet)
      fnd_cols_cnt <- 0
      for(currcol in 1:ncol(gSht_tosheet)){
        #
        matchcol <- match(gSht_tosheet[myhdr_row, currcol][[1]], myhdr_names_paste )
        if(!is.na(matchcol)){
          #copy found column from mydata_paste into mypaste_df for later writing to GS
          mypaste_df[, currcol] <- mydata_paste_curr[,matchcol]
          fnd_cols_cnt <- fnd_cols_cnt + 1
        }
      }
      
      
      #write rows
      range_write(ss = gSht_id, 
                  data = mypaste_df, 
                  sheet = pasteSheets_expected[[i]], 
                  range = cell_rows(mypaste_row:(mypaste_row+nrow(mydata_paste_curr)-1) ), 
                  col_names = FALSE,
                  reformat = FALSE
      )
    }else{
      #header row not found
      #todo write myhdr_names_new values as header row
      
    }

  }
  
  #write update time in Google sheet parameters to current time
  gSht_update$Last_Update <- gSht_update_readtime
  rs <- range_write(ss = gSht_id, data = gSht_update, 
                    range = gSht_update_rngA1,
                    reformat = FALSE)
  
  
}



#email sheets to desired recipients
if(okaytocontinue){
  #find A1 range of last update named range
  gSht_email_rngA1 <- gSht_info$named_ranges$A1_range[[which(gSht_info$named_ranges$name == gSht_email_rngname)]]
  #get emails needed for each sheet
  gSht_email <- range_read(ss = gSht_id, range = gSht_email_rngA1)
  
  ##get unique emails for the new additions
  test_compare <- gSht_email$Email_Sheet %in% pasteSheets_expected
  email_unique_addys <- paste(gSht_email$Email_Recipients[test_compare], collapse = ";") %>% str_split(., ";", simplify = TRUE) %>% as.vector() %>% unique()
  
  #download Excel version of newly updated file
  xl_fn <- "Real Estate Acquisitions.xlsx"
  xl_path <- file.path(logpath, rptfolder, "Real Estate Acquisitions.xlsx")
  drive_download(file = gSht_id, path = xl_path, overwrite = TRUE )
  xl_master_wb <- openxlsx::loadWorkbook(file = xl_path )
  
  if(length(email_unique_addys) > 0){
    
    for(x in 1:length(email_unique_addys)){
      curr_email <- email_unique_addys[[x]]
      curr_name <- curr_email %>% substring(., 1, unlist(gregexpr("@", curr_email))[1]-1 ) %>% gsub("."," ", ., fixed = TRUE) %>% str_to_upper()
      curr_path <- file.path(logpath, rptfolder, paste0("Real Estate ACQ Submittals-", curr_name, " ", rpt.end,".xlsx"))
      curr_wb <- copyWorkbook(xl_master_wb)
      curr_keeper_sheets <- c(gSht_parameters_SN)
      #if desirable to send recipient ALL sheets they are subscribed to then enable the following line
      #test_compare <- gSht_email$Email_Sheet %in% gSht_info$sheets$name
      email_sheets <- gSht_email$Email_Sheet[test_compare]
      
      for(i in 1:length(email_sheets)){
        #check if curr_email should get this sheet,
        #if so add to curr_wb and include rows from mydata_paste to df for email body
        curr_sheet <- email_sheets[[i]]
        if(grepl(curr_email, gSht_email[gSht_email$Email_Sheet == curr_sheet, "Email_Recipients"])){
          curr_keeper_sheets <- c(na.omit(curr_keeper_sheets), curr_sheet)
          #add current rows from mydata_paste to curr_additions
          if(exists("curr_rows")){
            curr_rows <- c(curr_rows, which(mydata_paste$to_sheet == curr_sheet))
          }else{
            curr_rows <- which(mydata_paste$to_sheet == curr_sheet)
          }
          
          #if(exists("curr_additions")){
          #  new_additions <- mydata_paste %>% filter(to_sheet == curr_sheet)
          #  curr_additions <- rbind(curr_additions, new_additions)
          #}else{
          #  curr_additions <- mydata_paste %>% filter(to_sheet == curr_sheet)
          #}
        }
      }
      curr_additions <- mydata_paste[curr_rows, ]
      curr_additions <- curr_additions[order(curr_additions$to_sheet, curr_additions$Timestamp),]
      rm(curr_rows)
      #summarise additions for email body
      mydata_emailbody <- curr_additions[,gSht_colname_emailbody]
      mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") %>% format(., "%m/%d/%y") else x)
      setnames(mydata_emailbody, 
               old = emailbody_oldnames, 
               new = emailbody_newnames,
               skip_absent = TRUE)
      
      curr_del_sheets <- curr_wb$sheet_names[!curr_wb$sheet_names %in% curr_keeper_sheets]
      if(length(curr_del_sheets) > 0){
        for(i in 1:length(curr_del_sheets)){
          removeWorksheet(curr_wb, curr_del_sheets[[i]])
        }
      }
      #hide parameters sheet
      sheetVisibility(curr_wb)[match(gSht_parameters_SN, curr_wb$sheet_names)] <- "hidden"
      #write sheet and email it
      save_okay <- saveWorkbook(wb = curr_wb, file = curr_path, overwrite = TRUE, returnValue = TRUE)
      if(save_okay){
        this_recip <- curr_email
        myemailfiles <- curr_path
        #remember last GMail send time, sleep if less than 6 seconds since last send
        this_request <- Sys.time()
        seconds_elapsed <- as.numeric(difftime(this_request, last_request, units = "secs"), units = "secs")
        wait_time <- max(0, 6 - seconds_elapsed)
        last_request <- Sys.time()
        Sys.sleep(wait_time)
        
        
        bodytext <- paste0("<p><h3>The attached file contains real estate acquisition submittals under consideration.</h3></p>",
                           "<p>The latest additions to the attached file are:<br>",
                           print(
                             xtable(mydata_emailbody, 
                              #caption = paste0(this_ReportName, " (", query.date, ")"),
                              digits = rep(0,ncol(mydata_emailbody)+1)
                             ),
                             #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                             html.table.attributes = "border=2 cellspacing=1",
                             type = "html",
                             caption.placement = "top",
                             include.rownames=FALSE
                           ),
                           "</p>",
                           "<p>The online Google Sheet version can be found at ",
                           "<a href=\"", gSht_info$spreadsheet_url, "\">", gSht_info$name, "</a> </p>",
                           norm_sig
        )
        rs <- mailsend(recipient = this_recip,
                       subject = "Real Estate Acquisition Submittals",
                       body = bodytext,
                       if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                       inline = TRUE,
                       test = testing_emails, testrecipient = test_recip
        )
        #unlink (delete) emailed version of file from report directory
        unlink(curr_path, force = TRUE)
      }
      #remove results df for this email addy
      rm(curr_additions)
    }
  }
}






















