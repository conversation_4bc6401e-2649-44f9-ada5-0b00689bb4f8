library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyr)
library(DBI)
#library(ROracle)
library(keyring)
library(janitor)
library(odbc)


# written by <PERSON> January 2023


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
testing_emails <- TRUE


# Version 20240926

### 20240926 change:
### removed RJDBC library reference since it wasn't needed

### 20240915 change:
### converted from mailR package (SMTP), to gmailr (OAuth-GMail API) ahead of 20240930 SMTP deprecation in GMail
### gmailr masks base message() function, replaced with explicit base::message()


### 20240830 change:
### testing_emails <- TRUE until we move Tableau to using Snowflake data
### modified to populate Snowflake (previously Oracle)

### 20240618 change:
### fixed wrong variable name in warning email when expected sheet name not found

### 20240603 change:
### added DMs as recipients of the 'Missing Data' emails per Zach McLaughlin 20240530 email

### 20230516 change:
### added error trapping when a restaurant # is present in more than 1 row

### 20230425 change:
### added call out to which rows might have inconsistent data, based on which
### datatype in that column has the fewest instances (assumes most are correct datatype)

### 20230421 change:
### bug fix: when list columns (inconsistent data) detected, the report_url for the email
### was not being built causing a crash without the warning being sent

### 20230228 change:
### minor bug fix for issue introduced in 20230221 change

### 20230221 change:
### minor bug fix for when no errors present

### 20230209 change:
### added check for 'list' columns that might result if store data
### contains mixed datatypes in one or more needed columns, if so
### abort and send warning email.

### 20230123 change:
### new file


# Parameters

okaytocontinue <- TRUE

myReportName <- "Marco's Schedule Checker Import"
scriptfolder <- "MARCOS_Schedule_Checker"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
gSht_auth_email <- "<EMAIL>"
gSht_auth_email <- "<EMAIL>"
gDrv_mainURL <-'https://docs.google.com/spreadsheets/d/19QlpgP5-eLI4f17-0qu0euBKulyDlrQlzDrqeTe8uPo/' #"HRG Schedule Checker" file
#gDrv_directoryfoldername <- 'Directory Files'
gDrv_mainURL_email <- 'https://drive.google.com/drive/u/0/folders/1IgOY7tJe_x8cETx2EdK-Af0xdVM_OeJi'
#gDrv_folder_prefix <- 'https://drive.google.com/drive/u/0/folders/' #append ID (for use when not directory owner: <EMAIL>)
gDrv_folder_prefix <- 'https://drive.google.com/drive/folders/' #append ID (for use when not directory owner: <EMAIL>)
gDrv_sheet_prefix <- "https://docs.google.com/spreadsheets/d/"

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>",
                "<EMAIL>", 
                "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
RDO_recip <-  c("<EMAIL>")
DM_recip <- c("<EMAIL>")
#norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")


test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}


# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "09-Aug-24" #TEST OR MANUAL RUN ONLY
#query.date <- "16-Aug-24" #TEST OR MANUAL RUN ONLY
#query.date <- "13-Sep-24" #TEST OR MANUAL RUN ONLY
query.date <- "20-Sep-24" #TEST OR MANUAL RUN ONLY

query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) + 7, "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) + 13, "%d-%b-%y")# Oracle date format for end date of reporting

report.year <- lubridate::year(as.Date(query.startdate, "%d-%b-%y"))# YEAR for start date of reporting, this will be the folder to create workbooks in
report.startdate <- as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = TRUE)) + 7

report.removestart <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 49, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
report.removeend <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 35, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
rpt.start <- format(as.Date(query.startdate, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
rpt.end <- format(as.Date(query.enddate, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files


if(wday(as.Date(query.date, "%d-%b-%y")) > 5 & hour(Sys.time()) >= 17){
  #after 5 on Friday, load tables
  load_db <- TRUE
}else{
  #before load date/time, the routine should skip DB inserts and just email missing data (ahead of load)
  load_db <- FALSE
}

load_db <- TRUE  #TEST OR MANUAL RUN ONLY where database load is needed

email_missing <- TRUE #when TRUE, emails addresses in RDO_recip when rows have missing data


gSht_StoreColname <- "Store"
gSht_StoreMetaColname <- c("City","RDO","DM")
gSht_GMCorrectColname <- "GM working the correct days and hours"
gSht_ApprovedColname <- "Schedule Approved by DM"
gSht_OTColname <- "OT Hours"
gSht_Colnames_expected <- c(gSht_StoreColname,
                            gSht_StoreMetaColname,
                            "Monday Forecast",
                            "Monday Scheduled hours",
                            "Tuesday Forecast",
                            "Tuesday Scheduled hours",
                            "Wednesday Forecast",
                            "Wednesday Scheduled hours",
                            "Thursday Forecast",
                            "Thursday Scheduled hours",
                            "Friday Forecast",
                            "Friday Scheduled hours",
                            "Saturday Forecast",
                            "Saturday Scheduled hours",
                            "Sunday Forecast",
                            "Sunday Scheduled hours",
                            gSht_OTColname,
                            gSht_GMCorrectColname,
                            gSht_ApprovedColname
)
#Last expected data column, used to determine load area
gSht_LastColname <- gSht_ApprovedColname
day_fore_append <- " Forecast"
day_sched_append <- " Scheduled hours"


### define some functions ###

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
}

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role
                         #,authenticator = 'externalbrowser'
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="UTC") # to match Google Sheets TZ
#Sys.setenv(TZ='America/Chicago')
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
#myquery <- "ALTER SESSION SET TIMEZONE = 'UTC'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)


mySchema <- Sf_schema
myTableDaily <- "MP_SCHED_CHECK_DAILY"
myTableNameDaily <- paste(mySchema, myTableDaily, sep = ".")

mySchema <- Sf_schema
myTableNotes <- "MP_SCHED_CHECK_NOTES"
myTableNameNotes <- paste(mySchema, myTableNotes, sep = ".")



mailsend <- function(
    recipient, subject, body, attachment = NULL, inline = FALSE, 
    sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gmailr::gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  oldOpt <- options()
  options(xlsx.date.format="MM/dd/yyyy")
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(format(Sys.time(), "%H:%M:%S"), "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      openxlsx::saveWorkbook(myWB, file = myNewFN, overwrite = writeover)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext,
             inline = TRUE
    )
  }
  options(oldOpt)
}



list_cols <- function(testdf){
  my_results <- c("Data missing or no column names/indices supplied")
  if(is.data.frame(testdf)){
    my_results <- c()
    max_col_num <- ncol(testdf)
    col_names <- names(testdf)
    
    #sample run check if column is a 'list'
    for(i in 1:ncol(testdf)){
      if(typeof(testdf[,i][[1]]) == 'list'){
        
        
        #list_types <- table(sapply(day_data[,3][[1]], class)) %>% as.data.table() %>% subset(., V1 != 'NULL')
        #list_types <- table(sapply(testdf[,i][[1]], class)) %>% as.data.table() %>% subset(., V1 != 'NULL')
        list_types <- sapply(testdf[,i][[1]], class)
        types <- table(list_types) %>% as.data.table() %>% subset(., list_types != 'NULL')
        #t <- list_types[which.min(list_types$N), 1][[1]]
        error_type <- types[which.min(types$N), 1][[1]]
        error_positions <- which(error_type == list_types) + 1 #assumes headers in row 1, if not add higher # to which result
        error_message <- paste0(
          names(testdf)[i], 
          ": check for ", 
          error_type, 
          " entry at row", 
          if(length(error_positions)>1){"s"}, 
          " ", 
          paste0(error_positions, collapse = ", ")
        )
        base::message(error_message)
        
        my_results <- c(my_results, error_message)
      }
    }
  }
  
  return(my_results)
}


Snowflake_Insert_Sched <- function(Data, Schema, Tablename, Stores,  
                                StartDate, EndDate, DateColName){
  #POPULATE RESULTS INTO Snowflake, RETURN ERRORS IF APPLICABLE
  
  myStores_query <- paste0(Stores, collapse = ',')
  
  if(typeof(StartDate) != "character"){
    #format Date to character in Oracle format
    #StartDate <- format(StartDate, "%d-%b-%y") # Oracle
    #format Date to character in YYYY-MM-DD format
    StartDate <- format(StartDate, "%Y-%m-%d") # Snowflake
  }
  if(typeof(EndDate) != "character"){
    #format Date to character in Oracle format
    #EndDate <- format(StartDate, "%d-%b-%y") # Oracle, 20240830: note I think StartDate here was an inconsequential bug
    #format Date to character in YYYY-MM-DD format
    EndDate <- format(EndDate, "%Y-%m-%d") # Snowflake
  }
  myquery_select <- paste0(
    "
        select count(*)
        from ", Schema, ".", Tablename, "
        where STORE in (", myStores_query, ")
        and trunc(", DateColName, ",'day') >= to_date('", StartDate, "','YYYY-MM-DD')
        and trunc(", DateColName, ",'day') <= to_date('", EndDate, "','YYYY-MM-DD')
        "
  )
  #rs_sel <- dbSendQuery(myOracleDB, myquery_select)
  #select_cnt <- dbFetch(rs_sel, n = -1)
  #20240830:
  select_cnt <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
  myquery_delete <- paste0(
    "
        delete from ", Schema, ".", Tablename, "
        where STORE in (", myStores_query, ")
        and trunc(", DateColName, ",'day') >= to_date('", StartDate, "','YYYY-MM-DD')
        and trunc(", DateColName, ",'day') <= to_date('", EndDate, "','YYYY-MM-DD')
        "
  )
  #rs_del <- dbSendQuery(myOracleDB, myquery_delete)
  #20240830:
  dbBegin(mySfDB)
  rs_del <- dbSendQuery(mySfDB, myquery_delete)
  delete_cnt <- dbGetRowsAffected(rs_del)
  #if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
  #20240830:
  if(delete_cnt != select_cnt){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    #dbRollback(myOracleDB)
    #20240830: 
    dbRollback(mySfDB)
    dbClearResult(rs_del)
    #log stores where delete (and subsequently load) failed
    myDeleteStores_failed <- data.table(STORE = Stores, ISSUE = "DELETE FAILED, No Insert")
    
    #do not load since previous trans deletion failed
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    #dbCommit(myOracleDB)
    #20240830:
    dbCommit(mySfDB)
    dbClearResult(rs_del)
    
    #Insert rows into myTableDaily, count rows before and after
    #rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    #select_cnt_pre <- dbFetch(rs_sel, n = -1)
    #dbClearResult(rs_sel)
    #20240830:
    select_cnt_pre <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]

    #subset results to match subset of stores
    myCurrLoad <- subset(Data, STORE %in% Stores)
    #rs_write <- dbWriteTable(myOracleDB, Tablename, myCurrLoad, row.names = FALSE , append = TRUE, schema = Schema)
    #20240830:
    rw_write <- dbAppendTable(mySfDB, Id(schema = Schema, table = Tablename), myCurrLoad)
    
    #get new count of rows in table
    #rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    #select_cnt_post <- dbFetch(rs_sel, n = -1)
    #20240830:
    select_cnt_post <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    #myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
    #20240830:
    myload_numrows <- select_cnt_post - select_cnt_pre
    mydata_numrows <- nrow(myCurrLoad)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, get load counts by STORE
      myquery_loaded <- paste0(
        "
        select STORE
        ,   COUNT(*) AS LOADED
        from ", Schema, ".", Tablename, "
        where STORE in (", myStores_query, ")
        and trunc(", DateColName, ",'day') >= to_date('", StartDate, "','YYYY-MM-DD')
        and trunc(", DateColName, ",'day') <= to_date('", EndDate, "','YYYY-MM-DD')
        group by STORE
        "
      )
      #myLoadStores_curr <- dbGetQuery(myOracleDB, myquery_loaded)
      #20240830:
      myLoadStores_curr <- dbGetQuery(mySfDB, myquery_loaded)
      myLoadStores_expect <- myCurrLoad %>% group_by(STORE) %>% tally()
      myLoadStores_failed <- myLoadStores_expect %>% 
        dplyr::left_join(myLoadStores_curr, by = "STORE") %>% .[which(.$LOADED != .$n || is.na(.$LOADED)), ]
      myLoadStores_failed <- data.table(STORE = myLoadStores_failed$STORE, ISSUE = "Didn't load 1 or more rows of data")
    }
    
    
    
  }
  #check if either failure table exists
  fails <- data.table(STORE=numeric(), ISSUE=character())
  if(exists("myDeleteStores_failed")){
    rbind(fails, myDeleteStores_failed)
  }
  if(exists("myLoadStores_failed")){
    rbind(fails, myLoadStores_failed)
  }
  return(fails)
}




# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isFile <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was for folder
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isFile <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("spreadsheet")
    if(!isFile){okaytocontinue <- FALSE}else{
      #was file, get sheet names
      gSht_get <- gs4_get(as_id(drv_get_main))
    }
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file folder for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update.</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>File URL resolved: ", isFile, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Access Issue"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



if(okaytocontinue){
  #check if weekly sheetname is present
  myquery<- paste0(
    "
      select
          'P'||PERIOD_NUM||'W'||WEEK_NUM||period_year as sheetname
      from corporate.mp_calendar_weekly
      where s_date <= to_date('", query.startdate, "','DD-MON-YY')
      and e_date >= trunc(to_date('", query.startdate, "','DD-MON-YY'),'day')
    "
  )
  #mydata <- dbGetQuery(myOracleDB, myquery)
  #20240830:
  mydata <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]]){
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mydata$SHEETNAME)]
    gSht_Key <- gSht_get$spreadsheet_id
    if(length(gSht_Sheets)==0){
      okaytocontinue <- FALSE
      #email failure
      bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                         "the expected Google sheet is missing in the ",
                         myReportName, " routine! </p>",
                         "<p>The routine is aborting without an update.</p> ",
                         "<b>Google File Info:</b><ul>",
                         "<li>Google filename: ", gSht_get$name, "</li>",
                         "<li>File URL: ", gSht_get$spreadsheet_url, "</li>",
                         "<li>Sheetname expected (not found): <font color=\"red\">", 
                         paste(mydata$SHEETNAME, collapse = '; '), "</font></li>",
                         "<li>FYI, the first 8 sheets in the file are: ", paste(gSht_get$sheets$name[1:8], collapse = '; '), "</li>",
                         "</ul></p>",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Sheet Not Found In Google File"),
               bodytext,
               attachment = NULL,
               inline = TRUE,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }else{
    okaytocontinue <- FALSE
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "the desired sheetname could not be generated for the ",
                       myReportName, " routine! Check the MP_CALENDAR_WEEKLY table for ",
                       "an appropriate row for the week of ", query.startdate, ".</p>",
                       "<p>The routine is aborting without an update.</p> ",
                       "</p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Sheet Not Found In Google File"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


if(okaytocontinue){
  #determine data bounds in gSht_Sheets sheet
  # continue to load Weekly results
  gSht_Master <- read_sheet(gSht_Key, sheet = gSht_Sheets[[1]])
  gSht_LastRow <- min(which(vapply(gSht_Master$Store, is.null, TRUE)))
  gSht_LastCol <- which(names(gSht_Master) == gSht_LastColname)
  
  if(length(gSht_LastRow) == 0 || length(gSht_LastCol) == 0){
    #one or more dimensions not found
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "the bounds of the store data could note be found ",
                       "in the ", myReportName, " routine! The routine looks for ",
                       "an empty cell after the last store and expects the last ",
                       "column to be named '", gSht_LastColname, "'</p>",
                       "<p>The routine is aborting without an update.</p> ",
                       "</p>",
                       warn_sig
    )
    
  }else{
    
    gSht_Data <- range_read(gSht_Key, sheet = gSht_Sheets[[1]], cell_limits(c(1,1),c(gSht_LastRow, gSht_LastCol)))
    gSht_Colnames <- trimws(names(gSht_Data))
    names(gSht_Data) <- gSht_Colnames
    gSht_Namesintersect <- intersect(gSht_Colnames_expected, gSht_Colnames)
    if(length(gSht_Colnames_expected) != length(gSht_Namesintersect)){
      okaytocontinue <- FALSE
      gSht_missing_colnames <- setdiff(gSht_Colnames_expected, gSht_Namesintersect)
      bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                         "one or more expected column names could not be found ",
                         "in the ", myReportName, " routine! </p>",
                         "<p>The routine is aborting without an update.</p> ",
                         "<p>Check the '", gSht_Sheets[[1]], "' sheet for the ",
                         "following columns expected, but NOT found ",
                         "(check for case or spelling in Google): <br><br><b>",
                         paste(gSht_missing_colnames, collapse = "<br>"),
                         "</b></p>",
                         warn_sig
      )
    }
    
    
  }
  if(!okaytocontinue){
    #email failure
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Issue with Data in Google Sheet"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



#check needed columns in gSht_Data for 'list' columns
if(okaytocontinue){
  #check for 'list' columns in needed daily and notes columns of gSht_Data
  #and warn/abort if any present (mismatched datatypes)
  
  #check 'daily' columns
  bad_columns_daily <- c()
  for(i in 0:6){
    curr_date <- as.Date(report.startdate + i)
    curr_dow <- format(curr_date, "%A")
    curr_fore_colname <- paste0(curr_dow, day_fore_append)
    curr_sched_colname <- paste0(curr_dow, day_sched_append)
    curr_cols_needed <- c(gSht_StoreColname, curr_fore_colname, curr_sched_colname)
    #reorder_colnames <- c(gSht_StoreColname, "BUS_DATE", curr_fore_colname, curr_sched_colname)
    day_data <- gSht_Data[,curr_cols_needed]
    bad_columns_daily <- c(bad_columns_daily, list_cols(day_data))
  }
  #check 'notes' columns
  curr_notes <- gSht_Data[, c(gSht_StoreColname, gSht_OTColname, gSht_GMCorrectColname, gSht_ApprovedColname)]
  bad_columns_notes <- list_cols(curr_notes)
  bad_columns <- c(bad_columns_daily, bad_columns_notes)
  
  if(length(bad_columns) > 0){
    #there was inconsistent data in source causing 'list' column(s), warn and abort
    okaytocontinue <- FALSE
    #email failure
    rpt_url <- paste0(gDrv_sheet_prefix, gSht_Key, '/edit#gid=',gSht_get$sheets$id[which(gSht_get$sheets$name == gSht_Sheets[[1]])])
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "one or more expected columns of the Google sheet had unexpected ",
                       "data during the '", myReportName, "' routine! </p>",
                       "<p>The routine is aborting without an update.</p> ",
                       "<p>Check the following column(s) in the '",
                       "<a href=\"", rpt_url, "\">", gSht_Sheets[[1]],"</a>",
                       "' sheet for improper data ",
                       "(text in number columns or vice versa): <br><br><b>",
                       paste(bad_columns, collapse = "<br>"),
                       "</b></p>",
                       warn_sig
    )
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Issue with Data in Google Sheet"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


#check for duplicate stores
if(okaytocontinue){
  stores_present <- pull(gSht_Data, gSht_StoreColname)
  dup_stores <- unique(stores_present[duplicated(stores_present)])
  if(length(dup_stores) > 0){
    #abort load and send warning email
    okaytocontinue <- FALSE
    rpt_url <- paste0(gDrv_sheet_prefix, gSht_Key, '/edit#gid=',gSht_get$sheets$id[which(gSht_get$sheets$name == gSht_Sheets[[1]])])
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "there are one or more duplicated restaurant #s in the Google sheet ",
                       "data during the '", myReportName, "' routine! </p>",
                       "<p>The routine is aborting without an update.</p> ",
                       "<p>Check the following restaurant #s in the '",
                       "<a href=\"", rpt_url, "\">", gSht_Sheets[[1]],"</a>",
                       "' sheet for multiple rows: <br><br><b>",
                       paste(dup_stores, collapse = "<br>"),
                       "</b></p>",
                       warn_sig
    )
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Issue with Data in Google Sheet"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


#get data for Snowflake load
if(okaytocontinue){
  stores_present <- pull(gSht_Data, gSht_StoreColname)
  rpt_url <- paste0(gDrv_sheet_prefix,
                    gSht_get$spreadsheet_id,
                    "/edit#gid=",
                    gSht_get$sheets$id[which(gSht_get$sheets$name == gSht_Sheets)]
  )
  
  #loop for each day of week to build sched info
  for(i in 0:6){
    curr_date <- as.Date(report.startdate + i)
    curr_dow <- format(curr_date, "%A")
    curr_fore_colname <- paste0(curr_dow, day_fore_append)
    curr_sched_colname <- paste0(curr_dow, day_sched_append)
    curr_cols_needed <- c(gSht_StoreColname, curr_fore_colname, curr_sched_colname)
    reorder_colnames <- c(gSht_StoreColname, "BUS_DATE", curr_fore_colname, curr_sched_colname)
    day_data <- gSht_Data[,curr_cols_needed]
    day_data$BUS_DATE <- curr_date
    #reorder columns
    day_data <- day_data[, reorder_colnames]
    setnames(day_data, 
             old = curr_cols_needed, 
             new = c("STORE","FORE_SALES","SCHED_HOURS"),
             skip_absent = TRUE)
    #filter out incomplete rows
    day_data_comp <- day_data[complete.cases(day_data),]
    day_data_comp <- day_data_comp[order(day_data_comp$STORE),]
    day_data_missing <- setdiff(stores_present, day_data_comp$STORE) %>% sort(.)
    if(length(day_data_missing) == 0){
      curr_missing <- data.table("STORE" = numeric(), "MISSING" = character())
    }else{
      curr_missing <- data.table("STORE" = day_data_missing, "MISSING" = format(curr_date, "%a"))
    }
    #transfer day results to aggregate tables
    if(exists("results_missing")){
      results_missing <- rbind(results_missing, curr_missing)
    }else{
      results_missing <- copy(curr_missing)
    }
    if(exists("results_sched")){
      results_sched <- rbind(results_sched, day_data_comp)
    }else{
      results_sched <- day_data_comp
    }
    rm(curr_missing, day_data_comp)
    
  }
  
  #get weekly Notes data for stores
  curr_notes <- gSht_Data[, c(gSht_StoreColname, gSht_OTColname, gSht_GMCorrectColname, gSht_ApprovedColname)]
  curr_notes$E_DATE <- as.Date(report.startdate + 6)
  #Rename or reorder columns
  reorder_colnames_notes <- c(gSht_StoreColname, "E_DATE", gSht_OTColname, gSht_GMCorrectColname, gSht_ApprovedColname)
  curr_notes <- curr_notes[, reorder_colnames_notes]
  setnames(curr_notes, 
           old = reorder_colnames_notes, 
           new = c("STORE", "E_DATE","SCHED_HOURS_OT","GM_DAYS_HOURS","DM_APPROVED"),
           skip_absent = TRUE)
  results_notes <- curr_notes[complete.cases(curr_notes[,c("STORE", "E_DATE","GM_DAYS_HOURS","DM_APPROVED")]),]
  #replace NA SCHED_HOURS_OT with 0
  results_notes <- results_notes %>% replace(is.na(.), 0)
  notes_missing_stores <- setdiff(stores_present, results_notes$STORE) %>% sort(.)
  if(length(notes_missing_stores) == 0){
    notes_missing <- data.table("STORE" = numeric(), "MISSING" = character())
  }else{
    notes_missing <- data.table("STORE" = notes_missing_stores, "MISSING" = "GM days/hours or DM Approval")
  }
  if(exists("results_missing")){
    results_missing <- rbind(results_missing, notes_missing)
  }else{
    results_missing <- copy(notes_missing)
  }
  
}



if(okaytocontinue){
  #consolidate missing daily and notes, then email RDO email group if appropriate
  if(nrow(results_missing) > 0){
    missing_stores <- aggregate(MISSING~STORE,results_missing,function(x) paste0(x,collapse = ', '))
    #Add RDO, DM back into missing and order by RDO, DM, STORE
    gSht_Data_StMeta <- gSht_Data[,c(gSht_StoreColname, gSht_StoreMetaColname)]
    missing_email_tbl <- missing_stores %>% 
      dplyr::left_join(gSht_Data_StMeta, by = c("STORE" = "Store")) %>%
      select(STORE, all_of(gSht_StoreMetaColname), MISSING) %>%
      .[order(.$RDO, .$DM, .$STORE),]
    if(email_missing){
      bodytext <- paste0("<h2>", myReportName, ": Missing Data </h2>",
                         "<p>The following stores were missing ",
                         "<a href=\"", rpt_url, "\">", "Schedule Checker (", gSht_Sheets[[1]],")</a>",
                         " data as noted. ",
                         if(load_db){
                           "<b>The data IS BEING LOADED and future updates to the sheet will no longer be included in the data pull for Tableau.</b>"
                         }else{
                           "This is just a status update for your benefit, the data IS NOT being loaded at this time."
                         },
                         "</p>",
                         "<p>Criteria for inclusion in this report:<ul>",
                         "<li>Any day listed was missing either the ",
                         "forecasted $ or the scheduled hours</li>",
                         "<li>Either of the ",
                         "GMs working correct days/hours or the DM approved ",
                         "columns are blank</li>",
                         "</ul></p>",
                         print(xtable(missing_email_tbl, 
                                      caption = paste0(format(Sys.Date(),"%m/%d/%y"), 
                                                       " ", 
                                                       format(Sys.time(), "%H:%M:%S %Z"),
                                                       " (sorted by RDO, DM and then Store #)"
                                      ),
                                      digits = rep(0,ncol(missing_email_tbl)+1)
                         ),
                         html.table.attributes = "border=2 cellspacing=1",
                         type = "html",
                         caption.placement = "top",
                         include.rownames=FALSE
                         ),
                         "<br><br>",
                         norm_sig
      )
      mailsend(c(RDO_recip, DM_recip),
               paste0(myReportName, ": Missing Data"),
               bodytext,
               attachment = NULL,
               inline = TRUE,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
}



#populate Snowflake if appropriate
#load_db = TRUE #TEST ONLY
if(okaytocontinue && load_db && (nrow(results_sched) + nrow(results_notes) > 0) ){
  #load results
  myDeleteStores <- unique(c(results_sched$STORE, results_notes$STORE))
  mynumloops <- ceiling(length(myDeleteStores)/1000)
  
  for(i in 1:mynumloops){
    #oracle permits 1000 values for an IN clause, so break up delete into 1000 store increments if applicable
    mystart <- (i-1) * 1000 + 1
    mylimit <- min(c(length(myDeleteStores), i*1000))
    myStores_curr <- myDeleteStores[mystart:mylimit]
    
    #POPULATE ***DAILY*** RESULTS
    results_sched[] <- lapply(results_sched[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
    rs_load_daily <- Snowflake_Insert_Sched(Data = results_sched, 
                                         Schema = mySchema, 
                                         Tablename = myTableDaily,
                                         Stores = myStores_curr,
                                         #StartDate = query.startdate,
                                         #EndDate = query.enddate,
                                         #20240830:
                                         StartDate = as.Date(query.startdate, "%d-%b-%y"),
                                         EndDate = as.Date(query.enddate, "%d-%b-%y"),
                                         DateColName = "BUS_DATE"
    )
    #test write import data to Excel
    #myFN <- 'results_sched.xlsx'
    #mySN <- '20230516 1150'
    #writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = results_sched, colnames = TRUE, writeover = TRUE)
    
    #POPULATE ***NOTES*** RESULTS
    results_notes[] <- lapply(results_notes[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
    rs_load_notes <- Snowflake_Insert_Sched(Data = results_notes, 
                                         Schema = mySchema, 
                                         Tablename = myTableNotes,
                                         Stores = myStores_curr,
                                         #StartDate = query.startdate,
                                         #EndDate = query.enddate,
                                         #20240830:
                                         StartDate = as.Date(query.startdate, "%d-%b-%y"),
                                         EndDate = as.Date(query.enddate, "%d-%b-%y"),
                                         DateColName = "E_DATE"
    )
    
    #if load error, send warning email
    if(nrow(rs_load_daily) > 0 || nrow(rs_load_notes) > 0){
      mydata_load_issues <- rbind(rs_load_daily, rs_load_notes)
      myFN <- myReportName
      mySN <- query.date 
      myXLSXColWidths <- data.frame (
        colname  = c(
          "STORE",
          "ISSUE"
          #"",
        )
        ,
        width = c(
          9.5,
          42,
        )
        ,
        stringsAsFactors = FALSE
      ) #myXLSXColWidths
      writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata_load_issues, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
      #send load error email
      bodytext <- paste0("<h2>REPORT: ", myReportName,
                         "</h2>",
                         "<p>See the attached Excel file for details on issues ",
                         "noted below for the ",
                         "<a href=\"", rpt_url, "\">", "Schedule Checker (", gSht_Sheets[[1]],")</a>",
                         " load.</p>"
      )
      if(nrow(rs_load_daily) > 0){
        bodytext <- paste0(
          bodytext,
          "<h3>There were ", nrow(rs_load_daily),
          " stores with errors loading the '", myTableDaily, "' table.</h3> ",
          "<br>"
        )
        
      }
      if(nrow(rs_load_notes) > 0){
        bodytext <- paste0(
          bodytext,
          "<h3>There were ", nrow(rs_load_daily),
          " stores with errors loading the '", myTableNotes, "' table.</h3> ",
          "<br>"
        )
        
      }
      #add signature
      bodytext <- paste0(
        bodytext,
        warn_sig
      )
      mailsend(warn_recip,
               paste0(myReportName, ": Load Issues"),
               bodytext,
               attachment = NULL,
               inline = TRUE,
               test = testing_emails, testrecipient = test_recip
      )
      
    }
  }
}



