library(RODBC)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(stringr)
library(openxlsx)
library(readr)
library(keyring)


testing_emails <- FALSE  #NORMAL, next line over-rides this one, should be commented out in production
testing_emails <- TRUE

### 20241016 change:
### fixed reversion to "Hoogland Foods" (should be "HRG") in paths due to update of old script

### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced Signature logo from local file to published image URL (to avoid inline image attachment)

### 20220606 change:
### updated mailsend to use keyring

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)

macFn <- "Marcos Plan creator.xlsm"
macpath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars", macFn)
#googdrive_calsave <- file.path("G:", "My Drive", "Hoogland Restaurant Group", "Hoogland Foods - Marketing", "01 - Marketing Plans")
googdrive_calsave <- file.path("G:", "My Drive", "Hoogland Restaurant Group", "HRG - Marketing", "01 - Marketing Plans")
macdrive_calsave <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars")
CalTmpFn <- "MP_StoreCal_Tmp.csv"
CalLogPath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars")
CalRptFN <- "MP_StoreCal_Log.csv"
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
#origpath <- getwd()

myReportName <- "Marco's Marketing Plans - Create"

logname <- "MyStoreCals-Create-Log.csv"
#logpath <- getwd()
logpath <- file.path("C:","Users","table","Documents","ReportFiles","MARCOS_Store_Calendars")
query.date <- format(Sys.Date(), "%d-%b-%y")
report.date <- format(as.Date(query.date, "%d-%b-%y"), "%Y%m%d")  # YYYYMMDD format of date for saves of report files
report.time <- format(Sys.time(), "%H%M%S")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/>Highland Ventures Ltd.<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_st_from <- paste0("<b>Direct any questions about plans to your RM/DM. </b><br/><br/>",
                       " Steve Olson<br/> Purchasing Analyst<br/> Highland Ventures Ltd.<br/> <EMAIL><br/> (847)904-9043<br/>")

norm_RM_DM_from <- paste0("<b>Direct any questions about store plans to <a href=\"mailto:<EMAIL>\">Deanna Flynn</a>. </b><br/><br/>",
                          " Steve Olson<br/> Purchasing Analyst<br/> Highland Ventures Ltd.<br/> <EMAIL><br/> (847)904-9043<br/>")

#append signature logo
#(HVLTD Corp with Brands)
sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
if(exists("norm_st_from")){norm_st_from <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_sig")){norm_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_RM_DM_from")){norm_RM_DM_from <- paste0(norm_RM_DM_from, "<br/>", sig_image_src)}

# define some functions #
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


writelog <- function(LogTable){
  fn <- file.path(CalLogPath, logname)
  write.csv(LogTable, file = fn, row.names=FALSE)
}


st.emailaddy <- function(STORE_NUMBER){
  # Return the store email address based on current naming convention, default to warning email address if not found
  #print(paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"))
  addy <- case_when(
    STORE_NUMBER %in% seq(2, 888) ~ paste0("fv", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    STORE_NUMBER %in% seq(2100, 2999) ~ paste0("vt", str_pad(STORE_NUMBER, 4, pad = 0), "@familyvetgroup.com"),
    STORE_NUMBER %in% seq(3500, 3999) ~ paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    #(STORE_NUMBER >= 3500 & STORE_NUMBER <= 3999) ~ paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    STORE_NUMBER %in% seq(6500, 6999) ~ paste0("sf", str_pad(STORE_NUMBER, 4, pad = 0), "@stayfit24.com")
  )
  if(is.na(addy)){addy <- warn_recip}
  return(addy)
}


file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}



### check if log present/up-to-date ###
NewErrorLog <- FALSE
if(file.exists(file.path(logpath, logname)) ) {
  MyErrorLog <- read.csv(file = file.path(logpath, logname), sep=",", stringsAsFactors = FALSE)
  #check if log is from prior week, is so replace values with default starting values
  if( MyErrorLog[1,"QUERY_DATE"] != query.date ){
    #log is from previous date, replace with new default values
    NewErrorLog <- TRUE
  }
} else {
  # log not found, create new log values
  NewErrorLog <- TRUE
}
if( NewErrorLog ) {
  MyErrorLog <- data.frame(QUERY_DATE = query.date, 
                           CAL_STATUS = 'NO LOG FILE',
                           ST_EMAIL_STATUS = 'NO LOG FILE',
                           DM_EMAIL_STATUS = 'NO LOG FILE',
                           RM_EMAIL_STATUS = 'NO LOG FILE',
                           OTHYEAR_NEXTORLAST = 'NO LOG FILE',
                           PROGRESS = 'NO LOG FILE',
                           stringsAsFactors = FALSE)
}



#check if previous mailing of STORES failed part way through and set parameters to pick-up from where it left off
store.mail_from <- 1
store.mail_send <- TRUE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"ST_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed store that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    store.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"ST_EMAIL_STATUS"] == 'COMPLETE' ) {
    store.mail_send <- FALSE
  }
}

#check if previous mailing of DMS failed part way through and set parameters to pick-up from where it left off
dm.mail_from <- 1
dm.mail_send <- TRUE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"DM_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed DM that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    dm.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"DM_EMAIL_STATUS"] == 'COMPLETE' ) {
    dm.mail_send <- FALSE
  }
}

#check if previous mailing of RMS failed part way through and set parameters to pick-up from where it left off
rm.mail_from <- 1
rm.mail_send <- TRUE
#### disable RM sends with next line, RMs will access calendars via Google Drive#####
# if RM sends are desired, comment out the next line
rm.mail_send <- FALSE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"RM_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed DM that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    rm.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"CAL_STATUS"] == 'READY' & MyErrorLog[1,"RM_EMAIL_STATUS"] == 'COMPLETE' ) {
    rm.mail_send <- FALSE
  }
}



if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] != "READY") {

  MyErrorLog[1,"CAL_STATUS"] <- "STARTED"
  MyErrorLog[1,"PROGRESS"] <- "CAL_STATUS"
  OthYear_NextorLast <- "None"
  MyErrorLog[1,"OTHYEAR_NEXTORLAST"] <- OthYear_NextorLast
  writelog(MyErrorLog)
  CalsCreated <- FALSE
  CurrYear <- as.numeric(format(Sys.Date(), "%Y"))
  #CurrYearFolder <- paste0("Hoogland Foods - ", CurrYear, " Marketing Plan")
  CurrYearFolder <- paste0("HRG - ", CurrYear, " Marketing Plan")
  googdrive <- file.path("G:", "My Drive", "Hoogland Restaurant Group", CurrYearFolder)
  
  #setwd(googdrive)
  #fn <- paste0(CurrYear, " Hoogland Foods Plan MASTER.xlsx")
  fn <- paste0(CurrYear, " HRG Plan MASTER.xlsx")
  UseCurrYear <- file.exists(file.path(googdrive, fn))
  UseAltYear <- FALSE
  #lf <- list.files(pattern = " Hoogland Foods Plan MASTER.xlsx$", recursive = FALSE, ignore.case = TRUE)
  
  #create RDCOMClient connection to Excel
  xlApp <- COMCreate("Excel.Application")
  Sys.sleep(3)
  xlApp[["DisplayAlerts"]] <- FALSE
  xlApp[["AskToUpdateLinks"]] <- FALSE
  xlApp[['Visible']] <- TRUE
  Sys.sleep(1)
  
  if( UseCurrYear ) {
    planpath <- file.path(googdrive, fn)
    #check that file exists at expected location
    if(file.exists(planpath)){
      xlWbk.plan <- xlApp$Workbooks()$Open(planpath)
      Sys.sleep(3)
      #open macro file
      xlMac <- xlApp$Workbooks()$Open(macpath)
      Sys.sleep(2)
      
      # run macro to generate calendars
      xlApp$Run("create_store_calendars_auto")
      
      Sys.sleep(2)
      
      #close plan file
      xlWbk.plan$Close(FALSE)
      #close macro file
      xlMac$Close(FALSE)
      #read log
      logdata <- read.csv(file = file.path(CalLogPath, CalTmpFn), sep=",", stringsAsFactors = FALSE)
      if (nrow(logdata) >= 1){
        
        CalsCreated <- TRUE
        
        #get path to locally saved calendars and copy to Google Drive folder for same year
        if(dir.exists(googdrive_calsave)){
          copyfromPath <- file.path(macdrive_calsave, CurrYear)
          list.of.files <- list.files(copyfromPath, paste0(CurrYear,".pdf$"), full.names=TRUE)
          copytoPath <- file.path(googdrive_calsave, CurrYear)
          if(!dir.exists(file.path(googdrive_calsave, CurrYear))) { dir.create(file.path(googdrive_calsave, CurrYear))}
          file.copy(list.of.files, copytoPath, overwrite = TRUE, recursive = FALSE, copy.mode = TRUE)
        }
      }
    }else{
      #file not found in expected location, send warning
      #send error msg
      bodytext <- paste0("This is an automated email to inform you that it appears the <b>", fn, "</b> ",
                         "file was not found in the expected <b>", googdrive, "</b> directory.<br/><br/>",
                         "<br/><br/>Check to ensure the file exists in the directory noted above and that ",
                         "the path and file haven't changed and don't contain errors.",
                         "<br/><br/>The routine will NOT create calendars for the file noted.<br/> <br/>",
                         warn_sig
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = "Marco's Store Plan Calendars Issue: FILE NOT FOUND",
               body = bodytext,
               test = testing_emails, 
               testrecipient = test_recip
      )
    }
  }
  
  
  xlApp[["DisplayAlerts"]] <- TRUE
  #xlWbk$Close(FALSE)
  #xlMac$Close(FALSE)
  
  # run possible second loop when Sys.date is within 30 days of year end or 7 days into new year
  if( Sys.Date() - as.Date(paste0(as.numeric(format(Sys.Date(), "%Y")) - 1, "-12-31")) <= 20 ) {
    #within first two weeks of new year, check if last year plan available
    OthYear <- as.numeric(format(Sys.Date(), "%Y")) - 1
    OthYear_NextorLast <- "Last"
    MyErrorLog[1,"OTHYEAR_NEXTORLAST"] <- OthYear_NextorLast
    #OthYearFolder <- paste0("Hoogland Foods - ", OthYear, " Marketing Plan")
    OthYearFolder <- paste0("HRG - ", OthYear, " Marketing Plan")
    googdrive <- file.path("G:", "My Drive", "Hoogland Restaurant Group", OthYearFolder)
    #fn <- paste0(OthYear, " Hoogland Foods Plan MASTER.xlsx")
    fn <- paste0(OthYear, " HRG Plan MASTER.xlsx")
    UseAltYear <- file.exists(file.path(googdrive, fn))
  } else {
    if( as.Date(paste0(as.numeric(format(Sys.Date(), "%Y")) + 1, "-01-01")) - Sys.Date() <= 49 ) {
      #within 30 days of end of year, generate next year if plan available
      OthYear <- as.numeric(format(Sys.Date(), "%Y")) + 1
      OthYear_NextorLast <- "Next"
      MyErrorLog[1,"OTHYEAR_NEXTORLAST"] <- OthYear_NextorLast
      #OthYearFolder <- paste0("Hoogland Foods - ", OthYear, " Marketing Plan")
      OthYearFolder <- paste0("HRG - ", OthYear, " Marketing Plan")
      googdrive <- file.path("G:", "My Drive", "Hoogland Restaurant Group", OthYearFolder)
      fn <- paste0(OthYear, " HRG Plan MASTER.xlsx")
      UseAltYear <- file.exists(file.path(googdrive, fn))
    }
  }
  writelog(MyErrorLog)
  
  if( UseAltYear ) {
    planpath <- file.path(googdrive, fn)
    if(file.exists(planpath)){
      #make sure alerts and link updates turned off
      xlApp[["DisplayAlerts"]] <- FALSE
      Sys.sleep(2)
      xlApp[["AskToUpdateLinks"]] <- FALSE
      xlWbk.plan <- xlApp$Workbooks()$Open(planpath)
      Sys.sleep(2)
      #open macro file
      xlMac <- xlApp$Workbooks()$Open(macpath)
      Sys.sleep(3)
      
      # run macro to generate calendars
      xlApp$Run("create_store_calendars_auto")
      
      Sys.sleep(8)
      
      #close plan file
      xlWbk.plan$Close(FALSE)
      Sys.sleep(2)
      xlMac$Close(FALSE)
      Sys.sleep(2)
      
      #read log
      logdata2 <- read.csv(file = file.path(CalLogPath, CalTmpFn), sep=",", stringsAsFactors = FALSE)
      if (nrow(logdata2) >= 1){
        
        CalsCreated <- TRUE
        
        #get path to locally saved calendars and copy to Google Drive folder for same year
        if(dir.exists(googdrive_calsave)){
          copyfromPath <- file.path(macdrive_calsave, OthYear)
          list.of.files <- list.files(copyfromPath, paste0(OthYear,".pdf$"), full.names=TRUE)
          copytoPath <- file.path(googdrive_calsave, OthYear)
          if(!dir.exists(file.path(googdrive_calsave, OthYear))) { dir.create(file.path(googdrive_calsave, OthYear))}
          file.copy(list.of.files, copytoPath, overwrite = TRUE, recursive = FALSE, copy.mode = TRUE)
        }
      }
    }else{
      #file not found in expected location, send warning
      #send error msg
      bodytext <- paste0("This is an automated email to inform you that it appears the <b>", fn, "</b> ",
                         "file was not found in the expected <b>", googdrive, "</b> directory.<br/><br/>",
                         "<br/><br/>Check to ensure the file exists in the directory noted above and that ",
                         "the path and file haven't changed and don't contain errors.",
                         "<br/><br/>The routine will NOT create calendars for the file noted.<br/> <br/>",
                         warn_sig
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = "Marco's Store Plan Calendars Issue: FILE NOT FOUND",
               body = bodytext,
               test = testing_emails, 
               testrecipient = test_recip
      )
    }
  }
  
  #close excel instance
  if(exists('xlApp')){
    xlApp$Quit()
  }
  #xlApp$Quit()
  
  #combine calendar result dfs as needed
  if(CalsCreated){
    if( UseCurrYear & UseAltYear ){
      caldata <- rbind(logdata, logdata2)
      
    }else{
      
      if( UseCurrYear ) {caldata <- logdata}else{caldata <- logdata2}
    }
    
    #write finaldata to report
    #save local copy of the report first
    myFN <- file.path(CalLogPath, CalRptFN)
    if (dir.exists(CalLogPath) & file.opened(myFN) == FALSE) {
      #write_excel_csv(caldata, myFN, na="")
      write_csv(caldata, myFN, na="")
    } else {
      
      #send error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>Marco's Store Plan Calendars</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = "Marco's Store Plan Calendars Issue: REPORT FILE NOT SAVED",
               body = bodytext,
               test = testing_emails, 
               testrecipient = test_recip
      )
    }
    
    #update CAL_STATUS
    MyErrorLog[1,"CAL_STATUS"] <- "READY"
    MyErrorLog[1,"PROGRESS"] <- "CAL_STATUS"
    writelog(MyErrorLog)
    
  }else{
    
    #no appropriate calendars created, log
    #update CAL_STATUS
    MyErrorLog[1,"CAL_STATUS"] <- "NO CALENDARS"
    MyErrorLog[1,"PROGRESS"] <- "CAL_STATUS"
    writelog(MyErrorLog)
  }
  
  
  
  
}else{
  
  if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] == "READY" ){
    #Calendars previously generated, read in
    #read log
    caldata <- read.csv(file = file.path(CalLogPath, CalRptFN), sep=",", stringsAsFactors = FALSE)
    colnames(caldata)[1] <- "St_Num"
  }
}



if( MyErrorLog[1,"PROGRESS"] != "COMPLETE" & MyErrorLog[1,"CAL_STATUS"] == "READY" ){
  #mark calendar creation progress as "COMPLETE"
  MyErrorLog[1,"PROGRESS"] <- "COMPLETE"
  Sys.sleep(2)
  writelog(MyErrorLog)
  
  # Send routine complete email
  bodytext <- paste0("This is an automated email to inform you that it appears the <b>Marco's Store Plan Calendar CREATION</b> routine ",
                     "has completed.<br/><br/>",
                     "(Stores are emailed the calendars in a separate routine)<br/>",
                     "<br/>",
                     warn_sig
  )
  #send mail
  mailsend(recipient = warn_recip,
           subject = "Marco's Store Plan Calendar *CREATION* Status: COMPLETE",
           body = bodytext,
           test = testing_emails, 
           testrecipient = test_recip
  )
}


#rm(xlApp)
#rm(xlMac)
#rm(xlWbk)