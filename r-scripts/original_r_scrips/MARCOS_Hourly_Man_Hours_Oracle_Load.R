library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(purrr)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(tidyr)
library(DBI)
library(ROracle)
library(keyring)
library(janitor)

# written by <PERSON> March 2023


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version ********

### ******** change:
### new file, based on MARCOS_Weekly_Banking-Import_Transactions.R script


# Parameters

okaytocontinue <- TRUE

myReportName <- "<PERSON>'s Hourly Man-Hours-Oracle Load"
scriptfolder <- "MARCOS_Man_Hours"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_sig <- "<br/><b> <PERSON> <PERSON> </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010" || Sys.getenv("COMPUTERNAME") == "LAPTOPTOSHIBA13"){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}

# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "19-Feb-23" #testing line only

query.days <- 14
rpt.date <- as.Date(query.date, "%d-%b-%y")


### define some functions ###

#ROracle connection
#Sys.setenv(TZ='America/Chicago')
#Sys.setenv(ORA_SDTZ='America/Chicago')
Sys.setenv(TZ="GMT")
Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)

mySchema <- "STEVE"
myTable <- "MP_HOURLY_MAN_HOURS"
myTableName <- paste(mySchema, myTable, sep = ".")


mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, " <<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




# Query Oracle for needed Man_Hours data, uses CTE to speed up query to reasonable run-time
if(okaytocontinue){
  myquery <- paste0(
    "
        select /* current and last pay period */
          trunc(bpp) as s_date
      	,	trunc(to_date('", query.date, "')) as e_date
      	,	to_number(trunc(to_date('", query.date, "')) - trunc(bpp) + 1) as num_days
      	from steve.famv_begin_payperiods
      	where bpp <= to_date('", query.date, "') - ", query.days, "
          and epp >= trunc(to_date('", query.date, "')) - ", query.days, "
    "
  )
  mydates <- dbGetQuery(myOracleDB, myquery)
  
  
  myquery <- paste0(
    "
      WITH cte_dates as
      (
        select /* current and last pay period */
          trunc(bpp) as s_date
      	,	trunc(to_date('", query.date, "')) as e_date
      	,	to_number(trunc(to_date('", query.date, "')) - trunc(bpp) + 1) as num_days
      	from steve.famv_begin_payperiods
      	where bpp <= to_date('", query.date, "') - ", query.days, "
          and epp >= trunc(to_date('", query.date, "')) - ", query.days, "
      )
      ,
      cte_hours as
      (
          SELECT
              CAST(a.s_date + (rownum-1)/24 AS timestamp) AS HOUR_START
          ,	CAST(a.s_date + (rownum-1)/24 + (1/24) AS timestamp) AS HOUR_NEXT
          from
          cte_dates a
          connect by level <= (24*a.num_days)
      )
      ,
      cte_stores as
      (
      	select distinct
      	i.store
      	from ab_employees_punch i
      	inner join HR.hr_locations_all hrloc
              on lpad(i.store,4,0) = hrloc.location_code
              and UPPER(substr(hrloc.LOC_INFORMATION15,0,2)) = 'HF'
      	where i.e_date >= (select s_date from cte_dates)
          and i.s_date <= (select e_date + 1.2 from cte_dates)
      --/* TEST ONLY */ and i.store <= 3520
      )
      ,
      cte_store_hours as
      (
          SELECT *
          FROM CTE_STORES
          CROSS JOIN CTE_HOURS
      )
      ,
      cte_punches as
      (
          select b.store
          ,   b.s_date
          ,   nvl(b.e_date, b.s_date + (hours/24)) as e_date
          from ab_employees_punch b
          inner join cte_stores d
          on b.store = d.store
          where b.s_date >= (select s_date - 0.8 from cte_dates)
          and b.s_date < (select e_date + 1.2 from cte_dates)
          and b.p_type != 'S' /* exclude 'special' time (not worked) */
          and b.p_type != 'P' /* exclude 'personal' time (not worked) */
      )
      
      /* main query using CTE above*/
      select
          a.store
      ,   cast(a.hour_start as date) as hour_start
      ,   cast(a.hour_next as date) as hour_next
      ,   round(
              nvl(
                  SUM(/* punch minutes within hour*/
                      CASE WHEN b.s_date >= a.hour_start
                          THEN 0 - EXTRACT(MINUTE FROM CAST(b.s_date AS TIMESTAMP))
                      ELSE 0
                      END
                      +
                      CASE WHEN b.e_date >= a.hour_next
                          THEN 60
                      WHEN b.e_date < a.hour_next
                          THEN EXTRACT(MINUTE FROM CAST(b.e_date AS TIMESTAMP))
                      ELSE 0
                      END
                  )
              ,0) / 60 
          ,3) as MAN_HOURS
      from CTE_STORE_HOURS a
      left join cte_punches b
      on a.store = b.store
      and a.hour_start <= b.e_date
      and a.hour_next > b.s_date
      group by
          a.store
      ,   cast(a.hour_start as date)
      ,   cast(a.hour_next as date)
      order by store, hour_start
    "
  )
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 24, ReportName = myReportName)
  if(mydata_status[[1]]){
    
    
  }else{
    #apparent error, send warning email
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error querying the man-hours for the ", myReportName, " routine! ",
                       "The query failed or produced less than 24 rows of results.</p>",
                       "<br>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, missing data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
  
}



#Load Oracle
if(okaytocontinue){
  #Delete rows within expected date range and replace with current data
  myDeleteStores_failed <- FALSE
  myLoadStores_failed <- FALSE
  myDeleteError_text <- ""
  query.startdate <- format(mydates$S_DATE, "%d-%b-%y")
  query.enddate <- format(as.Date(mydates$E_DATE[[1]]) + 1, "%d-%b-%y")
  
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName, "
      where hour_start >= to_date('", query.startdate, "')
      and hour_start < to_date('", query.enddate, "')
    "
  )
  rs_sel <- dbSendQuery(myOracleDB, myquery_select)
  select_cnt <- dbFetch(rs_sel, n = -1)
  myquery_delete <- paste0(
    "
      delete from ", myTableName, "
      where hour_start >= to_date('", query.startdate, "')
      and hour_start < to_date('", query.enddate, "')
      "
  )
  rs_del <- dbSendQuery(myOracleDB, myquery_delete)
  if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(myOracleDB)
    myDeleteStores_failed <- TRUE
    myDeleteError_text <- paste0(
      "<p>There was an unexpected issue deleting previous data that might ",
      "have been present for dates between ", query.startdate,
      " and ", format(mydates$E_DATE, "%d-%b-%y"),". <b>The routine has ",
      "ABORTED without attempting to load the current results!</b></p>"
    )
    #do not load since deletion apparently failed
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    dbCommit(myOracleDB)
    
    #Insert trans rows into myTable, count rows before and after to catch load issues
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName, "
        where hour_start >= to_date('", query.startdate, "')
        and hour_start < to_date('", query.enddate, "')
      "
    )
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt_pre <- dbFetch(rs_sel, n = -1)
    dbClearResult(rs_sel)
    rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    #get new count of rows in table
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt_post <- dbFetch(rs_sel, n = -1)
    myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
    mydata_numrows <- nrow(mydata)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, get load counts by store
      myquery <- paste0(
        "
          select store
          ,   count(hour_start) as COUNT_LOADED
          from ", myTableName, "
          where hour_start >= to_date('", query.startdate, "')
            and hour_start < to_date('", query.enddate, "')
          group by store
          order by store
          "
      )
      myLoadStores_curr <- dbGetQuery(myOracleDB, myquery)
      #summarize dataframe to get counts by store
      myLoadStores_results_cnt <- mydata %>% select(STORE) %>% group_by(STORE) %>% summarize(QUERY_RESULTS = n())
      myLoad_failed <- myLoadStores_results_cnt %>% 
          dplyr::left_join(myLoadStores_curr, by = c("STORE")) %>%
          mutate_if(is.numeric,coalesce,0) %>%
          .[which(.$QUERY_RESULTS != .$COUNT_LOADED), ]
      
      
      if(nrow(myLoad_failed)>0){
        myLoadStores_failed <- TRUE
        myLoadError_text <- paste0(
          "<p>One or more stores failed to load the expected number or rows ",
          "into Oracle for the dates between ", query.startdate,
          " and ", format(mydates$E_DATE, "%d-%b-%y"),". <b>The Oracle table (",
          myTableName, ")", " will have incomplete results for these dates!</b> ",
          "Investigate the stores shown below and re-run this routine when issue ",
          "has been addressed.<br>",
          print(
            xtable(myLoad_failed, 
              digits = rep(0,ncol(myLoad_failed)+1),
              align = c(rep("c", ncol(myLoad_failed) + 1))
            ),
            html.table.attributes = 'border=2 cellspacing=1 align="center"',
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          ),
          "</p>"
        )
      }
    }
    
    
    
  }
  if(myDeleteStores_failed || myLoadStores_failed){
    #email warning
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error populating Oracle in the ", 
                       myReportName, " routine. </p>",
                       if(myDeleteStores_failed){myDeleteError_text},
                       if(myLoadStores_failed){myLoadError_text},
                       "<br>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Oracle load error"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



