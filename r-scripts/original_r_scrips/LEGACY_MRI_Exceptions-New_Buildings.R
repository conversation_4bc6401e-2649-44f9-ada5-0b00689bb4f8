#library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #20250108: replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
#library(RODBC)
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
testing_emails <- TRUE

# Version 20250108

### 20250108 change:
### convert from mailR to gmailr emailing package
### convert to 

### 20230428 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

scriptfolder <- "LEGACY_MRI_Exceptions-New_Buildings"
myReportName <- "MRI New Building Exceptions"
msg_text <- paste0("Routine Starting: ", myReportName)
base::message(msg_text)
rptFN <- paste0("MRI_New_Building_Exception_Details", ".xlsx")

myReportCriteria <- paste0("<p><b>Criteria for inclusion in the report:</b><ul>",
                           "<li>Building has mismatched BSQF-SSQF table sqft values</li>",
                           "</ul></p>"
)


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
norm_recip <- c("<EMAIL>","<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}
logpath <- file.path(mainpath,scriptfolder)
rptpath <- logpath
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}



check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
  ret_FN <- if(exists("myNewFN")){myNewFN}else{myFN}
  return(ret_FN)
}


file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}



### Find Exceptions and email results
if(okaytocontinue){
  
  myReportPath <- rptpath
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery_exceptions <- paste0(
    '
      SELECT BLDGID AS "BLDG ID"
      ,	ISSUE as "Issue"
      ,	ISSUE_DETAILS AS "Issue Details"
    ',"
      FROM
      (
        SELECT /* NEW BUILDINGS WITH MISMATCHED 'GLA' SQFT */ 
        	BLDG.BLDGID
        ,	'Mismatched Building-Suite GLA Sqft' as ISSUE
        ,	CONCAT(
        		'BSQF: ', 
        		ifnull(to_char(cast(bsqf.SQFT AS int)), 'NA'), 
        		', SSQF: ',  
        		ifnull(to_char(SUM(cast(Ssqf.SQFT AS int))), 'NA'),
        		' | BSQF Effdate: ',
        		ifnull(to_char(bsqf.EFFDATE,'yyyy-mm-dd'), 'NA'),
        		', SSQF Effdate: ',
        		ifnull(to_char(ssqf.EFFDATE,'yyyy-mm-dd'), 'NA')
        		) AS ISSUE_DETAILS
        FROM MRI.ENTITY
        left join MRI.BLDG on entity.ENTITYID = BLDG.ENTITYID
        left join MRI.BSQF 
        on bldg.BLDGID = bsqf.BLDGID
        AND BSQF.EFFDATE = (
        	SELECT MAX(I.EFFDATE) 
        	FROM MRI.BSQF I 
        	WHERE I.BLDGID = BSQF.BLDGID 
        	AND I.SQFTTYPE = 'GLA' 
        	AND I.EFFDATE <= TO_DATE(GETDATE()) 
        )
        AND BSQF.SQFTTYPE = 'GLA'
        left join MRI.ssqf 
        on bldg.BLDGID = SSQF.BLDGID and ssqf.SQFTTYPE = 'GLA'
        AND SSQF.EFFDATE = (
        	SELECT MAX(II.EFFDATE) 
        	FROM MRI.SSQF II 
        	WHERE II.BLDGID = SSQF.BLDGID 
        	AND II.SQFTTYPE = 'GLA' 
        	AND II.EFFDATE <= TO_DATE(GETDATE()) 
        )
        AND SSQF.SQFTTYPE = 'GLA'
        WHERE ENTITY.ACQUIRED >= DATEADD(month, -1, GETDATE())
        --/*TEST ONLY*/ WHERE ENTITY.ACQUIRED >= CONVERT(DATE, '20220101')
        GROUP BY 
          BLDG.BLDGID
        , bsqf.sqft
        , to_char(bsqf.EFFDATE,'yyyy-mm-dd')
        , to_char(ssqf.EFFDATE,'yyyy-mm-dd')
        HAVING ifnull(bsqf.sqft, 0) != SUM(cast(Ssqf.SQFT AS int))
      ) DETAILS
      order by DETAILS.BLDGID, DETAILS.ISSUE
    "
  )
  #20250108: mydata <- dbGetQuery(mySSdb, myquery_exceptions)
  mydata <- dbGetQuery(mySfDB, myquery_exceptions)
  #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
  mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
  mydata_status <- check_mydata_rows(MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("BLDG ID",
                                                "Issue",
                                                "Issue Details"
                                                #"",
    )
    ,
    width = c(10,
              if(max(nchar(na.omit(mydata[,2]))) > 24){min(108, max(nchar(na.omit(mydata[,2]))))}else{25},
              if(max(nchar(na.omit(mydata[,3]))) > 24){min(108, max(nchar(na.omit(mydata[,3]))))}else{25}
    )
    ,
    stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    written_FN <- writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(written_FN)
    # create email
    myemailbodycols <- c(1,2)
    mydata_emailbody <- mydata[,myemailbodycols]
    #DEDUP mydata_emailbody to get summary vs details of full dataframe
    mydata_emailbody <- mydata_emailbody %>% distinct()
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata_emailbody)<20){
      bodytable <- paste0("<p>The info below contains MRI data (from yesterday) that ",
                          "appears to be an exception. <b>See attached Excel file for full details.</b> ",
                          "</p>",
                          "<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)
                          ),
                          #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                          html.table.attributes = "border=2 cellspacing=1",
                          type = "html",
                          caption.placement = "top",
                          include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p><strong><em>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</em></strong></p>"
      )
    }
    bodytext <- paste0("<p><h2>REPORT: ", this_ReportName, "</h2>",
                       "</p>",
                       myReportCriteria,
                       bodytable,
                       "<br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   inline = TRUE,
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}


#20250108: DBI::dbDisconnect(mySSdb)
DBI::dbDisconnect(mySfDB)



