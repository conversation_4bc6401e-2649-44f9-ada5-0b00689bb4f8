library(DBI)
library(RO<PERSON>le)
library(keyring)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(dplyr)
library(data.table)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

### the daily refresh of the stm_email table occurs at 6:30 a.m. Central as of April 2023,
### this routine should run after that refresh on a daily basis to capture the previous
### business day's new hires from store management

# Version 20241106

### 20241106 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### Updated signature for emails to standard requested by <PERSON> in March 2024

### 20230523 change:
### added substitute to remove commas in data and replace with a space

### 20230505 change:
### added New Hire Email import file and minor bug fixes.

### 20230503 change:
### updated for actual import columns needed. Import file will only include paynum and email
### and the 'MISSING' email file will include more columns to aid end-user in identifying
### who the emp is and what location they were hired for

### 20230502 change:
### added columns for insertstamp and homestore and altered queries accordingly to use them

### 20230428 change:
### added in ability to rename columns for desired output file

### 20230425 change:
### changed to create file when emails present and another file for missing emails


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
#next line is a TEST LINE for running with an alternate testing date
#query.date <- query.date <- format(as.Date("02-May-23","%d-%b-%y"),"%d-%b-%y")
query.startdate <- query.date %>% as.Date("%d-%b-%y") %>% -1 %>% format("%d-%b-%y")
query.enddate <- query.date %>% as.Date("%d-%b-%y") %>% -0 %>% format("%d-%b-%y")

myemailfiles <- c()
myReportName <- "STM_EMAIL to ADP csv"
scriptfolder <- "HR_STM_EMAIL_To_csv"
rptfolder <- "reports"
report.datetime.txt <- paste0(as.Date(query.date, "%d-%b-%y"), ' ', format(Sys.time(), "%H%M%S %Z"))

okaytocontinue <- TRUE

#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
mySchema <- "FAMVDBA"
myTable <- "STM_EMAIL"
myTableName <- paste(mySchema, myTable, sep = ".")


# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
#norm_recip <- c("<EMAIL>", "<EMAIL>")  #this will probably end up being an email group
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")


myColNames <- c(
  "ID",
  "FNAME",
  "LNAME",
  "EMAIL",
  "HIREDATE",
  "INSERTSTAMP",
  "HOMESTORE"
)

myColNames_New <- c(
  "SrchKeyEMPLID",
  "FIRST_NAME",
  "LAST_NAME",
  "EMAIL_ADDRESS2",
  "HIREDATE",
  "CREATED",
  "LOCATION"
)

myImportNames <- c(
  "SrchKeyEMPLID",
  "FIRST_NAME",
  "LAST_NAME",
  "NAME",
  "STREET1",
  "CITY",
  "EMAIL_ADDRESS2",
  "CUSTOM_FIELD_ID",
  "PHONE_TYPE",
  "PHONE"
)

myMergeNames_Old <- c(
  "EMAIL",
  "FNAME",
  "LNAME",
  "ID",
  "HIRED_FOR",
  "HOMESTORE"
)
myMergeNames_New <- c(
  "Email",
  "First Name",
  "Last Name",
  "Emp ID",
  "Hired For",
  "Location"
)


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  rptpath <- file.path(logpath,rptfolder)
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- file.path(logpath, rptfolder)
sig_logo <- FALSE



### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}

check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}




######################
#--Query table data--#
######################

#query results *WITH AN EMAIL ADDRESS PRESENT*
if(okaytocontinue){
  sendresults <- FALSE
  
  
  #query results *WITH AN EMAIL ADDRESS PRESENT*
  myquery <- paste0(
    "select 
      ID
    , EMAIL
    from ", myTableName, "
    where email is not null
    and INSERTSTAMP >= to_date('", query.startdate, "')
    and INSERTSTAMP < to_date('", query.enddate, "')
    order by HOMESTORE, ID
    "
  )
  
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(mydata, MinNumRows = 1, ReportName = myReportName)
  
  if(mydata_status[[1]]){
    #write to .csv file and add to email attachment list
    
    #filter to desired columns
    #mydata <- mydata[,myColNames]
    
    #replace commas in data with space to prevent .csv file errors (not encapsulated)
    mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) gsub(',', ' ', x) else x)
    
    #rename columns
    setnames(mydata, 
             old = myColNames, 
             new = myColNames_New,
             skip_absent = TRUE)
    
    #get Import columns NOT present in mydata
    Curr_names <- names(mydata)
    Missing_names <- setdiff(myImportNames, Curr_names)
    #add blank columns needed to match needed Import and then reorder to match
    mydata[, Missing_names] <- NA
    mydata <- mydata[, myImportNames]
    #mySN <- format(Sys.Date(),'%b-%d-%Y')
    myFN <- paste0(myTable, " ", report.datetime.txt, ".csv")
    mySaveAs <- file.path(myReportPath, myFN)
    rs <- write.table(
      x = mydata, 
      file = mySaveAs,
      na = "",
      sep = ",", 
      quote = FALSE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    #read file to make sure all rows written
    mywrittendata <- read.table(file = mySaveAs, header = TRUE, sep = ",", dec = ".", stringsAsFactors = FALSE)
    #convert datetimes in mydata to characters to match .csv formatting
    mydata_comp <- copy(mydata)
    mydata_comp[] <- lapply(mydata_comp[], function(x) if(inherits(x, "POSIXct")) as.character(x, "%Y-%m-%d") else x)
    diff <- setdiff(mydata_comp, mywrittendata)
    
    #write with generic name (dated file above will be deleted after emailing)
    myFN2 <- paste0(myTable, ".csv")
    mySaveAs2 <- file.path(myReportPath, myFN2)
    rs <- write.table(
      x = mydata, 
      file = mySaveAs2,
      na = "",
      sep = ",", 
      quote = FALSE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    if(nrow(diff) == 0){
      #all rows written, add file to attach list and create email body text for this portion
      sendresults <- TRUE
      myemailfiles <- c(myemailfiles, mySaveAs)
      bodytext_emails_present <- paste0(
        "<p>The '", myFN, "' file that <b>INCLUDES EMAILS</B> is attached.</p>"
      )
      
    }else{
      #not all rows written, send warning and abort
      #no rows in query of table, abort and send warning!
      bodytext <- paste0(
        "<p>This is an automated email to inform you that it appears there is ",
        "an error in the ", myReportName, " routine.</p>",
        "<p>Data written to the <b>file that INCLUDES emails</b> ended up being ",
        "different than the source data from the ", myTableName, 
        " table. It appears that ", nrow(diff), " rows from the ",
        "source were not written properly. <b>The routine has ",
        "aborted.</b></p> ",
        warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: File write error"),
               bodytext,
               attachment = NULL,
               inline = sig_logo,
               test = testing_emails, testrecipient = test_recip
      )
      okaytocontinue <- FALSE
    }
    
  }
}





#query results *MISSING AN EMAIL ADDRESS*
if(okaytocontinue){
  myquery <- paste0(
    "select 
      ID
    , FNAME
    , LNAME
    , EMAIL
    , HOMESTORE
    from ", myTableName, "
    where email is null
    and INSERTSTAMP >= to_date('", query.startdate, "')
    and INSERTSTAMP < to_date('", query.enddate, "')
    order by HOMESTORE, ID
    "
  )
  mydata_nulls <- dbGetQuery(myOracleDB, myquery)
  mydata_status_nulls <- check_mydf_rows(mydata_nulls, MinNumRows = 1, ReportName = myReportName)
  
  if(mydata_status_nulls[[1]]){
    #write to .csv file and add to email attachment list
    
    #filter to desired columns
    #mydata_nulls <- mydata_nulls[,myColNames]
    
    #replace commas in data with space to prevent .csv file errors (not encapsulated)
    mydata_nulls[] <- lapply(mydata_nulls[], function(x) if(inherits(x, "character")) gsub(',', ' ', x) else x)
    
    #rename columns
    setnames(mydata_nulls, 
             old = myColNames, 
             new = myColNames_New,
             skip_absent = TRUE)
    
    #get Import columns NOT present in mydata_nulls
    Curr_names <- names(mydata_nulls)
    Missing_names <- setdiff(myImportNames, Curr_names)
    #add blank columns needed to match needed Import and then reorder to match
    mydata_nulls[, Missing_names] <- NA
    #add LOCATION column to end of standard import format to assist end user locating store to call to get missing email address
    mydata_nulls <- mydata_nulls[, c(myImportNames,"LOCATION")] 
    #mySN <- format(Sys.Date(),'%b-%d-%Y')
    myFN_nulls <- paste0(myTable, " -MISSING EMAILS- ", report.datetime.txt, ".csv")
    mySaveAs_nulls <- file.path(myReportPath, myFN_nulls)
    rs <- write.table(
      x = mydata_nulls, 
      file = mySaveAs_nulls, 
      na = "",
      sep = ",", 
      quote = FALSE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    #read file to make sure all rows written
    mywrittendata_nulls <- read.table(file = mySaveAs_nulls, header = TRUE, sep = ",", dec = ".", stringsAsFactors = FALSE)
    #diff <- setdiff(mydata_nulls, mywrittendata_nulls)
    mydata_nulls_comp <- copy(mydata_nulls)
    mydata_nulls_comp[] <- lapply(mydata_nulls_comp[], function(x) if(inherits(x, "POSIXct")) as.character(x, "%Y-%m-%d") else x)
    diff <- setdiff(mydata_nulls_comp, mywrittendata_nulls)
    
    #write with generic name (dated file above will be deleted after emailing)
    myFN2_nulls <- paste0(myTable, "-MISSING EMAILS", ".csv")
    mySaveAs2_nulls <- file.path(myReportPath, myFN2_nulls)
    rs <- write.table(
      x = mydata_nulls, 
      file = mySaveAs2_nulls, 
      na = "",
      sep = ",", 
      quote = FALSE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    if(nrow(diff) == 0){
      #all rows written, add file to attach list and create email body text for this portion
      sendresults <- TRUE
      myemailfiles <- c(myemailfiles, mySaveAs_nulls)
      bodytext_emails_notpresent <- paste0(
        "<p>The '", myFN_nulls, "' file for rows where the <b>EMAIL WAS NULL</B> is attached.</p>"
      )
      
    }else{
      #not all rows written, send warning
      #no rows in query of table, abort and send warning!
      bodytext <- paste0(
        "<p>This is an automated email to inform you that it appears there is ",
        "an error in the ", myReportName, " routine.</p>",
        "<p>Data written to the <b>file for MISSING emails</b> ended ",
        "up being different than the source data from the ", myTableName, 
        " table. It appears that ", nrow(diff), " rows from the ",
        "source were not written properly. <b>The routine has ",
        "aborted.</b></p> ",
        warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: File write error"),
               bodytext,
               attachment = NULL,
               inline = sig_logo,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
}
  
  
  
    
  
if(okaytocontinue & sendresults){
  #gather data in New Hire Email format
  myquery <- paste0(
    "
    select 
      a.EMAIL
    , a.FNAME
    , a.LNAME
    , a.ID
    , b.HIRED_FOR
    , HOMESTORE
    from ", myTableName, " a
  --  from stm_email a
    left join
    (
        SELECT
            to_number(hr.location_code) as STORE
        , case
            when UPPER(substr(hr.loc_information15, 1, 4)) = 'FVMC' then 'Family Video Movie Club'
            when UPPER(substr(hr.loc_information15, 1, 5)) = 'STFIT' then 'StayFit 24'
            when UPPER(substr(hr.loc_information15, 1, 2)) = 'HF' then 'Hoogland Restaurant Group/Marcos'
            when UPPER(substr(hr.loc_information15, 1, 4)) = 'HPWI' then 'Highland Pure Water '||chr(38)||' Ice'
          end as HIRED_FOR
        , HR.LOC_INFORMATION15
        FROM hr_locations_all hr
        WHERE REGEXP_LIKE(HR.LOCATION_CODE, '^[[:digit:]]+$')
    ) b
    on a.HOMESTORE = b.STORE
    where INSERTSTAMP >= to_date('", query.startdate, "')
    and INSERTSTAMP < to_date('", query.enddate, "')
  --  where INSERTSTAMP >= trunc(sysdate - 1)
  --  and INSERTSTAMP < trunc(sysdate - 0)
    order by CASE WHEN A.EMAIL IS NOT NULL THEN 0 ELSE 1 END, HOMESTORE, ID
    "
  )
  mydata_merge <- dbGetQuery(myOracleDB, myquery)
  mydata_status_merge <- check_mydf_rows(mydata_merge, MinNumRows = 1, ReportName = myReportName)
  
  
  
  if(mydata_status_merge[[1]]){
    #write to .csv file and add to email attachment list
    
    #replace commas in data with space to prevent .csv file errors (not encapsulated)
    mydata_merge[] <- lapply(mydata_merge[], function(x) if(inherits(x, "character")) gsub(',', ' ', x) else x)
    
    #rename columns
    setnames(mydata_merge, 
             old = myMergeNames_Old, 
             new = myMergeNames_New,
             skip_absent = TRUE)
    
    myFN_merge <- paste0("Mail Merge data -", myTable, "- ", report.datetime.txt, ".csv")
    mySaveAs_merge <- file.path(myReportPath, myFN_merge)
    rs <- write.table(
      x = mydata_merge, 
      file = mySaveAs_merge, 
      na = "",
      sep = ",", 
      quote = FALSE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    #read file to make sure all rows written
    mywrittendata_merge <- read.table(file = mySaveAs_merge, header = TRUE, sep = ",", dec = ".", stringsAsFactors = FALSE)
    #diff <- setdiff(mydata_nulls, mywrittendata_nulls)
    #replace "." in mywrittendata_merge with spaces to match mydata_merge
    names(mywrittendata_merge) <- gsub(x = names(mywrittendata_merge), pattern = "\\.", replacement = " ")
    #replace "" values in mywrittendata_merge with NA to match orig query
    mywrittendata_merge <- replace(mywrittendata_merge, mywrittendata_merge=="", NA)
    
    mydata_merge_comp <- copy(mydata_merge)
    mydata_merge_comp[] <- lapply(mydata_merge_comp[], function(x) if(inherits(x, "POSIXct")) as.character(x, "%Y-%m-%d") else x)
    diff <- setdiff(mydata_merge_comp, mywrittendata_merge)
    
    #write with generic name (dated file above will be deleted after emailing)
    myFN2_merge <- paste0("Mail Merge data - ", myTable, ".csv")
    mySaveAs2_merge <- file.path(myReportPath, myFN2_merge)
    rs <- write.table(
      x = mydata_merge, 
      file = mySaveAs2_merge, 
      na = "",
      sep = ",", 
      quote = FALSE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    if(nrow(diff) == 0){
      #all rows written, add file to attach list and create email body text for this portion
      sendresults <- TRUE
      myemailfiles <- c(myemailfiles, mySaveAs_merge)
      bodytext_emails_merge <- paste0(
        "<p>The '", myFN_merge, "' file for the <b>NEW HIRE EMAIL MERGE</B> is attached.</p>"
      )
      
    }else{
      #not all rows written, send warning
      #no rows in query of table, abort and send warning!
      bodytext <- paste0(
        "<p>This is an automated email to inform you that it appears there is ",
        "an error in the ", myReportName, " routine.</p>",
        "<p>Data written to the <b>file for the NEW HIRE EMAIL MERGE</b> ended ",
        "up being different than the source data from the ", myTableName, 
        " table. It appears that ", nrow(diff), " rows from the ",
        "source were not written properly. <b>The routine will ",
        "continue without this file.</b></p> ",
        warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: File write error"),
               bodytext,
               attachment = NULL,
               inline = sig_logo,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
  
  
  
  
  
  
}



if(okaytocontinue && sendresults){
  #send email
  bodytext <- paste0(
    "<h3>", paste0(myReportName, " updates for ", format(as.Date(query.date, "%d-%b-%y"), "%m/%d/%Y")), "</h3>",
    "<p>Data attached in the following file(s) reflects hires from the previous business day.</p>"
  )
  
  if(exists("bodytext_emails_present")){
    bodytext <- paste0(bodytext, bodytext_emails_present)
  }else{
    bodytext <- paste0(bodytext, "<p>No data for hires WITH emails was found.</p>")
  }
  if(exists("bodytext_emails_notpresent")){
    bodytext <- paste0(bodytext, bodytext_emails_notpresent)
  }else{
    bodytext <- paste0(bodytext, "<p>No data for hires MISSING emails was found.</p>")
  }
  if(exists("bodytext_emails_merge")){
    bodytext <- paste0(bodytext, bodytext_emails_merge)
  }else{
    bodytext <- paste0(bodytext, "<p>No data for NEW HIRE EMAIL MERGE was found.</p>")
  }
  
  
  bodytext <- paste0(bodytext, "<br>", norm_sig)
  
  mailsend(norm_recip,
           paste0(myReportName, " Results"),
           bodytext,
           if(length(myemailfiles) == 0){attachment = NULL}else{attachment = myemailfiles},
           inline = sig_logo,
           test = testing_emails, testrecipient = test_recip
  )
  
  #cleanup date specific file(s) (files with generic name also written to preserve latest results)
  unlink(x = myemailfiles)
  
  
}else{
  if(okaytocontinue & !sendresults){
    #no rows in either query of table or an error occurred, abort and send warning!
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "appears to be no data found in the ", myReportName, " routine.</p>",
                       "<p>There were no rows of data retured from the ",
                       myTableName, " table or an error occured while processing",
                       "the data so no files are attached. <b>The routine has ",
                       "completed.</b></p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, ": No data in table or error"),
             bodytext,
             attachment = NULL,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


