library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(dbplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
#library(RODBC)
library(DBI)
library(ROracle)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20220614

### 20220614 change:
### added logic so when the YTD totals are equal, the corresponding periods are removed from the report

### 20220518 change:
### changed SQL to ignore where DETAILS net is 0 and SUMMARY is NULL (sometimes invoices reversed, but no summary row present)

### 20220516 change:
### New file
### FYI, uses ROracle:DBI method (vs RODBC) which improves datatype matching of SQL queries


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("19-MAR-22","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

rptfolder <- "HV_Exceptions-GL_Summary_vs_Details"
myReportName <- "Oracle GL Summary vs Details Exceptions (exclude YTD balance)"
rptFN <- paste0("Oracle_GL_Summary_vs_Details_Exceptions_YTD_excl", ".xlsx")
myReportCriteria <- paste0("<p><b>Criteria for inclusion in the report:</b> ",
                           "(note comparisons are made at the GL Coding level within each period...if ",
                           "the YTD sum of the difference eventually nets out to $0, it will drop off ",
                           "this report at that point)<ul>",
                           "<li>GL Summary NET doesn't equal the sum of the Details view</li>",
                           "<li>GL Summary NET has a balance <> 0, but no corresponding Detail invoices present</li>",
                           "<li>GL Detail invoices present, but no corresponding GL Summary row present</li>",
                           "</ul></p><br/>"
                           )


logpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder)


#ROracle connection
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password = key_get("Oracle", "steve"), dbname = connect.string)


# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>")
norm_recip <- c("<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
                )
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")

rptpath <- logpath

if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010" || Sys.getenv("COMPUTERNAME") == "LAPTOPTOSHIBA13"){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  rptpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
}


### define some functions ###

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, " <<EMAIL>>")
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  
  send.mail(from = sender,
            to = recipients,
            replyTo = "<EMAIL>",
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com",
                        port = 465, 
                        user.name = "<EMAIL>",            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
}





### Find Exceptions and email results
if(okaytocontinue){
  
  myReportPath <- rptpath
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery <- paste0(
    "
      SELECT I.PERIOD_NUM
      ,   I.PERIOD_YEAR
      FROM MP_CALENDAR I
      WHERE TRUNC(I.S_DATE) <= TRUNC(to_date('", query.date, "') - 1)
          AND TRUNC(I.E_DATE) >= TRUNC(to_date('", query.date, "') - 1)
    "
  )
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 1, ReportName = myReportName)
  okaytocontinue <- mydata_status[[1]]

  
  if(okaytocontinue){
    myperiods <- data.frame( PERIOD_NUM = seq_len(mydata$PERIOD_NUM),
                             PERIOD_YEAR = rep(mydata$PERIOD_YEAR, mydata$PERIOD_NUM)
    )
    if(mydata$PERIOD_NUM < 3){
      #use previous year AND current year
      myperiods_py <- data.frame(PERIOD_NUM = seq_len(14),
                                 PERIOD_YEAR = rep((mydata$PERIOD_YEAR - 1), 14)
      )
      myperiods <- rbind(myperiods_py, myperiods)
    }
    for(i in 1:nrow(myperiods)){
      #loop to query each period in desired range
      if(exists("mydata")){rm(mydata)}
      myCurrPeriod <- myperiods$PERIOD_NUM[i]
      myCurrYear <- myperiods$PERIOD_YEAR[i]
      myquery_temp <- paste0(
        "
        SELECT
            RSLT.ISSUE
        ,   RSLT.PERIOD_YEAR
        ,   RSLT.PERIOD_NUM
        ,   RSLT.COMPANY_ID
        ,   RSLT.GL_ACCOUNT
        ,   RSLT.GL_ACCOUNT_DESCRIPTION
        ,   RSLT.COST_CENTER
        ,   RSLT.GL_CODING
        ,   RSLT.NUM_DETAIL_INVOICES
        ,   RSLT.DETAIL_PERIOD_DEBITS
        ,   RSLT.DETAIL_PERIOD_CREDITS
        ,   RSLT.DETAIL_PERIOD_NET
        ,   RSLT.SUMMARY_PERIOD_DEBITS
        ,   RSLT.SUMMARY_PERIOD_CREDITS
        ,   RSLT.SUMMARY_PERIOD_NET
        ,   RSLT.NET_DIFF
        FROM
        (
            SELECT
                CASE WHEN GLD.PERIOD_NET IS NULL THEN 'SUMMARY GL_ACCOUNT not present in DETAILS'
                  ELSE 'DETAILS NET different than SUMMARY'
                  END AS ISSUE
            ,   GLS.FISCAL_YEAR AS PERIOD_YEAR
            ,   GLS.PERIOD AS PERIOD_NUM
            ,   GLS.COMPANY_ID
            ,   GLS.GL_ACCOUNT
            ,   GLS.GL_ACCOUNT_DESRIPTION AS GL_ACCOUNT_DESCRIPTION
            ,   GLS.COST_CENTER
            ,   GLS.GL_CODING
            ,   NVL(GLD.INVOICE_CNT, 0) AS NUM_DETAIL_INVOICES
            ,   GLD.DEBITS AS DETAIL_PERIOD_DEBITS
            ,   GLD.CREDITS AS DETAIL_PERIOD_CREDITS
            ,   GLD.PERIOD_NET AS DETAIL_PERIOD_NET
            ,   GLS.TOTAL_PERIOD_DEBITS AS SUMMARY_PERIOD_DEBITS
            ,   GLS.TOTAL_PERIOD_CREDITS AS SUMMARY_PERIOD_CREDITS
            ,   GLS.TOTAL_PERIOD_NET AS SUMMARY_PERIOD_NET
            ,   GLS.TOTAL_PERIOD_NET - NVL(GLD.PERIOD_NET,0) AS NET_DIFF
            FROM
            (
                SELECT
                    S.FISCAL_YEAR
                ,   S.PERIOD
                ,   S.COMPANY_ID
                ,   S.GL_ACCOUNT
                ,   S.GL_ACCOUNT_DESRIPTION
                ,   S.COST_CENTER
                ,   S.GL_CODING
                ,   S.TOTAL_PERIOD_DEBITS
                ,   S.TOTAL_PERIOD_CREDITS
                ,   S.TOTAL_PERIOD_NET
                FROM RPT_GL_SUMMARY_V S
                WHERE S.COST_CENTER != '0000'
                    AND (S.FISCAL_YEAR = ", myCurrYear, " AND S.PERIOD = ", myCurrPeriod, ")
                    --/*TEST*/ AND S.COST_CENTER <= '0004'
            ) GLS
            LEFT JOIN
            (
                SELECT
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                ,   D.COST_CENTER
                ,   D.GL_CODING
                ,   COUNT(D.GL_TRANSACTION_DATE) AS INVOICE_CNT
                ,   SUM(D.DEBIT) AS DEBITS
                ,   SUM(D.CREDIT) AS CREDITS
                ,   SUM(D.NET) AS PERIOD_NET
                FROM RPT_GL_DETAIL_V D
                WHERE D.COST_CENTER != '0000'
                    AND (D.PERIOD_YEAR = ", myCurrYear, " AND D.PERIOD_NUM = ", myCurrPeriod, ")
                    --/*TEST*/ AND D.COST_CENTER <= '0004'
                GROUP BY
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                ,   D.COST_CENTER
                ,   D.GL_CODING
            ) GLD
            ON GLS.FISCAL_YEAR = GLD.PERIOD_YEAR
                AND GLS.PERIOD = GLD.PERIOD_NUM
                AND GLS.COMPANY_ID = GLD.COMPANY_ID
                AND GLS.COST_CENTER = GLD.COST_CENTER
                AND GLS.GL_CODING = GLD.GL_CODING
            WHERE GLS.TOTAL_PERIOD_NET != NVL(GLD.PERIOD_NET,0)
            
            
                UNION ALL
            
            
            SELECT /* This pulls the GL Detail view GLs where GL Summary is missing */
                'DETAILS GL_ACCOUNT not present in SUMMARY' as ISSUE
            ,   GLD.PERIOD_YEAR AS PERIOD_YEAR
            ,   GLD.PERIOD_NUM AS PERIOD_NUM
            ,   GLD.COMPANY_ID
            ,   GLD.GL_ACCOUNT
            ,   GLD.GL_ACCOUNT_DESCRIPTION
            ,   GLD.COST_CENTER
            ,   GLD.GL_CODING
            ,   GLD.INVOICE_CNT AS NUM_DETAIL_INVOICES
            ,   GLD.DEBITS AS DETAIL_PERIOD_DEBITS
            ,   GLD.CREDITS AS DETAIL_PERIOD_CREDITS
            ,   GLD.PERIOD_NET AS DETAIL_PERIOD_NET
            ,   GLS.TOTAL_PERIOD_DEBITS AS SUMMARY_PERIOD_DEBITS
            ,   GLS.TOTAL_PERIOD_CREDITS AS SUMMARY_PERIOD_CREDITS
            ,   GLS.TOTAL_PERIOD_NET AS SUMMARY_PERIOD_NET
            ,   GLD.PERIOD_NET - NVL(GLS.TOTAL_PERIOD_NET, 0) AS NET_DIFF
            FROM
            (
                SELECT
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                ,   D.COST_CENTER
                ,   D.GL_CODING
                ,   COUNT(D.GL_TRANSACTION_DATE) AS INVOICE_CNT
                ,   SUM(D.DEBIT) AS DEBITS
                ,   SUM(D.CREDIT) AS CREDITS
                ,   SUM(D.NET) AS PERIOD_NET
                FROM RPT_GL_DETAIL_V D
                WHERE D.COST_CENTER != '0000'
                    AND (D.PERIOD_YEAR = ", myCurrYear, " AND D.PERIOD_NUM = ", myCurrPeriod, ")
                    --/*TEST*/ AND D.COST_CENTER <= '0004'
                GROUP BY
                    D.PERIOD_YEAR
                ,   D.PERIOD_NUM
                ,   D.COMPANY_ID
                ,   D.GL_ACCOUNT
                ,   D.GL_ACCOUNT_DESCRIPTION
                ,   D.COST_CENTER
                ,   D.GL_CODING
            ) GLD
            LEFT JOIN
            (
                SELECT
                    S.FISCAL_YEAR
                ,   S.PERIOD
                ,   S.COMPANY_ID
                ,   S.GL_ACCOUNT
                ,   S.GL_ACCOUNT_DESRIPTION
                ,   S.COST_CENTER
                ,   S.GL_CODING
                ,   S.TOTAL_PERIOD_DEBITS
                ,   S.TOTAL_PERIOD_CREDITS
                ,   S.TOTAL_PERIOD_NET
                FROM RPT_GL_SUMMARY_V S
                WHERE S.COST_CENTER != '0000'
                    AND (S.FISCAL_YEAR = ", myCurrYear, " AND S.PERIOD = ", myCurrPeriod, ")
                    --/*TEST*/ AND S.COST_CENTER <= '0004'
            ) GLS
            ON GLD.PERIOD_YEAR = GLS.FISCAL_YEAR
                AND GLD.PERIOD_NUM = GLS.PERIOD
                AND GLD.COMPANY_ID = GLS.COMPANY_ID
                AND GLD.COST_CENTER = GLS.COST_CENTER
                AND GLD.GL_CODING = GLS.GL_CODING
            WHERE (GLD.PERIOD_NET <> 0 AND GLS.TOTAL_PERIOD_NET is NULL)
        ) RSLT
        ORDER BY RSLT.PERIOD_YEAR
        ,   RSLT.PERIOD_NUM
        ,   RSLT.COST_CENTER
        ,   RSLT.GL_ACCOUNT
      "
      )
      mydata <- dbGetQuery(myOracleDB, myquery_temp)
      mydata_status <- check_mydata_rows(MinNumRows = 1, ReportName = myReportName)
      if(mydata_status[[1]] == TRUE){
        #exceptions for the current period present, create or append to final exception DF
        if(exists("myexceptions")){
          #append additional results
          myexceptions <- rbind(myexceptions, mydata)
        }else{
          #create results
          myexceptions <- mydata
        }
      }
      
    }#for(i in 1:nrow(myperiods)){
    
    if(exists("myexceptions")){
      mydata <- myexceptions
      my_headers <- mydata[0,]
      colnames(mydata) <- gsub("_"," ",colnames(my_headers))
      #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
      mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
      rm(myexceptions)
    }
    
  }
  
  #the next three lines added to remove 'reconciled' instances where YTD activity is equal
  myYTDNet <- mydata %>% group_by(`PERIOD YEAR`, `GL CODING`) %>% summarise(sum = sum(`NET DIFF`))
  myDiffs <- myYTDNet[which(myYTDNet$sum != 0),]
  mydata <- semi_join(mydata, myDiffs)
  
  mydata_status <- check_mydata_rows(MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("ISSUE",
                                                "PERIOD YEAR",
                                                "PERIOD NUM",
                                                "COMPANY ID",
                                                "GL ACCOUNT",
                                                "GL ACCOUNT DESCRIPTION",
                                                "COST CENTER",
                                                "GL CODING",
                                                "NUM DETAIL INVOICES",
                                                "DETAIL PERIOD DEBITS",
                                                "DETAIL PERIOD CREDITS",
                                                "DETAIL PERIOD NET",
                                                "SUMMARY PERIOD DEBITS",
                                                "SUMMARY PERIOD CREDITS",
                                                "SUMMARY PERIOD NET",
                                                "NET DIFF"
                                                #"",
                                                )
                                   ,
                                   width = c(if(max(nchar(na.omit(mydata[,1]))) > 39){min(56, max(nchar(na.omit(mydata[,1])))+1)}else{40},
                                             8.25,
                                             8.25,
                                             10.5,
                                             10.5,
                                             if(max(nchar(na.omit(mydata[,6]))) > 26){min(32, max(nchar(na.omit(mydata[,6]))))}else{27},
                                             9,
                                             15,
                                             10,
                                             10.5,
                                             10.5,
                                             10.5,
                                             10.5,
                                             10.5,
                                             10.5,
                                             if(max(nchar(na.omit(mydata[,16]))) > 9){min(30, max(nchar(na.omit(mydata[,16]))))}else{9.5}
                                             )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mylastpernum <- nrow(myperiods)
    mySN <- paste0(
      "P",
      str_pad(myperiods$PERIOD_NUM[1], 2, pad = "0"),
      "-",
      str_pad(myperiods$PERIOD_YEAR[1] %% 100, 2, pad = "0"),
      " to ",
      "P",
      str_pad(myperiods$PERIOD_NUM[mylastpernum], 2, pad = "0"),
      "-",
      str_pad(myperiods$PERIOD_YEAR[mylastpernum] %% 100, 2, pad = "0")
    )
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    #myemailbodycols <- c(1,2,3,5,6,7,16) #original test columns
    myemailbodycols <- c(1,2,3,6,8,16) #20220517 test
    mydata_emailbody <- mydata[,myemailbodycols]
    #DEDUP mydata_emailbody to get summary vs details of full dataframe
    mydata_emailbody <- mydata_emailbody %>% distinct()
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata_emailbody)<40){
      bodytable <- paste0("<p>The info below contains Oracle GL Summary vs Detail view data that ",
                          "appears to not match for ", mySN, ". <b>See attached Excel file for full details.</b> ",
                          "</p>",
                          "<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)
                          ),
                          #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                          html.table.attributes = "border=2 cellspacing=1",
                          type = "html",
                          caption.placement = "top",
                          include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p><strong><em>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</em></strong></p>"
      )
    }
    bodytext <- paste0("<p><h2>REPORT: ", this_ReportName, " ", mySN, "</h2>",
                       "</p>",
                       myReportCriteria,
                       bodytable,
                       "<br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   inline = TRUE,
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}


DBI::dbDisconnect(myOracleDB)




