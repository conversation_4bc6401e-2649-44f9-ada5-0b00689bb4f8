library(dplyr)
library(googlesheets4)
#library(mailR)  #20240916 replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to emails
library(xtable)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(stringr)
library(openxlsx)
library(readr)
library(FLSSS)
library(data.table)
library(tidyverse)
library(keyring)
library(DBI)
library(odbc)
#20240930: library(ROracle)


# This script downloads the latest weekly Marcos Weekly results and
# mails the following:
# 1) Restaurants get the stores in their
# District (if a DM name is present, otherwise just their store)
# 2) DMs get the stores in their Region

# REQUIREMENTS:
# this file requires Google OAuth access be granted to tidyverse
# so that googlesheets4 can access needed Sheets, to grant access
# run this script with testing_emails set to TRUE and run the
# google sheets section (search for 'MyErrorLog[1,"GS_STATUS"] <- "LOADING GOOGLE SHEET"')
# You will be prompted in R if you wish to cache OAuth credentials
# in a local folder and a browser window will open where Google
# will verify you want to grant access to tidyverse (googlesheets4).

# VERSION:
# Version 20250312

### 20250312 change:
### updated check for prod computer vs test

### 20240930 change:
### move to Snowflake databases

### 20240916 change:
### converted from mailR package (SMTP), to gmailr (OAuth-GMail API) ahead of 20240930 SMTP deprecation in GMail
### changed signature HTML to use HVSigPath .csv file
### added 10 new columns to known columns

### 20240723 change:
### added "Pricing Tier (7/8/24)" to known columns

### 20240528 change:
### added in check that all 'store' rows have at least one assignment (RM, DM or Store #)

### 20240429 change:
### added "Pricing Tier (4/8/24)" column (Excel format like store # column)

### 20240318 change:
### Added "Average Load Time","PPLH", "ALT Rank", "PPLH Rank" columns

### 20231023 change:
### Added "Avg. # of Voids" column
### Fixed glitch in get_RM_DM_names() function where no RM or DM name present to exclude NA name rows

### 20231016 change:
### Added "Worked Hours Combined" column

### 20230925 change:
### Added "MOMs Installation Week" (Date) column

### 20230731 change:
### Added "# of Voids (Combined)" and "Combined Avg Worked Hours"

### 20221212 change:
### Added "Hang Up Calls (Net)" to known columns (same as previous "Hang Up Calls" but filtered differently)

### 20220711 change:
### Added loop to find "Grand Total" row regardless of which column it's in
### from now on this routine should be agnostic in regards to any column order of source

### 20220705 change:
### Source Google Sheet changed column A from "St #" to "Store #" which broke this
### routine, updated gSht_StColName variable to match. Some other columns
### were also renamed and would have caused a warning email.

### 20220606 change:
### updated mailsend to use keyring

### 20220517 change:
### added various 'rank' columns to known columns that also need special formatting
### added keyring, DBI and ROracle packages for SQL queries
### updated store email logic for storelist query

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)

#200903 version changes:
### replaced sheets_cells() with range_read_cells() as sheets_cells was deprecated in v0.2.0
### also added GM email to stlist query and will send to that email when it's present,
### otherwise use store email
# 200805 version changes:
### updated googlesheets4 Oauth method, sheets_auth and sheets_get 
### were deprecated in v0.2.0
### 200225-0930, fixed a couple bugs when no DM or DM has only 1 store
### 200203-1700, added sheets_cells read to get gsht formatting for columns
### 200110-1345, revised known columns for changes next week
### 200107-0900, added warning notification of unknown columns


# KNOWN COLUMN UPDATE PROCEDURE
# Search for gSht_knownCols and add column header
# go to the 'create_excel_files' function and add Excel number format
# to a new style (if needed) and add to an applystyle line
# NOTE: if a column header has '%' and should be formatted as '0.0%' then NO action needed


testing_emails <- FALSE  #NORMAL, next line over-rides & should be disabled in PRODUCTION instance
#testing_emails <- TRUE
# more TESTING parameters/paths below!

# Needed Dates
query.date <- format(as.Date(cut(Sys.Date(), "week", start.on.monday = FALSE)), "%d-%b-%y")
# next lines are test lines that replaces line above for test purposes only
#query.date <- format(as.Date("17-NOV-19","%d-%b-%y"),"%d-%b-%y")

myReportName <- "Marcos Weekly"

# insert a special note below the header in the message body of ALL EMAILS if text is present below AND
# the emailnote.date is set to the MONDAY of the report week being sent; '<span style="color: #0000ff;">' in the HTML is for BLUE text for the note
emailnote_store.date <- as.Date("19-DEC-22", "%d-%b-%y")
emailnote_store <- paste0(
  '<p><span style="color: #0000ff;">',
  "This is a re-send and replaces the one from this morning. OT has been corrected in this version. Sorry for any confusion!",
  "</span></p>"
)
if(emailnote_store.date == as.Date(query.date, "%d-%b-%y") + 1){
  attachemailnote_store <- TRUE
}else{
  attachemailnote_store <- FALSE
}



# insert a special note below the header in the message body of ALL EMAILS if text is present below AND
# the emailnote.date is set to the MONDAY of the report week being sent; '<span style="color: #0000ff;">' in the HTML is for BLUE text for the note
emailnote_DM.date <- as.Date("20-JUN-22", "%d-%b-%y")
emailnote_DM <- paste0(
  '<p><span style="color: #0000ff;">',
  "This is a re-send and replaces the one from this morning. OT has been corrected in this version. Sorry for any confusion!",
  "</span></p>"
)
emailnote_DM.date <- as.Date("19-DEC-22", "%d-%b-%y")
if(emailnote_DM.date == as.Date(query.date, "%d-%b-%y") + 1){
  attachemailnote_DM <- TRUE
}else{
  attachemailnote_DM <- FALSE
}



report.startdate <- query.date %>% as.Date("%d-%b-%y") %>% format("%Y%m%d")  # YYYYMMDD format of date for saves of report files
report.time <- format(Sys.time(), "%H%M%S")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
plot.startdate <- query.date %>% as.Date("%d-%b-%y") %>% format("%m/%d/%Y")
# standardize GSheet WE date to most recent Sunday as m/d/yy
gSht_weSN <- paste0("WE ",
                    month(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE))),"/",
                    day(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE))),"/",
                    format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)), "%y")
                    )

logname <- "MyMarcosWeekly-Log.csv"

rptfolder <- "Marcos_Weekly_To_Field"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder)
rptpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder)
plotpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder,"Plots")
RM_DMpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder,"RM-DM Files")
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
`%notin%` <- Negate(`%in%`)
if(this_computer %notin% prod_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  #mainpath <- centralPath
}else{
  testing_pc <- FALSE
  #mainpath <- tableauPath
}

#if(this_computer == "STEVEO-PLEX7010" || Sys.getenv("COMPUTERNAME") == "LAPTOPTOSHIBA13"){
#  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
#}else{testing_pc <- FALSE}
if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
  rptpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
  plotpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder,"Plots")
  RM_DMpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder,"RM-DM Files")
  
}




RM_DMxlfn <- "Combined Weekly-"  #RM or DM name and type is appended to this

rptpath_central <- file.path("//*************","public","steveo","monday_reports")

MPWkTFRptFN <- paste0("Marcos_Weekly_To_Field_", report.startdate, ".csv")

orig_wd <- getwd()
gsheet.tokenpath  <- file.path("C:","Users","table",".R","gargle","gargle-oauth")
gSht_Key <- '1iNA_sdIJ6etKbC40fqG-DHPOs7-rvdHhesuT7xFjBqQ'
#gSht_StColName <- "Store #"
#20240916:
gSht_StColName <- "STORE_NUMBER"
#gSht_MgrColName <- "Manager"
#20240916:
gSht_MgrColName <- "MGR"
gSht_DMColName <- "DM"
gSht_RMColName <- "RD"

###Snowflake Connection###

#Sf_environ <- "DEV"
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"

if(Sf_environ == "STAGE"){
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  sF_auth <- ""
}else{
  ###Default to PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  sF_auth <- ""
}

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = sF_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="UTC") # to match Google Sheets TZ
#Sys.setenv(TZ='America/Chicago')
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
#myquery <- "ALTER SESSION SET TIMEZONE = 'UTC'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)






# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
test_warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"

HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")

#norm_st_cc <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>","<EMAIL>")

okaytocontinue <- TRUE


### define some functions ###

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

writelog <- function(LogTable){
  fn <- file.path(logpath, logname)
  write.csv(LogTable, file = fn, row.names=FALSE)
}

file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}


###-------------------###
###Get email signature###
###-------------------###
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HRG Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}

day_ord <- function(dates){
  dayy <- day(dates)
  suff <- case_when(dayy %in% c(11,12,13) ~ "th",
                    dayy %% 10 == 1 ~ 'st',
                    dayy %% 10 == 2 ~ 'nd',
                    dayy %% 10 == 3 ~'rd',
                    TRUE ~ "th")
  return(paste0(dayy, suff))
}

st.emailaddy <- function(STORE_NUMBER){
  # Return the store email address based on current naming convention, default to warning email address if not found
  #print(paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"))
  addy <- case_when(
    STORE_NUMBER %in% seq(2, 888) ~ paste0("fv", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    STORE_NUMBER %in% seq(3500, 3999) ~ paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    #(STORE_NUMBER >= 3500 & STORE_NUMBER <= 3999) ~ paste0("mp", str_pad(STORE_NUMBER, 4, pad = 0), "@fvmc.com"),
    STORE_NUMBER %in% seq(6500, 6999) ~ paste0("sf", str_pad(STORE_NUMBER, 4, pad = 0), "@stayfit24.com")
  )
  if(is.na(addy)){addy <- warn_recip}
  return(addy)
}

#myTest <- get_RM_DM_names("DM")
#debugonce(get_RM_DM_names)
get_RM_DM_names <- function(RMorDM){
  # this function requires gSht_Stores
  # dataframe is present
  # returns a df 
  my.colname <- case_when(
    RMorDM == "DM" ~ gSht_DMColName,
    TRUE ~ gSht_RMColName
  )
  my.data <- subset(gSht_Stores[,my.colname], !is.na(gSht_Stores[,my.colname]))
  #remove any potential blanks
  summ_names <- unique(my.data)
  if(RMorDM == "DM"){
    # do all stores have DM? If not, create individual stores as well
    my.data <- gSht_Stores[!complete.cases(gSht_Stores[,c(gSht_StColName, my.colname)]), gSht_StColName]
    if(nrow(my.data) >= 1){
      names(my.data)[names(my.data) == gSht_StColName] <- my.colname
      summ_names <- rbind(summ_names, my.data)
    }
  }
  if(RMorDM == "RM"){
    # do all stores have RM? If not, add DM to list
    my.data <- gSht_Stores[!complete.cases(gSht_Stores[,c(gSht_StColName, my.colname)]), gSht_DMColName]
    #remove any rows without DM name
    my.data <- na.omit(my.data)
    if(nrow(my.data) >= 1){
      names(my.data)[names(my.data) == gSht_DMColName] <- my.colname
      summ_names <- rbind(summ_names, my.data)
    }
  }
  return(summ_names)
}

###Create Combined Weekly Excel Files Function###
#debugonce(create_excel_files)
#create_excel_files(RMorDM = "DM", MyPath = RM_DMpath)
create_excel_files <- function(RMorDM, MyPath){
  #debugonce(get_RM_DM_names)
  summ_names <- get_RM_DM_names(RMorDM = RMorDM)
  my.colname <- case_when(
    toupper(RMorDM) == "DM" ~ gSht_DMColName,
    toupper(RMorDM) == "RM" ~ gSht_RMColName
  )
  #subset each RMorDM and create Excel file
  if(nrow(summ_names)>0){
    for(name_idx in 1:nrow(summ_names)){
      #currname <- toString(summ_names[name_idx, my.colname])
      currname <- toString(summ_names[name_idx, 1])
      #if(is.numeric(currname)){
      if(length(grep("^[[:digit:]]*$", currname)) == 1){
        is_store <- TRUE
        my.data <- gSht_Stores[which(gSht_Stores[,gSht_StColName] == currname),]
        this_fn <- paste0(RM_DMxlfn, report.startdate, "-", "Store ", currname, ".xlsx")
        #paste0(report.startdate, "-", currname)
      }else{
        is_store <- FALSE
        my.data <- gSht_Stores[which(gSht_Stores[,my.colname] == currname),]
        this_fn <- paste0(RM_DMxlfn, report.startdate, "-", RMorDM, " ", str_to_upper(currname), ".xlsx")
        if(toupper(RMorDM) == "RM"){
          is_rm <- TRUE
          my.colname <- gSht_RMColName
          #test if name is DM (no RM assigned, create DM file if not present) (ADDED 200106 1600)
          if(nrow(gSht_Stores[which(gSht_Stores[,gSht_DMColName] == currname), gSht_DMColName])>0){
            #switch info from RM to DM
            is_rm <- FALSE
            my.colname <- gSht_DMColName
            my.data <- gSht_Stores[which(gSht_Stores[,my.colname] == currname),]
            this_fn <- paste0(RM_DMxlfn, report.startdate, "-", "DM", " ", str_to_upper(currname), ".xlsx")
            
          }
        }
        
      }
      #create Excel file for this summary
      
      #define styles to apply to workbook
      
      ##Custom Header Style, Bold, centered with gold fill with black font, rotate text 90 degrees
      styleHdrRow <- createStyle(fontColour = "#000000", fgFill = "#FFCC00",
                                 halign = "center", valign = "center",
                                 numFmt = "GENERAL", textDecoration = "bold",
                                 textRotation = 90, wrapText = TRUE)
      #Rows, light gold fill
      styleGoldRow <- createStyle(fontColour = "#000000", fgFill = "#FFF2CC")
      
      # Create a custom style for Names
      styleNames <- createStyle(numFmt = "GENERAL",
                                halign = "left", 
                                wrapText = TRUE)
      
      # Create a custom style for Thousands (numbers)
      styleNumThousands <- createStyle(numFmt = "#,##0",
                                       halign = "right")
      
      # Create a custom style for Thousands (numbers) and centered
      styleNumThousandsCenter <- createStyle(numFmt = "#,##0",
                                       halign = "center")
      
      # Create a custom style for Percentages (1 decimal)
      styleNumPct1 <- createStyle(numFmt = "0.0%",
                                  halign = "right")
      
      # Create a custom style for Dollars ($ and 2 decimal)
      styleNumDollars2 <- createStyle(numFmt = "$#,##0.00_);[Red]($#,##0.00)",
                                      halign = "right")
      
      
      # Create a custom style for DATE and Time column (centered)
      styleDateAndTime <- createStyle(numFmt = "m/d/yy h:mm",
                                      halign = "center")
      
      # Create a custom style for DATE only column (centered)
      styleDate <- createStyle(numFmt = "m/d/yyyy",
                                      halign = "center")
      
      # Create a custom style for Time only column (centered)
      styleTime <- createStyle(numFmt = "h:mm:ss;@",
                                      halign = "center")
      
      # Create a custom style for dollar columns (2 decimals, centered)
      styleDollars <- createStyle(numFmt = "#,##0.00",
                                  halign = "center")
      
      # Create a custom style for Store#, PC, qty columns (centered)
      styleCenter <- createStyle(numFmt = "GENERAL",
                                 halign = "center")
      
      ###################
      # CREATE WORKBOOK #
      ###################
      wb <- createWorkbook(creator = "SteveO",
                           title = "Combined Weekly")
      
      modifyBaseFont(wb, fontSize = 10, fontColour = "black", fontName = "Arial")
      
      # sheet name (max length = 31)
      mySN <- substr(paste0(report.startdate, "-", currname),1,31)
      
      addWorksheet(wb, mySN, 
                   gridLines = FALSE,
                   paperSize = getOption("openxlsx.paperSize", default = 1),
                   orientation = getOption("openxlsx.orientation", default = "landscape"))
      
      #page setup, print top row and first column in case of physical print
      pageSetup(wb, sheet = mySN, 
                left = 0.4, right = 0.4, top = 0.4, bottom = 0.4,
                printTitleCols = 1, printTitleRows = 1)
      
      # lock in header row (firstRow = TRUE) and first two columns (firstActiveCol = 3)
      freezePane(wb, sheet = mySN, firstActiveRow = 2, firstActiveCol = 3)
      
      
      # resize and hide Sale Qty column
      #mycols <- which(names(my.data)%in%c("SAL TOT"))
      #setColWidths(wb, sheet = mySN,
      #             cols = mycols,
      #             widths = 4,
      #             hidden = rep(TRUE, length(11)))
      
      #write data, include gray borders
      writeData(wb, sheet = mySN, my.data, startCol = 1, startRow = 1,
                headerStyle = styleHdrRow, withFilter = TRUE, borders = "all",
                borderColour = getOption("openxlsx.borderColour", "#808080"))
      
      
      rowIndex = seq(length = nrow(my.data)) + 1
      lastrow <- nrow(my.data) + 1
      rowEvenIndex = seq(2, lastrow, 2)
      if(lastrow > 2){rowOddIndex = seq(3, lastrow, 2)}
      #rowOddIndex = seq(3, lastrow, 2)
      colIndex = seq(length = ncol(my.data))
      
      myColNum <- ncol(my.data)
      #set general column width (any special widths should occur after this setting)
      setColWidths(wb, sheet = mySN,
                   cols = c(1:myColNum),
                   widths = rep(7.75,myColNum))
      
      #resize Store# col width
      mycols <- which(names(my.data) %in% c(gSht_StColName))
      setColWidths(wb, sheet = mySN,
                   cols = mycols,
                   widths = rep(4.75,length(mycols)))
      #resize City col width
      mycols <- which(names(my.data) %in% c("CITY","LOC_NAME"))
      setColWidths(wb, sheet = mySN,
                   cols = mycols,
                   widths = rep(16.5,length(mycols)))
      #resize Name col widths
      mycols <- which(names(my.data) %in% c(gSht_RMColName,gSht_DMColName,gSht_MgrColName))
      setColWidths(wb, sheet = mySN,
                   cols = mycols,
                   widths = rep(12,length(mycols)))
      #resize Rank col widths and hide?
      mycols <- which(names(my.data) %in% c(gSht_rankCols))
      setColWidths(wb, sheet = mySN,
                   cols = mycols,
                   widths = rep(7.75,length(mycols)),
                   hidden = rep(TRUE, length(mycols))
                   )
      #resize Tier col width
      mycols <- which(names(my.data) %in% c("Final Tier"))
      setColWidths(wb, sheet = mySN,
                   cols = mycols,
                   widths = rep(6.5,length(mycols)))
      #resize MOMs Install Week col width
      mycols <- which(names(my.data) %in% c("MOMs Installation Week"))
      setColWidths(wb, sheet = mySN,
                   cols = mycols,
                   widths = rep(10.5,length(mycols)))
      #widths = c(4.75,17,6.5,15,10.25,10.25,10.25,rep(4.75,length(mycols))))
      
      #set row heights
      setRowHeights(wb, sheet = mySN, rows = rowIndex, heights = 30)
      # set header row height
      setRowHeights(wb, sheet = mySN, rows = 1, heights = 57)
      
      ##################
      # FORMAT COLUMNS #
      ##################
      #added 200203-1700
      #build default numFmt styles using gsht_colfmts and createStyle(numFmt = "")
      #column formats after this generic section will over-ride where columns are
      #expressly formatted
      mycols <- gsht_colfmts$ColNum %>% unique()
      if(length(mycols)>0){
        for(fmtcol in mycols){
          tmpStyle <- createStyle(numFmt = gsht_colfmts$Format[min(which(gsht_colfmts$ColNum == fmtcol))])
          addStyle(wb, sheet = mySN, tmpStyle, rows = rowIndex, cols = fmtcol,
                   gridExpand = TRUE, stack = TRUE )
        }
      }
      
      # format St# column
      mycols <- which(names(my.data) %in% c(gSht_StColName,"Pricing Tier (4/8/24)"))
      addStyle(wb, sheet = mySN, styleCenter, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      # format Name columns
      mycols <- which(names(my.data) %in% c(gSht_RMColName,gSht_DMColName,gSht_MgrColName))
      addStyle(wb, sheet = mySN, styleNames, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      # format Number with Thousands column
      mycols <- which(
        names(my.data) %in% c(
          "Net Sales","4 Wk Avg (NS)","Total Order","LY Net Sales",
          "WAUS","8wk WAUS","8 Wk WAUS","Worked Hours","Ideal Hours","LY WAUS",
          "Ideal Hour Variance","Avg. Ideal Hour Variance", "Var Total Hours vs. Ideal",
          "# of Voids", "# of Voids (Combined)","Combined Avg Worked Hours"
        )
      )
      addStyle(wb, sheet = mySN, styleNumThousands, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      # format Percent (1 decimal) columns
      mycols <- which(grepl('%', colnames(my.data)))
      addStyle(wb, sheet = mySN, styleNumPct1, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      # format Dollar ($, 2 decimal) columns
      mycols <- which(names(my.data) %in% c("Avg Check","SPLH","Avg Hourly Wage","Min Wage","Avg Ticket"))
      addStyle(wb, sheet = mySN, styleNumDollars2, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      # format Delivery Time (hh:mm:ss) columns
      mycols <- which(names(my.data) %in% c("Average Delivery Time","Avg Call Wait Time"))
      addStyle(wb, sheet = mySN, styleTime, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      # format rank columns
      mycols <- which(names(my.data) %in% c(gSht_rankCols))
      addStyle(wb, sheet = mySN, styleNumThousandsCenter, rows = rowIndex, cols = mycols,
               gridExpand = TRUE, stack = TRUE)
      
      ###############
      # FORMAT ROWS #
      ###############
      # format header row
      #addStyle(wb, sheet = mySN, styleHdrRow, rows = 1, cols = colIndex,
      #         gridExpand = TRUE, stack = TRUE)
      
      # format odd rows (banded table...light gold)
      if(lastrow > 2){
        addStyle(wb, sheet = mySN, styleGoldRow, rows = rowOddIndex, cols = colIndex,
                 gridExpand = TRUE, stack = TRUE)
      }
      
      #addWorksheet(wb, "Rates", 
      #             gridLines = TRUE,
      #             paperSize = getOption("openxlsx.paperSize", default = 1),
      #             orientation = getOption("openxlsx.orientation", default = "portrait"))
      
      
      # save Excel file
      fn = file.path(MyPath, this_fn)
      openxlsx::saveWorkbook(wb, file = fn, overwrite = TRUE)
      
    }
  }
  #RM_DMpath
} # End of create Excel files function


'%notin%' <- Negate('%in%')


###GET STARTED###

### check if log present/up-to-date ###
NewErrorLog <- FALSE
if(file.exists(file.path(logpath, logname)) ) {
  MyErrorLog <- read.csv(file = file.path(logpath, logname), sep=",", stringsAsFactors = FALSE)
  #check if log is from prior week, is so replace values with default starting values
  if( MyErrorLog[1,"QUERY_DATE"] != query.date ){
    #log is from previous week, replace with new default values
    NewErrorLog <- TRUE
  }
}else{
  # log not found, create new log values
  NewErrorLog <- TRUE
}
# create new log if needed
if( NewErrorLog ) {
  MyErrorLog <- data.frame(QUERY_DATE = query.date, 
                           QUERY_STATUS = 'NO LOG FILE',
                           GS_STATUS = 'NO LOG FILE',
                           SUMMARY_STATUS = 'NO LOG FILE',
                           ST_EMAIL_STATUS = 'NO LOG FILE',
                           DM_EMAIL_STATUS = 'NO LOG FILE',
                           RM_EMAIL_STATUS = 'NO LOG FILE',
                           PROGRESS = 'NO LOG FILE',
                           stringsAsFactors = FALSE)
}
writelog(MyErrorLog)


#check if previous mailing of STORES failed part way through and set parameters to pick-up from where it left off
store.mail_from <- 1
store.mail_send <- TRUE
#### disable store sends with next line#####
#store.mail_send <- FALSE
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"ST_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed store that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    store.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"ST_EMAIL_STATUS"] == 'COMPLETE' ) {
    store.mail_send <- FALSE
  }
}


#check if previous mailing of DMS failed part way through and set parameters to pick-up from where it left off
dm.mail_from <- 1
dm.mail_send <- TRUE
### disable DM sends with next line ###
#dm.mail_send <- FALSE
# stop status check
if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"DM_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
  # extract last completed DM that was emailed
  laststatus <- MyErrorLog[1,"PROGRESS"]
  if(regexpr(" of ", laststatus ) > 0 ) { 
    dm.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
  }
} else {
  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"DM_EMAIL_STATUS"] == 'COMPLETE' ) {
    dm.mail_send <- FALSE
  }
}


#check if previous mailing of RMS failed part way through and set parameters to pick-up from where it left off
rm.mail_from <- 1
rm.mail_send <- TRUE

### disable RM sends with next line, RMs will NOT get store emails ###
rm.mail_send <- FALSE
# stop status check
#if( MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"INVEN_STATUS"] == 'READY' & MyErrorLog[1,"RM_EMAIL_STATUS"] == 'EMAILING STARTED' ) {
#   extract last completed DM that was emailed
#  laststatus <- MyErrorLog[1,"PROGRESS"]
#  if(regexpr(" of ", laststatus ) > 0 ) { 
#    rm.mail_from <- (1 + as.integer(substr(laststatus, 1, regexpr(" of ", laststatus) - 1 )))
#  }
#} else {
#  if(MyErrorLog[1,"QUERY_DATE"] == query.date & MyErrorLog[1,"INVEN_STATUS"] == 'READY' & MyErrorLog[1,"RM_EMAIL_STATUS"] == 'COMPLETE' ) {
#    rm.mail_send <- FALSE
#  }
#}


### Test if routine completed in early run attempt ###
if(MyErrorLog[1,"PROGRESS"] == "COMPLETE"){okaytocontinue <- FALSE}


# query store list info
if(okaytocontinue){
  #update log
  MyErrorLog[1,"PROGRESS"] <- "QUERY STATUS"
  MyErrorLog[1,"QUERY_STATUS"] <- "STARTING ST LIST"
  writelog(MyErrorLog)
  myquery <- paste0(
    "select
     ab_store.st as STORE,
     ab_store.CITY as CITY,
     (case when Mgr.fname is not null
     then initcap(Mgr.fname)
     else 'No' end) as MANAGER_FNAME,
     (case when Mgr.lname is not null
     then initcap(Mgr.lname)
     else 'Mgr' end) as MANAGER_LNAME,
     Mgr.EMAIL as MANAGER_EMAIL,
     (case when district.fname is not null
     then initcap(district.fname)
     else 'No' end) as DM_FNAME,
     (case when district.lname is not null
     then initcap(district.lname)
     else 'DM' end) as DM_LNAME,
     (case when district.fname is not null
     then initcap(district.fname)||' '||initcap(district.lname)
     else 'No DM' end) as DM_FULLNAME,
     district.EMAIL AS DM_EMAIL,
     (case when regional.fname is not null
     then initcap(regional.fname)
     else 'No' end) as RM_FNAME,
     (case when regional.lname is not null
     then initcap(regional.lname)
     else 'RM' end) as RM_LNAME,
     regional.EMAIL as RM_EMAIL,
     ab_store.o_date as OPEN_DATE,
     (case 
        when UPPER(hrloc.LOC_INFORMATION15) LIKE 'FVMC%'
          then 'fv0'||lpad(ab_store.st,3,0)||'@fvmc.com'
        when UPPER(hrloc.LOC_INFORMATION15) LIKE 'VET%'
          then 'vt'||lpad(ab_store.st,4,0)||'@familyvetgroup.com'
        when UPPER(hrloc.LOC_INFORMATION15) LIKE 'HF%'
          then 'mp'||lpad(ab_store.st,4,0)||'@fvmc.com'
        when UPPER(hrloc.LOC_INFORMATION15) LIKE 'STFIT%'
          then 'sf'||lpad(ab_store.st,4,0)||'@stayfit24.com'
        end
     ) as STORE_EMAIL
     from ab_store
     left join
     (select ab_employees.paynum,
     ab_employees.fname,
     ab_employees.lname,
     ab_store_employees.store,
     ab_employees.EMAIL
     from ab_store_employees
     inner join ab_employees
     on ab_store_employees.paynum = ab_employees.paynum
     inner join famv_payroll_position pos
     on ab_employees.position = pos.position
     and sub_categories like '%,DIST_RPT_INC,%'
     where status = 'A') district
     on ab_store.st = district.store
     left join
     (select ab_employees.paynum,
     ab_employees.fname,
     ab_employees.lname,
     ab_store_employees.store,
     ab_employees.EMAIL
     from ab_store_employees
     inner join ab_employees
     on ab_store_employees.paynum = ab_employees.paynum
     inner join famv_payroll_position pos
     on ab_employees.position = pos.position
     and sub_categories like '%,REG_RPT_INC,%'
     where status = 'A') regional
     on ab_store.st = regional.store
     left join
     (select ab_employees.paynum,
     ab_employees.fname,
     ab_employees.lname,
     ab_store_employees.store,
     ab_employees.EMAIL
     from ab_store_employees
     inner join ab_employees
     on ab_store_employees.paynum = ab_employees.paynum
     inner join famv_payroll_position pos
     on ab_employees.position = pos.position
     and sub_categories like '%,MGR_HISTORY,%'
     where status = 'A') Mgr
     on ab_store.st = Mgr.store
     left join hr_locations_all hrloc
     on lpad(ab_store.st,4,0) = hrloc.location_code
     where ab_store.st >= 2
     and ab_store.st < 7000
     order by ab_store.st"
  )
  #20240930: stlist <- dbGetQuery(myOracleDB, myquery)
  stlist <- dbGetQuery(mySfDB, myquery)
  
  
  if(nrow(stlist) > 1){
    MyErrorLog[1,"PROGRESS"] <- "QUERY STATUS"
    MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    writelog(MyErrorLog)
  }else{
    # ERROR, LOG AND STOP
    MyErrorLog[1,"PROGRESS"] <- "QUERY STATUS"
    MyErrorLog[1,"QUERY_STATUS"] <- "FAILED"
    writelog(MyErrorLog)
    okaytocontinue <- FALSE
  }
}


# get Google Sheet data and verify
######try googlesheets4 to import data###
if(okaytocontinue){
  MyErrorLog[1,"PROGRESS"] <- "GS STATUS"
  MyErrorLog[1,"GS_STATUS"] <- "LOADING GOOGLE SHEET"
  writelog(MyErrorLog)
  # old googlesheets methods commented out for reference
  #load OAuth token for google sheet access
  gs4_auth(email = "<EMAIL>")
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive first, then use the same token
  #in googlesheets4 something like this:
  #drive_auth()
  #sheets_auth(token = drive_token())
  
  gSht_WkComb <- gs4_get(gSht_Key)
  #COMBINED WEEKLY URL: https://docs.google.com/spreadsheets/d/1iNA_sdIJ6etKbC40fqG-DHPOs7-rvdHhesuT7xFjBqQ/

  if(is.na(match(gSht_weSN, gSht_WkComb$sheets$name))){
    # desired sheet NOT PRESENT yet
    # update log and quit
    MyErrorLog[1,"GS_STATUS"] <- "WEEK NOT FOUND"
    writelog(MyErrorLog)
    okaytocontinue <- FALSE
    
  }else{
    # continue to load Weekly results
    gSht_Master <- read_sheet(gSht_Key, sheet = gSht_weSN)
    
    # new df without Grand Total and Summary rows at bottom
    #gSht_GTRow <- max(0,which(gSht_Master[,gSht_StColName] == "Grand Total"))
    #loop below replaces line above to eliminate dependence on knowing which column contains the first instance of "Grand Total"
    gSht_GTRow <- 0
    for(x in 1:ncol(gSht_Master)){
      test_row <- max(0,which(gSht_Master[,x] == "Grand Total"))
      gSht_GTRow <- max(test_row, gSht_GTRow)
    }
    if(gSht_GTRow == 0){gSht_GTRow <- nrow(gSht_Master)}
    gSht_Stores <- read_sheet(gSht_Key, sheet = gSht_weSN, n_max = (gSht_GTRow - 1))
    #update log (don't write)
    MyErrorLog[1,"PROGRESS"] <- "GS STATUS"
    #writelog(MyErrorLog)
  }
  if(nrow(gSht_Stores)>0){
    #okay, continue
    #get column formats
    gSht_numCols <- ncol(gSht_Stores)
    #gSht_colmeta <- sheets_cells(gSht_Key, sheet = gSht_weSN, skip = 1, n_max = (gSht_GTRow - 2))
    #20200903 line above replaced by line below due to sheets_cells being deprecated in googlesheets4 0.2.0
    gSht_colmeta <- range_read_cells(gSht_Key, sheet = gSht_weSN, skip = 1, n_max = (gSht_GTRow - 2))
    gsht_colfmts <- data.frame(ColNum=integer(),
                               Type=character(),
                               Format=character(),
                               stringsAsFactors = FALSE)
    for(i in 1:nrow(gSht_colmeta)){
      gsht_colfmts[i,"ColNum"] <- as.integer(gSht_colmeta$col[i])
      gsht_colfmts[i,"Type"] <- if(is.null(gSht_colmeta$cell[[i]]$effectiveFormat$numberFormat$type)){
        "GENERAL"
      }else{
        gSht_colmeta$cell[[i]]$effectiveFormat$numberFormat$type
      }
      
      gsht_colfmts[i,"Format"] <- if(is.null(gSht_colmeta$cell[[i]]$effectiveFormat$numberFormat$pattern)){
        "GENERAL"
      }else{
        #gsub('"','',gSht_colmeta$cell[[i]]$effectiveFormat$numberFormat$pattern)
        gSht_colmeta$cell[[i]]$effectiveFormat$numberFormat$pattern
      }
    }
    gsht_colfmts <- unique(gsht_colfmts) %>% arrange(., ColNum)
    
    MyErrorLog[1,"GS_STATUS"] <- "LOADED"
  }else{
    #Week found but no rows, abort
    MyErrorLog[1,"GS_STATUS"] <- "No Data or Error"
    writelog(MyErrorLog)
    #email warning
    # create body of warning email
    bodytext <- paste0("This is an automated email to inform you that it appears that there weren't ",
                       "any rows found in the <b>Combined Weekly Google Sheet</b> (", gSht_weSN, "), ",
                       "the sheet was present but some other error occured.  Check the sheet for ",
                       "store rows and that there is a 'Grand Total' value at the bottom of the ",
                       "'", gSht_StColName, "' column.  If those look good, check R code in the ",
                       orig_wd, " directory of the Tableau Desktop.<br/><br/>",
                       print(xtable(MyErrorLog, 
                                    caption = paste0(logname, " (", report.time.txt, ")")),
                             align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                             html.table.attributes = "border=2 cellspacing=1",
                             type = "html",
                             caption.placement = "top",
                             include.rownames=FALSE),
                       "<br/>The routine will most likely attempt to run again in about 30 minutes.<br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    if(testing_emails){warn_recip <- test_warn_recip}
    mailsend(warn_recip,
             "Marco's Combined Weekly Issue: GOOGLE SHEET DATA",
             bodytext
    )
  }
  
  # check for new column header and report if any are found
  if(MyErrorLog[1,"GS_STATUS"] == "LOADED"){
    # find % columns, combined with others in next line
    pctcols <- colnames(gSht_Stores)[which(grepl('%', colnames(gSht_Stores)))]
    #todo: need to except gSht_rankCols from pctcols
    gSht_rankCols <- c("WAUS Rank","8wk Avg Sales Rank","SSS % Rank",
                       "Coup % Rank","Non Coup % Rank","Avg Check Rank","SSO % Rank",
                       "# of Voids Rank","Cash Over/Short Rank","Cash Over/Short Rank (ABS)","Delivery <30 % Rank",
                       "Delivery>40 % Rank","ADT Rank","DPR Rank","ACWT Rank",
                       "Hang Up Calls Rank","Hang Up % Rank","Del Order % Rank",
                       "Total Online % Rank","Labor % Rank","Ideal Hour Var Rank",
                       "OT Rank","SPLH Rank","Food Var Rank", "ALT Rank", "PPLH Rank"
    )
    #remove any gSht_rankCols from pctcols as they need to be formatted differently
    pctcols <- pctcols[which(pctcols %notin% gSht_rankCols)]
    gSht_knownCols <- c("CITY","Final Tier","Avg Check","SPLH",
                        "Avg Hourly Wage","Min Wage","Net Sales",
                        "4 Wk Avg (NS)","Total Order","LY Net Sales",
                        "WAUS","8wk WAUS","8 Wk WAUS","Worked Hours","Ideal Hours",
                        "LY WAUS","Average Delivery Time","Hang Up Calls","Hang Up Calls (Net)",
                        "Avg Call Wait Time","Ideal Hour Variance","Avg. Ideal Hour Variance",
                        "Deliverys per Run","Deliveries per Run",
                        "Net Hires","# of Voids",
                        "Cash Over/Short", "OT (CW)",
                        "# of Voids (Combined)","Combined Avg Worked Hours",
                        "MOMs Installation Week","Avg. Average Delivery Time",
                        "Worked Hours Combined","Avg. # of Voids",
                        "Average Load Time","PPLH", "ALT Rank", "PPLH Rank",
                        "Pricing Tier (4/8/24)", "Pricing Tier (7/8/24)",
                        "STORE_NUMBER", "LOC_NAME", "MGR",
                        "PRICING TIER", "Avg Ticket", "Hangup Calls",
                        "Total Worked Hours", "Var Total Hours vs. Ideal",
                        "Total OT", "Cash Over/Short Rank (ABS)",
                        pctcols,gSht_StColName,gSht_RMColName,
                        gSht_DMColName,gSht_MgrColName,
                        gSht_rankCols)
    gSht_assignmentCols <- c(gSht_StColName,gSht_RMColName,gSht_DMColName)
    gSht_UNknownCols <- setdiff(colnames(gSht_Stores), gSht_knownCols)
    gSht_UNknownColsNum <- length(gSht_UNknownCols)
    #test number of unknown columns in google sheet
    if(gSht_UNknownColsNum > 0){
      #if Monday log error and stop...if Tuesday continue anyway
      MyErrorLog[1,"GS_STATUS"] <- "UNKNOWN COL HDRS"
      writelog(MyErrorLog)
      
      if(format(Sys.Date(),"%a") == "Tue" | lubridate::hour(Sys.time()) > 15 ){
        bodytext <- paste0("This is an automated email to inform you that it appears that there may ",
                           "be new columns in the <b>Combined Weekly Google Sheet</b> (", gSht_weSN, "). ",
                           "The columns may need special formatting in the Excel files being created. <br><br>",
                           "The ", gSht_UNknownColsNum, " unknown column(s) are: <br>",
                           paste(gSht_UNknownCols, collapse = "; "), "<br><br>",
                           "<b>The Excel files are being created and sent as-is.</b>  ",
                           "Review the formatting of these columns and add them to the 'gSht_knownCols' ",
                           "parameter and the 'create_excel_files' function in the ",
                           "Marcos_Weekly_To_Field.R script that is located in the ",
                           orig_wd, " directory of the Tableau/R desktop.<br/><br/>",
                           warn_sig,
                           sep = ""
        )
        #send mail
        if(testing_emails){warn_recip <- test_warn_recip}
        mailsend(warn_recip,
                 "Marco's Combined Weekly Issue: GOOGLE SHEET COLUMN HEADERS",
                 bodytext
        )
      }else{
        #Not Tuesday or before 3pm, send warning email about new columns and ABORT
        # create body of warning email
        bodytext <- paste0("This is an automated email to inform you that it appears that there may ",
                           "be new columns in the <b>Combined Weekly Google Sheet</b> (", gSht_weSN, "). ",
                           "The columns may need special formatting in the Excel files being created. <br><br>",
                           "The ", gSht_UNknownColsNum, " unknown column(s) are: <br>",
                           paste(gSht_UNknownCols, collapse = "; "), "<br><br>",
                           "Review the formatting of these columns and add them to the 'gSht_knownCols' ",
                           "parameter in the Marcos_Weekly_To_Field.R script that is located in the ",
                           orig_wd, " directory of the Tableau/R desktop.<br/><br/>",
                           "The routine will most likely attempt to run again in about 30 minutes. If ",
                           "the columns are not added to the script by 3 p.m., the routine will send out ", 
                           "the Excel files with default formatting of these columns (number formats ",
                           "should be okay, other column formats may not be as desired).<br/><br/>",
                           warn_sig,
                           sep = ""
        )
        #send mail
        if(testing_emails){warn_recip <- test_warn_recip}
        mailsend(warn_recip,
                 "Marco's Combined Weekly Issue: GOOGLE SHEET HEADERS",
                 bodytext
        )
      }
    }
    
    #gSht_assignmentNAcounts <- apply(gSht_Stores[,c("RD","DM","Store #")], MARGIN = 1, function(x) sum(is.na(x)))
    #20240916:
    gSht_assignmentNAcounts <- apply(gSht_Stores[,c("RD","DM","STORE_NUMBER")], MARGIN = 1, function(x) sum(is.na(x)))
    gSht_assignmentIssues <- length(which(gSht_assignmentNAcounts == 3))
    if(gSht_assignmentIssues > 0){
      #abort! 'store' rows without a RM, DM or store # present...might be issue finding "Grand Total" row
      MyErrorLog[1,"GS_STATUS"] <- "UNASSIGNED ROWS (missing RD, DM AND Store#)"
      writelog(MyErrorLog)
      # create body of warning email
      bodytext <- paste0(
        "<p>This is an automated email to inform you that it appears that there may ",
        "be rows in the <b>Combined Weekly Google Sheet</b> (", gSht_weSN, ") ",
        "that are missing assignment info (missing RD, DM <b>AND</b> Store #). This ",
        "sometimes happens if the \"Grand Total\" row is not found, or ",
        " is found AFTER the end of the store rows. The routine reports ",
        nrow(gSht_Stores), " store rows which might help finding the issue.</p>",
        "<p>The routine aborted and will attempt to run again in about 30 minutes. If ",
        "the issue persists the distributions will not be sent.</p>",
        warn_sig,
        sep = ""
      )
      #send mail
      if(testing_emails){warn_recip <- test_warn_recip}
      mailsend(warn_recip,
               "Marco's Combined Weekly Issue: GOOGLE SHEET MISSING ASSIGNMENTS",
               bodytext
      )
      
    }
    if(gSht_UNknownColsNum == 0 & gSht_assignmentIssues == 0){
      #okay, continue
      MyErrorLog[1,"GS_STATUS"] <- "COMPLETE"
      writelog(MyErrorLog)
    }else{
      #data validation failed, abort
      okaytocontinue <- FALSE
    }
  }
}


# create folder to hold RM, DM summaries
# AND create Excel files for each
if(okaytocontinue & MyErrorLog[1,"SUMMARY_STATUS"] != "COMPLETE"){
  
  # clear prior run Excel files
  MyErrorLog[1,"PROGRESS"] <- "SUMMARY STATUS"
  MyErrorLog[1,"SUMMARY_STATUS"] <- "CLEAR OLD FILES"
  writelog(MyErrorLog)
  
  if(!dir.exists(RM_DMpath)) { 
    # create new folder
    dir.create(RM_DMpath)
  }else{
    # delete old summaries
    old_files <- list.files(RM_DMpath, include.dirs = FALSE, full.names = TRUE, recursive = FALSE)
    unlink(old_files)
  }
  
  MyErrorLog[1,"SUMMARY_STATUS"] <- "CREATING DM FILES"
  writelog(MyErrorLog)
  # create DM files
  create_excel_files(RMorDM = "DM", MyPath = RM_DMpath)
  
  MyErrorLog[1,"SUMMARY_STATUS"] <- "CREATING RM FILES"
  writelog(MyErrorLog)
  # create RM files
  create_excel_files(RMorDM = "RM", MyPath = RM_DMpath)
  
  MyErrorLog[1,"PROGRESS"] <- "SUMMARY STATUS"
  MyErrorLog[1,"SUMMARY_STATUS"] <- "COMPLETE"
  writelog(MyErrorLog)
}


#start sending emails
last_sent <- Sys.time()


#start emailing stores (get sent DM summary)
if(okaytocontinue & store.mail_send){
  MyErrorLog[1,"PROGRESS"] <- "ST EMAIL STATUS"
  MyErrorLog[1,"ST_EMAIL_STATUS"] <- "GETTING STORE COUNT"
  writelog(MyErrorLog)
  # mail stores by their district to reduce # emails sent
  wkly.stores <- gSht_Stores[,c(gSht_StColName,gSht_DMColName)] %>% unique()
  #append store emails to wkly.stores
  wkly.stores$StEmail <- with(stlist, MANAGER_EMAIL[match(wkly.stores[[gSht_StColName]], STORE)])
  wkly.stores$StEmail[is.na(wkly.stores$StEmail)] <- with(stlist, STORE_EMAIL[match(wkly.stores[[gSht_StColName]][is.na(wkly.stores$StEmail)], STORE)])
  
  summ_names <- get_RM_DM_names("DM")
  store.mail_to <- nrow(summ_names)
  
  for(i in store.mail_from:store.mail_to ){  # normal, disable next TEST line
  #for(i in 1:3 ){   # test, disable above NORMAL line
    
    currDMName <- as.character(summ_names[i,1])
    #check if current DM row is actually a store number
    if(length(grep("^[[:digit:]]*$", currDMName)) == 1){ 
      # store number...no DM, use store file
      Store_or_District <- "Store" 
      currFN <- file.path(RM_DMpath, 
                          paste0(RM_DMxlfn, report.startdate, "-", "Store ", currDMName, ".xlsx")
      )
      store_email <- st.emailaddy(as.integer(currDMName))
      #check if GM email addy present, if not use store email addy
      
      
      
      
    }else{
      # use DM file
      Store_or_District <- "District" 
      currFN <- file.path(RM_DMpath, 
                          paste0(RM_DMxlfn, report.startdate, "-DM", " ", str_to_upper(currDMName), ".xlsx")
      )
      store_email <- unlist(as.list(wkly.stores[which(wkly.stores[[gSht_DMColName]] == currDMName), "StEmail"]), use.names = FALSE)
    }
    
    bodytext <- paste0("<p> <strong><h1>", currDMName, " - ", Store_or_District," Combined Weekly</h1></span></strong></p>",
                       if(attachemailnote_store){emailnote_store},
                       "<p>The <b>Marco's Combined Weekly</b> for your ", Store_or_District,
                       " is attached. This Weekly is for the week ending ", query.date, ".</p><p>",
                       "Stores receive the file for all the stores in their DISTRICT.",
                       "<br/> <br/>",
                       norm_sig,
                       sep = ""
    )
    
    #remember last send time, only pad sends if less than 6 seconds since last_sent (gmail server fails when sending many emails faster than this)
    this_sent <- Sys.time()
    seconds_elapsed <- as.numeric(difftime(this_sent, last_sent, units = "secs"), units = "secs")
    wait_time <- max(0, 6 - seconds_elapsed)
    #recycle last_sent
    last_sent <- Sys.time()
    Sys.sleep(wait_time)
    
    #send store email
    if(testing_emails){
      #test send
      #store_email <- test_recip
      mailsend(recipient =  test_recip,
               subject =  paste0(currDMName, " - ", Store_or_District," Combined Weekly"),
               body =   paste0(bodytext, 
                               "<br><br>TEST SEND, normal <b>TO:</b> emails(s) would have been:<br>",
                               paste(store_email, collapse="; ")),
               attachment = currFN
      )
    }else{
      #normal send
      mailsend(recipient =  store_email,
               subject =  paste0(currDMName, " - ", Store_or_District," Combined Weekly"),
               body =   bodytext,
               attachment = currFN
      )
    }
    MyErrorLog[1,"ST_EMAIL_STATUS"] <- "EMAILING STARTED"
    MyErrorLog[1,"PROGRESS"] <- paste0(i," of ", store.mail_to)
    writelog(MyErrorLog)
    if(i %% 20 == 0) {Sys.sleep(2)}
  } #for(i in store.mail_from:dm.mail_to )
  
  #update LOG
  MyErrorLog[1,"ST_EMAIL_STATUS"] <- "COMPLETE"
  writelog(MyErrorLog)
} # end of STORE emailing


#start emailing DMs
if(okaytocontinue & dm.mail_send){
  MyErrorLog[1,"PROGRESS"] <- "DM EMAIL STATUS"
  MyErrorLog[1,"DM_EMAIL_STATUS"] <- "EMAILING STARTED"
  writelog(MyErrorLog)
  #mail DMs by region to reduce # of emails sent
  
  wkly.DMs <- gSht_Stores[which(!is.na(gSht_Stores[,gSht_DMColName])),c(gSht_DMColName,gSht_RMColName)] %>% unique()
  
  #append DM emails to wkly.DMs
  wkly.DMs$DMEmail <- with(stlist, DM_EMAIL[match(toupper(wkly.DMs[[gSht_DMColName]]), toupper(DM_FULLNAME))])
  #dm.mail_to <- nrow(wkly.DMs)
  summ_names <- get_RM_DM_names("RM")
  dm.mail_to <- nrow(summ_names)
  
  
  for(i in dm.mail_from:dm.mail_to ){  # normal, disable next TEST line
    currRMName <- as.character(summ_names[i,1])
    
    ###todo-search for DMs not covered by RM and mail them their district file
    #if(is.numeric(currRMName)){
    if(nrow(gSht_Stores[which(gSht_Stores[,gSht_DMColName] == currRMName), gSht_DMColName]) > 0){
      # no RM, use DM file
      District_or_Region <- "District" 
      currFN <- file.path(RM_DMpath, 
                          paste0(RM_DMxlfn, report.startdate, "-DM", " ", str_to_upper(currRMName), ".xlsx")
      )
      #dm_email <- st.emailaddy(as.integer(currRMName))
      dm_email <- unlist(as.list(na.omit(wkly.DMs[which(wkly.DMs[[gSht_DMColName]] == currRMName), "DMEmail"])), use.names = FALSE)
      
    }else{
      # use RM file
      District_or_Region <- "Region" 
      currFN <- file.path(RM_DMpath, 
                          paste0(RM_DMxlfn, report.startdate, "-RM", " ", str_to_upper(currRMName), ".xlsx")
      )
      #dm_email <- unlist(as.list(wkly.DMs[which(wkly.DMs[[gSht_RMColName]] == currRMName), "DMEmail"]), use.names = FALSE)
      dm_email <- unlist(as.list(na.omit(wkly.DMs[which(wkly.DMs[[gSht_RMColName]] == currRMName), "DMEmail"])), use.names = FALSE)
    }
    
    if(!is.na(dm_email)){
      bodytext <- paste0("<p> <strong><h1>", currRMName, " - ", District_or_Region," Combined Weekly</h1></span></strong></p>",
                         if(attachemailnote_DM){emailnote_DM},
                         "<p>The <b>Marco's Combined Weekly</b> for your ", District_or_Region,
                         " is attached. This Weekly is for the week ending ", query.date, ".</p><p>",
                         "DMs receive the file for all the stores in their REGION. ",
                         if(store.mail_send){
                           "Stores were sent a file with all the stores in their DISTRICT."
                         }else{""},
                         "</p>",
                         if(District_or_Region == "District"){
                           paste0("<p>The attached file contains just the stores in your District.  One or more of ",
                                  "your stores aren't assigned to a regional so you are receiving this ",
                                  "file in addition to any RM files you may or may not receive in another email.",
                                  "</p>"
                           )
                         },
                         "<br/>",
                         norm_sig,
                         sep = ""
      )
      
      #remember last send time, only pad sends if less than 6 seconds since last_sent (gmail server fails when sending many emails faster than this)
      this_sent <- Sys.time()
      seconds_elapsed <- as.numeric(difftime(this_sent, last_sent, units = "secs"), units = "secs")
      wait_time <- max(0, 6 - seconds_elapsed)
      #recycle last_sent
      last_sent <- Sys.time()
      Sys.sleep(wait_time)
      
      #send DM email
      if(testing_emails){
        #test send
        #dm_email <- test_recip
        mailsend(recipient =  test_recip,
                 subject =  paste0(currRMName, " - ", District_or_Region," Combined Weekly"),
                 body =   paste0(bodytext, 
                                 "<br><br>TEST SEND, normal <b>TO:</b> emails(s) would have been:<br>",
                                 paste(dm_email, collapse="; ")),
                 attachment = currFN
        )
      }else{
        #normal send
        mailsend(recipient =  dm_email,
                 subject =  paste0(currRMName, " - ", District_or_Region," Combined Weekly"),
                 body =   bodytext,
                 attachment = currFN
        )
      }
      MyErrorLog[1,"DM_EMAIL_STATUS"] <- "EMAILING STARTED"
      MyErrorLog[1,"PROGRESS"] <- paste0(i," of ", dm.mail_to)
      writelog(MyErrorLog)
      if(i %% 20 == 0) {Sys.sleep(2)}
    }
  } #for(i in dm.mail_from:dm.mail_to )

  
  #update LOG
  MyErrorLog[1,"DM_EMAIL_STATUS"] <- "COMPLETE"
  writelog(MyErrorLog)
  
  ###if no RM emails to send, make routine as COMPLETE now
  if(!rm.mail_send){
    MyErrorLog[1,"RM_EMAIL_STATUS"] <- "FALSE"
    MyErrorLog[1,"PROGRESS"] <- "COMPLETE"
    writelog(MyErrorLog)
  }
}
