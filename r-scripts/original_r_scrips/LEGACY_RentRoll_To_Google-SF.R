library(xtable)
library(reshape2)
library(dplyr)
library(lubridate)
library(formattable)
library(data.table)
library(gmailr) #replaces prior MailR package which used SMTP, gmailr uses OAuth and Google APIs
library(magrittr) #forward-pipe operator %>% (part of Tidyverse)
library(purrr) #used for attachments to emails
library(stringr)
library(utils)
library(DBI)
library(googledrive)
library(googlesheets4)
library(keyring)

# written by <PERSON> March 2023

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20241008

### 20241008 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### updated email signature to use latest format provided by <PERSON> earlier in 2024
### replaced check_mydata_rows() function with more universal check_mydf_rows()

### 20240219 change:
### added 'HRG Interested' (blank) column to RENT ROLL, column
### is populated by apps script

### 20240116 change:
### added 'Postal Code' column to RENT ROLL sheet

### 20231219 change:
### changed from hard coded <NAME_EMAIL> 
### Google Group for the AP_changes_recip (Rent Roll Changes)

### 20231211 change:
### added Rachael and Bobbi to the distribution of Rent Roll Changes email

### 20230613 change:
### clarified email body when # of changes is high enough to exclude them
### from the body of the email and only have them in the attachment

### 20230601 change

### 20230531 change:
### added save of csv file with key AP data. Weekly changes compared and emailed to them

### 20230518 change:
### Joined Legacy Assignments to ab_employees table on fname & lname (not ideal)

### 20230322 change:
### added a column for STATE

### 20230314 change:
### new file, based on the LEGACY_Portfolio_To_Google - 20220831.R File


### NOTE: After this routine runs, there is a Google Apps script that runs to 
### handle some column formatting: "Rent_Roll_BLDG_NumFormat"

# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
#next line is a TEST LINE for running with an alternate testing date
#query.date <- query.date <- format(as.Date("30-May-23","%d-%b-%y"),"%d-%b-%y")

inc_name_chgs <- TRUE  #TRUE will report changes to name, FALSE will not

scriptfolder <- "LEGACY_Portfolio_to_Google"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)

okaytocontinue <- TRUE

myReportName <- "LEGACY Rent Roll to Google"
gSht_id <- "1cAP2B4koxzC21dSVfwo3dy9jmZ-soaHdfzRxz2mZCQ8"
#gSht_id <- "16QUYf48WFORTzDW3ihR8C-UP0-JwAwy39QcKjBngMIY" ####20241008 TEST version "20241008 TEST Copy of Legacy_Property Mgmt PORTFOLIO" ####
mySheets <- c("RENT ROLL")

msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)

gSht_auth_email <- "<EMAIL>"


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="GMT")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)


# email parameters: recipient(s) of warning emails and signatures
AP_changes_recip <- c("<EMAIL>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")


report.date.text <- format(as.Date(query.date, "%d-%b-%y"), "%m-%d-%Y")
report.date.prior.text <- format(as.Date(query.date, "%d-%b-%y") - 7, "%m-%d-%Y")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
rentroll.date.text <- case_when(
  as.numeric(format(Sys.Date(),"%d")) > 15 ~ format(ceiling_date(Sys.Date(), "month") + days(14), "%Y%m%d"),
  TRUE ~ format(floor_date(Sys.Date(), "month") + days(14), "%Y%m%d")
)

rentroll.date.header.text <- format(as.Date(rentroll.date.text, "%Y%m%d"), "%m-%d-%Y")
date.header.text <- paste0("Updated ", report.date.text, " using Rent Roll date as ", rentroll.date.header.text)

if(wday(as.Date(query.date, "%d-%b-%y")) == 3){
  #if TUESDAY, compare to last weeks rent roll and email diffs to AP
  comp_prior <- TRUE
  dow <- format(as.Date(query.date, "%d-%b-%y"), "%A")
}else{
  #before load date/time, the routine should skip DB inserts and just email missing data (ahead of load)
  comp_prior <- FALSE
}

if(Sys.getenv("COMPUTERNAME") != "DESKTOP-TABLEAU"){
  testing_pc <- TRUE  #TESTING, changes some paths to network share instead of R/Tableau PC
  testing_pc_location <- "Office"
}else{testing_pc <- FALSE}

if(testing_pc){
    # Steve PC testing paths, replace above when testing_pc is TRUE
    logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
    HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
}


myReportPath <- file.path(logpath, rptfolder)


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


###Get email signature###
get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
  warn_sig <- norm_sig
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



# check google sheet status
if(okaytocontinue){
  gs4_auth(email = gSht_auth_email)
  if (gs4_has_token()) {
    gSht_get <- gs4_get(as.character(gSht_id))
  }else{
    #token not available
    gSht_get <- c("")
  }
  
  if(length(gSht_get) > 2){
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_URL <- gSht_get$spreadsheet_url[[1]]
    gSht_Sheets_num <- length(gSht_Sheets)
    test_compare <- mySheets %in% gSht_Sheets
    mySheets_present <- mySheets[test_compare]
    mySheets_notpresent <- mySheets[!test_compare]
    
    #check that at least ONE sheet found
    if(length(mySheets_notpresent) == length(mySheets)){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>The following sheets where expected but NOT found ",
                         "in the '", gSht_get$name, "' workbook:<br><b>", 
                         paste(mySheets_notpresent, collapse = "<br> "),
                         "</b><p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }else{
      if(length(mySheets_notpresent) > 0){
        bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                           "an error in the ", myReportName, " routine!</p>",
                           #"<p>There weren't any sheets named like '", mySheets, "' ",
                           "<p>The following sheets where expected but NOT found ",
                           "in the '", gSht_get$name, "' workbook:<br>", 
                           paste(mySheets_notpresent, collapse = "<br> "),
                           "<p>The routine will continue <B>BUT WILL NOT UPDATE ",
                           "THE SHEETS LISTED ABOVE.</B></p> ",
                           warn_sig
        )
        #send mail
        mailsend(warn_recip,
                 paste0(myReportName, " Issue: Missing one or more expected sheets"),
                 bodytext,
                 attachment = NULL,
                 test = testing_emails, testrecipient = test_recip
        )
      }
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}


### Populate sheets in Google

if(okaytocontinue){
  if(exists("results_this_sheet")){rm(results_this_sheet)}
  for(i in 1:length(mySheets_present)){
    gSht_Sheet_curr <- mySheets_present[[i]]
    
    
    #TEMP ACQUIS building at top of main Rent Roll
    myquery_ACQUIS <- paste0(
      '
        SELECT /* SNOWFLAKE VERSION */
          CASE WHEN TRY_CAST(SUIT.BLDGID AS INT) IS NULL THEN SUIT.BLDGID ELSE TO_CHAR(TRY_CAST(SUIT.BLDGID AS INT)) END AS "Bldg_ID"
        ,	NULL as "HRG Interested"
        ,	RTRIM(SUIT.SUITID) AS "Suite"
        ,	NULL as "Master_ID"
        ,	RTRIM(SUIT.ADDRESS) as "Occupant_Name"
        ,	TO_DATE(NULL) as "Rent_Start"
        ,	TO_DATE(NULL) as "Expiration"
        ,	TRY_CAST(NULL AS INT) AS "Sqft"
        ,	NULL AS "Base_Rent"
        ,	NULL AS "Rate_PSF"
        ,	NULL AS "Addl_Rents"
        ,	NULL AS "Lease_ID"
        ,	NULL as "Address"
        ,	NULL as "City"
        ,	NULL AS "State"
        ,	NULL AS "Postal Code"
        ,	NULL as "MAP HYPERLINK"
        ,	NULL AS "Corporate PM"
        ,	NULL AS "PM_Email"
        from MRI.SUIT 
        left join MRI.BLDG
        on SUIT.BLDGID = BLDG.BLDGID
        LEFT JOIN MRI.TB_CM_SUITETYPE
        on SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
        where SUIT.BLDGID = \'ACQUIS\'
        and (BLDG.INACTIVE is NULL or BLDG.INACTIVE = \'N\')
        and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> \'E\')
        order by SUIT.SUITID
      '
    )
    #20241008: mydata <- dbGetQuery(mySSdb, myquery_ACQUIS)
    mydata <- dbGetQuery(mySfDB, myquery_ACQUIS)
    #20241008: mydata_status <- check_mydata_rows(MinNumRows = 0, ReportName = myReportName)
    mydata_status <- check_mydf_rows(mydf = mydata, MinNumRows = 0, ReportName = myReportName)
    if(mydata_status[[1]] == TRUE){
      additional_results <- copy(mydata)
      rm(mydata)
    }
    
    
    myquery_set_rptdt <- paste0("SET rptdt = to_date('", rentroll.date.text, "','yyyyMMdd')")
    myquery <- case_when(
      gSht_Sheet_curr == "RENT ROLL" ~ paste0(
          '
            select /* SNOWFLAKE version */
              CASE WHEN TRY_CAST(u.BLDG_ID AS INT) IS NULL THEN u.BLDG_ID ELSE TO_CHAR(TRY_CAST(u.BLDG_ID AS INT)) END as "Bldg_ID"
            , NULL as "HRG Interested"
            , u.SUITE AS "Suite"
            , u.MASTER_ID AS "Master_ID"
            , u.OCCUPANT_NAME AS "Occupant_Name"
            , u.RENT_START as "Rent_Start"
            , u.EXPIR AS "Expiration"
            , u.SQFT AS "Sqft"
            , u.BASE_RENT AS "Base_Rent"
            , u.RATE_PSF AS "Rate_PSF"
            , u.ADDL_RENTS AS "Addl_Rents"
            , u.LEASID AS "Lease_ID"
            , u.ADDRESS AS "Address"
            , u.CITY AS "City"
            , u.STATE AS "State"
            , u.ZIPCODE AS "Postal Code"
            , u.MAP_HL AS "MAP HYPERLINK"
            , u.CORP_PM AS "Corporate PM"
            , u.CORP_PM_EMAIL AS "PM_Email"
            ',"
            from
            (	
            	select /* standard active leases and holdovers where exec date or rent start before $rptdt and end date (or stopbill date) is null or in future */
            	  RTRIM(leas.BLDGID) AS BLDG_ID
            	,	RTRIM(leas.SUITID) AS SUITE
            	,	RTRIM(leas.MOCCPID) as MASTER_ID
            	,	concat(RTRIM(leas.OCCPNAME), 
            			case 
            				when TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' and LEAS.RENTSTRT IS NOT NULL
            					then concat(' ',MONTH(LEAS.RENTSTRT),'/',DAY(LEAS.RENTSTRT),'/',YEAR(LEAS.RENTSTRT)) 
            				when IFNULL(leas.VACATE,to_date('2999-12-31','yyyy-mm-dd')) < $rptdt
            					then concat(' Stop Bill Date: ', case when leas.STOPBILLDATE IS NULL THEN CONCAT('NA Vacate: ',MONTH(LEAS.VACATE),'/',DAY(LEAS.VACATE),'/',YEAR(LEAS.VACATE)) ELSE CONCAT(MONTH(LEAS.STOPBILLDATE),'/',DAY(LEAS.STOPBILLDATE),'/',YEAR(LEAS.STOPBILLDATE)) END) 
            				ELSE ''
            			end
            		) as OCCUPANT_NAME
            	,	to_date(leas.RENTSTRT) as RENT_START
            	,	to_date(leas.EXPIR) as EXPIR
            	,	CAST(IFNULL(SSQF_TYPE.SQFT,0) AS INT) AS SQFT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE IFNULL(RENT.AMOUNT,0) END) AS BASE_RENT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE ROUND(IFNULL(RENT.AMOUNT,0)*12/(case when SSQF_TYPE.SQFT = 0 then 1 else SSQF_TYPE.SQFT END),2) END) AS RATE_PSF
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE NULL END) AS ADDL_RENTS
            	,	RTRIM(leas.LEASID) AS LEASID
            	,	RTRIM(suit.ADDRESS) as ADDRESS
            	,	RTRIM(bldg.CITY) as CITY
            	,	RTRIM(bldg.STATE) as STATE
            	,	RTRIM(bldg.ZIPCODE) as ZIPCODE
            	,	rtrim(bldg.MAP) as MAP_HL
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE rtrim(MNGR.MNGRNAME) END AS CORP_PM
  			      , CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  			        ELSE rtrim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS CORP_PM_EMAIL
            	from MRI.LEAS
            	left join MRI.SUIT
            	on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
            	left join MRI.BLDG
            	on leas.BLDGID = bldg.BLDGID
            	left join MRI.MNGR
            	on bldg.MNGRID = mngr.MNGRID
            	LEFT JOIN MRI.TB_CM_SUITETYPE
            		ON suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            	LEFT JOIN 
            	(
            		SELECT *
            		FROM MRI.SSQF
            		WHERE SSQF.EFFDATE = (
            			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= to_date($rptdt)
            			)
            	) SSQF_TYPE
            		ON suit.SUITID = SSQF_TYPE.SUITID
            		AND suit.BLDGID = SSQF_TYPE.BLDGID
            	left join								
            	(								
            		SELECT CMRECC.LEASID
            		,	SUM(CMRECC.AMOUNT) AS AMOUNT
            		FROM MRI.CMRECC
            		INNER JOIN MRI.INCH_LCP_REPORTS
            		ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
            		JOIN MRI.LEAS 
            		ON CMRECC.LEASID = LEAS.LEASID
            		WHERE CMRECC.EFFDATE = (
            					SELECT MAX(IC.EFFDATE) AS EFFDATE
            					FROM MRI.CMRECC IC 
            					WHERE to_date(LEAS.RENTSTRT) <= $rptdt
            					AND IC.BLDGID=CMRECC.BLDGID 
            					AND IC.LEASID=CMRECC.LEASID 
            					AND IC.INCCAT=CMRECC.INCCAT 
            					--AND IC.EFFDATE <= SYSDATETIME() /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					AND 
            					(
            						(IC.ENDDATE IS NULL AND IC.EFFDATE <= $rptdt)
            						OR 
            						IC.ENDDATE >= to_date($rptdt)
            					) /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					--AND IC.INEFFECT = 'Y'
            			)
            			AND INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT')
            		GROUP BY
            			CMRECC.LEASID					
            	) RENT	
            		ON leas.LEASID = RENT.LEASID
            	where
            		(leas.EXECDATE <= $rptdt or leas.RENTSTrt <= $rptdt)
            		and
            		(
            			(COALESCE(leas.VACATE,leas.EXPIR) >= $rptdt or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
            			OR
            			(leas.EXPIR < $rptdt and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers */
            			OR
            			(
            				(
            					leas.VACATE >= $rptdt 
            					or 
            					(leas.VACATE < $rptdt and leas.STOPBILLDATE is NULL)
            					or 
            					leas.STOPBILLDATE >= $rptdt
            				)
            				AND leas.OCCPSTAT != 'I'
            			)
            		)
            		and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
            
            	UNION ALL
            
            	select /* future leases */
            	  RTRIM(leas.BLDGID) AS BLDG_ID
            	,	RTRIM(leas.SUITID) AS SUITE
            	,	RTRIM(leas.MOCCPID) as MASTER_ID
            	,	concat(RTRIM(leas.OCCPNAME), 
            			case 
            				when TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' and LEAS.RENTSTRT IS NOT NULL
            					then concat(' ',MONTH(LEAS.RENTSTRT),'/',DAY(LEAS.RENTSTRT),'/',YEAR(LEAS.RENTSTRT)) 
            				when IFNULL(leas.VACATE,to_date('2999-12-31','yyyy-mm-dd')) < $rptdt
            					then concat(' Stop Bill Date: ', case when leas.STOPBILLDATE IS NULL THEN CONCAT('NA Vacate: ',MONTH(LEAS.VACATE),'/',DAY(LEAS.VACATE),'/',YEAR(LEAS.VACATE)) ELSE CONCAT(MONTH(LEAS.STOPBILLDATE),'/',DAY(LEAS.STOPBILLDATE),'/',YEAR(LEAS.STOPBILLDATE)) END) 
            				ELSE ''
            			end
            		) as OCCUPANT_NAME
            	,	to_date(leas.RENTSTRT) as RENT_START
            	,	to_date(leas.EXPIR) as EXPIR
            	,	CAST(IFNULL(SSQF_TYPE.SQFT,0) AS INT) AS SQFT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE IFNULL(RENT.AMOUNT,0) END) AS BASE_RENT
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE ROUND(IFNULL(RENT.AMOUNT,0)*12/(case when SSQF_TYPE.SQFT = 0 then 1 else SSQF_TYPE.SQFT END),2) END) AS RATE_PSF
            	,	(CASE WHEN leas.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE NULL END) AS ADDL_RENTS
            	,	RTRIM(leas.LEASID) AS LEASID
            	,	RTRIM(suit.ADDRESS) as ADDRESS
            	,	RTRIM(bldg.CITY) as CITY
            	,	RTRIM(bldg.STATE) as STATE
            	,	RTRIM(bldg.ZIPCODE) as ZIPCODE
            	,	trim(bldg.MAP) as MAP_HL
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE trim(MNGR.MNGRNAME) END AS CORP_PM
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  			        ELSE trim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS CORP_PM_EMAIL
            	from MRI.LEAS
            	left join MRI.SUIT
            	on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
            	left join MRI.BLDG
            	on leas.BLDGID = bldg.BLDGID
            	left join MRI.MNGR
            	on bldg.MNGRID = mngr.MNGRID
            	LEFT JOIN MRI.TB_CM_SUITETYPE
            	on suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            	LEFT JOIN 
            	(
            		SELECT *
            		FROM MRI.SSQF
            		WHERE SSQF.EFFDATE = (
            			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
            			)
            	) SSQF_TYPE
            		ON suit.SUITID = SSQF_TYPE.SUITID
            		AND suit.BLDGID = SSQF_TYPE.BLDGID
            	left join								
            	(								
            		SELECT CMRECC.LEASID
            		,	SUM(CMRECC.AMOUNT) AS AMOUNT
            		FROM MRI.CMRECC
            		INNER JOIN MRI.INCH_LCP_REPORTS
            		ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
            		JOIN MRI.LEAS 
            		ON CMRECC.LEASID = LEAS.LEASID
            		WHERE CMRECC.EFFDATE = (
            					SELECT MAX(IC.EFFDATE) AS EFFDATE
            					FROM MRI.CMRECC IC 
            					WHERE LEAS.RENTSTRT <= $rptdt
            					AND IC.BLDGID=CMRECC.BLDGID 
            					AND IC.LEASID=CMRECC.LEASID 
            					AND IC.INCCAT=CMRECC.INCCAT 
            					--AND IC.EFFDATE <= SYSDATETIME() /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					AND 
            					(
            						(IC.ENDDATE IS NULL AND IC.EFFDATE <= $rptdt)
            						OR 
            						IC.ENDDATE >= to_date($rptdt)
            					) /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            					--AND IC.INEFFECT = 'Y' /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
            			)
            			AND INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT')
            		GROUP BY
            			CMRECC.LEASID					
            	) RENT	
            		ON leas.LEASID = RENT.LEASID
            	left join
            	(
            		select
            			leas.BLDGID
            		,	RTRIM(leas.SUITID) AS SUITID
            		,	RTRIM(leas.MOCCPID) as MOCCPID
            		,	RTRIM(leas.LEASID) AS LEASID
            		from MRI.LEAS
            		left join MRI.SUIT
            		on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
            		LEFT JOIN MRI.TB_CM_SUITETYPE
            			ON suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            		where
            			(leas.EXECDATE <= $rptdt or leas.RENTSTrt <= $rptdt)
            			and
            			(
            				(COALESCE(leas.VACATE,leas.EXPIR) >= $rptdt or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
            				OR
            				(leas.EXPIR < $rptdt and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers */
            			)
            			and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
            	) exist
            	on leas.BLDGID = exist.BLDGID
            		and leas.SUITID = exist.SUITID
            		and leas.MOCCPID = exist.MOCCPID
            		and leas.LEASID = exist.LEASID /* includes renewals of existing MOCCPID that are under a new LEASID */
            	where (leas.RENTSTRT >= $rptdt or leas.RENTSTRT is NULL)
            	and (leas.EXECDATE is NULL or leas.EXECDATE >= $rptdt)
            	and exist.MOCCPID is NULL
            
            	UNION ALL
            	
            	SELECT /* VACANT */
            	  RTRIM(SUIT.BLDGID) AS BLDG_ID
            	,	SUIT.SUITID AS SUITE
            	,	NULL as MASTER_ID
            	,	'Vacant' as OCCUPANT_NAME
            	,	NULL as RENT_START
            	,	NULL as EXPIR
            	,	CAST(IFNULL(SSQF_TYPE.SQFT,0) AS INT) AS SQFT
            	,	NULL AS BASE_RENT
            	,	NULL AS RATE_PSF
            	,	NULL AS ADDL_RENTS
            	,	NULL AS LEASID
            	,	RTRIM(suit.ADDRESS) as ADDRESS
            	,	RTRIM(bldg.CITY) as CITY
            	,	RTRIM(bldg.STATE) as STATE
            	,	RTRIM(bldg.ZIPCODE) as ZIPCODE
            	,	trim(bldg.MAP) as MAP_HL
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL ELSE trim(MNGR.MNGRNAME) END AS CORP_PM
            	,	CASE WHEN bldg.INACTIVE = 'Y' THEN NULL 
  			        ELSE trim(case when mngr.email like '%@legacypro.' then replace(mngr.email, '@legacypro.', '@legacypro.com') else mngr.email end) END AS CORP_PM_EMAIL
            	FROM MRI.SUIT
            	LEFT JOIN MRI.BLDG
            	ON SUIT.BLDGID = BLDG.BLDGID
            	left join MRI.MNGR
            	on bldg.MNGRID = mngr.MNGRID
            	left join
            	(
            		SELECT *
            		FROM MRI.SSQF
            		WHERE SSQF.EFFDATE = 
            			(
            				SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= $rptdt
            			)
            	) SSQF_TYPE
            	ON SUIT.SUITID = SSQF_TYPE.SUITID
            		AND SUIT.BLDGID = SSQF_TYPE.BLDGID
            	LEFT JOIN 
            	(
            		SELECT LEAS.*
            		FROM MRI.LEAS
            		JOIN MRI.SUIT
            		ON LEAS.BLDGID = SUIT.BLDGID
            		AND LEAS.SUITID = SUIT.SUITID
            		LEFT JOIN MRI.TB_CM_SUITETYPE
            		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            		WHERE 
            			--(leas.EXECDATE <= $rptdt or leas.RENTSTrt <= $rptdt)
            			leas.RENTSTrt <= $rptdt
            			and
            			(
            				(COALESCE(leas.VACATE,leas.EXPIR) >= $rptdt or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
            				OR
            				(leas.EXPIR < $rptdt and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers NOT counted as Vacant in MRI Rent Roll*/
            			)
            			and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
            	) LEASED
            	ON SUIT.BLDGID = LEASED.BLDGID
            	AND SUIT.SUITID = LEASED.SUITID
            	LEFT JOIN MRI.TB_CM_SUITETYPE
            	ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
            	WHERE LEASED.SUITID IS NULL
            		AND (BLDG.INACTIVE is NULL or BLDG.INACTIVE = 'N') /* Active Buildings only */
            		AND
            		(
            			(
            				TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL 
            				or 
            				(TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' and TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'N') 
            			) /* Suitetypeusage is NULL or not in 'E' or 'N' */
            			or
            			(
            				TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' 
            				AND SSQF_TYPE.SQFT > 0 
            				AND SSQF_TYPE.SQFTTYPE = 'GLA'
            			)
            		)
            		AND NOT (SUIT.SUITETYPE_MRI is NULL and (COALESCE(SSQF_TYPE.SQFT,0) = 0 or SSQF_TYPE.SQFTTYPE != 'GLA') )
            ) u
            order by
            	u.BLDG_ID
            ,	case when u.Occupant_Name = 'Vacant' then 0 else 1 end
            ,	u.SUITE
          "
        ),
      TRUE ~ "skip" #unknown
    )
    
    
    
    myquery_LCPAssign <- case_when(
      gSht_Sheet_curr == "RENT ROLL" ~ paste0(
        "
          select  /* SNOWFLAKE version */
          to_char(a.BLDG) AS \"Bldg_ID\"
          , a.RM
          , case when rm.phone is NULL then NULL
              else '('||substr(rm.phone,1,3)||') '||substr(rm.phone,4,3)||'-'||substr(rm.phone,7,4) end as \"RM Phone\"
          , rm.email as \"RM Email\"
          , a.SUPPORT_LEASING AS \"Support Leasing\"
          , case when a.SUPPORT_LEASING = 'BRH' then '(*************' 
              when sl.phone is NULL then NULL
              else '('||substr(sl.phone,1,3)||') '||substr(sl.phone,4,3)||'-'||substr(sl.phone,7,4) end as \"SL Phone\"
          , case when a.SUPPORT_LEASING = 'BRH' then '<EMAIL>' else sl.email end as \"SL Email\"
          , a.SUPPORT_PROP_MGMT AS \"Support Property Management\"
          , case when rm.phone is NULL then NULL
              else '('||substr(spm.phone,1,3)||') '||substr(spm.phone,4,3)||'-'||substr(spm.phone,7,4) end as \"SPM Phone\"
          , spm.email as \"SPM Email\"
          from CORPORATE.lcp_assignments_sean a
          left join CORPORATE.ab_employees rm
          on upper(a.rm) = upper(rm.fname)||' '||upper(rm.lname)
          and rm.status not in ('T','R','D')
          left join CORPORATE.ab_employees sl
          on upper(a.SUPPORT_LEASING) = upper(sl.fname)||' '||upper(sl.lname)
          and sl.status not in ('T','R','D')
          left join CORPORATE.ab_employees spm
          on upper(a.SUPPORT_PROP_MGMT) = upper(spm.fname)||' '||upper(spm.lname)
          and spm.status not in ('T','R','D')
        "
      ),
      TRUE ~ "skip" #unknown
    )
    
    
    myquery_PMPhone <- case_when(
      gSht_Sheet_curr == "RENT ROLL" ~ paste0(
        "
          select /* SNOWFLAKE version */
            email as \"PM_Email\"
          , '(847) 904-'||extension as \"PM_Phone\"
          from CORPORATE.ab_employees
          where status not in ('R','T')
          and extension is not null
          and email is not null
        "
      ),
      TRUE ~ "skip" #unknown
    )
    
    #20241008: mydata <- dbGetQuery(mySSdb, myquery)
    #set rent roll report data variable
    rs_rpdt <-dbExecute(mySfDB, myquery_set_rptdt)
    #send main query
    rs <- dbSendQuery(mySfDB, myquery)
    mydata <- dbFetch(rs, n = -1)
    dbClearResult(rs)
    #20241008: mydata_status <- check_mydata_rows(MinNumRows = 0, ReportName = myReportName)
    mydata_status <- check_mydf_rows(mydf = mydata, MinNumRows = 0, ReportName = myReportName)
    
    mydata_PMPhone <- dbGetQuery(mySfDB, myquery_PMPhone)
    mydata_status_PMPhone <- check_mydf_rows(mydf = mydata_PMPhone, MinNumRows = 1, ReportName = myReportName)
    mydata_LCPAssign <- dbGetQuery(mySfDB, myquery_LCPAssign)
    mydata_status_LCPAssign <- check_mydf_rows(mydf = mydata_LCPAssign, MinNumRows = 1, ReportName = myReportName)
    
    if(mydata_status[[1]] == TRUE && mydata_status_PMPhone[[1]] == TRUE && mydata_status_LCPAssign[[1]] == TRUE){
    #if(mydata_status[[1]] == TRUE){
      #combine temp rows (if applicable) with main data
      if(exists("additional_results")){
        mydata <- rbind(additional_results, mydata)
      }
      #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
      mydata[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
      
      
      #join PM phone to MRI data
      mydata <- mydata %>%
        dplyr::left_join(mydata_PMPhone, by = "PM_Email")
      #dplyr::left_join(mydata_PMPhone, by = join_by("PM_Email", "EMAIL"))
      mydata <- mydata %>% relocate(PM_Phone, .before = PM_Email)
      
      #add LCP Assignments to mydata
      #myLoadStores_failed <- myLoadStores_expect %>% 
      #  dplyr::left_join(myLoadStores_curr, by = "STORE") %>% .[which(.$LOADED != .$n || is.na(.$LOADED)), ]
      mydata <- mydata %>%
        dplyr::left_join(mydata_LCPAssign, by = 'Bldg_ID')
      
      #replace headers to replace underscores with spaces
      names(mydata) <- gsub("_"," ",names(mydata))
      
      # add 'Updated' column at end of mydata
      mydata[, date.header.text] <- NA
      
      
      #convert to dates
      #convert POSIXct dates to match previous Oracle output
      mydata[] <- lapply(mydata[], function(x) if(inherits(x, "POSIXct")) as.Date(x) else x)
      #UPDATE sheet
      #clear existing data in sheet
      range_clear(gSht_get$spreadsheet_id, sheet = mySheets_present[[i]], range = NULL, reformat = FALSE)
      #write new data
      range_write(ss = gSht_get$spreadsheet_id, 
                  data = mydata, 
                  sheet = mySheets_present[[i]], 
                  #range = cell_rows(), 
                  col_names = TRUE,
                  reformat = FALSE
      )
      Sys.sleep(2)
      #Flag to add vacate date column and save csv version to file for AP changes comparison and notification
      save_comp_version <- TRUE
    }

  }
}


###Compare last week's rent roll to this week's###
if(save_comp_version && comp_prior){
  #add Vacate date to current data in order to save a csv of this week's rent roll
  myquery_VACATE <- paste0(
    '
      SELECT /* SNOWFLAKE version */
      	CASE WHEN TRY_CAST(leas.BLDGID AS INT) IS NULL THEN leas.BLDGID ELSE TO_CHAR(TRY_CAST(leas.BLDGID AS INT)) END as "Bldg ID"
      ,	leas.SUITID as "Suite"
      , leas.LEASID as "Lease ID"
      ,	to_date(leas.vacate) as "Vacate Date"
      from MRI.LEAS
      where leas.VACATE is not NULL
    '
  )
  mydata_VACATE <- dbGetQuery(mySfDB, myquery_VACATE)
  mydata_status_VACATE <- check_mydf_rows(mydf = mydata_VACATE, MinNumRows = 0, ReportName = myReportName)
  if(mydata_status_VACATE[[1]] == TRUE){
    #convert possible POSIXct to just Date
    mydata_VACATE[] <- lapply(mydata_VACATE[], function(x) if(inherits(x, "POSIXct")) as.Date(x) else x)
    #join vacate dates to mydata as arrange columns
    mydata_csv <- mydata %>%
      dplyr::left_join(mydata_VACATE, by = c("Bldg ID","Suite","Lease ID"))
    rm(mydata_VACATE)
    mydata_csv <- mydata_csv %>% relocate(`Vacate Date`, .after = Expiration)
    mydata_csv <- mydata_csv[,c("Bldg ID","Suite","Master ID","Occupant Name","Rent Start","Expiration","Vacate Date","Address","City","State")]
    #report.date.text
    
    
    myFN <- paste0(mySheets, " ", report.date.text, ".csv")
    mySaveAs <- file.path(myReportPath, myFN)
    rs <- write.table(
      x = mydata_csv, 
      file = mySaveAs,
      na = "",
      sep = ",", 
      quote = TRUE,
      row.names = FALSE, 
      col.names = TRUE
    )
    
    #read file for comparison to previous week
    mywrittendata_curr <- read.table(file = mySaveAs, header = TRUE, sep = ",", dec = ".", colClasses = "character", stringsAsFactors = FALSE)
    
    #replace "." in mywrittendata_curr with spaces to match original header data
    names(mywrittendata_curr) <- gsub(x = names(mywrittendata_curr), pattern = "\\.", replacement = " ")
    
    #if last week's file exists, compare to curr
    myFNPrior <- paste0(mySheets, " ", report.date.prior.text, ".csv")
    mySaveAsPrior <- file.path(myReportPath, myFNPrior)
    if(file.exists(mySaveAsPrior)){
      mywrittendata_prior <- read.table(file = mySaveAsPrior, header = TRUE, sep = ",", dec = ".", colClasses = "character", stringsAsFactors = FALSE)
      names(mywrittendata_prior) <- gsub(x = names(mywrittendata_prior), pattern = "\\.", replacement = " ")
      if(inc_name_chgs){
        diff_curr <- setdiff(mywrittendata_curr,mywrittendata_prior)
        diff_prior <- setdiff(mywrittendata_prior, mywrittendata_curr)
      }else{
        diff_curr <- setdiff(mywrittendata_curr[,!(names(mywrittendata_curr) %in% c("Occupant Name"))], 
                             mywrittendata_prior[,!(names(mywrittendata_prior) %in% c("Occupant Name"))])
        diff_prior <- setdiff(mywrittendata_prior[,!(names(mywrittendata_prior) %in% c("Occupant Name"))],
                              mywrittendata_curr[,!(names(mywrittendata_curr) %in% c("Occupant Name"))])
      }
      
      if(nrow(diff_curr)>0){
        diff_curr$`Roll Date` <- as.Date(report.date.text, "%m-%d-%Y")
      }else{
        diff_curr$`Roll Date` <- Date(0)
      }
      diff_curr <- diff_curr %>% relocate(`Roll Date`, .before = `Bldg ID`)
      if(nrow(diff_prior)>0){
        diff_prior$`Roll Date` <- as.Date(report.date.prior.text, "%m-%d-%Y")
      }else{
        diff_prior$`Roll Date` <- Date(0)
      }
      diff_prior <- diff_prior %>% relocate(`Roll Date`, .before = `Bldg ID`)
      if(inc_name_chgs == FALSE){
        #Add occupant names back
        diff_curr <- diff_curr %>%
          dplyr::left_join(mywrittendata_curr[ ,(names(mywrittendata_curr) %in% c("Master ID","Occupant Name"))])
        diff_curr <- diff_curr %>% relocate(`Occupant Name`, .after = `Master ID`)
        diff_prior <- diff_prior %>%
          dplyr::left_join(mywrittendata_prior[ ,(names(mywrittendata_prior) %in% c("Master ID","Occupant Name"))])
        diff_prior <- diff_prior %>% relocate(`Occupant Name`, .after = `Master ID`)
      }
      
      diff_combined <- rbind(diff_curr, diff_prior)
      setnames(diff_combined, 
               old = c("Bldg ID"), 
               new = c("Bldg"),
               skip_absent = TRUE)
      #sort by Bldg ID, Suite, Date
      diff_combined <- arrange(diff_combined, Bldg, Suite, `Roll Date`)
      myFN <- paste0(mySheets, " - Changes.csv")
      mySaveAs <- file.path(myReportPath, myFN)
      rs <- write.table(
        x = diff_combined, 
        file = mySaveAs,
        na = "",
        sep = ",", 
        quote = TRUE,
        row.names = FALSE, 
        col.names = TRUE
      )
      
      
      
      mydata_emailbody <- copy(diff_combined)
      mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
      if(nrow(mydata_emailbody)<=40){
        bodytable <- paste0("<p>",
                            print(xtable(mydata_emailbody, 
                                         #caption = paste0(this_ReportName, " (", query.date, ")"),
                                         digits = rep(0,ncol(mydata_emailbody)+1)
                            ),
                            #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                            html.table.attributes = "border=2 cellspacing=1",
                            type = "html",
                            caption.placement = "top",
                            include.rownames=FALSE
                            ),
                            "</p>"
        )
      }else{
        bodytable <- paste0("<p><b>There are ", nrow(mydata_emailbody), 
                            " results, see attached file for them. ",
                            "They are ONLY in the attachment in order ",
                            "to keep the body of this email shorter.",
                            "</b></p>"
        )
      }
      
      bodytext <- paste0("<p>A file with the <b>RENT ROLL</b> changes is attached. ",
                         "The file contains changes made ", 
                         " since last ", dow, ".</p>",
                         "<p>The results are sorted by BLDG, SUITE and then by ROLL DATE. ",
                         "The ROLL DATE is the date the rent roll was posted. ",
                         "Changes in a particular SUITE will normally have a row with ",
                         "last week's data followed by a row with this week's data. ",
                         "If it doesn't have a row for both dates, it was most likely ",
                         "added or removed in the last week.</p>",
                         "<p>",
                         bodytable,
                         "</p><br>"
      )
      
      #check for recent activity for reminder section
      if(query.date == "15-Oct-24"){
        Recent_start_day_offset <- -74
      }else{
        Recent_start_day_offset <- -7
      }
      Recent_end_day_offset <- 7
      myquery_Recent <- paste0(
        "
          SELECT /* SNOWFLAKE version */
          *
          from
          (
          
          	select
          		LEAS.BLDGID
          	,	LEAS.SUITID
          	,	LEAS.LEASID
          	,	LEAS.OCCPNAME as \"Occupant Name\"
          	,	concat('Stop Billing: ', to_char(leas.STOPBILLDATE, 'MM/dd/yy'), 
          		case when LEAS.VACATE is not NULL then concat(' (Vacated: ', to_char(LEAS.VACATE, 'MM/dd/yy'), ')') end) as \"Activity\"
          	from MRI.LEAS
          	where to_date(LEAS.STOPBILLDATE) >= dateadd(day, ", Recent_start_day_offset, ", getdate())
          	and to_date(LEAS.STOPBILLDATE) <= dateadd(day, ", Recent_end_day_offset, ", getdate())
          
          	union all
          
          	select
          		LEAS.BLDGID
          	,	LEAS.SUITID
          	,	LEAS.LEASID
          	,	LEAS.OCCPNAME as \"Occupant Name\"
          	,	concat('Rent Starting: ', to_char(LEAS.RENTSTRT, 'MM/dd/yy')) as \"Activity\"
          	from MRI.LEAS
          	where to_date(LEAS.RENTSTRT) >= dateadd(day, ", Recent_start_day_offset, ", CURRENT_DATE)
          	and to_date(LEAS.RENTSTRT) <= dateadd(day, ", Recent_end_day_offset, ", CURRENT_DATE)
          	
          	union all
          
          	select
          		LEAS.BLDGID
          	,	LEAS.SUITID
          	,	LEAS.LEASID
          	,	LEAS.OCCPNAME as \"Occupant Name\"
          	,	concat('Newly Executed Lease: ', to_char(LEAS.EXECDATE, 'MM/dd/yy')) as \"Activity\"
          	from MRI.LEAS
          	where to_date(LEAS.EXECDATE) >= dateadd(day, ", Recent_start_day_offset, ", CURRENT_DATE)
          	and to_date(LEAS.EXECDATE) <= dateadd(day, ", Recent_end_day_offset, ", CURRENT_DATE)
          ) comb
          order by \"Activity\"
        "
      )
      mydata_Recent <- dbGetQuery(mySfDB, myquery_Recent)
      mydata_status_Recent <- check_mydf_rows(mydf = mydata_Recent, MinNumRows = 1, ReportName = myReportName)
      if(mydata_status_Recent[[1]] == TRUE){
        #query okay and results returned
        #remove trailing spaces to avoid using 'trim' in multiple column selections
        mydata_Recent[] <- lapply(mydata_Recent[], function(x) if(inherits(x, "character")) trimws(x, "r") else x)
        
        #create addition to bodytext
        recent.date.text <- paste0(
          ' (',
          format(as.Date(report.date.text, "%m-%d-%Y") + Recent_start_day_offset, "%m-%d-%Y"),
          ' to ',
          format(as.Date(report.date.text, "%m-%d-%Y") + Recent_end_day_offset, "%m-%d-%Y"),
          ')'
        )
        
        bodytablerecent <- paste0("<p>",
                            print(xtable(mydata_Recent, 
                                         #caption = paste0(this_ReportName, " (", query.date, ")"),
                                         digits = rep(0,ncol(mydata_Recent)+1)
                            ),
                            #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                            html.table.attributes = "border=2 cellspacing=1",
                            type = "html",
                            caption.placement = "top",
                            include.rownames=FALSE
                            ),
                            "</p>"
        )
        
        
        bodyrecent <- paste0(
          "<br><h3>Recent/Upcoming Activity Reminder", recent.date.text, "</h3>",
          "<p>Below are some effective dates of changes taking place in the ",
          "recent past or near future.</p>",
          bodytablerecent,
          "<br><br>"
        )
        bodytext <- paste0(bodytext, bodyrecent)
        
      }
      
      bodytext <- paste0(bodytext, norm_sig)
      
      rs <- mailsend(recipient = AP_changes_recip,
                     subject = "RENT ROLL Changes",
                     body = bodytext,
                     if(is.na(mySaveAs)){attachment = NULL}else{attachment = mySaveAs},
                     test = testing_emails, testrecipient = test_recip
      )
      
      #clean up older comparison files no longer needed (28-56 days older than this run's prior comp file)
      for(x in 28:56){
        remove.report.date.text <- format(as.Date(report.date.prior.text, "%m-%d-%Y") - x, "%m-%d-%Y")
        rmFN <- paste0(mySheets, " ", remove.report.date.text, ".csv")
        rmSavedAs <- file.path(myReportPath, rmFN)
        if(file.exists(rmSavedAs)){
          try(unlink(x = rmSavedAs), TRUE)
        }
      }
      
    }else{
      #warn that last week's file not found
      bodytext <- paste0("<p>This is an automated email to inform you that it appears ",
                         "last week's Rent Roll comparison file '", myFNPrior, 
                         "' was not found. It was expected to be found at: <br>",
                         mySaveAsPrior, "</p>",
                         "<p>The routine won't be able to determine weekly changes ",
                         " to the Rent Roll. </p>",
                         "<p>This week's comp file should have been saved.</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, ": Missing comparision file"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }else{
    #warn of vacate date query failure and resulting abort of comparison for AP
    bodytext <- paste0("<p>This is an automated email to inform you that it appears ",
                       "the query to gather the MRI Vacancy Dates failed. ",
                       "The routine could not generate a comparison file for ",
                       "this week or email any changes out.</p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, ": Vacancy Date query failure"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}

