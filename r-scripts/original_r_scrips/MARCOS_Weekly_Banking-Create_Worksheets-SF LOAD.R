library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(purrr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyr)
library(DBI)
library(keyring)
library(janitor)
library(odbc)

#library(ROracle) #remove after testing Snowflake

# written by <PERSON> July 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20240912

### 20240912 change:
### gmailr masks base message() function, replaced with explicit base::message()

### 20240909 change:
### converted from mailR package (SMTP), to gmailr (OAuth-GMail API) ahead of ******** SMTP deprecation in GMail

### ******** change
### fixed bug in logpath declaration

### ******** change
### modified to Tableau SF user and allow Tableau PC local paths

### ******** change
### Move over DB queries to use Snowflake

### ******** change
### changed email signature to new corp style and using saved HTML file

### ******** change:
### added loop timer to slow down new sheet creation to help avoid Google timeouts

### ******** change:
### changed ORDER BY clause of bank SQL query to order by bank name (previously B_ID_MASTER)

### ******** change:
### added new folder for Excel files, added verbiage to email noting that directory file is only
### for the current week

### ******** change:
### added filters to load routine to exclude transactions outside these dates so email verbiage changed here
### to remove mention of possible duplication of transactions

### ******** change:
### added bank login URL to steve.mp_bank_id_master, add hyperlink to Bank name in the directory files
### also added missing mime package load


### ******** change:
### new file


# Parameters

okaytocontinue <- TRUE

myReportName <- "Marco's Weekly Banking-Create Worksheets"
scriptfolder <- "MARCOS_Weekly_Banking"
rptfolder <- "reports"
#logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
#********:
#logpath <- file.path("C:","Users","steve","Documents","ReportFiles",scriptfolder)
#********: use network shared drive paths as default, but can be changed depending on envirnment further down
logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")

gSht_auth_email <- "<EMAIL>"
gDrv_mainURL <-'https://drive.google.com/drive/folders/1gb0wL_R_4oznC0zYIUL8c1tLK5C0ikqa/' #"Marcos Bank Transactions" folder
gDrv_directoryfoldername <- 'Directory Files'
gDrv_mainURL_email <- 'https://drive.google.com/drive/u/0/folders/1gb0wL_R_4oznC0zYIUL8c1tLK5C0ikqa'
gDrv_folder_prefix <- 'https://drive.google.com/drive/u/0/folders/' #append ID (for use when not directory owner: <EMAIL>)
gDrv_sheet_prefix <- "https://docs.google.com/spreadsheets/d/"
loop_cnt <- 0
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")


#********: 
#test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
test_computers <- c("STEVEANDJENYOGA")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to shared/central drive instead of local paths
}else{
  testing_pc <- FALSE
}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  # default assigned paths are network paths
}else if(this_computer == "DESKTOP-TABLEAU"){
  logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")
}else if(this_computer == "STEVEO-PLEX7010"){
  logpath <- file.path("C:","Users","steve","Documents","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("C:","Users","steve","Documents","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("C:","Users","steve","Documents","ReportFiles","HTML_signatures.csv")
}

myReportPath <- file.path(logpath, rptfolder)


#if(file.exists(HVSigLogopath)){
  #******** no longer used as 'inline' signature logo no longer used
  #append signature logo to norm_st_from
  #if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  #if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
#}


# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "16-AUG-22"
query.days <- 13
query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - (query.days - 2), "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) + 2, "%d-%b-%y")# Oracle date format for end date of reporting

report.year <- lubridate::year(as.Date(query.startdate, "%d-%b-%y"))# YEAR for start date of reporting, this will be the folder to create workbooks in
report.removestart <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 49, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
report.removeend <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 35, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
rpt.start <- format(as.Date(query.startdate, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
rpt.end <- format(as.Date(query.enddate, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
email.start <- format(as.Date(query.startdate, "%d-%b-%y"), "%m/%d/%Y")
email.end <- format(as.Date(query.enddate, "%d-%b-%y"), "%m/%d/%Y")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
email.executedate <- format(as.Date(query.enddate, "%d-%b-%y")+1, "%a, %b %d")
rptFN_noEXT <- paste0("MP Weekly Banking Files Created ", rpt.start,"-", rpt.end)
rptFN <- paste0(rptFN_noEXT, ".xlsx")


### define some functions ###

#********: 
###STAGE Snowflake versions###
#Sf_DB <- "STAGE_CSM_DB"
#Sf_schema <- "MOMS"
#Sf_wh <- "STAGE_DATA_ANA_WH"
##Sf_role <- "FR_STAGE_ANA_USERS"
#Sf_role <- "AR_STAGE_CONSUMPTION_RO"
#Sf_user <- key_get("SfHV", "tableau_ID_prod")
#Sf_pw <- key_get("SfHV", "tableau_PW_prod")
###PROD Snowflake versions###
Sf_DB <- "PROD_CSM_DB"
Sf_schema <- "CORPORATE"
Sf_wh <- "PROD_DATA_ANA_WH"
Sf_role <- "AR_PROD_CONSUMPTION_RW" #this should probably be AR_PROD_CONSUMPTION_RO (read-only), but that role isn't set up fully as of ********
Sf_user <- key_get("SfHV", "tableau_ID_prod")
Sf_pw <- key_get("SfHV", "tableau_PW_prod")

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role
                         #,authenticator = 'externalbrowser'
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ='America/Chicago')
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

mySchema <- "CORPORATE"
myTable <- "MP_BANK_TRANS"
myTableName <- paste(mySchema, myTable, sep = ".")

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}


mailsend <- function(
    recipient, subject, body, attachment = NULL, inline = FALSE, 
    sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gmailr::gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


writeXLSX_banking <- function(dirpath, fname, sname = "Sheet1", RptDF, 
                              colnames = TRUE, colwidths = NULL,  writeover = TRUE,
                              hyperlink_google = NULL, hypercol_google = NULL,
                              hyperlink_bank = NULL, hypercol_bank = NULL){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  my_cmnt <- createComment(
    comment = paste0("This workbook created: ",format(now(), "%a, %b %d, %Y at %I:%M%p %Z")),
    author = gSht_auth_email,
    style = NULL
    ,visible = FALSE
    ,width = 2.5
    ,height = 2
  )
  writeComment(wb = myWB, sheet = sname, col = "A", row = 1, comment = my_cmnt)
  #write 'paste to' hyperlinks
  if(class(hyperlink_google) == 'hyperlink' & !is.null(hypercol_google)){
    writeData(wb = myWB, sheet = sname, x = hyperlink_google, startRow = 2, startCol = hypercol_google)
  }
  #write bank login hyperlinks
  if(class(hyperlink_bank) == 'hyperlink' & !is.null(hypercol_bank)){
    writeData(wb = myWB, sheet = sname, x = hyperlink_bank, startRow = 2, startCol = hypercol_bank)
  }
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext,
             inline = FALSE
    )
  }
}



# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isFolder <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was for folder
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isFolder <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("folder")
    if(!isFolder){okaytocontinue <- FALSE}else{
      #was folder, check if yearly folder and directory folder present, create if not
      mySearch <- drive_ls(drv_get_main$id, recursive = TRUE) #dribble of drv_get_main contents
      myFolder_year <- mySearch %>% 
        hoist(drive_resource, "mimeType") %>% 
        filter(mimeType == drive_mime_type("folder")) %>%
        #filter(name %like% myNameLike)
        filter(name %in% report.year)
      if(nrow(myFolder_year) == 0){
        #create folder
        myFolder_year <- drive_mkdir(as.character(report.year), path = drv_get_main$id[[1]], description = paste0("Folder for ", report.year, " Marco's bank transaction workbooks"))
      }
      myFolder_directory <- mySearch %>% 
        hoist(drive_resource, "mimeType") %>% 
        filter(mimeType == drive_mime_type("folder")) %>%
        filter(name %in% gDrv_directoryfoldername)
      if(nrow(myFolder_directory) == 0){
        #create folder
        myFolder_directory <- drive_mkdir(gDrv_directoryfoldername, path = drv_get_main$id[[1]], description = paste0("Folder for directory files linking to Marco's bank transaction workbooks/sheets"))
      }
    }
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file folder for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Folder URL resolved: ", isFolder, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Access Issue"),
             bodytext,
             attachment = NULL,
             inline = FALSE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


###-------------------###
###Get email signature###
###-------------------###
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}


# check if any desired files are needed
if(okaytocontinue){
  
  myquery <- paste0(
    "
      SELECT /* SNOWFLAKE myFilesSheets_expected query */
          M.NAME_LONG AS BANK
      ,   CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' THEN 'JUST THIS STORE in this sheet' ELSE 'MULTIPLE stores in this sheet' END AS IND_or_MUL
      ,   L.LOC_NUM AS LOCATION
      ,   NULL as GSHT_LINK
      ,   to_char(to_date('", query.startdate, "','DD-MON-YY'), 'yyyymmdd')||'-'||to_char(to_date('", query.enddate, "','DD-MON-YY'), 'yyyymmdd')||' '||M.NAME_SHORT AS FILENAME
      ,   CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' THEN M.NAME_SHORT||'_'||L.LOC_NUM ELSE M.NAME_SHORT END AS SHEETNAME
      ,   L.ACCOUNT AS ACCOUNT
      ,   CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' THEN NULL ELSE NVL(L.IMPORT_ALT_ID, L.ACCOUNT) END AS IDENTIFIER
      ,   L.B_ID_LOCAL
      ,   M.LOGIN_URL as BANK_LOGIN_URL
      FROM ", mySchema, ".MP_BANK_ID_LOCAL L
      JOIN ", mySchema, ".MP_BANK_ID_MASTER M
      ON L.B_ID_MASTER = M.B_ID_MASTER
      WHERE 
          L.S_DATE <= to_date('", query.enddate, "','DD-MON-YY')
          AND (L.E_DATE IS NULL OR L.E_DATE >= to_date('", query.startdate, "','DD-MON-YY'))
      order by M.NAME_LONG, M.IMPORT_METHOD, L.LOC_NUM
    "
  )
  #********: 
  #myFilesSheets_expected <- dbGetQuery(myOracleDB, myquery)
  myFilesSheets_expected <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydf_rows(myFilesSheets_expected, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]]){
    #files needed, proceed
  }else{
    #no expected files based on query, abort
    myFiles_expected_notpresent <- c("No expected files returned by Oracle query of bank tables")
    okaytocontinue <- false
  }
}


# identify if any workbooks already present
if(okaytocontinue){
  myFiles_expected <- unique(myFilesSheets_expected$FILENAME)
  mySearch <- drive_ls(drv_get_main$id, recursive = TRUE) #dribble of drv_get_main contents
  myFiles <- mySearch %>% 
    hoist(drive_resource, "mimeType") %>% 
    filter(mimeType == drive_mime_type("spreadsheet")) %>%
    filter(name %in% myFiles_expected)
  test_compare <- myFiles_expected %in% c(myFiles$name)
  myFiles_expected_present <- myFiles_expected[test_compare]
  myFiles_expected_notpresent <- myFiles_expected[!test_compare]
  rm(test_compare)
}


#check if expected sheets present in each EXISTING file
if(okaytocontinue & length(myFiles_expected_present) > 0){
  for(WbNum in 1:length(myFiles_expected_present)){
    loop_cnt <- loop_cnt + 1
    if(loop_cnt %% 25 == 0){
      #pause an additional 45 seconds
      base::message("Intentional pause to prevent Google errors")
      Sys.sleep(45)
    }else{
      Sys.sleep(1.5)
    }
    gSht_id <- myFiles$id[which(myFiles$name == myFiles_expected_present[WbNum])][[1]]
    gSht_name <- myFiles_expected_present[WbNum]
    gSht_sheets <- sheet_properties(gSht_id)
    #compare gSht_sheets to expected sheet names
    mySheets_expected <- unique(myFilesSheets_expected$SHEETNAME[which(myFilesSheets_expected$FILENAME == gSht_name)])
    test_compare <- mySheets_expected %in% c(gSht_sheets$name)
    mySheets_expected_present <- mySheets_expected[test_compare]
    mySheets_expected_notpresent <- mySheets_expected[!test_compare]
    rm(test_compare)
    #check sheets in this workbook
    for(WsNum in 1:length(mySheets_expected)){
      
      if(mySheets_expected[WsNum] %notin% mySheets_expected_present){
        #Add needed sheet
        sheet_add(gSht_id, sheet = mySheets_expected[WsNum], .before = WsNum, )
      }
      
      #get gid of sheet and construct page URL
      gSht_gid <- gSht_sheets$id[which(gSht_sheets$name == mySheets_expected[WsNum])]
      mySheetURL <- paste0(gDrv_sheet_prefix,
                           gSht_id,
                           "/edit#gid=",
                           gSht_gid
      )
      #Populate URL to sheet in myFilesSheets_expected
      myFilesSheets_expected$GSHT_LINK[which(myFilesSheets_expected$FILENAME == gSht_name & myFilesSheets_expected$SHEETNAME == mySheets_expected[WsNum])] <-
        mySheetURL
    }
  }
}




#add files that don't exist yet
if(okaytocontinue & length(myFiles_expected_notpresent) > 0){
  for(WbNum in 1:length(myFiles_expected_notpresent)){
    #create workbook with needed sheets
    gSht_name <- myFiles_expected_notpresent[WbNum]
    mySheets_expected <- unique(myFilesSheets_expected$SHEETNAME[which(myFilesSheets_expected$FILENAME == gSht_name)])
    with_drive_quiet(
      gSht_created <- drive_create(gSht_name, path = myFolder_year$id[[1]], type = "spreadsheet")
    )
    #add needed sheets to file
    gSht_id <- gSht_created$id
    #remove existing sheet
    gSht_sheets <- sheet_properties(gSht_id)
    gSht_todelete <- gSht_sheets$name[which(gSht_sheets$name %notin% mySheets_expected)]
    with_gs4_quiet(
      sheet_add(gSht_id, sheet = mySheets_expected, .before = 1 )
    )
    with_gs4_quiet(
      sheet_delete(gSht_id, gSht_todelete)
    )
    gSht_sheets <- sheet_properties(gSht_id)
    
    
    loop_cnt <- loop_cnt + 1
    if(loop_cnt %% 25 == 0){
      #pause an additional 45 seconds
      base::message("Intentional pause to prevent Google errors")
      Sys.sleep(45)
    }else{
      Sys.sleep(1.5)
    }
    
    #loop through sheets (mySheets_expected) and build URLs
    for(WsNum in 1:length(mySheets_expected)){
      
      
      
      #get gid of sheet and construct page URL
      gSht_gid <- gSht_sheets$id[which(gSht_sheets$name == mySheets_expected[WsNum])]
      mySheetURL <- paste0(gDrv_sheet_prefix,
                           gSht_id,
                           "/edit#gid=",
                           gSht_gid
      )
      #Populate URL to sheet in myFilesSheets_expected
      myFilesSheets_expected$GSHT_LINK[which(myFilesSheets_expected$FILENAME == gSht_name & myFilesSheets_expected$SHEETNAME == mySheets_expected[WsNum])] <-
        mySheetURL
      
    }
  }
  options(googledrive_quiet = FALSE)
}


#Create Excel file with URLs, email it and upload as a Google Sheet
if(okaytocontinue){
  mydata <- copy(myFilesSheets_expected)
  mydata$Google_URL <- mydata$GSHT_LINK #to keep text version of URL in final workbook (GSHT_LINK is overwritten by hyperlink)
  myFN_noEXT <- rptFN_noEXT
  myFN <- rptFN
  mySN <- paste0(rpt.start, " to ", rpt.end)
  #create hyperlinks data
  ##paste sheets
  myLinks_paste <- mydata$GSHT_LINK
  names(myLinks_paste) <- mydata$SHEETNAME
  class(myLinks_paste) <- "hyperlink"
  ##bank logins
  myLinks_bank <- mydata$BANK_LOGIN_URL
  names(myLinks_bank) <- mydata$BANK
  class(myLinks_bank) <- "hyperlink"
  
  #rename columns to Excel version
  OldNames <- c(
    "BANK",
    "IND_OR_MUL",
    "LOCATION",
    "GSHT_LINK",
    "FILENAME",
    "SHEETNAME",
    "ACCOUNT",
    "IDENTIFIER",
    "BANK_LOGIN_URL"
  )
  NewNames <- c(
    "Bank",
    "Ind. or Mult. Per Sheet",
    "MP#",
    "Paste Trans Into This Sheet",
    "Google File Name",
    "Sheet Name",
    "Account Number",
    "Identifier In Statement",
    "Bank Login URL"
  )
  setnames(x = mydata, 
           old = OldNames, 
           new = NewNames,
           skip_absent=TRUE)
  mydata <- mydata[,!names(mydata) %in% c("Sheet Name")] #drop Sheet Name column from Excel file
  #prepare Excel file
  myXLSXColWidths <- data.frame (colname  = c("Bank",
                                              "Ind. or Mult. Per Sheet",
                                              "MP#",
                                              "Paste Trans Into This Sheet",
                                              "Google File Name",
                                              "Account Number",
                                              "Identifier In Statement",
                                              "B_ID_LOCAL",
                                              "Google_URL"
                                              #"",
  )
  ,
  width = c(23,
            27,
            7.5,
            27,
            if(max(nchar(na.omit(mydata[,"Google File Name"]))) > 35){min(60, max(nchar(na.omit(mydata[,"Google File Name"]))))}else{35},
            if(max(nchar(na.omit(mydata[,"Account Number"]))) > 14){min(28, max(nchar(na.omit(mydata[,"Account Number"]))))}else{14},
            if(max(nchar(na.omit(mydata[,"Identifier In Statement"]))) > 14){min(28, max(nchar(na.omit(mydata[,"Identifier In Statement"]))))}else{14},
            13,
            13
  )
  ,
  stringsAsFactors = FALSE
  ) #myXLSXColWidths
  
  writeXLSX_banking(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, 
            colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE,
            hyperlink_google = myLinks_paste, hypercol_google = 4,
            hyperlink_bank = myLinks_bank, hypercol_bank = 1)
  myemailfiles <- file.path(myReportPath, myFN)
  
  #upload file to Google directory folder (converted to Google Sheet instead of Excel), overwrite previous file if present
  #rs_upload <- drive_upload(myemailfiles, path = myFolder_directory$id[[1]], type = "spreadsheet", overwrite = TRUE)
  rs_upload <- drive_put(myemailfiles, path = myFolder_directory$id[[1]], name = myFN_noEXT, type = drive_mime_type("spreadsheet"))
  bodytext <- paste0("<p><h3>", paste0(myReportName, " For ", rpt.start, "-", rpt.end, " Transactions"), "</h3>",
                     "</p>",
                     "<p>The attached Excel file is a directory of the stores to download ",
                     "bank transactions for. Transactions should be pulled on ", email.executedate, 
                     " and <strong>DOWNLOADED FOR ", email.start, " to ", email.end, 
                     " and pasted into the Google Sheets at the links provided</strong>. ",
                     "Any previously loaded transactions WITHIN this date range ",
                     "will be deleted if ANY new transactions are present for the location ",
                     "in this load.</p>",
                     "<p>A copy of this file has also been created as ",
                     "<a href=\"", paste0(gDrv_sheet_prefix, rs_upload$id[[1]]), "\">", 
                     rs_upload$name[[1]], "</a> ",
                     " in the ",
                     "<a href=\"", paste0(gDrv_folder_prefix, myFolder_directory$id[[1]]), "\">", 
                     paste0(drv_get_main$name[[1]], "/", myFolder_directory$name[[1]]), "</a> ",
                     " folder if you prefer to work with it in your browser. </p>",
                     "<p>To access the needed Google files, you must be logged into Google ",
                     "under a user that is a member of the ", norm_recip, " email group. Use the ",
                     "hyperlinks in the directory file (Excel or Google Sheet version), ",
                     "otherwise the files are located in the ",
                     "<a href=\"", paste0(gDrv_folder_prefix, myFolder_year$id[[1]]), "\">", 
                     paste0(drv_get_main$name[[1]], "/", myFolder_year$name[[1]]), "</a> ",
                     " folder. </p>",
                     "<p>If there are any bank changes (store switching banks, new bank) ",
                     "please contact me with the new information A.S.A.P. so that can ",
                     "be loaded. If the new info is needed this week, another version of this ",
                     "file and email will be generated when that's complete (use that version). <br>",
                     "<em>(the Google version linked to above will always be the most ",
                     "recent version that has been generated <b>for this week</b>)</em></p>",
                     "<br>",
                     norm_sig
  )
  rs <- mailsend(recipient = norm_recip,
                 subject = paste0(myReportName, " For ", rpt.start, "-", rpt.end),
                 body = bodytext,
                 if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                 inline = FALSE,
                 test = testing_emails, testrecipient = test_recip
  )
  
  myemailfiles <- NA
  rm(mydata)
  
}

#********: 
#DBI::dbDisconnect(myOracleDB)
DBI::dbDisconnect(mySfDB)
