library(xtable)
library(reshape2)
library(dplyr)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)
library(tidyr)
library(purrr)
library(openxlsx)
library(DBI)
library(ROracle)
library(odbc)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20230616

### 20230616 change:
### adds column to df for YourFare if not present in gSht


### 20230329 change:
### new file based on LEGACY_Portfolio_Details_import_update - 20230328.R


# Parameters
myReportName <- "<PERSON>'s 3rd Party Management"
scriptfolder <- "MARCOS_3rd_Party_Mgmt"
rptfolder <- "reports"
gSht_auth_email <- "<EMAIL>"
#https://docs.google.com/spreadsheets/d/1bwHw4veu5ITupVTInScGfHtTPLGJgka0fAVSiPlA6Ho/edit#gid=109859760
gSht_mainURL <-'https://docs.google.com/spreadsheets/d/1bwHw4veu5ITupVTInScGfHtTPLGJgka0fAVSiPlA6Ho/'
mySheets <- c("Store List")

logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
okaytocontinue <- TRUE


# NOTE myColNames order dictates column order in resulting dataframe when filtered
# myColNames are the needed names in source Google Sheet
myColNames <- c(
  "ST",
  "DoorDash",
  "GrubHub",
  "UberEats",
  "EzCater",
  "BiteSquad",
  "EatStreet",
  "YourFare"
  #,"TEST DOESN'T EXIST"
)
myColName_bldg <- c("ST")
myColName_bldgid_New <- "ST"
#myColNames_New are Oracle db column names
myColNames_New <- c(
  "ST",
  "DOORDASH",
  "GRUBHUB",
  "UBEREATS",
  "EZCATER",
  "BITESQUAD",
  "EATSTREET",
  "YOURFARE"
)


#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
mySchema <- "STEVE"
myTable <- "MP_3RD_PARTY_MGMT"
myTableName <- paste(mySchema, myTable, sep = ".")

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC & central instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}


### define some functions ###

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, " <<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

find_hdr_row <- function(mydf, hdr_colnames){
  output <- 0
  if(nrow(mydf) > 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames) %>% apply(., 1, sum)
    if(max(myhdr_matches) > 0){
      output <- myhdr_matches %>% which.max(.)
    }
  }
  if(nrow(mydf) == 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames)
    if(sum(myhdr_matches) > 0){output <- 1}
  }
  return(output)
}

nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
}





#--Query existing table data to compare to new run (will preserve old data in case of error)--#
if(okaytocontinue){
  myquery <- paste0("select 
                      *
                    from ", myTableName, "
                    order by ST"
  )
  priordata <- dbGetQuery(myOracleDB, myquery)
  myXLSXColWidths <- data.frame (colname  = names(priordata)
                                 ,
                                 width = c(rep(15,ncol(priordata)))
                                 ,
                                 stringsAsFactors = FALSE
  ) #myXLSXColWidths
  mySN <- format(Sys.Date(),'%b-%d-%Y')
  myFN <- paste0(myTable, " Prior Data", ".xlsx")
  writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = priordata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
  myemailfiles <- c(file.path(myReportPath, myFN))
}


#--Query new data from Google Sheet--#
# check google sheet status
if(okaytocontinue){
  
  gs4_auth(email = gSht_auth_email)
  gSht_get <- gs4_get(gSht_mainURL)
  
  if(length(gSht_get) > 2){
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>There weren't any sheets with the expected names (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: No sheets with expected names"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Sheet Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}


#Get Google sheet data
if(okaytocontinue){
  
  #read sheet into temp df
  gSht_Curr <- read_sheet(gSht_get$spreadsheet_id, sheet = mySheets[[1]])
  #check if "YourFare" is present
  if("YourFare" %notin% names(gSht_Curr)){gSht_Curr$YourFare <- as.character(NA)}
  Sys.sleep(2)
  if(names(gSht_Curr)[[1]]=='...1'){
    #look for header row using find_hdr_row (myColNames defined at beginning of routine)
    myhdr_row <- find_hdr_row(mydf = gSht_Curr, hdr_colnames = myColNames)
    
    if(myhdr_row > 0){
      #header row found, re-read data
      gSht_Curr <- range_read(gSht_get$spreadsheet_id, sheet = mySheets[[1]], skip = myhdr_row)
    }else{
      myhdr_row <- 0
    }
  }
  #check that expected column names are present
  gSht_missing_colnames <- setdiff(myColNames,names(gSht_Curr))
  if(length(gSht_missing_colnames)>0){
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "one or more expected column names could not be found ",
                       "in the '", myReportName, "' routine! </p>",
                       "<p><b>The routine is aborting without an update.</b></p> ",
                       "<p>Check the '", gSht_Sheets[[1]], "' Google sheet for the ",
                       "following columns expected, but NOT found ",
                       "(check for case or spelling): <br><br><b>",
                       paste(gSht_missing_colnames, collapse = "<br>"),
                       "</b></p>",
                       warn_sig
    )
    mailsend(warn_recip,
             paste0("Issue: Issue with Data in Google Sheet"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



#upload data into Oracle table
if(okaytocontinue){
  #filter sheet data to needed columns and order...rename to match oracle
  mydata_upload <- gSht_Curr[,myColNames]
  names(mydata_upload) <- myColNames_New
  #remove potentially blank rows
  st_valid <- do.call(rbind, lapply(mydata_upload[, 1], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
  mydata_upload <- mydata_upload[st_valid,]
  
  
  #gather initial table row count and attempt delete of old data
  myquery_cnt <- paste0(
    "
        select count(*)
        from ", myTableName, "
        "
  )
  rs_cnt <- dbSendQuery(myOracleDB, myquery_cnt)
  select_cnt <- dbFetch(rs_cnt, n = -1)
  myquery_delete <- paste0(
    "
        delete from ", myTableName, "
        where ST is not NULL
        "
  )
  rs_del <- dbSendQuery(myOracleDB, myquery_delete)
  send_warning <- FALSE
  if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(myOracleDB)
    #do not load since previous trans deletion failed
    send_warning <- TRUE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "data for the '", myTableName, "' table was not updated ",
                       "in the '", myReportName, "' routine! </p>",
                       "<p><b>The routine was unable to delete existing data ",
                       "in the table so it aborted the table update.</b></p> ",
                       "<p>It will attempt to update the Google Drive sheet ",
                       "with the updated data.</p>",
                       warn_sig
    )
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    dbCommit(myOracleDB)
    #populate Oracle
    rs_write <- dbWriteTable(myOracleDB, myTable, mydata_upload, row.names = FALSE , append = TRUE, schema = mySchema)
    dbCommit(myOracleDB)
    #compare oracle count of rows vs dataframe rows to ensure all rows loaded
    myload_numrows <- dbGetQuery(myOracleDB, myquery_cnt)
    mydata_numrows <- nrow(mydata_upload)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, delete and try to restore previous data
      send_warning <- TRUE
      rs_del <- dbSendQuery(myOracleDB, myquery_delete)
      dbCommit(myOracleDB)
      rs_write <- dbWriteTable(myOracleDB, myTable, priordata, row.names = FALSE , append = TRUE, schema = mySchema)
      dbCommit(myOracleDB)
      myload_numrows <- dbGetQuery(myOracleDB, myquery_cnt)
      mydata_numrows <- nrow(priordata)
      if(myload_numrows != mydata_numrows){
        #warn that load and restore apparently failed, write date specific copy of prior data as well
        myXLSXColWidths <- data.frame (colname  = names(priordata)
                                       ,
                                       width = c(rep(15,ncol(priordata)))
                                       ,
                                       stringsAsFactors = FALSE
        ) #myXLSXColWidths
        myFN <- paste0(myTable, " Prior Data - ",mySN, ".xlsx")
        writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = priordata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
        bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                           "data for the '", myTableName, "' table failed to load properly ",
                           "in the '", myReportName, "' routine! </p>",
                           "<p><b>The routine attempted to restore the previous ",
                           "data in the table but that also seems to have failed.</b></p> ",
                           "<p>Check the attached 'Failures' file for improper data that ",
                           "might have caused the load to fail. A file with prior ",
                           "data from the table (which failed to restore) is also attached.</p>",
                           warn_sig
        )
        
      }else{
        #warn that load failed, but prior data restored
        bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                           "data for the '", myTableName, "' table failed to load properly ",
                           "in the '", myReportName, "' routine! </p>",
                           "<p><b>The routine WAS ABLE to restore the previous ",
                           "data in the table, but that data is likely out-of-date.</b></p> ",
                           "<p>Check the attached 'Failures' file for improper data that ",
                           "might have caused the load to fail. A file with the prior ",
                           "data from the table is also attached for comparison.</p>",
                           warn_sig
        )
      }
      
      #create excel files of data
      #specify report column widths where alternate width desired
      myXLSXColWidths <- data.frame (colname  = names(mydata_upload)
                                     ,
                                     width = c(rep(15,ncol(mydata_upload)))
                                     ,
                                     stringsAsFactors = FALSE
      ) #myXLSXColWidths
      mySN <- format(Sys.Date(),'%b-%d-%Y')
      myFN <- paste0(myTable, " Failures - ",mySN, ".xlsx")
      writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata_upload, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
      myemailfiles <- c(myemailfiles, file.path(myReportPath, myFN))
    }
    
  }
  
  if(send_warning){
    #send email of failure if appropriate
    mailsend(warn_recip,
             paste0("Issue: Issue with Oracle table load"),
             bodytext,
             attachment = myemailfiles,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
}




