library(xtable)
library(reshape2)
library(dplyr)
library(lubridate)
library(formattable)
library(data.table)
#********: library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)
library(tidyr)
library(openxlsx)
library(DBI)
#********: library(ROracle)
library(odbc)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version ********

### ******** change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### Updated signature for emails to standard requested by <PERSON> in March 2024

### 20230821 change:
### revised how routine checks if first row of Portfolio Details sheet has expected
### headers or if it should search other rows. Previously only checked other rows 
### if cell <PERSON> was empty, but will now check if that value is NOT in expected headers 

### 20230417 change:
### added vacant suite count to MRI data

### 20230404 change:
### fixed available sq ft when building vacant (occupied sqft is NULL)

### 20230329 change:
### fixed NULL from showing up in Zoning and Major Retailers columns

### 20230324 change:
### new file based on LEGACY_PROPERTY_ASSIGNMENTS_upload - 20230217.R



# Parameters
myReportName <- "LCP Portfolio Details - import and update"
scriptfolder <- "LEGACY_Portfolio_Details"
rptfolder <- "reports"
gSht_auth_email <- "<EMAIL>"
#https://docs.google.com/spreadsheets/d/1lR6RQEZ8-psW9kdkm_UZcvKwj0K6AjUObIvbardk3y0/edit#gid=0
gSht_mainURL <-'https://docs.google.com/spreadsheets/d/1lR6RQEZ8-psW9kdkm_UZcvKwj0K6AjUObIvbardk3y0/'

mri.date.text <- format(as.Date(Sys.Date()), "%Y%m%d")

logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
okaytocontinue <- TRUE

mySheets <- c("Portfolio Details")


# NOTE myColNames order dictates column order in resulting dataframe when filtered
# myColNames are the names in source Google Sheet
myColNames <- c(
  "PROPERTY",
  "Address",
  "City",
  "State",
  "RM",
  "DM",
  "Other Tenants in Property",
  "Property Type",
  "BTS AVAILABLE?",
  "Vacant Suite Count",
  "Sq. Ft. Available",
  "GLA",
  "Population 1 mile",
  "Population 3 mile",
  "Population 5 minute drive",
  "Median Househould Income 1 mile",
  "Median Househould Income 3 mile",
  "Median Househould Income 5 minute drive",
  "County Growth Rate 1 mile",
  "County Growth Rate 3 mile",
  "County Growth Rate 5 minute drive",
  "Traffic Counts",
  "Property Size (Acreage)",
  "Zoning",
  "Amenities",
  "Anchor Type",
  "Anchor Type Responses",
  "Major Retailers/Anchors within 1 mile",
  "Signalized Intersection",
  "Drive thru",
  "Drive thru capability",
  "Businesses not allowed \n(ie liquor, smoke/vape, cannabis)",
  "City incentives"
  #,"TEST DOESN'T EXIST"
)
myColName_bldg <- c("PROPERTY")
gSht_bldg_colname_New <- "BLDG"
myColName_bldgid_New <- "BLDGID"
#myColNames_New are db column names
myColNames_New <- c(
  "BLDG",
  "BLDGID",
  "ADDRESS",
  "CITY",
  "STATE",
  "RM",
  "DM",
  "TENANTS",
  "PROP_TYPE",
  "BTS_AVAIL",
  "VACANT_SUITES",
  "SQFT_AVAIL",
  "SQFT_GLA",
  "POP_1_MILE",
  "POP_3_MILE",
  "POP_5_MIN_DRIVE",
  "HH_INCOME_1_MILE",
  "HH_INCOME_3_MILE",
  "HH_INCOME_5_MIN",
  "CTY_GROWTH_RATE_1_MILE",
  "CTY_GROWTH_RATE_3_MILE",
  "CTY_GROWTH_RATE_5_MIN",
  "TRAFFIC_COUNTS",
  "PROP_SIZE_ACREAGE",
  "ZONING",
  "AMENITIES",
  "ANCHOR_TYPE",
  "ANCHOR_TYPE_RESPONSES",
  "MAJOR_RETAILERS_1_MILE",
  "SIGNALIZED_INTERSECTION",
  "DRIVE_THRU",
  "DRIVE_THRU_CAPABILITY",
  "BUSINESSES_NOT_ALLOWED",
  "CITY_INCENTIVES"
)


#ROracle connection
#******** disabled: Sys.setenv(TZ='America/Chicago')
#Sys.setenv(ORA_SDTZ='America/Chicago')
#drv <- dbDriver("Oracle")
#connect.string <- paste0(
#  "(DESCRIPTION=",
#  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
#  "(CONNECT_DATA=(SID=", "fvpa", ")))"
#)
#********: myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

#********: mySchema <- "STEVE"
mySchema <- "CORPORATE"
myTable <- "LCP_PORTFOLIO_DETAILS"
myTableName <- paste(mySchema, myTable, sep = ".")

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- file.path(logpath, rptfolder)


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    #Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}



check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}

find_hdr_row <- function(mydf, hdr_colnames){
  output <- 0
  if(nrow(mydf) > 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames) %>% apply(., 1, sum)
    if(max(myhdr_matches) > 0){
      output <- myhdr_matches %>% which.max(.)
    }
  }
  if(nrow(mydf) == 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames)
    if(sum(myhdr_matches) > 0){output <- 1}
  }
  return(output)
}

nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext
    )
  }
}



#--Query existing table data to compare to new run--#
if(okaytocontinue){
  myquery <- paste0("select 
                      *
                    from ", myTableName, "
                    order by BLDG"
  )
  #priordata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  #********: priordata <- dbGetQuery(myOracleDB, myquery)
  priordata <- dbGetQuery(mySfDB, myquery)
  myXLSXColWidths <- data.frame (colname  = names(priordata)
                                 ,
                                 width = c(rep(15,ncol(priordata)))
                                 ,
                                 stringsAsFactors = FALSE
  ) #myXLSXColWidths
  mySN <- format(Sys.Date(),'%b-%d-%Y')
  myFN <- paste0(myTable, " Prior Data", ".xlsx")
  if(nrow(priordata)>0){
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = priordata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- c(file.path(myReportPath, myFN))
  }

}


#--Query new data from Google Sheet--#
# check google sheet status
if(okaytocontinue){
  
  gs4_auth(email = gSht_auth_email)
  gSht_get <- gs4_get(gSht_mainURL)
  
  if(length(gSht_get) > 2){
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>There weren't any sheets with the expected names (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " Issue: No sheets with expected names"),
               body = bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Sheet Access Issue"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}



### Get CORPORATE.LCP_ASSIGNMENTS_SEAN data needed for updates
if(okaytocontinue){
  update_assign <- FALSE
  myTable_assignments <- "CORPORATE.LCP_ASSIGNMENTS_SEAN"
  myquery <- paste0(
    "
      select
        BLDG
      , BLDGID
      , RM
      , SUPPORT_PROP_MGMT
      FROM ", myTable_assignments, "
      ORDER BY BLDG
    "
  )
  #********: mydata_assign <- dbGetQuery(myOracleDB, myquery)
  #NOTE: LCP_ASSIGNMENTS_SEAN maintained by another .R script
  mydata_assign <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydf_rows(mydata_assign, MinNumRows = 5, ReportName = myReportName)
  if(mydata_status[[1]]){
    update_assign <- TRUE
  }else{
    #error with assignments, send warning (re-use existing)
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine.</p>",
                       "<p>There appears to be an issue getting assignment data from ",
                       "the '", myTable_assignments, "' table. The routine will ",
                       "continue and <b>use existing data from the Google Sheet</b> ", 
                       "instead of updated info from the table.</p> ",
                       warn_sig
    )
    #send mail 
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Missing assignment info"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



### Get MRI data needed for updates
if(okaytocontinue){
  myquery <- paste0(
    "
      SELECT /* SNOWFLAKE version Portfolio Details .R script */
      	CASE WHEN TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL THEN BLDG.BLDGID ELSE CAST(NULL AS INT) END as BLDG
      ,	BLDG.BLDGID AS BLDGID
      ,	RTRIM(BLDG.ADDRESS1) AS ADDRESS
      ,	RTRIM(BLDG.CITY) AS CITY
      ,	RTRIM(BLDG.STATE) AS STATE
      ,	LISTAGG(OCC.Occupant_Name, ' | ') AS TENANTS
      ,	PTYP.DESCRPN AS PROP_TYPE
      ,	CASE WHEN BTS.BLDGID IS NOT NULL THEN 'YES' ELSE 'NO' END AS BTS_AVAIL
      , IFNULL(VACANT.VACANT_SUITES, 0) AS VACANT_SUITES
      ,	SUM(SSQF_TOT.SQFT) - IFNULL(SUM(OCC.SQFT),0) AS SQFT_AVAIL
      ,	SUM(SSQF_TOT.SQFT) AS SQFT_GLA
      FROM MRI.BLDG
      INNER JOIN MRI.ENTITY
      ON BLDG.ENTITYID = ENTITY.ENTITYID
      LEFT JOIN MRI.PTYP
      ON ENTITY.PROPTYPE = PTYP.PROPTYPE
      INNER JOIN MRI.SUIT
      ON BLDG.BLDGID = SUIT.BLDGID
      LEFT JOIN MRI.TB_CM_SUITETYPE
      ON suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
      LEFT JOIN
      (
      	    select /* standard active leases and holdovers where exec date or rent start before CURRENT_DATE and end date (or stopbill date) is null or in future */
          /* pulled from RENT ROLL logic used to populate that in 'Legacy_Property Mgmt PORTFOLIO' 
      		aka Portfolio General Google sheet with minor modifications to include rent start in occupant name */
      		RTRIM(leas.BLDGID) AS Bldg_ID
          ,	RTRIM(leas.SUITID) AS SUITE
          --,	RTRIM(leas.MOCCPID) as [Master_ID]
          ,	concat(RTRIM(leas.OCCPNAME), 
                  case 
                  	--when TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' 
                  	--	then concat(' ',LTRIM(STR(MONTH(leas.RENTSTRT)))+'/'+LTRIM(STR(DAY(leas.RENTSTRT)))+'/'+STR(YEAR(leas.RENTSTRT),4)) 
      				when leas.RENTSTRT > CURRENT_DATE
      					then concat(' Rent Start: ',MONTH(leas.RENTSTRT),'/',DAY(leas.RENTSTRT),'/',YEAR(leas.RENTSTRT))
      					--MONTH(LEAS.VACATE),'/',DAY(LEAS.VACATE),'/',YEAR(LEAS.VACATE)
                  	when leas.VACATE < CURRENT_DATE
                  		then concat(' Stop Bill Date: ',MONTH(leas.STOPBILLDATE),'/',DAY(leas.STOPBILLDATE),'/',YEAR(leas.STOPBILLDATE)) 
                  end
              ) as Occupant_Name
          --,	TO_DATE(leas.RENTSTRT) as [Rent_Start]
          --,	TO_DATE(leas.EXPIR) as [Expiration]
          ,	CAST(IFNULL(case when SSQF_TYPE.SQFTTYPE = 'GLA' THEN SSQF_TYPE.SQFT END,0) AS INT) AS SQFT
          --,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE IFNULL(RENT.AMOUNT,0) END) AS [Base_Rent]
          --,	(CASE WHEN leas.COMPANYGRPID = 2 THEN NULL ELSE ROUND(IFNULL(RENT.AMOUNT,0)*12/(case when SSQF_TYPE.SQFT = 0 then 1 else SSQF_TYPE.SQFT END),2) END) AS [Rate_PSF]
          --,	(CASE WHEN leas.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE NULL END) AS [Addl_Rents]
          --,	RTRIM(leas.LEASID) AS [Lease_ID]
          --,	RTRIM(suit.ADDRESS) as [Address]
          --,	RTRIM(bldg.CITY) as [City]
          --,	RTRIM(bldg.STATE) as [State]
          --,	trim(bldg.MAP) as [MAP HYPERLINK]
          from MRI.LEAS
          left join MRI.SUIT
          on leas.BLDGID = suit.BLDGID and leas.SUITID = suit.SUITID
          left join MRI.BLDG 
          on leas.BLDGID = bldg.BLDGID
          LEFT JOIN MRI.TB_CM_SUITETYPE
              ON suit.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
          LEFT JOIN 
          (
              SELECT *
              FROM MRI.SSQF
              WHERE SSQF.EFFDATE = (
                  --SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= TO_DATE(CURRENT_DATE)
              		SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
                  )
          ) SSQF_TYPE
              ON suit.SUITID = SSQF_TYPE.SUITID
              AND suit.BLDGID = SSQF_TYPE.BLDGID
                  	
          --left join								
          --(								
          --	SELECT CMRECC.LEASID
          --	,	SUM(CMRECC.AMOUNT) AS AMOUNT
          --	FROM CMRECC
          --	JOIN LEAS ON CMRECC.LEASID = LEAS.LEASID
          --	WHERE CMRECC.EFFDATE = (
          --				SELECT MAX(IC.EFFDATE) AS EFFDATE
          --				FROM CMRECC IC 
          --				WHERE TO_DATE( LEAS.RENTSTRT) <= CURRENT_DATE
          --				AND IC.BLDGID=CMRECC.BLDGID 
          --				AND IC.LEASID=CMRECC.LEASID 
          --				AND IC.INCCAT=CMRECC.INCCAT 
          --				AND 
          --				(
          --					(IC.ENDDATE IS NULL AND IC.EFFDATE <= CURRENT_DATE)
          --					OR 
          --					IC.ENDDATE >= TO_DATE( CURRENT_DATE)
          --				) /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
          --				--AND IC.INEFFECT = 'Y'
          --		)
          --		AND CMRECC.INCCAT IN (SELECT INCCAT FROM test.dbo.BASE_RENTS)
          --		/* TEST */ --AND CMRECC.LEASID IN ('001557','002123') /*'001557','002123'*/
          --	GROUP BY
          --		CMRECC.LEASID					
          --) RENT	
          --	ON leas.LEASID = RENT.LEASID
                  
          where
              (leas.EXECDATE <= CURRENT_DATE or leas.RENTSTrt <= CURRENT_DATE or leas.OCCUPNCY <= CURRENT_DATE)
              and
              (
                  (COALESCE(leas.VACATE,leas.EXPIR) >= CURRENT_DATE or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
                  OR
                  (leas.EXPIR < CURRENT_DATE and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers */
                  OR
                  (
                  	(
                  		leas.VACATE >= CURRENT_DATE 
                  		or 
                  		(leas.VACATE < CURRENT_DATE and leas.STOPBILLDATE is NULL) /* vacated, but paying rent */
                  		or 
                  		leas.STOPBILLDATE >= CURRENT_DATE
                  	)
                  	AND leas.OCCPSTAT != 'I'
                  )
              )
              and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
      	/* TEST ONLY */ --and leas.BLDGID = '0655'
      	--order by leas.BLDGID, convert(int, IFNULL(SSQF_TYPE.SQFT,0)) desc
      ) OCC
      on SUIT.BLDGID = OCC.BLDG_ID
      AND SUIT.SUITID = OCC.SUITE
      
      LEFT JOIN
      (			
      	--SET rptdt = CURRENT_DATE;  /*TEST ONLY*/
      	SELECT SSQF.BLDGID		
      	,	SSQF.SUITID	
      	,	SSQF.SQFTTYPE	
      	,	TO_DATE(SSQF.EFFDATE) as EFFDATE	
      	,	SSQF.SQFT
      	,	SSQF.LSDSQFT
      	,	SSQF.LASTDATE	
      	,	SSQF.USERID	
      	FROM MRI.SSQF
      	WHERE 
      		SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE)
      		AND SSQF.SQFTTYPE = 'GLA'		
      )SSQF_TOT
      ON SUIT.BLDGID = SSQF_TOT.BLDGID
      AND SUIT.SUITID = SSQF_TOT.SUITID
      
      LEFT JOIN
      (			
      	SELECT SUIT.BLDGID
      	, COUNT(*) AS BTS_CNT
      	FROM MRI.SUIT 
      	WHERE SUITETYPE_MRI = 'BTS'
      	GROUP BY SUIT.BLDGID		
      ) BTS
      ON BLDG.BLDGID = BTS.BLDGID
	  LEFT JOIN
	  (
  		SELECT /* VACANT SUITE COUNT */
  			SUIT.BLDGID
  		,	count(SUIT.SUITID) AS VACANT_SUITES     
  		FROM MRI.SUIT
  		LEFT JOIN MRI.BLDG
  		ON SUIT.BLDGID = BLDG.BLDGID
  		left join
  		(
  			--SET rptdt = CURRENT_DATE;  /*TEST ONLY*/
  			SELECT *
  			FROM MRI.SSQF
  			WHERE SSQF.EFFDATE = 
  				(
  					SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
  				)
  		) SSQF_TYPE
  		ON SUIT.SUITID = SSQF_TYPE.SUITID
  			AND SUIT.BLDGID = SSQF_TYPE.BLDGID
  		LEFT JOIN 
  		(--DECLARE CURRENT_DATE DATE = '20230215';
  			SELECT LEAS.*
  			FROM MRI.LEAS
  			JOIN MRI.SUIT
  			ON LEAS.BLDGID = SUIT.BLDGID
  			AND LEAS.SUITID = SUIT.SUITID
  			LEFT JOIN MRI.TB_CM_SUITETYPE
  			ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
  			WHERE 
  				--(leas.EXECDATE <= CURRENT_DATE or leas.RENTSTrt <= CURRENT_DATE)
  				leas.RENTSTrt <= CURRENT_DATE
  				and
  				(
  					(COALESCE(leas.VACATE,leas.EXPIR) >= CURRENT_DATE or COALESCE(leas.VACATE,leas.EXPIR) IS NULL)
  					OR
  					(leas.EXPIR < CURRENT_DATE and COALESCE(leas.STOPBILLDATE,leas.VACATE) IS NULL) /* Holdovers NOT counted as Vacant in MRI Rent Roll*/
  				)
  				and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
  		) LEASED
  		ON SUIT.BLDGID = LEASED.BLDGID
  		AND SUIT.SUITID = LEASED.SUITID
  		LEFT JOIN MRI.TB_CM_SUITETYPE
  		ON SUIT.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
  		WHERE LEASED.SUITID IS NULL
  			AND (BLDG.INACTIVE is NULL or BLDG.INACTIVE = 'N') /* Active Buildings only */
  			AND
  			(
  				(
  					TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL 
  					or 
  					(TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E' and TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'N') 
  				) /* Suitetypeusage is NULL or not in 'E' or 'N' */
  				or
  				(
  					TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' 
  					AND SSQF_TYPE.SQFT > 0 
  					AND SSQF_TYPE.SQFTTYPE = 'GLA'
  				)
  			)
			  AND NOT (SUIT.SUITETYPE_MRI is NULL and (COALESCE(SSQF_TYPE.SQFT,0) = 0 or SSQF_TYPE.SQFTTYPE != 'GLA') )
        GROUP BY SUIT.BLDGID
      ) VACANT
      ON BLDG.BLDGID = VACANT.BLDGID
      WHERE (BLDG.INACTIVE IS NULL OR BLDG.INACTIVE = 'N')
      and TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
      and (TB_CM_SUITETYPE.SUITETYPEUSAGE is NULL or TB_CM_SUITETYPE.SUITETYPEUSAGE <> 'E')
      GROUP BY
      	CASE WHEN TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL THEN BLDG.BLDGID ELSE CAST(NULL AS INT) END
      ,	BLDG.BLDGID
      ,	RTRIM(BLDG.ADDRESS1)
      ,	RTRIM(BLDG.CITY)
      ,	RTRIM(BLDG.STATE)
      ,	PTYP.DESCRPN
      ,	CASE WHEN BTS.BLDGID IS NOT NULL THEN 'YES' ELSE 'NO' END
      , IFNULL(VACANT.VACANT_SUITES, 0)
    "
  )
  #********: mydata_MRI <- dbGetQuery(mySSdb, myquery)
  mydata_MRI <- dbGetQuery(mySfDB, myquery)
  mydata_status <- check_mydf_rows(mydata_MRI, MinNumRows = 5, ReportName = myReportName)
  if(mydata_status[[1]]){
    update_assign <- TRUE
    #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
    mydata_MRI[] <- lapply(mydata_MRI[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
  }else{
    #error with assignments, send warning (re-use existing)
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the '", myReportName, "' routine.</p>",
                       "<p>There appears to be an issue getting MRI data from ",
                       "the database. The routine will ",
                       "continue and <b>use existing data from the Google Sheet</b> ", 
                       "instead of updated info from the table.</p> ",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0("Issue: Missing assignment info"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}





### Header row in the Google sheet is not in first row, load sheet starting with header row
if(okaytocontinue){
  
  #read sheet into temp df
  gSht_Curr <- read_sheet(gSht_get$spreadsheet_id, sheet = mySheets[[1]])
  Sys.sleep(2)
  if(names(gSht_Curr)[[1]] %notin% myColNames){
    #look for header row using find_hdr_row
    myhdr_row <- find_hdr_row(mydf = gSht_Curr, hdr_colnames = myColNames)

    if(myhdr_row > 0){
      #header row found, re-read data
      gSht_Curr <- range_read(gSht_get$spreadsheet_id, sheet = mySheets[[1]], skip = myhdr_row)
    }else{
      myhdr_row <- 0
    }
  }
  #check that expected column names are present
  gSht_missing_colnames <- setdiff(myColNames,names(gSht_Curr))
  if(length(gSht_missing_colnames)>0){
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "one or more expected column names could not be found ",
                       "in the '", myReportName, "' routine! </p>",
                       "<p><b>The routine is aborting without an update.</b></p> ",
                       "<p>Check the '", gSht_Sheets[[1]], "' Google sheet for the ",
                       "following columns expected, but NOT found ",
                       "(check for case or spelling): <br><br><b>",
                       paste(gSht_missing_colnames, collapse = "<br>"),
                       "</b></p>",
                       warn_sig
    )
    mailsend(recipient = warn_recip,
             subject = paste0("Issue: Issue with Data in Google Sheet"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



#combine Google sheet data with mydata_MRI
if(okaytocontinue){
  mydata_upload <- mydata_MRI
  
  #maybe should filter non-ASCII? https://stackoverflow.com/questions/9934856/removing-non-ascii-characters-from-data-files
  
  #assemble needed data
  if(update_assign){
    #use oracle table data for field property managers
    mydata_upload$RM <- mydata_assign$RM[match(mydata_upload$BLDGID,mydata_assign$BLDGID)]
    mydata_upload$DM <- mydata_assign$SUPPORT_PROP_MGMT[match(mydata_upload$BLDGID,mydata_assign$BLDGID)]
  }else{
    #keep existing Google sheet data for field property managers
    xx <- gSht_Curr[,c(myColName_bldg,"RM")]
    names(xx) <- c("BLDG","RM")
    xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
    xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 254))
    mydata_upload <- dplyr::left_join(mydata_upload,xx)
    
    xx <- gSht_Curr[,c(myColName_bldg,"DM")]
    names(xx) <- c("BLDG","DM")
    xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
    xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 254))
    mydata_upload <- dplyr::left_join(mydata_upload,xx)
  }
  
  xx <- gSht_Curr[,c(myColName_bldg,"Population 1 mile")]
  names(xx) <- c("BLDG","POP_1_MILE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) as.integer(x)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Population 3 mile")]
  names(xx) <- c("BLDG","POP_3_MILE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) as.integer(x)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Population 5 minute drive")]
  names(xx) <- c("BLDG","POP_5_MIN_DRIVE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) as.integer(x)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Median Househould Income 1 mile")]
  names(xx) <- c("BLDG","HH_INCOME_1_MILE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,4)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Median Househould Income 3 mile")]
  names(xx) <- c("BLDG","HH_INCOME_3_MILE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,4)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Median Househould Income 5 minute drive")]
  names(xx) <- c("BLDG","HH_INCOME_5_MIN")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,4)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"County Growth Rate 1 mile")]
  names(xx) <- c("BLDG","CTY_GROWTH_RATE_1_MILE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,5)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"County Growth Rate 3 mile")]
  names(xx) <- c("BLDG","CTY_GROWTH_RATE_3_MILE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,5)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"County Growth Rate 5 minute drive")]
  names(xx) <- c("BLDG","CTY_GROWTH_RATE_5_MIN")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,5)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Traffic Counts")]
  names(xx) <- c("BLDG","TRAFFIC_COUNTS")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) if(if(length(x)>0){!grepl("\\D", x)}else{FALSE}){x}else{NA}) %>% do.call(c, .) #strips out strings
  xx[,2] <- lapply(xx[, 2][[1]], function(x) as.integer(x)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Property Size (Acreage)")]
  names(xx) <- c("BLDG","PROP_SIZE_ACREAGE")
  xx[,2] <- lapply(xx[, 2][[1]], function(x) round(x,3)) %>% do.call(c, .)
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Zoning")]
  names(xx) <- c("BLDG","ZONING")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[xx=='NULL'] <- NA  #replace NULL with NA
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 299))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Amenities")]
  names(xx) <- c("BLDG","AMENITIES")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 999))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Anchor Type")]
  names(xx) <- c("BLDG","ANCHOR_TYPE")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 254))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Anchor Type Responses")]
  names(xx) <- c("BLDG","ANCHOR_TYPE_RESPONSES")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 254))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Major Retailers/Anchors within 1 mile")]
  names(xx) <- c("BLDG","MAJOR_RETAILERS_1_MILE")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[xx=='NULL'] <- NA  #replace NULL with NA
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 999))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Signalized Intersection")]
  names(xx) <- c("BLDG","SIGNALIZED_INTERSECTION")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 50))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Drive thru")]
  names(xx) <- c("BLDG","DRIVE_THRU")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 50))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Drive thru capability")]
  names(xx) <- c("BLDG","DRIVE_THRU_CAPABILITY")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 254))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"Businesses not allowed \n(ie liquor, smoke/vape, cannabis)")]
  names(xx) <- c("BLDG","BUSINESSES_NOT_ALLOWED")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 254))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  xx <- gSht_Curr[,c(myColName_bldg,"City incentives")]
  names(xx) <- c("BLDG","CITY_INCENTIVES")
  xx[,2] <- xx[,2] %>% mutate(across(everything(), as.character))
  xx[,2] <- sapply(xx[, 2][[1]], function(x) str_trunc(x, 999))
  mydata_upload <- dplyr::left_join(mydata_upload,xx)
  
  #re-order columns
  mydata_upload <- mydata_upload[,myColNames_New]
  
  rm(xx)
  
}






#upload data into Oracle table
if(okaytocontinue){
  
  #gather initial table row count and attempt delete of old data
  myquery_cnt <- paste0(
    "
        select count(*)
        from ", myTableName, "
        "
  )
  #********: rs_cnt <- dbSendQuery(myOracleDB, myquery_cnt)
  #********: select_cnt <- dbFetch(rs_cnt, n = -1)
  select_cnt <- dbGetQuery(mySfDB, myquery_cnt) %>% .[1,1]
  
  dbBegin(mySfDB)
  myquery_delete <- paste0(
    "
        delete from ", myTableName, "
        where BLDG is not NULL
        "
  )
  #********: rs_del <- dbSendQuery(myOracleDB, myquery_delete)
  rs_del <- dbSendQuery(mySfDB, myquery_delete)
  delete_cnt <- dbGetRowsAffected(rs_del)
  send_warning <- FALSE
  #********: if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
  if(delete_cnt != select_cnt){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    #********: dbRollback(myOracleDB)
    dbRollback(mySfDB)
    dbClearResult(rs_del)
    #do not load since previous trans deletion failed
    send_warning <- TRUE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "data for the '", myTableName, "' table was not updated ",
                       "in the '", myReportName, "' routine! </p>",
                       "<p><b>The routine was unable to delete existing data ",
                       "in the table so it aborted the table update.</b></p> ",
                       "<p>It will attempt to update the Google Drive sheet ",
                       "with the updated data.</p>",
                       warn_sig
    )
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    #********: dbCommit(myOracleDB)
    dbCommit(mySfDB)
    dbClearResult(rs_del)
    #populate DB
    #********: rs_write <- dbWriteTable(myOracleDB, myTable, mydata_upload, row.names = FALSE , append = TRUE, schema = mySchema)
    #********: dbCommit(myOracleDB)
    rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata_upload)
    #compare oracle count of rows vs dataframe rows to ensure all rows loaded
    #********: myload_numrows <- dbGetQuery(myOracleDB, myquery_cnt)
    myload_numrows <- dbGetQuery(mySfDB, myquery_cnt) %>% .[1,1]
    mydata_numrows <- nrow(mydata_upload)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, delete and try to restore previous data
      send_warning <- TRUE
      #********: rs_del <- dbSendQuery(myOracleDB, myquery_delete)
      #********: dbCommit(myOracleDB)
      dbBegin(mySfDB)
      rs_del <- dbSendQuery(mySfDB, myquery_delete)
      dbCommit(mySfDB)
      #********: rs_write <- dbWriteTable(myOracleDB, myTable, priordata, row.names = FALSE , append = TRUE, schema = mySchema)
      #********: dbCommit(myOracleDB)
      rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), priordata)
      #********: myload_numrows <- dbGetQuery(myOracleDB, myquery_cnt)
      myload_numrows <- dbGetQuery(mySfDB, myquery_cnt) %>% .[1,1]
      mydata_numrows <- nrow(priordata)
      if(myload_numrows != mydata_numrows){
        #warn that load and restore apparently failed, write date specific copy of prior data as well
        myXLSXColWidths <- data.frame (colname  = names(priordata)
                                       ,
                                       width = c(rep(15,ncol(priordata)))
                                       ,
                                       stringsAsFactors = FALSE
        ) #myXLSXColWidths
        myFN <- paste0(myTable, " Prior Data - ",mySN, ".xlsx")
        writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = priordata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
        bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                           "data for the '", myTableName, "' table failed to load properly ",
                           "in the '", myReportName, "' routine! </p>",
                           "<p><b>The routine attempted to restore the previous ",
                           "data in the table but that also seems to have failed.</b></p> ",
                           "<p>Check the attached 'Failures' file for improper data that ",
                           "might have caused the load to fail. A file with prior ",
                           "data from the table (which failed to restore) is also attached.</p>",
                           warn_sig
        )
        
      }else{
        #warn that load failed, but prior data restored
        bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                           "data for the '", myTableName, "' table failed to load properly ",
                           "in the '", myReportName, "' routine! </p>",
                           "<p><b>The routine WAS ABLE to restore the previous ",
                           "data in the table, but that data is likely out-of-date.</b></p> ",
                           "<p>Check the attached 'Failures' file for improper data that ",
                           "might have caused the load to fail. A file with the prior ",
                           "data from the table is also attached for comparison.</p>",
                           warn_sig
        )
      }
      
      #create excel files of data
      #specify report column widths where alternate width desired
      myXLSXColWidths <- data.frame (colname  = names(mydata_upload)
      ,
      width = c(rep(15,ncol(mydata_upload)))
      ,
      stringsAsFactors = FALSE
      ) #myXLSXColWidths
      mySN <- format(Sys.Date(),'%b-%d-%Y')
      myFN <- paste0(myTable, " Failures - ",mySN, ".xlsx")
      writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata_upload, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
      myemailfiles <- c(myemailfiles, file.path(myReportPath, myFN))
    }
    
  }
  
  if(send_warning){
    #send email of failure if appropriate
    mailsend(recipient = warn_recip,
             subject = paste0("Issue: Issue with database table load"),
             body = bodytext,
             attachment = myemailfiles,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
}



### Populate Google sheet
if(okaytocontinue){
  #drop BLDGID column used in table but not Google sheet
  mydata_upload_gSht <- mydata_upload[,!(names(mydata_upload) %in% myColName_bldgid_New)]
  
  #clear previous data
  range_clear(gSht_get$spreadsheet_id, sheet = mySheets[[1]], range = cell_rows(c(myhdr_row + 2, NA)), reformat = FALSE)
  Sys.sleep(2)
  #check if sheet will hold data
  gSht_prop <- sheet_properties(ss = gSht_get$spreadsheet_id)
  gSht_lastrow <- gSht_prop[which(gSht_prop$name == mySheets[[1]]), "grid_rows"][[1]]
  gSht_lastcol <- gSht_prop[which(gSht_prop$name == mySheets[[1]]), "grid_columns"][[1]]
  gSht_needrows <- nrow(mydata_upload_gSht) + myhdr_row + 1
  gSht_needcols <- ncol(mydata_upload_gSht) + 3
  if(gSht_needrows < gSht_lastrow | ncol(mydata_upload_gSht)){
    sheet_resize(ss = gSht_get$spreadsheet_id, 
                 sheet = mySheets[[1]], 
                 nrow = gSht_needrows, 
                 ncol = gSht_needcols, 
                 exact = FALSE)
    gSht_lastrow <- gSht_needrows
    
  }
  #write new data
  range_write(ss = gSht_get$spreadsheet_id, 
              data = mydata_upload_gSht, 
              sheet = mySheets[[1]], 
              range = cell_rows(c(myhdr_row + 2, myhdr_row + 1 + nrow(mydata_upload_gSht))), 
              col_names = FALSE,
              reformat = FALSE
  )
  Sys.sleep(2)
  
  
  #verify written data row count
  gSht_check_row <- myhdr_row + nrow(mydata_upload_gSht)
  gSht_postwrite <- range_read(
    gSht_get$spreadsheet_id, 
    sheet = mySheets[[1]], 
    range = cell_limits(c(gSht_lastrow - 1, 1), c(gSht_lastrow, 1))
  )
  gSht_lastBLDG <- gSht_postwrite[1,1][[1]]
  mydata_lastBLDG <- mydata_upload_gSht[nrow(mydata_upload_gSht), 1][[1]]
  if(gSht_lastBLDG != mydata_lastBLDG){
    #send warning that data may not have written correctly
    bodytext <- paste0("<p>This is an automated email to inform you that it appears that ",
                       "data for the  '", myReportName, "' routine <em>may</em> not have ",
                       "written to the '", mySheets[[1]], "' Google sheet correctly! </p>",
                       "<p>The last row in the data to upload had building ", mydata_lastBLDG,
                       " and it looks like the last row in the sheet (", gSht_lastrow, ") might have ", 
                       gSht_lastBLDG,
                       ". Review for possible errors.</p>",
                       warn_sig
    )
    
    #send email of failure if appropriate
    mailsend(recipient = warn_recip,
             subject = paste0("Issue: Issue with Google Sheet load"),
             body = bodytext,
             attachment = myemailfiles,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}








