library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(tidyverse)
library(dplyr)
#library(data.table)
#library(utils)
library(DBI)
library(ROracle)
library(googledrive)
library(googlesheets4)
library(keyring)

# written by <PERSON> August 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20240919

### 20240917 change:  ####-----------TESTING, NOT READY FOR PROD YET-----------###
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message()
### updated email signature to use latest format provided by <PERSON> earlier in 2024
### updated 'test' computer path assignments

### 20240401 change:
### added 'User ID' GSheet column and AC_EMAIL.PAYNUM to load

### 20230511 change:
### new file


# Parameters
options(stringsAsFactors = FALSE)

myReportName <- "Google User Emails to Oracle"
scriptfolder <- "HV_Google_Emails"
rptfolder <- "reports"
msg_text <- paste0("Routine Starting: ", myReportName)
base::message(msg_text)
Sys.sleep(2)
centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles")
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))

okaytocontinue <- TRUE

# Google sheets parameters, create empty df then add rows with needed info
gSht_info <- data.frame(FN=character(), F_ID=character(), SN=character(), S_GID=numeric())
gSht_info <- gSht_info %>% add_row(FN = "Workspace User Emails and Bulk Delete", F_ID = "1SjqqNRKSV2N25Z4GA53W4L-qCFgVU15faXt0qiOn7eA", SN = "All User Emails", S_GID = 149984372 )
#gSht_info <- gSht_info %>% add_row(FN = "", F_ID = "", SN = "", S_GID =  )
#gSht_info <- gSht_info %>% add_row(FN = as.character(NA), F_ID = as.character(NA), SN = as.character(NA), S_GID = as.numeric(NA) )

gSht_auth_email <- "<EMAIL>"

sig_logo <- FALSE

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  mainpath <- centralPath
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}


logpath <- file.path(mainpath,scriptfolder)
myReportPath <- file.path(mainpath, rptfolder)


### define some functions ###

# ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
myOracleDB_deanna <- dbConnect(drv, username = "deanna", password =  key_get("Oracle", "deanna"), dbname = connect.string)

mySchema <- "STEVE"


#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get normal email signature###
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)



# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isFile <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  #gs4_auth(email = gSht_auth_email)
  if (gs4_has_token()) {
    #auth okay, check if file IDs and sheets are found
    gSht_fnd_ID_cnt <- 0
    gSht_F_IDs <- unique(gSht_info$F_ID[!is.na(gSht_info$F_ID)])
    if(length(gSht_F_IDs)>0){
      #check file IDs and then metadata for needed sheets
      for(i in 1:length(gSht_F_IDs)){
        #gSht_get <- gs4_get(as_id(gSht_info$F_ID[i]))
        drv_get_check <- drive_get(id = as_id(gSht_F_IDs[i]))
        isFile <- drv_get_check$drive_resource[[1]]$mimeType == drive_mime_type("spreadsheet")
        if(isFile){
          #check if sheet names are in file
          gSht_get <- gs4_get(as_id(gSht_F_IDs[i]))
          gSht_sheets <- gSht_info$SN[which(gSht_info$F_ID == gSht_F_IDs[i])]
          diff <- setdiff(gSht_sheets, gSht_get$sheets$name)
          if(!is_empty(diff)){
            okaytocontinue <- FALSE
            #send warning email that sheet doesn't exist
            bodytext <- paste0(
              "<p>This is an automated email to inform you that it appears there ",
              "is at least one missing sheet needed in the ",
              myReportName, " routine! ",
              "<p>The routine is aborting without an update.</p> ",
              "<b>Google Sheet Info:</b><ul>",
              "<li>Google sheet filename: ", gSht_get$name, "</li>",
              "<li>Sheetname(s) NOT FOUND in file above:<b> ", paste0(diff, collapse = '; '), "</b></li>",
              "</ul></p><br>",
              warn_sig
            )
            #send mail
            mailsend(warn_recip,
                     paste0(myReportName, " Issue: Google File Issue"),
                     bodytext,
                     attachment = NULL,
                     test = testing_emails, testrecipient = test_recip
            )
            break
          }
          gSht_fnd_ID_cnt <- gSht_fnd_ID_cnt + 1
        }else{
          #desired file not found, abort
          okaytocontinue <- FALSE
          #send warning email that sheet doesn't exist
          bodytext <- paste0(
            "<p>This is an automated email to inform you that it appears there ",
            "is at least one missing file needed in the ",
            myReportName, " routine! ",
            "<p>The routine is aborting without an update.</p> ",
            "<b>Google Sheet Info:</b><ul>",
            "<li>Google sheet ID not found:<b> ", gSht_F_IDs[i], "</b><</li>",
            "</ul></p><br>",
            warn_sig
          )
          #send mail
          mailsend(warn_recip,
                   paste0(myReportName, " Issue: Google File Issue"),
                   bodytext,
                   attachment = NULL,
                   test = testing_emails, testrecipient = test_recip
          )
          break
        }
      }
      
    }else{
      #no IDs found in gSht_info dataframe, abort
      okaytocontinue <- FALSE
      
    }
    
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update.</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}


#Get AC_EMAIL data (for associated paynums)
if(okaytocontinue){
  myquery <- paste0(
    "select email, paynum from ac_email
    "
  )
  ac_email <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(mydf = ac_email, MinNumRows = 1, ReportName = myReportName)
  if(!mydata_status[[1]]){
    #load failed, create empty df
    ac_email <- data.frame(EMAIL = character(), PAYNUM = numeric())
  }
}


#Load Google Sheet data
if(okaytocontinue){
  for(i in 1:nrow(gSht_info)){
    if(complete.cases(gSht_info[i, c("FN","SN")])){
      curr_SN <- gSht_info$SN[i]
      if(curr_SN == "All User Emails"){
        mydata <- range_read(ss = gSht_info$F_ID[i], 
                             sheet = curr_SN
        )
        
        old_names <- c(
          "Email",
          "User Name",
          "Type (P-Primary, A-Alias)",
          "User ID"
        )
        new_names <- c(
          "EMAIL",
          "USER_NAME",
          "TYPE",
          "ID"
        )
        
        setnames(mydata, 
                 old = old_names, 
                 new = new_names,
                 skip_absent = TRUE)
        mydata <- mydata[,new_names]
        
        mydata_status <- check_mydf_rows(mydf = mydata, MinNumRows = 1, ReportName = myReportName)
        mySchema <- "STEVE"
        myTable <- "GOOGLE_USER_EMAILS"
        myTableName <- paste(mySchema, myTable, sep = ".")
        
        #join gmail ID to ac_email
        ac_email <- ac_email %>%
          left_join(mydata[,c('EMAIL','ID')], by = c("EMAIL" = "EMAIL"))
        #join paynum to mydata by ID (primary and aliases for any given user acct have same ID)
        mydata <- mydata %>%
          left_join(ac_email[,c("ID","PAYNUM")], by = c("ID" = "ID"))
        
      }else{
        okaytocontinue <- FALSE
      }
    }
  }
}


if(okaytocontinue && mydata_status[[1]]){
  #truncate Oracle table
  myquery <- paste0('truncate table ', myTableName, ' drop storage')
  myTrucResults <- dbGetQuery(myOracleDB, myquery)
  dbCommit(myOracleDB)
  
  #populate Oracle
  rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
  dbCommit(myOracleDB)
  #compare row count of the data frame to Oracle to ensure all rows inserted
  myquery <- paste0(
    "select count(*)
        from ", myTableName
  )
  oracle_checksum <- dbGetQuery(myOracleDB, myquery)
  
  mydata_checksum <- nrow(mydata)
  
  
  if(round(oracle_checksum[[1]],2) != round(mydata_checksum,2)){
    #send warning that sums are different
    # create body of warning email
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       " Oracle load. The count of rows in the '", curr_SN, "' Google sheet was: <br/>",
                       round(mydata_checksum,2), "<br/><br/>",
                       "The count of rows in the ", myTableName, " table is: <br/>",
                       round(oracle_checksum[[1]],2), "<br/><br/>",
                       "The query is contained in the ", myReportName, " script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Mis-match of Google Sheet rows vs. Oracle load"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
  
  
  
  
}