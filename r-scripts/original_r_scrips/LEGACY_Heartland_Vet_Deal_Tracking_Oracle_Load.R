library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(dbplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(openxlsx)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(stringi)
library(readr)
library(tidyr)
library(utils)
library(keyring)
library(DBI)
library(odbc)
library(ROracle)
library(googlesheets4)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20240926

### 20240926 change:
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### replaced Signature logo from local file to published image URL (to avoid inline image attachment)

### 20231207 change:
### columns were removed from source, mirror that in table and this routine

### 20221104 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

gSht_auth_email = "<EMAIL>"
scriptfolder <- "LEGACY_Property_Submittal_Responses"
rptfolder <- "reports"
myReportName <- "Heartland Vet Deal Tracking Load"
mySheets <- c("Heartland Vet Deal Tracking")
mySheetIDs <- c(1818624179)
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")

#SSMS connection
#mySSdb <- dbConnect(odbc(),
#                 Driver = "ODBC Driver 17 for SQL Server",
#                 Server = "************",
#                 Database = "HIGHLANDPROD",
#                 UID = "SteveO_ro",
#                 PWD = key_get("MRI_bak", "SteveO_ro"),
#                 Port = 1433)

#Oracle connection
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password = key_get("Oracle", "steve"), dbname = connect.string)
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')

# email parameters: recipient(s) of warning emails and signatures
norm_recip <- c("<EMAIL>")
#norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>", "<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
#NOTE: gmailr masks the base function message() and causes errors, change to explicit base::message("Your message here")
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)
#gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

#append signature logo
#(HVLTD Corp with Brands)
sig_image_src <- '<img style="" src="https://uploads-ssl.webflow.com/63bc8dbf9954f445c139e9d3/65242d848ffc66ee9e2767c4_hv-logos.png" width="337" height="">'
if(exists("norm_st_from")){norm_st_from <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_sig")){norm_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", sig_image_src)}
if(exists("norm_RM_DM_from")){norm_RM_DM_from <- paste0(norm_RM_DM_from, "<br/>", sig_image_src)}


if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010" || Sys.getenv("COMPUTERNAME") == "LAPTOPTOSHIBA13"){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
}

myReportPath <- file.path(logpath, rptfolder)



check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- NA
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


invalid_cols <- function(testdf, col_list){
  my_results <- c("Data missing or no column names/indices supplied")
  if(is.data.frame(testdf) & length(col_list) > 0){
    my_results <- c()
    max_col_num <- ncol(testdf)
    col_names <- names(testdf)
    #col_list might include vectors so extra loop for each list item
    for(i in 1:length(col_list)){
      curr_issue <- NA
      for(x in 1:length(col_list[[i]])){
        if(!is.na(col_list[[i]][x])){
          if(is.numeric(col_list[[i]][x])){
            #test position
            curr_issue <- if(col_list[[i]][x] > 0 & col_list[[i]][x] <= max_col_num){NA}else{as.character(col_list[[i]][x])}
          }else{
            #test name
            curr_issue <- if(col_list[[i]][x] %in% col_names){NA}else{paste0('"', col_list[[i]][x], '"')}
          }
        }
        if(!is.na(curr_issue)){my_results <- c(my_results, curr_issue)}
      }
    }
    if(length(my_results) > 0){
      my_results <- paste(my_results, collapse = "; ") %>% paste0("Columns missing: ", .)
    }
  }
  
  return(my_results)
}



find_hdr_row <- function(mydf, hdr_colnames){
  output <- 0
  if(nrow(mydf) > 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames) %>% apply(., 1, sum)
    if(max(myhdr_matches) > 0){
      output <- myhdr_matches %>% which.max(.)
    }
  }
  if(nrow(mydf) == 1){
    myhdr_matches <- apply(mydf, 2, function(x) x %in% hdr_colnames)
    if(sum(myhdr_matches) > 0){output <- 1}
  }
  return(output)
}


rename_dup_colnames <- function(myDF, myDesiredColnames){
  #this functions takes a df with potential duplicate names
  #where R renames them with "..." & column # appended
  #and tries to find the desired column (one most populated) and rename it
  #with it's originally expected name
  myDF_Curr_Colnames <- colnames(myDF)
  myColIntersect <- intersect(myDesiredColnames, myDF_Curr_Colnames)
  mySetDiff <- setdiff(myDesiredColnames, myColIntersect)
  if(length(mySetDiff) > 0){
    #remove special characters from column names
    myDF_Curr_Colnames_comp <- str_replace_all(myDF_Curr_Colnames, "[:punct:]|[:space:]|[$]",".")
    myDesiredColnames_comp <- str_replace_all(mySetDiff, "[:punct:]|[:space:]|[$]",".")
    #loop through each myColNames that wasn't found & search for column BEGINNING with that name
    for(i in 1:length(mySetDiff)){
      #test_matches <- which(myDF_Curr_Colnames_comp %like% paste0("^", myDesiredColnames_comp[i]) )
      test_matches <- which(myDF_Curr_Colnames_comp %like% paste0("^", myDesiredColnames_comp[i], "...") & myDF_Curr_Colnames %notin% myColIntersect )
      if(length(test_matches) > 0){
        #one or more columns might be a match, find how many rows are populated and select one with the most (or last if no rows populated)
        fndcol <- FALSE
        popcols <- data.frame(COLNUM = integer(),
                              COLROWS_POPULATED = numeric()
        )
        for(x in 1:length(test_matches)){
          #test how many rows populated
          numpop <- nrow(myDF) - colSums(is.na(myDF[,test_matches[[x]]]) | myDF[,test_matches[[x]]] == "" )
          if(numpop > 0 ){
            #add column # and # of rows populated to temp df
            popcols <- rbind(popcols, data.frame(COLNUM = c(test_matches[[x]]),
                                                 COLROWS_POPULATED = c(numpop)
            )
            )
            fndcol <- TRUE
          }else{
            #check if no populated column found and on last match and use that column if so
            if(fndcol == FALSE & x == length(test_matches) ){
              popcols <- data.frame(COLNUM = c(test_matches[[x]]), COLROWS_POPULATED = c(numpop))
            }
          }
        }
        if(length(popcols) > 0){
          #select column with most rows populated as desired column
          fndcolnum <- popcols$COLNUM[match(max(popcols$COLROWS_POPULATED, na.rm = TRUE), popcols$COLROWS_POPULATED)]
          #change column name to original desired name
          colnames(myDF)[fndcolnum] <- mySetDiff[i]
        }
      }
      
    }
  }
  return(myDF)
}

file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  oldOpt <- options()
  options(xlsx.date.format="MM/dd/yyyy")
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(format(Sys.time(), "%H:%M:%S"), "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      openxlsx::saveWorkbook(myWB, file = myNewFN, overwrite = writeover)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext,
             test = testing_emails, 
             testrecipient = test_recip
    )
  }
  options(oldOpt)
}



if(okaytocontinue){
  #check Google for workbook and sheets needed
  #https://docs.google.com/spreadsheets/d/1ne1dputHFfVUO1sy0FGuo8sV3GWnsldZDGaAoMZ39_Y/edit#gid=1818624179
  gs4_auth(email = gSht_auth_email)
  if(gs4_has_token()) {
    gSht_get <- gs4_get('1ne1dputHFfVUO1sy0FGuo8sV3GWnsldZDGaAoMZ39_Y')
  }else{
    gSht_get <- "NA"
  }
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    
    #return sheet names present from the expected IDs
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$id %in% mySheetIDs)]
    #return sheet names present from the expected sheet names
    #gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    
    
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that all sheets found
    if(length(mySheetIDs) != gSht_Sheets_num){
    #if(length(mySheets) != gSht_Sheets_num){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>One or more expected sheets (", 
                         paste(mySheets, collapse = "; "),
                         ") were NOT found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " Issue: Missing Expected Sheet Name(s)"),
               body = bodytext,
               attachment = NULL,
               test = testing_emails, 
               testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file or expected sheets may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Sheet Issue"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, 
             testrecipient = test_recip
    )

    okaytocontinue <- FALSE
  }
}


#read Google sheet
if(okaytocontinue){
  #myReportPath <- myReportPath
  this_recip <- c(norm_recip)
  this_ReportName <- paste0(myReportName, "")
  print(paste0("Starting: ", this_ReportName) )
  mySchema <- "STEVE"
  myTable <- "HVP_DEAL_TRACK"
  myTableName <- paste(mySchema, myTable, sep = ".")
  mySheets_Curr <- gSht_Sheets
  
  myColNames <- c("City",
                  "State",
                  "HVP Lead",
                  "Search Type",
                  "Status",
                  "Probability",
                  "Offered or Projected Cash Amount",
                  "Field Manager",
                  "HVP Date sent",
                  "Situation Overview",
                  "HVP Expected   $ / Sq. Ft.",
                  "Highland Comps",
                  "Current Status",
                  "Address",
                  "Square Footage",
                  "Offered or Expected Cap Rate" #,
                  #"Seller",
                  #"Broker",
                  #"NOI",
                  #"Expected Prop. Valuation",
                  #"Property Type"
  )
  
  
  
  myColNames_New <- c("CITY",
                      "STATE",
                      "HVP_LEAD",
                      "SEARCH_TYPE",
                      "STATUS",
                      "PROBABILITY",
                      "OFF_PROJ_CASH",
                      "FIELD_MGR",
                      "HVP_DATE_SENT",
                      "SITUATION_OVERVIEW",
                      "HVP_EXP_DOLLARS_SQFT",
                      "HIGHLAND_COMPS",
                      "CURRENT_STATUS",
                      "ADDRESS",
                      "SQFT",
                      "OFF_EXP_CAP_RATE" #,
                      #"SELLER",
                      #"BROKER",
                      #"NOI",
                      #"EXP_PROP_VAL",
                      #"PROP_TYPE"
  )
  
  #During processing additional columns for "STATUS" "LOCATION_NUM" and "BLDGID" will need to be added
  
  #read Google sheet
  gSht_Curr <- read_sheet(gSht_get$spreadsheet_id, sheet = mySheets_Curr)
  #check column names against desired columns to extract
  gSht_Curr_Colnames <- colnames(gSht_Curr)
  
  last_row <- max(which(!is.na(gSht_Curr$`HVP Lead`) & !is.na(gSht_Curr$`Search Type`) & !is.na(gSht_Curr$Status)))
  #re-read data with only good rows
  gSht_Curr <- read_sheet(gSht_get$spreadsheet_id, sheet = mySheets_Curr, n_max = last_row)
  
  myColIntersect <- intersect(myColNames, gSht_Curr_Colnames)
  orig_fndcols <- which(gSht_Curr_Colnames %in% myColIntersect)
  
  
  
  if(exists("mydata")){rm(mydata)}
  
  
  if(length(myColIntersect) < length(myColNames)){
    testDF <- rename_dup_colnames(gSht_Curr, myColNames)
    testDF_Colnames <- colnames(testDF)
    test_ColIntersect <- intersect(myColNames, testDF_Colnames)
    if(length(test_ColIntersect) == length(myColNames)){
      #use this renamed DF to populate mydata
      myCols <- which(testDF_Colnames %in% myColNames)
      mydata <- testDF[, myCols]
      rm(testDF)
    }else{
      #couldn't locate all columns, abort this load and email warning
      mySetDiff <- setdiff(myColNames, myColIntersect)
      bodytext <- paste0("<p>This is an automated email to inform you that it appears ",
                         "there were one or more missing columns in the '",
                         this_ReportName, "' routine. </p>",
                         "<p><strong>The '", myTableName, "' table was not loaded.</strong></p>",
                         "<p>The missing columns were: ", 
                         paste(mySetDiff, collapse = "; "), "</p>",
                         "<p>The routine will still attempt other loads in this script if applicable.</p>",
                         warn_sig
      )
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " : MISSING COLUMN(S)"),
               body = bodytext,
               test = testing_emails, 
               testrecipient = test_recip
      )
    }
  }else{
    #all expected columns present, populate mydata with just the known columns
    myCols <- which(gSht_Curr_Colnames %in% myColNames)
    mydata <- gSht_Curr[, myCols]
    
  }
  
  
  #proceed with Oracle load if mydata is present
  if(exists("mydata")){
    
    if(nrow(mydata)>1){
      #rename columns to match Oracle table
      setnames(mydata, 
               old = myColNames, 
               new = myColNames_New,
               skip_absent = TRUE)
      
      #remove summary rows (if not done earlier)
      remove_rows <- is.na(mydata$HVP_LEAD) & is.na(mydata$SEARCH_TYPE) & is.na(mydata$STATUS)
      mydata <- mydata[!remove_rows, ]
      
      #convert 'NULL' to NA and convert list columns
      mydata <- mydata %>% mutate(across(where(is.list), map, `%||%`, NA)) %>%
        mutate(across(where(is.list), ~unlist(map(.x, `[`, 1))))
      
      
      #truncate columns that might exceed length
      ##limit notes length
      mydata$CITY <- str_trunc(mydata$CITY, 50, "right", ellipsis = "...")
      mydata$STATE <- str_trunc(mydata$STATE, 2, "right", ellipsis = "")
      mydata$HVP_LEAD <- str_trunc(mydata$HVP_LEAD, 251, "right", ellipsis = "...")
      mydata$SEARCH_TYPE <- str_trunc(mydata$SEARCH_TYPE, 251, "right", ellipsis = "...")
      mydata$STATUS <- str_trunc(mydata$STATUS, 251, "right", ellipsis = "...")
      mydata$FIELD_MGR <- str_trunc(mydata$FIELD_MGR, 96, "right", ellipsis = "...")
      mydata$SITUATION_OVERVIEW <- str_trunc(mydata$SITUATION_OVERVIEW, 496, "right", ellipsis = "...")
      mydata$HVP_EXP_DOLLARS_SQFT <- str_trunc(mydata$HVP_EXP_DOLLARS_SQFT, 251, "right", ellipsis = "...")
      mydata$HIGHLAND_COMPS <- str_trunc(mydata$HIGHLAND_COMPS, 96, "right", ellipsis = "...")
      mydata$CURRENT_STATUS <- str_trunc(mydata$CURRENT_STATUS, 496, "right", ellipsis = "...")
      mydata$ADDRESS <- str_trunc(mydata$ADDRESS, 251, "right", ellipsis = "...")
      #mydata$SELLER <- str_trunc(mydata$SELLER, 255, "right", ellipsis = "...")
      #mydata$BROKER <- str_trunc(mydata$BROKER, 255, "right", ellipsis = "...")
      #mydata$EXP_PROP_VAL <- str_trunc(mydata$EXP_PROP_VAL, 100, "right", ellipsis = "...")
      #mydata$PROP_TYPE <- str_trunc(mydata$PROP_TYPE, 100, "right", ellipsis = "...")
      
      #if sqft is double datatype, convert to integer to prevent weird oracle load
      if(typeof(mydata$SQFT)=="double"){
        mydata$SQFT <- as.integer(mydata$SQFT)
      }
      
      #convert NULL to NA?
      
      #query existing data in case of later issue
      myquery <- paste0('select * from ', myTableName)
      orig_data <- dbGetQuery(myOracleDB, myquery)
      
      #truncate existing Oracle table
      myquery <- paste0('truncate table ', myTableName, ' drop storage')
      dbExecute(myOracleDB, myquery)
      
      #load Oracle table
      rs_write <- dbWriteTable(myOracleDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
      
      #check that all rows loaded
      myquery <- paste0("select count(*) as cnt from ", myTableName)
      myNumRowsResult <- dbGetQuery(myOracleDB, myquery)
      
      if(nrow(mydata) != myNumRowsResult[[1]]){
        #save orig_data to excel file and warn that there may have been a load error
        
        #writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
        myFN <- paste0(myTableName, "-orig data-", report.time,".xlsx")
        mySN <- report.time
        #writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE)
        writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = orig_data, colnames = TRUE, writeover = TRUE)
        myemailfiles <- file.path(myReportPath, myFN)
        
        
        bodytext <- paste0("<p>This is an automated email to inform you that it appears ",
                           "there may have been a loading error in the '",
                           this_ReportName, "' routine. </p>",
                           "<p><strong>An attempt to load the '", myTableName, "' table was ",
                           "made, but the # of expected rows was off.</strong></p>",
                           "<p>The source data had ", nrow(mydata), " rows and the ",
                           "table ended up with ", myNumRowsResult[[1]], " rows.</p>",
                           "<p>The original data from the table before the load attempt is attached in an Excel file.</p>",
                           "<p>The routine will still attempt other loads in this script if applicable.</p>",
                           warn_sig
        )
        mailsend(recipient = warn_recip,
                 subject = paste0(myReportName, " : LOADING ERROR"),
                 body = bodytext,
                 if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                 test = testing_emails, 
                 testrecipient = test_recip
        )
        
      }
    }
  }
  
}







