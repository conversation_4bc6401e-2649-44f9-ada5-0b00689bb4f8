library(RODBC)
library(xtable)
library(reshape2)
library(dplyr)
library(purrr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyr)
library(DBI)
library(ROracle)
library(keyring)
library(janitor)

# written by <PERSON> July 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version ********

### ******** change
### replaced mailR with gmailr package
### gmailr masks base message() function, replaced with explicit base::message()

### ******** change
### changed email signature to new corp style and using saved HTML file

### ******** change
### fixed bug in get_bank_trans_004 where it was crashing with 0 transaction rows

### ******** change
### fix for directory bug due to 'testing' computer

### ******** change
### updated get_bank_trans functions to recognize serial dates in source and convert to proper date

### ******** change
### added ability to run this in a test mode (using load_db Boolean) that doesn't
### load Oracle, this clues in recipient email group that data might have issues
### BEFORE the actual load

### ******** change
### added explicit dplyr::filter() calls instead of just filter() to avoid potential ambiguity

### ******** change
### changed timezone declarations to fix issue where trans were loading into Oracle
### as day before Gsht and result dataframe date

### ******** change
### added another folder to hold Excel files generated

### ******** change:
### Hitting request limit in Google API calls, add built in delays to avoid getting
### '429' response when that happens.
### Also added query to add sequence numbers (from steve.mp_bank_trans_id_seq)
### to load data in order to generate unique rows in Oracle so Tableau doesn't
### aggregate transactions

### ******** change:
### new file, based on LEGACY_PROPERTY_ASSIGMENTS_update.R script


# Parameters

okaytocontinue <- TRUE

myReportName <- "Marco's Weekly Banking-Oracle Load"
scriptfolder <- "MARCOS_Weekly_Banking"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("C:","Users","table","Documents","ReportFiles","HV Logo Email Signature.png")
HVSigPath <- file.path("C:","Users","table","Documents","ReportFiles","HTML_signatures.csv")
gSht_auth_email <- "<EMAIL>"
gDrv_mainURL <-'https://drive.google.com/drive/folders/1gb0wL_R_4oznC0zYIUL8c1tLK5C0ikqa/' #"Marcos Bank Transactions" folder

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
  HVSigPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HTML_signatures.csv")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  #if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  #if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}

# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "15-FEB-23" #testing line only
query.days <- 14
query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - (query.days - 3), "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) + 2, "%d-%b-%y")# Oracle date format for end date of reporting

report.removestart <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 49, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
report.removeend <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - 35, "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
rpt.date <- as.Date(query.date, "%d-%b-%y")
rpt.start <- format(as.Date(query.startdate, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
rpt.end <- format(as.Date(query.enddate, "%d-%b-%y"), "%Y%m%d")# YYYYMMDD format of date for deletion of OLD week ending of report files
email.start <- format(as.Date(query.startdate, "%d-%b-%y"), "%m/%d/%Y")
email.end <- format(as.Date(query.enddate, "%d-%b-%y"), "%m/%d/%Y")

serialcheck.startdate <- as.numeric(as.Date(query.startdate, "%d-%b-%y") - as.Date(0, origin="1899-12-30", tz='UTC') - 31)
serialcheck.enddate <- as.numeric(as.Date(query.enddate, "%d-%b-%y") - as.Date(0, origin="1899-12-30", tz='UTC') + 31)

report.time.txt <- format(Sys.time(), "%H%M%S %Z")
rptFN_noEXT <- paste0("MP Weekly Banking Import Issues ", rpt.start,"-", rpt.end)
rptFN <- paste0(rptFN_noEXT, ".xlsx")

last_request <- Sys.time() # initialize variable for timer that limits Google sheet requests

### determine if test or actual load
#if(wday(Sys.Date()) > 5 && hour(Sys.time()) >= 17){
  #after 5 on Friday, load tables
if(wday(rpt.date) > 4){
  #Thursday or later, load tables
  load_db <- TRUE
}else{
  #before load date/time, the routine should skip DB inserts and just email missing data (ahead of load)
  load_db <- FALSE
}




### define some functions ###

#ROracle connection
#Sys.setenv(TZ='America/Chicago')
#Sys.setenv(ORA_SDTZ='America/Chicago')
Sys.setenv(TZ="GMT")
Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)

mySchema <- "STEVE"
myTable <- "MP_BANK_TRANS"
myTableName <- paste(mySchema, myTable, sep = ".")


get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}


mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


invalid_cols <- function(testdf, col_list){
  my_results <- c("Data missing or no column names/indices supplied")
  if(is.data.frame(testdf) & length(col_list) > 0){
    my_results <- c()
    max_col_num <- ncol(testdf)
    col_names <- names(testdf)
    #col_list might include vectors so extra loop for each list item
    for(i in 1:length(col_list)){
      curr_issue <- NA
      for(x in 1:length(col_list[[i]])){
        if(!is.na(col_list[[i]][x])){
          if(is.numeric(col_list[[i]][x])){
            #test position
            curr_issue <- if(col_list[[i]][x] > 0 & col_list[[i]][x] <= max_col_num){NA}else{as.character(col_list[[i]][x])}
          }else{
            #test name
            curr_issue <- if(col_list[[i]][x] %in% col_names){NA}else{paste0('"', col_list[[i]][x], '"')}
          }
        }
        if(!is.na(curr_issue)){my_results <- c(my_results, curr_issue)}
      }
    }
    if(length(my_results) > 0){
      my_results <- paste(my_results, collapse = "; ") %>% paste0("Columns missing: ", .)
    }
  }
  
  return(my_results)
}



writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  file.opened <- function(path) {
    suppressWarnings(
      "try-error" %in% class(
        try(file(path, 
                 open = "w"), 
            silent = TRUE
        )
      )
    )
  }
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  oldOpt <- options()
  options(xlsx.date.format="MM/dd/yyyy")
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(format(Sys.time(), "%H:%M:%S"), "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      openxlsx::saveWorkbook(myWB, file = myNewFN, overwrite = writeover)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext,
             inline = FALSE
    )
  }
  options(oldOpt)
}


###Get email signature###
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}


###-------------------------------------###
### Bank statement processing functions ###
###-------------------------------------###
init_my_trans <- function(){
  #this function used in the 'get_bank_trans_' functions to initialize the results data frame with the desired names
  empty_dt <- data.table(
    BANK_DATE=Date(),
    TRANS_TYPE=character(),
    AMOUNT=double(),
    NOTE=character(),
    stringsAsFactors = FALSE
  )
  return(empty_dt)
}


get_bank_trans_001 <- function(mydf, IDENTIFIER, IDENTITY_column, DATE_column, DEBITCREDIT_column, NOTE_column){
  #originally designed to get ASSOCIATED, BANK OF AMERICA and SUNFLOWER BANK transactions
  #this function returns values by column name or number
  #works with either individual store:sheet import (leaving IDENTIFIER or IDENTITY_column as NA) or
  #multiple stores per sheet (using IDENTIFIER/IDENTIY_column values)
  #it filters the user supplied data frame to rows having data in the IDENTIFIER (if passed to function) DATE, DEBITCREDIT and NOTE columns
  #my_trans 'TRANS_TYPE' column (2) returned is based on polarity of the DEBITCREDIT column
  #my_trans 'AMOUNT' column (3) assumes any value in the DEBITCREDIT column is either a DEBIT or CREDIT (based on existing polarity of the value)
  #my_trans 'NOTE' column (4) can have multiple columns combined by passing a vector of column names or numbers to the NOTE_column variable...e.g. NOTE_column = c(9,10)
  
  #verify if columns passed are present
  col_list <- list(IDENTITY_column, DATE_column, DEBITCREDIT_column, NOTE_column)
  col_check <- invalid_cols(testdf = mydf, col_list = col_list)
  if(!is.null(col_check)){
    #return error message
    return(col_check)
  }else{
    #proceed
    #subset mydf to get only desired rows *IF* IDENTIFIER passed
    if(is.na(IDENTIFIER)|is.na(IDENTITY_column) ){
      #not using IDENTIFIER, do not pre-filter
    }else{
      #using IDENTIFIER
      mydf <- mydf[which(mydf[,IDENTITY_column] == IDENTIFIER),]
    }
    if(nrow(mydf)==0){
      #no rows matching IDENTIFIER
      my_trans <- init_my_trans() 
    }else{
      #continue
      #filter for rows containing dates, amounts and notes (all required)
      dates_valid <- sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      convert_serial_dates <- FALSE
      if(sum(dates_valid)==0){
        #check for serial dates
        dates_serial <- sapply(mydf[ ,DATE_column][[1]], function(x) if(class(x)=="numeric"){
          if(x >= serialcheck.startdate && x <= serialcheck.enddate){TRUE}else{FALSE} 
        }else{FALSE})
        if(sum(dates_serial)>=0){
          dates_valid <- dates_serial
          convert_serial_dates <- TRUE
        }
      }
      amounts_valid <- do.call(rbind, lapply(mydf[, DEBITCREDIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
      notes_valid <- mydf[ ,NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) %>% apply(., 1, function(x) any(!is.na(x)))
      #the following line might replace the one above due to changes to mutate(), need to test further
      #notes_valid2 <- mydf[ ,NOTE_column] %>% mutate(across(where(is.list), function(x) map(x, `%||%`, NA)  )) %>% apply(., 1, function(x) any(!is.na(x)))
      mydf_filter <- mydf[dates_valid & amounts_valid & notes_valid, ]
      #mydf_filter <- mydf[
      #  sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      #  &
      #  amounts_valid
      #  &
      #  sapply(mydf[ ,NOTE_column], function(x) !is.na(x))
      #  , ]
      rm(mydf)
      
      if(nrow(mydf_filter) == 0){
        my_trans <- init_my_trans()
      }else{
        #tibble returned by googlesheets4 may have list columns, unnest those columns 1 level down
        mydf_filter <- mydf_filter %>% rowwise() %>% mutate_all(~ unlist(.)[1])
        #extract dates, debits, credits, net, notes
        #BANK_DATE
        if(convert_serial_dates){
          my_dates <- lapply(mydf_filter[, DATE_column][[1]], function(x) convert_to_date(x)) %>% do.call(c, .)
        }else{
          my_dates <- lapply(mydf_filter[, DATE_column][[1]], function(x) as.Date(x)) %>% do.call(c, .)
        }
        #TRANS_TYPE
        my_types <- lapply(mydf_filter[, DEBITCREDIT_column][[1]], function(x) if(x > 0){'CREDIT'}else{'DEBIT'}) %>% do.call(c, .)
        #AMOUNT
        my_amounts <- mydf_filter[, DEBITCREDIT_column][[1]] %>% as.list() %>% do.call(c, .)
        #NOTE
        #my_notes <- lapply(mydf_filter[, NOTE_column][[1]], function(x) str_trunc(x, 255)) %>% do.call(c, .)
        if(length(NOTE_column) > 1){
          mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) #replace NULL with NA
          #mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(everything(), as.character))
          mydf_note <- mydf_note %>% unite(c_notes, colnames(.), sep = "_", remove = TRUE, na.rm = FALSE)  
          my_notes <- sapply(mydf_note$c_notes, function(x) str_trunc(x, 255))
          rm(mydf_note)
        }else{
          my_notes <- sapply(mydf_filter[, NOTE_column][[1]], function(x) str_trunc(x, 255))
        }
        #consolidate results into data table and return to caller
        my_trans <- data.table(BANK_DATE=my_dates, 
                               TRANS_TYPE=my_types, 
                               AMOUNT=my_amounts, 
                               NOTE=my_notes   )
      }
    }
    return(my_trans)
  }
}



get_bank_trans_002 <- function(mydf, IDENTIFIER, IDENTITY_column, DATE_column, DEBIT_column, CREDIT_column, NOTE_column){
  #originally designed to get EMPRISE BANK and PEOPLES BANK transactions
  #this function returns values by column name or number
  #works with either individual store:sheet import (leaving IDENTIFIER or IDENTITY_column as NA) or
  #multiple stores per sheet (using IDENTIFIER/IDENTIY_column values)
  #this function assumes distinct DEBIT and CREDIT columns and will work whether DEBITS are negative or positive
  #it filters the user supplied data frame to rows having data in the IDENTIFIER (if passed to function) DATE, DEBITCREDIT and NOTE columns
  #my_trans 'TRANS_TYPE' column (2) returned is based on whether value 
  #my_trans 'AMOUNT' column (3) assumes any value in the DEBITCREDIT column is either a DEBIT or CREDIT (based on existing polarity of the value)
  #my_trans 'NOTE' column (4) can have multiple columns combined by passing a vector of column names or numbers to the NOTE_column variable...e.g. NOTE_column = c(9,10)
  
  #verify if columns passed are present
  col_list <- list(IDENTITY_column, DATE_column, DEBIT_column, CREDIT_column, NOTE_column)
  col_check <- invalid_cols(testdf = mydf, col_list = col_list)
  if(!is.null(col_check)){
    #return error message
    return(col_check)
  }else{
    #proceed
    #subset mydf to get only desired rows *IF* IDENTIFIER passed
    if(is.na(IDENTIFIER)|is.na(IDENTITY_column) ){
      #not using IDENTIFIER, do not pre-filter
      #create list to verify if columns exist
      col_list <- list(DATE_column, DEBIT_column, CREDIT_column, NOTE_column)
      
    }else{
      #using IDENTIFIER
      
      col_list <- list(DATE_column, DEBIT_column, CREDIT_column, NOTE_column)
      
      
      
      mydf <- mydf[which(mydf[,IDENTITY_column] == IDENTIFIER),]
    }
    if(nrow(mydf)==0){
      #no rows matching IDENTIFIER
      my_trans <- init_my_trans() 
    }else{
      #continue
      #filter for rows containing dates, amounts and notes (all required)
      dates_valid <- sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      convert_serial_dates <- FALSE
      if(sum(dates_valid)==0){
        #check for serial dates
        dates_serial <- sapply(mydf[ ,DATE_column][[1]], function(x) if(class(x)=="numeric"){
          if(x >= serialcheck.startdate && x <= serialcheck.enddate){TRUE}else{FALSE} 
        }else{FALSE})
        if(sum(dates_serial)>=0){
          dates_valid <- dates_serial
          convert_serial_dates <- TRUE
        }
      }
      #debits_valid <- do.call(rbind, lapply(mydf[, DEBIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) is.numeric(x) & !is.na(x))
      debits_valid <- do.call(rbind, lapply(mydf[, DEBIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
      #credits_valid <- do.call(rbind, lapply(mydf[, CREDIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) is.numeric(x) & !is.na(x) & as.numeric(x) != 0)
      credits_valid <- do.call(rbind, lapply(mydf[, CREDIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
      amounts_valid <- debits_valid | credits_valid
      notes_valid <- mydf[ ,NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) %>% apply(., 1, function(x) any(!is.na(x)))
      mydf_filter <- mydf[dates_valid & amounts_valid & notes_valid, ]
      rm(mydf)
      
      if(nrow(mydf_filter) == 0){
        my_trans <- init_my_trans()
      }else{
        #extract dates, debits, credits, net, notes from filtered data
        #BANK_DATE
        if(convert_serial_dates){
          my_dates <- lapply(mydf_filter[, DATE_column][[1]], function(x) convert_to_date(x)) %>% do.call(c, .)
        }else{
          my_dates <- lapply(mydf_filter[, DATE_column][[1]], function(x) as.Date(x)) %>% do.call(c, .)
        }
        #TRANS_TYPE
        #check for $0 entries
        debits_valid <- do.call(rbind, lapply(mydf_filter[, DEBIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
        my_types <- sapply(debits_valid, function(x) if(x){'DEBIT'}else{'CREDIT'})
        #AMOUNT
        #my_amounts <- mydf_filter[, DEBITCREDIT_column][[1]] %>% as.list() %>% do.call(c, .)
        my_amounts <- mapply(function(isdebit, debit, credit) if(isdebit){-1 * abs(debit)}else{credit}, debits_valid, mydf_filter[, DEBIT_column][[1]], mydf_filter[, CREDIT_column][[1]])
        #NOTE
        #my_notes <- lapply(mydf_filter[, NOTE_column][[1]], function(x) str_trunc(x, 255)) %>% do.call(c, .)
        if(length(NOTE_column) > 1){
          mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) #replace NULL with NA
          #mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(everything(), as.character))
          mydf_note <- mydf_note %>% mutate(across(everything(), as.character))
          mydf_note <- mydf_note %>% unite(c_notes, colnames(.), sep = "_", remove = TRUE, na.rm = FALSE)  
          my_notes <- sapply(mydf_note$c_notes, function(x) str_trunc(x, 255))
          rm(mydf_note)
        }else{
          my_notes <- sapply(mydf_filter[, NOTE_column][[1]], function(x) str_trunc(x, 255))
        }
        
        #consolidate results into data table and return to caller
        my_trans <- data.table(BANK_DATE=my_dates, 
                               TRANS_TYPE=my_types, 
                               AMOUNT=my_amounts, 
                               NOTE=my_notes   )
      }
    }
    return(my_trans)
  }
}



get_bank_trans_003 <- function(mydf, IDENTIFIER, IDENTITY_column, DATE_column, BAI_column, DEBITCREDIT_column, NOTE_column){
  #originally designed to get HUNTINGTON NATIONAL BANK and PNC Bank transactions
  #this function returns values when there is one combined Debit/Credit column and bank uses BAI.org codes
  #can have MULTIPLE ACCOUNTS (STORES) PER SOURCE WORKSHEET as location's unique IDENTIFIER is part of filter
  #it filters the user supplied data frame to rows matching:
  ###IDENTIFIER (usually account #) & has DATE & has DEBITCREDIT value & BAI code is debit or credit detail code
  #'TRANS_TYPE' column (2) returned is based on whether BAI code is in bai_debits or bai_credits
  #'AMOUNT' column (3) polarity is based on whether TRANS_TYPE data is 'DEBIT' or 'CREDIT'
  #'NOTE' column (4) can have multiple columns combined by passing a vector of column names or numbers to the NOTE_column variable...e.g. NOTE_column = c(9,10)
  
  #BAI Debit Detail Codes (as of 8/3/2022)
  bai_debits <- c(408,409,415,421,422,423,435,445,447,451,452,455,462,464,466,
                  468,469,472,474,475,476,477,479,481,484,485,487,489,491,493,
                  495,496,498,501,502,506,508,512,513,514,516,518,522,524,526,
                  527,529,531,533,535,538,540,541,542,543,544,546,547,548,549,
                  552,554,555,557,558,561,563,564,566,567,568,574,575,577,578,
                  581,595,597,616,622,627,629,631,633,634,641,644,651,654,656,
                  657,658,659,661,662,663,664,666,667,668,669,672,673,674,676,
                  677,678,679,681,682,683,684,686,687,688,691,692,693,694,695,
                  696,697,698,699)
  
  #BAI Credit Detail Codes (as of 8/3/2022)
  bai_credits <- c(108,115,116,118,121,122,123,135,136,142,143,145,147,155,156,
                   164,165,166,168,169,171,172,173,174,175,176,184,187,189,191,
                   195,196,198,201,202,206,208,212,213,214,216,218,221,222,224,
                   226,227,229,232,233,234,235,236,237,238,240,241,242,243,244,
                   246,247,248,249,252,254,255,257,258,261,263,266,268,274,275,
                   276,277,278,281,286,295,301,306,308,331,342,344,345,346,347,
                   348,349,351,353,354,357,358,359,362,363,364,366,367,368,369,
                   372,373,374,376,377,378,379,381,382,383,384,386,387,388,391,
                   392,393,394,395,397,398,399,721,722,723,724,725,726,727,728)
  
  #verify if columns passed are present
  col_list <- list(IDENTITY_column, DATE_column, BAI_column, DEBITCREDIT_column, NOTE_column)
  col_check <- invalid_cols(testdf = mydf, col_list = col_list)
  if(!is.null(col_check)){
    #return error message
    return(col_check)
  }else{
    #proceed
    #subset mydf to get only desired rows *IF* IDENTIFIER passed
    if(is.na(IDENTIFIER)|is.na(IDENTITY_column) ){
      #not using IDENTIFIER, do not pre-filter
    }else{
      #using IDENTIFIER
      mydf <- mydf[which(mydf[,IDENTITY_column] == IDENTIFIER),]
    }
    if(nrow(mydf)==0){
      #no rows matching IDENTIFIER
      my_trans <- init_my_trans() 
    }else{
      #continue
      #subset mydf to get only desired rows containing Dates, an AMOUNT, valid BAI code and valid NOTEs
      
      ####CHECK/FILTER IF DATE COLUMN CONTAINS 8 DIGIT NUMBERS FOR DATES (like 20220801 in Huntington National statements) OR ACTUAL DATE FORMATS (like PNC)
      ### ******** update also recognizes Excel serial dates (1/1/1900) within a month of target dates
      list_Dates <- sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      list_Digits <- sapply(mydf[ ,DATE_column][[1]], function(x) is.numeric(x) & x >= 20010101 & x <= 21991231)
      list_Serial <- sapply(mydf[ ,DATE_column][[1]], function(x) is.numeric(x) & x >= serialcheck.startdate & x <= serialcheck.enddate)
      #dates_valid <- sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      dates_type <- case_when(
        sum(list_Digits) > sum(list_Dates) & sum(list_Digits) > sum(list_Serial) ~ "Digits",
        sum(list_Serial) > sum(list_Dates) & sum(list_Serial) > sum(list_Digits) ~ "Serial",
        TRUE ~ "Dates"
      )
      dates_valid <- case_when(
        dates_type == "Digits" ~ list_Digits,
        dates_type == "Serial" ~ list_Serial,
        dates_type == "Dates" ~ list_Dates
      )
      rm(list_Dates, list_Digits, list_Serial)
      mydf <- mydf[dates_valid, ]
      #continue finding valid BAI, AMOUNTS and NOTEs
      bai_valid <- sapply(mydf[ ,BAI_column][[1]], function(x) x %in% c(bai_debits, bai_credits))
      amounts_valid <- do.call(rbind, lapply(mydf[, DEBITCREDIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
      notes_valid <- mydf[ ,NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) %>% apply(., 1, function(x) any(!is.na(x)))
      mydf_filter <- mydf[bai_valid & amounts_valid & notes_valid, ]
      rm(mydf)
      
      if(nrow(mydf_filter) == 0){
        my_trans <- init_my_trans()
      }else{
        #extract dates, debits, credits, net, notes from filtered data
        #BANK_DATE
        mydf_date <- unnest_legacy(mydf_filter[,DATE_column])
        if(dates_type == "Digits"){my_dates <- lapply(mydf_date, function(x) as.Date(as.character(x), '%Y%m%d')) %>% do.call(c, .)}
        if(dates_type == "Serial"){my_dates <- lapply(mydf_date, function(x) convert_to_date(x)) %>% do.call(c, .)}
        if(dates_type == "Dates"){my_dates <- lapply(mydf_date[[1]], function(x) as.Date(x)) %>% do.call(c, .)}
        
        
        #TRANS_TYPE
        my_types <- sapply(mydf_filter[, BAI_column][[1]], function(x) if(x %in% bai_debits){'DEBIT'}else{'CREDIT'})
        #AMOUNT
        my_amounts <- mapply(function(type, debitcredit) if(type == 'DEBIT'){-1 * abs(debitcredit)}else{debitcredit}, my_types, mydf_filter[,DEBITCREDIT_column][[1]])
        #NOTE
        if(length(NOTE_column) > 1){
          mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) #replace NULL with NA
          #mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(everything(), as.character))
          mydf_note <- mydf_note %>% mutate(across(everything(), as.character))
          mydf_note <- mydf_note %>% unite(c_notes, colnames(.), sep = "_", remove = TRUE, na.rm = FALSE)  
          my_notes <- sapply(mydf_note$c_notes, function(x) str_trunc(x, 255))
          rm(mydf_note)
        }else{
          my_notes <- sapply(mydf_filter[, NOTE_column][[1]], function(x) str_trunc(x, 255))
        }
        #consolidate results into data table and return to caller
        my_trans <- data.table(BANK_DATE=my_dates, 
                               TRANS_TYPE=my_types, 
                               AMOUNT=my_amounts, 
                               NOTE=my_notes   )
      }
    }
    return(my_trans)
  }
}



get_bank_trans_004 <- function(mydf, IDENTIFIER, IDENTITY_column, DATE_column, TYPE_column, DEBITCREDIT_column, NOTE_column){
  #originally designed to get US BANK transactions
  #this function returns values when there is one combined Debit/Credit column and bank uses another column with 'Debit' and 'Credit' codes
  #can have MULTIPLE ACCOUNTS (STORES) PER SOURCE WORKSHEET as location's unique IDENTIFIER is part of filter
  #it filters the user supplied data frame to rows matching:
  ###IDENTIFIER (usually account #) & has DATE & has DEBITCREDIT value & BAI code is debit or credit detail code
  #'TRANS_TYPE' column (2) returned is based on whether TYPE code is in type_debits or type_credits
  #'AMOUNT' column (3) polarity is based on whether TRANS_TYPE data is 'DEBIT' or 'CREDIT'
  #'NOTE' column (4) can have multiple columns combined by passing a vector of column names or numbers to the NOTE_column variable...e.g. NOTE_column = c(9,10)
  
  type_debits <- c('DEBIT')
  
  #BAI Credit Detail Codes (as of 8/3/2022)
  type_credits <- c('CREDIT')
  
  #verify if columns passed are present
  col_list <- list(IDENTITY_column, DATE_column, TYPE_column, DEBITCREDIT_column, NOTE_column)
  col_check <- invalid_cols(testdf = mydf, col_list = col_list)
  if(!is.null(col_check)){
    #return error message
    return(col_check)
  }else{
    #proceed
    #subset mydf to get only desired rows *IF* IDENTIFIER passed
    if(is.na(IDENTIFIER)|is.na(IDENTITY_column) ){
      #not using IDENTIFIER, do not pre-filter
    }else{
      #using IDENTIFIER
      mydf <- mydf[which(mydf[,IDENTITY_column] == IDENTIFIER),]
    }
    if(nrow(mydf)==0){
      #no rows matching IDENTIFIER
      my_trans <- init_my_trans() 
    }else{
      #continue
      ####CHECK/FILTER IF DATE COLUMN CONTAINS 8 DIGIT NUMBERS FOR DATES (like 8012022 in US Bank statements) OR ACTUAL DATE FORMATS (like PNC)
      ### ******** update also recognizes Excel serial dates (1/1/1900) within a month of target dates
      list_Dates <- sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      list_Digits <- sapply(mydf[ ,DATE_column][[1]], function(x) is.numeric(x) & x >= 1012001 & x <= ********)
      list_Serial <- sapply(mydf[ ,DATE_column][[1]], function(x) is.numeric(x) & x >= serialcheck.startdate & x <= serialcheck.enddate)
      #dates_valid <- sapply(mydf[ ,DATE_column][[1]], function(x) inherits(x, "POSIXt"))
      dates_type <- case_when(
        sum(list_Digits) > sum(list_Dates) & sum(list_Digits) > sum(list_Serial) ~ "Digits",
        sum(list_Serial) > sum(list_Dates) & sum(list_Serial) > sum(list_Digits) ~ "Serial",
        TRUE ~ "Dates"
      )
      dates_valid <- case_when(
        dates_type == "Digits" ~ list_Digits,
        dates_type == "Serial" ~ list_Serial,
        dates_type == "Dates" ~ list_Dates
      )
      rm(list_Dates, list_Digits, list_Serial)
      mydf <- mydf[dates_valid, ]
      
      if(nrow(mydf)>0){
        #******** change: this section was not inside an if statement and mydf_filter assignment was crashing when 0 transaction rows
        type_valid <- sapply(mydf[ , TYPE_column][[1]], function(x) str_to_upper(x) %in% c(type_debits, type_credits))
        amounts_valid <- do.call(rbind, lapply(mydf[, DEBITCREDIT_column], rbind)) %>% nullToNA(.) %>% sapply(., function(x) if(is.numeric(x)){!is.na(x) & x != 0}else{FALSE})
        notes_valid <- mydf[ ,NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) %>% apply(., 1, function(x) any(!is.na(x)))
        #subset mydf to get only desired rows containing an AMOUNT, valid TYPE code and valid NOTEs
        mydf_filter <- mydf[type_valid & amounts_valid & notes_valid, ]
        rm(mydf)
      }else{
        mydf_filter <- mydf[0, ]
      }

      if(nrow(mydf_filter) == 0){
        my_trans <- init_my_trans()
      }else{
        #extract dates, debits, credits, net, notes from filtered data
        #BANK_DATE
        mydf_date <- unnest_legacy(mydf_filter[,DATE_column])
        if(dates_type == "Digits"){my_dates <- lapply(mydf_date, function(x) as.Date(str_pad(x, width = 8, side = "left", pad = "0"), '%m%d%Y')) %>% do.call(c, .)}
        if(dates_type == "Serial"){my_dates <- lapply(mydf_date, function(x) convert_to_date(x)) %>% do.call(c, .)}
        if(dates_type == "Dates"){my_dates <- lapply(mydf_date[[1]], function(x) as.Date(x)) %>% do.call(c, .)}
        
        #TRANS_TYPE
        my_types <- sapply(mydf_filter[, TYPE_column][[1]], function(x) if(str_to_upper(x) %in% type_debits){'DEBIT'}else{'CREDIT'})
        #AMOUNT
        my_amounts <- mapply(function(type, debitcredit) if(type == 'DEBIT'){-1 * abs(debitcredit)}else{debitcredit}, my_types, mydf_filter[,DEBITCREDIT_column][[1]])
        #NOTE
        if(length(NOTE_column) > 1){
          mydf_note <- mydf_filter[, NOTE_column] %>% mutate(across(where(is.list), map, `%||%`, NA)) #replace NULL with NA
          mydf_note <- mydf_note %>% mutate(across(everything(), as.character))
          mydf_note <- mydf_note %>% unite(c_notes, colnames(.), sep = "_", remove = TRUE, na.rm = FALSE)  
          my_notes <- sapply(mydf_note$c_notes, function(x) str_trunc(x, 255))
          rm(mydf_note)
        }else{
          my_notes <- sapply(mydf_filter[, NOTE_column][[1]], function(x) str_trunc(x, 255))
        }
        #consolidate results into data table and return to caller
        my_trans <- data.table(BANK_DATE=my_dates, 
                               TRANS_TYPE=my_types, 
                               AMOUNT=my_amounts, 
                               NOTE=my_notes   )
      }
    }
    return(my_trans)
  }
}








# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isFolder <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was for folder
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isFolder <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("folder")
    if(!isFolder){okaytocontinue <- FALSE}
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file folder for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Folder URL resolved: ", isFolder, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Access Issue"),
             body = bodytext,
             attachment = NULL,
             inline = FALSE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}


# check if any desired files are present in Google
if(okaytocontinue){
  
  myquery <- paste0(
    "
      SELECT
          L.LOC_NUM AS LOCATION
      ,   M.NAME_SHORT AS BANK
      ,   CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' THEN M.NAME_SHORT||'_'||L.LOC_NUM ELSE M.NAME_SHORT END AS SHEETNAME
      ,   NULL as GSHT_LINK
      ,   NULL as DATA_ISSUE
      ,   NULL as TRANS_PROCESSED
      ,   NULL as TRANS_REJECTED
      ,   to_char(to_date('", query.startdate, "'), 'yyyymmdd')||'-'||to_char(to_date('", query.enddate, "'), 'yyyymmdd')||' '||M.NAME_SHORT AS FILENAME
      ,   L.B_ID_LOCAL
      ,   CASE WHEN UPPER(M.IMPORT_METHOD) = 'IND' THEN NULL ELSE NVL(L.IMPORT_ALT_ID, L.ACCOUNT) END AS IDENTIFIER
      ,   L.S_DATE AS ACCT_START_DATE
      ,   L.E_DATE AS ACCT_END_DATE
      ,   NVL(M.IMPORT_FUN,'NA') AS IMPORT_FUNCTION_CALL
      FROM STEVE.MP_BANK_ID_LOCAL L
      JOIN STEVE.MP_BANK_ID_MASTER M
      ON L.B_ID_MASTER = M.B_ID_MASTER
      WHERE 
          L.S_DATE <= to_date('", query.enddate, "')
          AND (L.E_DATE IS NULL OR L.E_DATE >= to_date('", query.startdate, "'))
      order by M.NAME_SHORT, L.LOC_NUM, L.B_ID_LOCAL
    "
  )
  myFilesSheets_expected <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydf_rows(myFilesSheets_expected, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]]){
    #ensure cols are needed datatype if NULL
    myFilesSheets_expected <- transform(
      myFilesSheets_expected, 
      DATA_ISSUE = as.character(DATA_ISSUE),
      TRANS_PROCESSED = as.numeric(TRANS_PROCESSED), 
      TRANS_REJECTED = as.numeric(TRANS_REJECTED),
      ACCT_END_DATE = as.Date(ACCT_END_DATE)
      )
    myFiles_expected <- unique(myFilesSheets_expected$FILENAME)
    mySearch <- drive_ls(drv_get_main$id, recursive = TRUE) #dribble of drv_get_main contents
    myFiles <- mySearch %>% 
      hoist(drive_resource, "mimeType") %>% 
      dplyr::filter(mimeType == drive_mime_type("spreadsheet")) %>%
      dplyr::filter(name %in% myFiles_expected)
    test_compare <- myFiles_expected %in% c(myFiles$name)
    myFiles_expected_present <- myFiles_expected[test_compare]
    myFiles_expected_notpresent <- myFiles_expected[!test_compare]
    if(length(myFiles_expected_present)==0){okaytocontinue <- FALSE} #none of the expected files from the query present
  }else{
    #no expected files based on query, abort
    myFiles_expected_notpresent <- c("No expected files returned by Oracle query of bank tables")
    okaytocontinue <- false
  }

  if(!okaytocontinue){
    #no expected files present, abort and email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error finding the Google Sheet workbooks for the ", myReportName, " routine! ",
                       "The following files were expected, but NONE were found.</p>",
                       paste(myFiles_expected_notpresent, collapse = "<br>"),
                       "<br>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Expected Files NA or NOT Present"),
             bodytext,
             attachment = NULL,
             inline = FALSE,
             test = testing_emails, testrecipient = test_recip
    )
    okaytocontinue <- FALSE
  }
}


if(okaytocontinue){
  #loop through workbooks present
  for(WbNum in 1:length(myFiles_expected_present)){
    gSht_id <- myFiles$id[which(myFiles$name == myFiles_expected_present[WbNum])][[1]]
    gSht_name <- myFiles_expected_present[WbNum]
    mySheets <- sheet_properties(gSht_id) %>% dplyr::filter(visible == TRUE)
    
    #compare to expected sheet names
    mySheets_expected <- unique(myFilesSheets_expected$SHEETNAME[which(myFilesSheets_expected$FILENAME == gSht_name)])
    test_compare <- mySheets_expected %in% c(mySheets$name)
    mySheets_expected_present <- mySheets_expected[test_compare]
    mySheets_expected_notpresent <- mySheets_expected[!test_compare]
    rm(test_compare)
    
    ###need to add logic to catch when length(mySheets_expected_present) == 0
    
    if(length(mySheets_expected_present)>0){

      #process sheets in this workbook
      for(WsNum in 1:length(mySheets_expected_present)){
        mySheetName <- mySheets_expected_present[WsNum]
        #get store info expected in this worksheet
        myStores <- subset(myFilesSheets_expected,
                           FILENAME == gSht_name & SHEETNAME == mySheetName,
                           c(LOCATION, B_ID_LOCAL, IMPORT_FUNCTION_CALL, IDENTIFIER)
                          )
        #read current sheet
        
        #remember last Google request time, sleep execution if less than 5.5 seconds since last_request
        this_request <- Sys.time()
        seconds_elapsed <- as.numeric(difftime(this_request, last_request, units = "secs"), units = "secs")
        wait_time <- max(0, 5.5 - seconds_elapsed)
        last_request <- Sys.time()
        Sys.sleep(wait_time)
        
        #mySheetData <- read_sheet(gSht_id, sheet = mySheetName) #Banks with column names, causes issues when no header and transactions start in row 1
        mySheetData <- read_sheet(gSht_id, sheet = mySheetName, col_names = FALSE)
        mySheetData <- mySheetData %>% 
          row_to_names(row_number = 1, remove_row = FALSE) %>%
          as_tibble(.name_repair = ~ make.names(., unique = TRUE))#sometimes sheet has header row, sometimes not, this ensures all needed data rows present, but keeps header names as dataframe column names when present
        #test if data present
        myLoadOkay <- check_mydf_rows(mySheetData, MinNumRows = 1, 
                                      ReportName = paste0("Sheet Load: ID-'", gSht_id, " Name-", mySheetName, "'"))
        gSht_gid <- mySheets$id[which(mySheets$name == mySheetName)]
        mySheetURL <- paste0("https://docs.google.com/spreadsheets/d/",
                             gSht_id,
                             "/edit#gid=",
                             gSht_gid
        )
        if(myLoadOkay[[1]]){
          for(StNum in 1:nrow(myStores)){
            #get store number, STEVE.MP_BANK_ID_LOCAL value, identity info and function call
            mycurr_Store <- myStores$LOCATION[StNum]
            mycurr_B_ID_LOCAL <- myStores$B_ID_LOCAL[StNum]
            mycurr_Identifier <- myStores$IDENTIFIER[StNum]
            mycurr_FunctionCall <- myStores$IMPORT_FUNCTION_CALL[StNum]
            mycurr_numprocessed <- 0
            mycurr_numrejected <- 0
            mycurr_datapresent <- FALSE
            
            myFSrow <- which(myFilesSheets_expected$LOCATION == mycurr_Store & myFilesSheets_expected$B_ID_LOCAL == mycurr_B_ID_LOCAL)
            myFilesSheets_expected$GSHT_LINK[myFSrow] <- mySheetURL
            
            #call processing function
            #debugonce(get_bank_trans_004) #testing only
            mycurr_Results <- eval(parse(text = mycurr_FunctionCall))
            if(is.data.frame(mycurr_Results)){
              if(nrow(mycurr_Results) > 0){mycurr_datapresent <- TRUE}
            }else{
              if(is.character(mycurr_Results)){
                #populate data issue (most likely missing columns)
                myFilesSheets_expected$DATA_ISSUE[myFSrow] <- mycurr_Results
              }
            }
            #if(nrow(mycurr_Results) > 0){
            if(mycurr_datapresent){
              #some results returned, add meta-data and check date ranges
              #add column with location to beginning of df
              mycurr_Results <- cbind(LOC_NUM = mycurr_Store, B_ID_LOCAL = mycurr_B_ID_LOCAL, mycurr_Results)
              
              #filter to expected date range
              #filter myResults to only dates between expected dates?
              ok_rows <- which(mycurr_Results$BANK_DATE >= as.Date(query.startdate, '%d-%b-%y') & 
                                 mycurr_Results$BANK_DATE <= as.Date(query.enddate, '%d-%b-%y') 
                               )
              mycurr_Results_load <- mycurr_Results[ok_rows, ]
              mycurr_numprocessed <- nrow(mycurr_Results_load)
              mycurr_Results_rejected <- mycurr_Results[!ok_rows, ]
              mycurr_numrejected <- nrow(mycurr_Results_rejected)
              
              if(exists("myResults")){
                myResults <- rbind(myResults, mycurr_Results_load)
              }else{
                myResults <- mycurr_Results_load
              }
              if(exists("myResults_rejected")){
                myResults_rejected <- rbind(myResults_rejected, mycurr_Results_rejected)
              }else{
                myResults_rejected <- mycurr_Results_rejected
              }
            }
            
            #populate number of processed and rejected transactions
            myFilesSheets_expected$TRANS_PROCESSED[myFSrow] <- mycurr_numprocessed
            myFilesSheets_expected$TRANS_REJECTED[myFSrow] <- mycurr_numrejected
            
            if(exists("mycurr_Results")){rm(mycurr_Results)}
            if(exists("mycurr_Results_load")){rm(mycurr_Results_load)}
            if(exists("mycurr_Results_rejected")){rm(mycurr_Results_rejected)}
            #rm(mycurr_Results, mycurr_Results_load, mycurr_Results_rejected)
          }
        }else{
          #sheet load NOT okay, populate GSHT_LINK column with error result
          myFSrows <- which(myFilesSheets_expected$LOCATION %in% myStores$LOCATION & myFilesSheets_expected$SHEETNAME == mySheetName)
          myFilesSheets_expected$GSHT_LINK[myFSrows] <- myLoadOkay[[3]]
        }
      }
    }else{
      #file present, but none of the expected sheets present (maybe someone renamed)
      myFSrows <- which(myFilesSheets_expected$FILENAME == gSht_name & myFilesSheets_expected$SHEETNAME %in% mySheets_expected_notpresent)
      myFilesSheets_expected$GSHT_LINK[myFSrows] <- "File present, Sheet not found"
    }
  }
  
}




###test if any results found
if(okaytocontinue){
  if(!exists("myResults")){
    #email warning and abort
    okaytocontinue <- FALSE
    
    #send error email
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "were issues in the '", myReportName, 
                       "' routine for the ", email.start, " to ", email.end, " import.</p>",
                       "<p>NO TRANSACTIONS WERE FOUND OR LOADED!</p> ",
                       "<br><br>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Import Failure"),
             bodytext,
             attachment = NULL,
             inline = FALSE,
             test = testing_emails, testrecipient = test_recip
    )
    
    
  }
}



###load Oracle with results
##if(okaytocontinue){
if(okaytocontinue && load_db){
  myDeleteStores <- myFilesSheets_expected[which(myFilesSheets_expected$TRANS_PROCESSED >= 1), c("LOCATION", "B_ID_LOCAL")]
  mynumloops <- ceiling(nrow(myDeleteStores)/1000)
  for(i in 1:mynumloops){
    #oracle permits 1000 values for an IN clause, so break up delete into 1000 store increments if applicable
    mystart <- (i-1) * 1000 + 1
    mylimit <- min(c(nrow(myDeleteStores), i*1000))
    myDeleteIDs_curr <- myDeleteStores$B_ID_LOCAL[mystart:mylimit]
    myDeleteIDs_query <- paste0(myDeleteIDs_curr, collapse = ',')
    myquery_select <- paste0(
      "
      select count(*)
      from ", myTableName, "
      where b_id_local in (", myDeleteIDs_query, ")
      and bank_date >= to_date('", query.startdate, "')
      and bank_date <= to_date('", query.enddate, "')
      "
    )
    rs_sel <- dbSendQuery(myOracleDB, myquery_select)
    select_cnt <- dbFetch(rs_sel, n = -1)
    myquery_delete <- paste0(
      "
      delete from ", myTableName, "
      where b_id_local in (", myDeleteIDs_query, ")
      and bank_date >= to_date('", query.startdate, "')
      and bank_date <= to_date('", query.enddate, "')
      "
    )
    rs_del <- dbSendQuery(myOracleDB, myquery_delete)
    if(dbGetInfo(rs_del, what = "rowsAffected") != select_cnt[[1]]){
      #delete failed
      warning("dubious deletion -- rolling back transaction")
      dbRollback(myOracleDB)
      #log stores where delete (and subsequently load) failed
      if(exists("myDeleteStores_failed")){
        myDeleteStores_failed <- c(myDeleteStores_failed, myDeleteStores[mystart:mylimit])
      }else{
        myDeleteStores_failed <- myDeleteStores[mystart:mylimit, ]
      }
      #do not load since previous trans deletion failed
    }else{
      #delete was apparently successful, commit and proceed with load of these locations
      dbCommit(myOracleDB)
      
      #Insert trans rows into myTable, count rows before and after
      myquery_select <- paste0(
        "
      select count(*)
      from ", myTableName, "
      where b_id_local in (", myDeleteIDs_query, ")
      and bank_date >= to_date('", query.startdate, "')
      and bank_date <= to_date('", query.enddate, "')
      "
      )
      rs_sel <- dbSendQuery(myOracleDB, myquery_select)
      select_cnt_pre <- dbFetch(rs_sel, n = -1)
      dbClearResult(rs_sel)
      
      #subset myResults to match b_id_local
      myCurrLoad <- subset(myResults, B_ID_LOCAL %in% myDeleteIDs_curr)
      #get seq_id values for rows from Oracle
      
      myquery_sequence <- paste0(
        "
        select MP_BANK_TRANS_ID_SEQ.nextval 
        from dual 
        connect by level <= ", nrow(myCurrLoad)
      )
      myCurrSeqIDs <- dbGetQuery(myOracleDB, myquery_sequence)
      myCurrLoad$SEQ_ID <- myCurrSeqIDs$NEXTVAL
      rs_write <- dbWriteTable(myOracleDB, myTable, myCurrLoad, row.names = FALSE , append = TRUE, schema = mySchema)
      
      #get new count of rows in table
      rs_sel <- dbSendQuery(myOracleDB, myquery_select)
      select_cnt_post <- dbFetch(rs_sel, n = -1)
      myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
      mydata_numrows <- nrow(myCurrLoad)
      
      if(myload_numrows != mydata_numrows){
        #mis-match in rows loaded, get load counts by b_id_local
        myquery <- paste0(
          "
          select l.loc_num as LOCATION
          ,   l.b_id_local
          ,   count(tran.b_id_local) as TRANS_LOADED
          from steve.mp_bank_id_local l
          left join 
          (
              select b_id_local
              from steve.mp_bank_trans
              where b_id_local in (", myDeleteIDs_query, ")
              and bank_date >= to_date('", query.startdate, "')
              and bank_date <= to_date('", query.enddate, "')
          ) tran
          on l.b_id_local = tran.b_id_local
          where l.b_id_local in (", myDeleteIDs_query, ")
          group by
              l.loc_num
          ,   l.b_id_local
          "
        )
        myLoadStores_curr <- dbGetQuery(myOracleDB, myquery)
        myLoadStores_results <- subset(myFilesSheets_expected,
                                       B_ID_LOCAL %in% myDeleteIDs_curr,
                                       c(LOCATION, B_ID_LOCAL, TRANS_PROCESSED)
        )
        myLoadStores_failed <- myLoadStores_results %>% 
          dplyr::left_join(myLoadStores_curr, by = c("LOCATION","B_ID_LOCAL")) %>%
          .[which(.$TRANS_PROCESSED != .$TRANS_LOADED), ]
        
        if(exists("myLoad_failed")){
          myLoad_failed <- rbind(myLoad_failed, myLoadStores_failed)
        }else{
          myLoad_failed <- myLoadStores_failed
        }
        rm(myLoadStores_curr, myLoadStores_results, myLoadStores_failed)
      }
      #remove temp subset of results to prepare for next loop (if needed)
      rm(myCurrLoad)
    }
    if(exists("rs_sel")){
      dbClearResult(rs_sel)
      rm(rs_sel)
    }
    if(exists("rs_del")){
      dbClearResult(rs_del)
      rm(rs_del)
    }
  }
}



###Send exceptions if present
if(okaytocontinue){
  #find if any exceptions to notify
  err_fnd <- FALSE
  #create copy of results with needed columns
  err_copycols <- c(
    "LOCATION", 
    "BANK", 
    "B_ID_LOCAL", 
    "ACCT_START_DATE",
    "ACCT_END_DATE",
    "IDENTIFIER",
    "FILENAME",
    "SHEETNAME",
    "GSHT_LINK",
    if(sum(!is.na(myFilesSheets_expected$DATA_ISSUE))>0){"DATA_ISSUE"}else{NA},
    "TRANS_REJECTED",
    "TRANS_PROCESSED"
    ) %>% .[which(!is.na(.))]
  err_Results <- myFilesSheets_expected[,err_copycols]
  #start list for body of error email
  bodytext_err <- '<ul style="list-style-type:square">'
  
  
  #DATA_ISSUE
  err_dataissue_cnt <- sum(!is.na(myFilesSheets_expected$DATA_ISSUE))
  if(err_dataissue_cnt > 0){
    err_fnd <- TRUE
    bodytext_err <- paste0(bodytext_err, '<li>',
                           err_dataissue_cnt, if(err_dataissue_cnt > 1){" locations "}else{" location "}, 
                           'where <strong>a DATA ISSUE prevented transaction processing</strong>. ', 
                           'This is normally due to an expected column (either name ',
                           'or number) not being found in the Google sheet. ',
                           '</li>')
  }
  
  #TRANS_PROCESSED is NA
  err_na_cnt <- sum(is.na(myFilesSheets_expected$TRANS_PROCESSED))
  if(err_na_cnt > 0){
    err_fnd <- TRUE
    err_stores <- myFilesSheets_expected$LOCATION[is.na(myFilesSheets_expected$TRANS_PROCESSED)]
    if(length(err_stores) < 6){
      err_stores_txt <- paste0("(", paste0(err_stores, collapse = ", "), ") ")
    }else{
      err_stores_txt <- ""
    }
    bodytext_err <- paste0(bodytext_err, '<li>',
                           err_na_cnt, if(err_na_cnt > 1){" locations "}else{" location "}, 
                           err_stores_txt, 'where <strong>TRANS PROCESSED was NA</strong>. ', 
                           'This can be due to the needed sheet not being found ',
                           'or it failed to load for some reason. See the "GSHT LINK" ',
                           'column of the results for potential info on this.',
                           '</li>')
  }
  
  #TRANS_PROCESSED is 0
  err_zero_cnt <- length(which(myFilesSheets_expected$TRANS_PROCESSED == 0))
  if(err_zero_cnt > 0){
    err_fnd <- TRUE
    err_stores <- myFilesSheets_expected$LOCATION[which(myFilesSheets_expected$TRANS_PROCESSED == 0)]
    if(length(err_stores) < 6){
      err_stores_txt <- paste0("(", paste0(err_stores, collapse = ", "), ") ")
    }else{
      err_stores_txt <- ""
    }
    bodytext_err <- paste0(bodytext_err, '<li>',
                           err_zero_cnt, if(err_zero_cnt > 1){" locations "}else{" location "}, 
                           err_stores_txt, 'where <strong>TRANS PROCESSED was 0</strong>. ', 
                           'In this case, the needed sheet was found and loaded, but no valid ',
                           'transactions for the location were found in the expected sheet. This ',
                           'could also happen if the bank changed the layout of the transaction data; ',
                           'it wasn\'t pasted into the correct columns (always start pasting into cell A1); ',
                           'the account has just been activated or deactivated and wasn\'t used. ',
                           'If the DATA ISSUE column is present, this will indicate that needed ',
                           'columns specified there were not found in the sheet. ',
                           '</li>')
  }
  
  #TRANS_REJECTED is > 0
  err_rej_cnt <- length(which(myFilesSheets_expected$TRANS_REJECTED > 0))
  if(err_rej_cnt > 0){
    err_fnd <- TRUE
    err_stores <- myFilesSheets_expected$LOCATION[which(myFilesSheets_expected$TRANS_REJECTED > 0)]
    if(length(err_stores) < 6){
      err_stores_txt <- paste0("(", paste0(err_stores, collapse = ", "), ") ")
    }else{
      err_stores_txt <- ""
    }
    bodytext_err <- paste0(bodytext_err, '<li>',
                           err_rej_cnt, if(err_rej_cnt > 1){" locations "}else{" location "}, 
                           err_stores_txt, 'where <strong>TRANS REJECTED was > 0</strong>. ', 
                           'The noted # of transactions for the location were rejected from the load. ',
                           'This could happen if the dates associated with them fell outside the ',
                           'expected date range. These ARE NOT included in the TRANS PROCESSED count.',
                           '</li>')
  }
  
  #DELETE statement error
  if(exists("myDeleteStores_failed")){
    err_del_cnt <- nrow(myDeleteStores_failed)
  }else{
    err_del_cnt <- 0
  }
  if(err_del_cnt > 0){
    err_fnd <- TRUE
    bodytext_err <- paste0(bodytext_err, '<li>',
                           err_del_cnt, if(err_del_cnt > 1){" locations "}else{" location "}, 
                           'where <strong>the database DELETE statement ',
                           'failed</strong>. Previous transactions within the load date range were ',
                           'probably not deleted properly. Because of this the <strong>new ',
                           'transactions were not loaded</strong> to avoid creating duplicates.',
                           'See the DELETE column of the results for which locations FAILED.',
                           '</li>')
    #add delete failed column to results
    myDeleteStores_failed$DELETE <- "FAILED"
    err_Results <- err_Results %>% dplyr::left_join(myDeleteStores_failed)
  }
  
  #INSERT rows didn't match expected
  if(exists("myLoad_failed")){
    err_load_cnt <- nrow(myLoad_failed)
  }else{
    err_load_cnt <- 0
  }
  if(err_load_cnt > 0){
    err_fnd <- TRUE
    bodytext_err <- paste0(bodytext_err, '<li>',
                           err_load_cnt, if(err_load_cnt > 1){" locations "}else{" location "}, 
                           'where <strong>the database INSERT statement ',
                           'failed</strong>. The number of transactions present in the database ',
                           'after the insert (TRANS LOADED column) didn\'t match the number in ',
                           'the TRANS_PROCESSED column in the results. Any previous transactions ',
                           'withing the report dates would have been deleted, so the location is ',
                           'likely missing some of the needed transactions. <em>(note-the ',
                           'TRANS LOADED column is only populated for locations with this issue)</em>',
                           '</li>')
    #add TRANS_LOADED column to results, after TRANS_REJECTED
    err_Results <- err_Results %>% dplyr::left_join(myLoad_failed)
  }
  
  #end error list HTML
  bodytext_err <- paste0(bodytext_err, '</ul>')
  
  if(err_fnd){
    #errors detected, create file of results and email it
    base::message("ISSUES DETECTED, sending warning email!")
    myFN_noEXT <- rptFN_noEXT
    myFN <- rptFN
    mySN <- paste0("RESULTS ", format(Sys.Date(), "%d-%b-%y"), " ", report.time.txt)
    #replace underscores in column names with spaces
    names(err_Results) <- gsub("_"," ",names(err_Results))
    #prepare Excel file
    myXLSXColWidths <- data.frame (colname  = c("LOCATION",
                                                "BANK",
                                                "B ID LOCAL",
                                                "ACCT START DATE",
                                                "ACCT END DATE",
                                                "IDENTIFIER",
                                                "FILENAME",
                                                "SHEETNAME",
                                                "GSHT LINK",
                                                "TRANS REJECTED",
                                                "TRANS PROCESSED",
                                                "DELETE",
                                                "TRANS_LOADED"
                                                #"",
    )
    ,
    width = c(10.75,
              23,
              7.5,
              10.75,
              10.75,
              if(max(nchar(na.omit(err_Results[,"IDENTIFIER"]))) > 14){min(28, max(nchar(na.omit(err_Results[,"IDENTIFIER"]))))}else{14},
              if(max(nchar(na.omit(err_Results[,"FILENAME"]))) > 35){min(60, max(nchar(na.omit(err_Results[,"FILENAME"]))))}else{35},
              23.5,
              20,
              11,
              13,
              8.5,
              9
    )
    ,
    stringsAsFactors = FALSE
    ) #myXLSXColWidths
    
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = err_Results,
              colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    
    
    #send error email
    if(load_db){
      # load version
      bodytext <- paste0(
        "<p>This is an automated email to inform you that it appears there ",
        "may have been issues in the '", myReportName, 
        "' routine for the ", email.start, " to ", email.end, " import.</p>",
        "<p>Transactions may not have imported as intended and a file ",
        "summarizing results is attached.</p> ",
        "<b>ISSUES IDENTIFIED:</b><br>",
        bodytext_err,
        "<br>",
        norm_sig
      )
    }else{
      # test version
      bodytext <- paste0(
        "<h3>TEST LOAD: Checking for missing data!</h3>",
        "<p>This is an automated email to inform you that it appears there ",
        "may be data issues for the '", myReportName, 
        "' routine ", email.start, " to ", email.end, " import.</p>",
        "<p><b>THIS WAS A TEST RUN AND DATA WAS NOT LOADED.</b> ",
        "If this had been the final load attempt there may have been ",
        "transactions that wouldn't have loaded as intended. A file ",
        "summarizing results is attached.</p> ",
        "<b><em>TEST LOAD</em> ISSUES IDENTIFIED:</b><br>",
        bodytext_err,
        "<br>",
        norm_sig
      )
    }

    #send mail
    mailsend(warn_recip,
             paste0(myReportName, if(!load_db){" **TEST**"}, " Import Issues"),
             bodytext,
             attachment = myemailfiles,
             inline = FALSE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}






DBI::dbDisconnect(myOracleDB)

################################################################
  




### functions under development ###








