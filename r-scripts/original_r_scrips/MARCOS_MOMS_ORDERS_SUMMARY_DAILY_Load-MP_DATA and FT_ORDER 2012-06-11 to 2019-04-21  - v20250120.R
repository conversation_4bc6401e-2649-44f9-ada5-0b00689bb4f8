# special MP_DATA and FT-ORDER table based load of MOMS.ORDERS_SUMMARY_DAILY
# for data starting 2012-06-11: From 2018-10-02 through 2019-05-04 was mix of Pyrimont (MP) and FT, earlier than that is all Pyrimont (back to 2012-06-11)
# for data up to 2019-04-21 

library(xtable)
library(reshape2)
library(dplyr)
library(purrr)
library(lubridate)
library(formattable)
library(data.table)
library(mailR)
library(stringr)
library(utils)
library(tidyr)
library(DBI)
library(odbc)
library(keyring)
library(janitor)

library(ROracle)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
testing_emails <- TRUE

# Version 20250120

### 20250120 change:
### changed SQL to only gather net sales and orders

### 20241223 change:
### added new columns requested by <PERSON><PERSON> for some delivery food cost columns added to table

### 20240808 change:
### modified MAKETIME_COUNT column to ignore any 0 second maketimes instead of excluding certain item categories

### 20240807 change:
### modified mydates query so max E_DATE can be up to sysdate - 1 (previously last Sunday)
### modified main query for revised DELIVERY_3PD logic (uses payment_type instead of SOURCE)
### added TOTAL_FOOD_COST_IDEAL column that was left out of previous version

### 20240729 change:
### new script, based on then current version of
### MARCOS_FT_ORDER_SUMMARY_DAILY_Load.R
### ported for Snowflake DB and MOMS tables
### and to run on Steve Desktop (vs Tableau PC) due to SSO security

# Parameters
okaytocontinue <- TRUE

myReportName <- "Marco's MOMS.ORDERS_DAILY_SUMMARY-Snowflake Load"
scriptfolder <- "MARCOS_Daily_Reports"
rptfolder <- "reports"
logpath <- file.path("C:","Users","steve","Documents","ReportFiles",scriptfolder)
HVSigLogopath <- file.path("C:","Users","steve","Documents","ReportFiles","HV Logo Email Signature.png")

#ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
#Sys.setenv(TZ="GMT")
#Sys.setenv(ORA_SDTZ="GMT")
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)


###STAGE Snowflake versions###
#Sf_DB <- "STAGE_CSM_DB"
#Sf_schema <- "MOMS"
#Sf_wh <- "STAGE_DATA_ANA_WH"
#Sf_role <- "AR_STAGE_CONSUMPTION_RW"
#Sf_user <- key_get("SfHV", "steve_ID")
#Sf_pw <- key_get("SfHV", "steve_PW")
###PROD Snowflake versions###
Sf_DB <- "PROD_CSM_DB"
Sf_schema <- "MOMS"
Sf_wh <- "PROD_DATA_ANA_WH"
Sf_role <- "AR_PROD_CONSUMPTION_RW"
Sf_user <- key_get("SfHV", "steve_ID")
Sf_pw <- key_get("SfHV", "steve_PW")

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = 'externalbrowser'
)
rm(Sf_user,Sf_pw)

mySchema <- "MOMS"
myTable <- "ORDERS_SUMMARY_DAILY"
myTableName <- paste(mySchema, myTable, sep = ".")



# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>","<EMAIL>")
warn_recip <- c("<EMAIL>") #testing purposes, normal is line above
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

test_computers <- c("STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{testing_pc <- FALSE}

if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",scriptfolder)
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

myReportPath <- file.path(logpath, rptfolder)

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  if(exists("norm_sig")){norm_sig <- paste0(norm_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
  if(exists("warn_sig")){warn_sig <- paste0(warn_sig, "<br/>", '<img src="', HVSigLogopath, '" width="420"> ')}
}

# date and time variables
#query.date <- format(Sys.Date(), "%Y-%m-%d")

#manual dates below to load MP_DATA and FT_ORDER net sales and order counts 2012-06-11 through 2019-04-21
#query.date <- "2012-08-12"
#query.date <- "2012-11-04" #2012-08-13:2012-11-04
#query.date <- "2013-01-27" #2012-11-05:2013-01-27
#query.date <- "2013-04-21" #2013-01-28:2013-04-21
#query.date <- "2013-07-14" #2013-04-22:2013-07-14
#query.date <- "2013-10-06" #2013-07-15:2013-10-06
#query.date <- "2013-12-29" #2013-10-07:2013-12-29
#query.date <- "2014-03-23" #2013-12-30:2014-03-23
#query.date <- "2014-06-15" #2014-03-24:2014-06-15
#query.date <- "2014-09-07" #2014-06-16:2014-09-07
#query.date <- "2014-11-30" #2014-09-08:2014-11-30
#query.date <- "2015-02-22" #2014-12-01:2015-02-22
#query.date <- "2015-05-17" #2015-02-23:2015-05-17
#query.date <- "2015-08-09" #2015-05-18:2015-08-09
#query.date <- "2015-11-01" #2015-08-10:2015-11-01
#query.date <- "2016-01-24" #2015-11-02:2016-01-24
#query.date <- "2016-04-17" #2016-01-25:2016-04-17
#query.date <- "2016-07-10" #2016-04-18:2016-07-10
#query.date <- "2016-10-02" #2016-07-11:2016-10-02
#query.date <- "2016-12-25" #2016-10-03:2016-12-25
#query.date <- "2017-03-19" #2016-12-26:2017-03-19
#query.date <- "2017-06-11" #2017-03-20:2017-06-11
#query.date <- "2017-09-03" #2017-06-12:2017-09-03
#query.date <- "2017-11-26" #2017-09-04:2017-11-26
#query.date <- "2018-02-25" #2017-11-27:2018-02-25
#query.date <- "2018-05-20" #2018-02-26:2018-05-20
#query.date <- "2018-08-12" #2018-05-21:2018-08-12
#query.date <- "2018-11-04" #2018-08-13:2018-11-04
#query.date <- "2019-01-27" #2018-11-05:2019-01-27
query.date <- "2019-04-21" #2019-01-28:2019-04-21


query.periods <- 2 #total number of additional periods to cover in update query
#query.periods <- 0 #0 will run just the period covered by query.date
query.period_offset <- 0 #trailing period(s) for END of update query (start is s_date for query.periods + query.period_offset)
rpt.date <- as.Date(query.date, "%Y-%m-%d")



### define some functions ###
mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     test = FALSE, testrecipient = NULL, reportname = myReportName){
  library(mailR)
  sender <- paste0(reportname, " <<EMAIL>>")
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  myreplyto <- myemail
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  send.mail(from = sender,
            to = recipients,
            replyTo = myreplyto,
            subject = subject,
            body = body,
            smtp = list(host.name = "smtp.gmail.com", 
                        port = 465, 
                        user.name = myemail,            
                        passwd = key_get("GMail", "steve"),
                        ssl = TRUE),
            authenticate = TRUE,
            attach.files = attachment,
            html = TRUE,
            inline = inline,
            send = TRUE)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0("INCOMPLETE DATA: ", ReportName)
    }
  }else{
    #problem with data load.
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0("LOAD ERROR: ", ReportName)
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



# Query Snowflake for recent ORDERSRESULT data, 
if(okaytocontinue){
  #Get dates from MP_CALENDAR for main query
  #SPECIAL LIMITATION TO NOT GO PAST 12/31/2023 (Because native MOMS data from Snowflake used after that point in regular script)
  myquery <- paste0(
    "
      select
      to_char(to_date(min(s_date))) as s_date
      , to_char(to_date(max(case when e_date > TO_DATE('2023-12-31','YYYY-MM-DD') then TO_DATE('2023-12-31','YYYY-MM-DD') else e_date end))) as e_date
      from CORPORATE.mp_calendar
      where cal_id >= (
        select i.cal_id - ", query.periods + query.period_offset, "
        from CORPORATE.mp_calendar i
        where to_date('", query.date, "') between i.s_date and i.e_date
      )
      and cal_id <= (
        select i.cal_id - ", query.period_offset, "
	      from CORPORATE.mp_calendar i
	      where to_date('", query.date, "') between i.s_date and i.e_date
      )
    "
  )
  mydates <- dbGetQuery(mySfDB, myquery)
  
  #Main Data Query
  
  
  ###ORACLE version for dates up to 04/21/2019###
  myquery <- paste0(
    "
      SELECT
      	STORE_NUMBER,
      	BUSINESS_DATE,
      	LY_DATE,
      	LW_DATE,
      	SUM(TOTAL_ORDERS) as TOTAL_ORDERS,
      	SUM(NET_SALES) as NET_SALES,
        NULL as GROSS_SALES,
        NULL as DELIVERY_ALL_COUNT,
        NULL as DELIVERY_ALL_NETSALES,
        NULL as DELIVERY_ALL_GROSSSALES,
        NULL as DELIVERY_ALL_RUNS,
        NULL as DELIVERY_NDEF_COUNT,
        NULL as DELIVERY_NDEF_OTD_TIME,
        NULL as DELIVERY_NDEF_DELIVERY_TIME,
        NULL as DELIVERY_NDEF_PROMISE_TIME,
        NULL as DELIVERY_NDEF_COUNT_L30,
        NULL as DELIVERY_NDEF_COUNT_G40,
        NULL as DELIVERY_NDEF_COUNT_G60,
        NULL AS DELIVERY_3PD_COUNT,
        NULL AS DELIVERY_3PD_NETSALES,
        NULL AS DELIVERY_3PD_GROSSSALES,
        NULL as DELIVERY_DDD_COUNT,
        NULL AS DELIVERY_DDD_NETSALES,
        NULL AS DELIVERY_DDD_GROSSSALES,
        NULL AS WEB_COUNT,
        NULL AS WEB_NETSALES,
        NULL AS WEB_GROSSSALES,
        NULL AS APP_COUNT,
        NULL AS APP_NETSALES,
        NULL AS APP_GROSSSALES,
        NULL AS MAKETIME_COUNT,
        NULL AS MAKETIME_TIME,
        NULL as TOTAL_FOOD_COST_IDEAL,
        NULL AS TOTAL_FOOD_COST_VOID,
        NULL AS DELIVERY_ALL_FOOD_COST_IDEAL,
        NULL AS DELIVERY_3PD_FOOD_COST_IDEAL,
        NULL AS DELIVERY_DDD_FOOD_COST_IDEAL
      FROM
      (  
	      select  /* MP_DATA data */
	        a.STORE_NUMBER,
	        a.E_DATE AS BUSINESS_DATE,
	        a.E_DATE - 364 as LY_DATE,
	        a.E_DATE - 7 as LW_DATE,
	        sum(a.sag_total_orders) as TOTAL_ORDERS,
	        round(SUM(a.sag_net_sales),2) as NET_SALES,
	        NULL as GROSS_SALES,
	        NULL as DELIVERY_ALL_COUNT,
	        NULL as DELIVERY_ALL_NETSALES,
	        NULL as DELIVERY_ALL_GROSSSALES,
	        NULL as DELIVERY_ALL_RUNS,
	        NULL as DELIVERY_NDEF_COUNT,
	        NULL as DELIVERY_NDEF_OTD_TIME,
	        NULL as DELIVERY_NDEF_DELIVERY_TIME,
	        NULL as DELIVERY_NDEF_PROMISE_TIME,
	        NULL as DELIVERY_NDEF_COUNT_L30,
	        NULL as DELIVERY_NDEF_COUNT_G40,
	        NULL as DELIVERY_NDEF_COUNT_G60,
	        NULL AS DELIVERY_3PD_COUNT,
	        NULL AS DELIVERY_3PD_NETSALES,
	        NULL AS DELIVERY_3PD_GROSSSALES,
	        NULL as DELIVERY_DDD_COUNT,
	        NULL AS DELIVERY_DDD_NETSALES,
	        NULL AS DELIVERY_DDD_GROSSSALES,
	        NULL AS WEB_COUNT,
	        NULL AS WEB_NETSALES,
	        NULL AS WEB_GROSSSALES,
	        NULL AS APP_COUNT,
	        NULL AS APP_NETSALES,
	        NULL AS APP_GROSSSALES,
	        NULL AS MAKETIME_COUNT,
	        NULL AS MAKETIME_TIME,
	        NULL as TOTAL_FOOD_COST_IDEAL,
	        NULL AS TOTAL_FOOD_COST_VOID,
	        NULL AS DELIVERY_ALL_FOOD_COST_IDEAL, /* added 2024-12-23 */
	        NULL AS DELIVERY_3PD_FOOD_COST_IDEAL, /* added 2024-12-23 */
	        NULL AS DELIVERY_DDD_FOOD_COST_IDEAL /* added 2024-12-23 */
	      from mp_data a
	      --WHERE a.E_DATE >= to_date('2019-01-01','yyyy-mm-dd') /* TESTING */
	      --AND a.E_DATE <= to_date('2019-01-08','yyyy-mm-dd') /* TESTING */
	      WHERE a.E_DATE >= to_date('", mydates$S_DATE[1] , "','yyyy-mm-dd')
	      and a.E_DATE <= to_date('", mydates$E_DATE[1] , "','yyyy-mm-dd')
	      GROUP BY 
	      	a.STORE_NUMBER,
	        a.E_DATE,
	        a.E_DATE - 364,
	        a.E_DATE - 7
	      
	      UNION ALL
	      
	      select  /* FT_ORDER data */
	        a.STORE_NUMBER,
	        a.DAY AS BUSINESS_DATE,
	        a.day - 364 as LY_DATE,
	        a.day - 7 as LW_DATE,
	        count(distinct(a.o_id)) as TOTAL_ORDERS,
	        round(sum(a.net),2) as NET_SALES,
	        NULL as GROSS_SALES,
	        NULL as DELIVERY_ALL_COUNT,
	        NULL as DELIVERY_ALL_NETSALES,
	        NULL as DELIVERY_ALL_GROSSSALES,
	        NULL as DELIVERY_ALL_RUNS,
	        NULL as DELIVERY_NDEF_COUNT,
	        NULL as DELIVERY_NDEF_OTD_TIME,
	        NULL as DELIVERY_NDEF_DELIVERY_TIME,
	        NULL as DELIVERY_NDEF_PROMISE_TIME,
	        NULL as DELIVERY_NDEF_COUNT_L30,
	        NULL as DELIVERY_NDEF_COUNT_G40,
	        NULL as DELIVERY_NDEF_COUNT_G60,
	        NULL AS DELIVERY_3PD_COUNT,
	        NULL AS DELIVERY_3PD_NETSALES,
	        NULL AS DELIVERY_3PD_GROSSSALES,
	        NULL as DELIVERY_DDD_COUNT,
	        NULL AS DELIVERY_DDD_NETSALES,
	        NULL AS DELIVERY_DDD_GROSSSALES,
	        NULL AS WEB_COUNT,
	        NULL AS WEB_NETSALES,
	        NULL AS WEB_GROSSSALES,
	        NULL AS APP_COUNT,
	        NULL AS APP_NETSALES,
	        NULL AS APP_GROSSSALES,
	        NULL AS MAKETIME_COUNT,
	        NULL AS MAKETIME_TIME,
	        NULL as TOTAL_FOOD_COST_IDEAL,
	        NULL AS TOTAL_FOOD_COST_VOID,
	        NULL AS DELIVERY_ALL_FOOD_COST_IDEAL, /* added 2024-12-23 */
	        NULL AS DELIVERY_3PD_FOOD_COST_IDEAL, /* added 2024-12-23 */
	        NULL AS DELIVERY_DDD_FOOD_COST_IDEAL /* added 2024-12-23 */
	      from ft_order a
	      --WHERE a.DAY >= to_date('2019-01-01','yyyy-mm-dd') /* TESTING */
	      --AND a.DAY <= to_date('2019-01-08','yyyy-mm-dd') /* TESTING */
	      WHERE a.day >= to_date('", mydates$S_DATE[1] , "','yyyy-mm-dd')
	      and a.day <= to_date('", mydates$E_DATE[1] , "','yyyy-mm-dd')
	      group by a.store_number, a.day, a.day - 364, a.day - 7
      ) COMBINED
      GROUP BY
        STORE_NUMBER,
      	BUSINESS_DATE,
      	LY_DATE,
      	LW_DATE
      ORDER BY 
      	STORE_NUMBER,
      	BUSINESS_DATE
    "
  )
  #mydata <- dbGetQuery(mySfDB, myquery)
  ###ORACLE VERSION###
  mydata <- dbGetQuery(myOracleDB, myquery)
  
  #convert Oracle business_date coming in as POSIXct to R Date
  mydata[] <- lapply(mydata[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  
  mydata_status <- check_mydf_rows(mydata, MinNumRows = (28 * (query.periods - 1)), ReportName = myReportName)
  if(mydata_status[[1]]){
    
    
  }else{
    #apparent error, send warning email
    okaytocontinue <- FALSE
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error querying the man-hours for the ", myReportName, " routine! ",
                       "The query failed or produced less than 24 rows of results.</p>",
                       "<br>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Query error, missing data"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}





#Load Snowflake
if(okaytocontinue){
  #Delete rows within expected date range and replace with current data
  myDeleteRows_failed <- FALSE
  myLoadRows_failed <- FALSE
  myDeleteError_text <- ""
  
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName, "
      where BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
      and BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
    "
  )
  #rs_sel <- dbSendQuery(mySfDB, myquery_select)
  #select_cnt <- dbFetch(rs_sel, n = -1)
  #dbClearResult(rs_sel)
  select_cnt <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
  dbBegin(mySfDB)
  myquery_delete <- paste0(
    "
      delete from ", myTableName, "
      where BUSINESS_DATE >= to_date('", mydates$S_DATE[1], "')
      and BUSINESS_DATE <= to_date('", mydates$E_DATE[1], "')
      "
  )
  rs_del <- dbSendQuery(mySfDB, myquery_delete)
  delete_cnt <- dbGetRowsAffected(rs_del)
  if(delete_cnt != select_cnt){
    #delete failed
    warning("dubious deletion -- rolling back transaction")
    dbRollback(mySfDB)
    myDeleteRows_failed <- TRUE
    myDeleteError_text <- paste0(
      "<p>There was an unexpected issue deleting previous data that might ",
      "have been present for dates between ", mydates$S_DATE[1],
      " and ", mydates$E_DATE[1],". <b>The routine has ",
      "ABORTED without attempting to load the current results!</b></p>"
    )
    dbClearResult(rs_del)
    #do not load since deletion apparently failed
  }else{
    #delete was apparently successful, commit and proceed with load of these locations
    dbCommit(mySfDB)
    dbClearResult(rs_del)
    #Insert trans rows into myTable, count rows before and after to catch load issues
    myquery_select <- paste0(
      "
        select count(*)
        from ", myTableName, "
        where BUSINESS_DATE >= to_date('", mydates$S_DATE[1] , "')
        and BUSINESS_DATE <= to_date('", mydates$E_DATE[1] , "')
      "
    )
    #rs_sel <- dbSendQuery(mySfDB, myquery_select)
    #select_cnt_pre <- dbFetch(rs_sel, n = -1)
    #dbClearResult(rs_sel)
    select_cnt_pre <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    rs_write <- dbWriteTable(mySfDB, myTable, mydata, row.names = FALSE , append = TRUE, schema = mySchema)
    #get new count of rows in table
    #rs_sel <- dbSendQuery(mySfDB, myquery_select)
    #select_cnt_post <- dbFetch(rs_sel, n = -1)
    select_cnt_post <- dbGetQuery(mySfDB, myquery_select) %>% .[1,1]
    #myload_numrows <- select_cnt_post[[1]] - select_cnt_pre[[1]]
    myload_numrows <- select_cnt_post - select_cnt_pre
    mydata_numrows <- nrow(mydata)
    if(myload_numrows != mydata_numrows){
      #mis-match in rows loaded, get load counts by store
      myquery <- paste0(
        "
          select store_number
          ,   count(BUSINESS_DATE) as COUNT_LOADED
          from ", myTableName, "
          where BUSINESS_DATE >= to_date('", mydates$S_DATE[1], "')
            and BUSINESS_DATE <= to_date('", mydates$E_DATE[1], "')
          group by store_number
          order by store_number
          "
      )
      myLoadStores_curr <- dbGetQuery(mySfDB, myquery)
      #summarize dataframe to get counts by store
      myLoadStores_results_cnt <- mydata %>% select(STORE_NUMBER) %>% group_by(STORE_NUMBER) %>% summarize(QUERY_RESULTS = n())
      myLoad_failed <- myLoadStores_results_cnt %>% 
        dplyr::left_join(myLoadStores_curr, by = c("STORE_NUMBER")) %>%
        mutate_if(is.numeric,coalesce,0) %>%
        .[which(.$QUERY_RESULTS != .$COUNT_LOADED), ]
      
      
      if(nrow(myLoad_failed)>0){
        myLoadRows_failed <- TRUE
        myLoadError_text <- paste0(
          "<p>One or more stores failed to load the expected number or rows ",
          "into Snowflake for the dates between ", mydates$S_DATE[1],
          " and ", mydates$E_DATE[1],". <b>The Snowflake table (",
          myTableName, ")", " will have incomplete results for these dates!</b> ",
          "Investigate the stores shown below and re-run this routine when issue ",
          "has been addressed.<br>",
          print(
            xtable(myLoad_failed, 
                   digits = rep(0,ncol(myLoad_failed)+1),
                   align = c(rep("c", ncol(myLoad_failed) + 1))
            ),
            html.table.attributes = 'border=2 cellspacing=1 align="center"',
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          ),
          "</p>"
        )
      }
    }
    
    
    
  }
  if(myDeleteRows_failed || myLoadRows_failed){
    #email warning
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there has ",
                       "been an error populating Snowflake in the ", 
                       myReportName, " routine. </p>",
                       if(myDeleteRows_failed){myDeleteError_text},
                       if(myLoadRows_failed){myLoadError_text},
                       "<br>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Snowflake load error"),
             bodytext,
             attachment = NULL,
             inline = TRUE,
             test = testing_emails, testrecipient = test_recip
    )
  }
}

