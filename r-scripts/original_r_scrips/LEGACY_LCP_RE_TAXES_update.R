#library(RODBC) #20250207: disabled
library(xtable)
library(reshape2)
library(dplyr)
#library(RDCOMClient) #20250207: disabled
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #20250207: disabled with move to gmailr
library(stringr)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)
library(DBI)
library(odbc)

# written by <PERSON> February 2022

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
testing_emails <- TRUE


# Version 20250207

### 20250207 change
### moved to gmailr for emails, moved to Snowflake CORPORATE.LCP_RE_TAXES table (from Oracle STEVE.RE_TAXES_DATA)

### 20220606 change:
### updated mailsend to use keyring

### 20210804 change:
### paths for Tableau PC and testing PC updated due
### to replaced hard drives (new user paths)


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
#next line is for testing or loading previous time-frames only, normally should be commented out
#query.date <- '11-OCT-19'
#query.daynum <- as.integer(format(as.Date(query.date, "%d-%b-%y"),"%w"))
okaytocontinue <- TRUE

scriptfolder <- "LEGACY_RE_TAXES_DATA"
myReportName <- "Real Estate Taxes Update"
myScriptName <- "LEGACY_LCP_RE_TAXES_update.R"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
Sys.sleep(1)

#20250207: myTableName <- "STEVE.RE_TAXES_DATA"
mySchema <- "CORPORATE"
myTable <- "LCP_RE_TAXES"
myTableName <- paste(mySchema, myTable, sep = ".")
gSht_auth_email <- "<EMAIL>"
gSht_url <- "https://docs.google.com/spreadsheets/d/1VsMKcmoRpkiid8MsHM2tb1OX4CS0UuVDXY_Jc77BhA0/edit#gid=0"
gSht_key <- as_id(gSht_url)

#20250207: mydb <- odbcConnect("FVPA64", "deanna", key_get("Oracle", "deanna"))

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Purchasing Analyst<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")


centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- file.path(logpath)


### define some functions ###

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null || 'NULL')] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


###Get email signatures###
get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
sig_logo <- FALSE
if(file.exists(HVSigPath)){
  sigName <- 'NA'
  sigTitle <- 'NA'
  sigEmail <- 'NA'
  sigTemplate <- 'LCP Reporting' #LCP Reporting doesn't use any personal info substitutions
  sigPhone <- 'NA'
  #update_log(
  #  event = "Signature Template", 
  #  desc = paste0("Creating...PARAMS - Name: ", sigName, "; Email: ", sigEmail, "; Signature Template: ", sigTemplate)
  #)
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == sigTemplate)],
    Name = sigName,
    Title = sigTitle,
    Email = sigEmail,
    Phone = sigPhone
  )
}
warn_sig <- norm_sig



#--Query existing table data to compare to new run--#
if(okaytocontinue){
  myquery <- paste0("select 
                      *
                    from ", myTableName
  )
  #20250207: priordata <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  priordata <- dbGetQuery(mySfDB, myquery)
}




#--Query new data from Google Sheet--#
# check google sheet status
if(okaytocontinue){
  #20250207: gs4_auth(email = "<EMAIL>")
  gs4_auth(email = gSht_auth_email)
  gSht_get <- gs4_get(gSht_key)
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    #read data in from desired sheet
    gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RE Taxes Data")
  }else{
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading Google Sheet in the ", myReportName, "!</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: No Google Sheet Tax Rows"),
             bodytext
    )
    okaytocontinue <- FALSE
  }
}




if(okaytocontinue){
  #existing and new names for columns
  gSht_bldg_colname <- 'Building #'
  gSht_bldg_colname_New <- 'BLDG'
  gSht_amount_colname <- 'Parcel Amount Entered in Oracle during this fiscal Year - Need to update Formula on annual basis'
  gSht_amount_colname_New <- 'AMOUNT'
  
  #copy only needed columns from data
  mydata <- dplyr::select(dplyr::filter(gSht_Orig, 
                                        #`Building #` < 10000 & !is.na(as.numeric(gSht_amount_colname))),
                          `Building #` < 10000),
                          all_of(gSht_bldg_colname),
                          all_of(gSht_amount_colname)
                          )
  #rename columns
  mydata_OrigRename <- c(gSht_bldg_colname, 
                       gSht_amount_colname)
  mydata_NewRename <- c(gSht_bldg_colname_New, 
                        gSht_amount_colname_New)
  setnames(mydata, 
           old = mydata_OrigRename, 
           new = mydata_NewRename,
           skip_absent = TRUE)
  
  #Convert Amount column to character (from list), then filter to numeric rows
  #this first attempts to cast as numeric resulting in alpha values being coerced to NA 
  #and then those NAs are left out for clean values
  mydata <- mydata %>% 
    mutate(across(all_of(gSht_amount_colname_New), as.character)) %>% 
    filter(!is.na(as.numeric(.data[[gSht_amount_colname_New]]))) 
  #convert amount column to numeric
  mydata <- as.data.frame(apply(mydata, 2, as.numeric))
  #summarise amount by bldg
  mydata <- mydata %>%  group_by(.data[[gSht_bldg_colname_New]]) %>% summarise(sum(.data[[gSht_amount_colname_New]]) )
  names(mydata)[2] <- gSht_amount_colname_New
  
  if(nrow(mydata) == 0){
    #send warning email, then continue. At turn of the year, it's possible to have 0 rows of results
    # create body of warning email
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error populating the ", myTableName,
                       " table. The routine returned 0 buildings with tax data and will truncate ",
                      "the table to 0 rows.</p>",
                       "<p>If this is expected result due to yearly turnover then no action needed.</p>",
                       "<p>Otherwise, check the '", gSht_get$name , "' Google sheet, URL: ",
                      gSht_get$spreadsheet_url, ". Alternately, check the ", myReportName, " routine located ",
                       "on the Tableau/Marcos desktop (located in the ", logpath," directory). </p>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: No Google Sheet Tax Rows"),
             bodytext
    )
    
  }
  
  # get column names and datatypes of Oracle table
  #20250207: mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve"))
  #20250207: tmpDBinfo <- sqlColumns(mydb, myTableName)
  #20250207: columnTypes <- as.character(tmpDBinfo$TYPE_NAME)
  #20250207: names(columnTypes) <- as.character(tmpDBinfo$COLUMN_NAME)
  
  # Trunc database table
  #20250207: myquery <- paste0('truncate table ', myTableName, ' drop storage')
  #20250207: myTrucResults <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  myquery_trunc <- paste0(
    "TRUNCATE TABLE ",
    myTable
  )
  rs <- dbGetQuery(mySfDB, myquery_trunc)
  
  #populate database
  #20250207: sqlSave(mydb, 
  #        mydata, 
  #        tablename = myTableName,  
  #        append = TRUE, 
  #        rownames = FALSE, 
  #        colnames = FALSE, 
  #        safer = TRUE,
  #        fast = FALSE,
  #        addPK = FALSE, 
  #        varTypes = columnTypes,
  #        nastring = NULL)
  rs_write <- dbAppendTable(mySfDB, Id(schema = mySchema, table = myTable), mydata)
  
  #compare sum of the AMOUNT column of the data frame to Oracle to ensure all rows inserted
  myquery <- paste0(
    "select sum(", gSht_amount_colname_New, ")
      from ", myTableName
  )
  #20250207: oracle_checksum <- sqlQuery(mydb, myquery, stringsAsFactors = FALSE)
  db_checksum <- dbGetQuery(mySfDB, myquery)
  
  mydata_checksum <- sum(as.numeric(mydata[[gSht_amount_colname_New]]),na.rm=TRUE)
  
  #20250207: if(round(oracle_checksum[[1]],2) != round(mydata_checksum,2)){
  if(round(db_checksum[[1]],2) != round(mydata_checksum,2)){
    #send warning that sums are different
    # create body of warning email
    bodytext <- paste0("This is an automated email to inform you that it appears there may ",
                       "have been an error in the ", myTableName,
                       " database load. The sum of the tax column in the Google sheet was: <br/>",
                       round(mydata_checksum,2), "<br/><br/>",
                       "The sum of the ", myTableName, " table is: <br/>",
                       round(db_checksum[[1]],2), "<br/><br/>",
                       "The query is contained in the ", myScriptName, " script on the ",
                       "Tableau/Marcos desktop (located in the ", logpath," directory). <br/> <br/>",
                       warn_sig,
                       sep = ""
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Mis-match of Google Sheet sum vs. Database Load"),
             body = bodytext
    )
    
  }else{
    #send completion email
    bodytext <- paste0("This is an automated email to inform you that it appears the ",
                       "<b>", myReportName, "</b> routine has ",
                       "completed and results should now be available in the ", 
                       myTableName, " table.<br/> <br/>",
                       warn_sig)
    #send mail
    #mailsend(norm_recip,
    #         paste0(myReportName, " Status: COMPLETE"),
    #         bodytext
    #)
  }
}


DBI::dbDisconnect(mySfDB)


