library(xtable)
library(reshape2)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyverse)
library(readxl)
library(DBI)
library(ROracle)
library(odbc)
library(keyring)
library(janitor)
library(stringr)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version 20241029

### 20241029 change:
### added section to load SNOWFLAKE DB in addition to Oracle
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message()
### updated email signature to use latest format provided by <PERSON> earlier in 2024
### updated 'test' computer path assignments

### 20240312 change:
### new file, first release (much of it based on HR Onboarding Reports script)


# Parameters
options(stringsAsFactors = FALSE)

myReportName <- "MARCOS OTU Codes to Database"
scriptfolder <- "MARCOS_OTU_Codes"
rptfolder <- "reports"
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles")
logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)


myemailfiles <- c()
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))
report.date.txt <- format(Sys.Date(), "%Y%m%d")
report.time.txt <- format(Sys.time(), "%H%M%S%Z")
process_dates_begin <- as.Date(report.date.txt,"%Y%m%d") - 6
#delete_dates_before
old_dates_delete <- format(as.Date(report.date.txt,"%Y%m%d") - 542,"%Y-%m-%d") #keep last 1.5 years of raw files
OTU_channel_delimiter <- "_"
myFiles_errors <- data.frame(name=character(), link=character(), 
                             channel=character(), issue=character() )
FN_fileerrors <- paste0(myReportName, ' - FILE ISSUES ', 
                        report.date.txt, " - ", report.time.txt, '.csv')

okaytocontinue <- TRUE

# Google sheets/Drive parameters, create empty df then add rows with needed info
gSht_info <- data.frame(FN=character(), F_ID=character(), SN=character(), S_GID=numeric())
gSht_info <- gSht_info %>% add_row(FN = "Workspace User Emails and Bulk Delete", F_ID = "1SjqqNRKSV2N25Z4GA53W4L-qCFgVU15faXt0qiOn7eA", SN = "All User Emails", S_GID = 149984372 )
#gSht_info <- gSht_info %>% add_row(FN = "", F_ID = "", SN = "", S_GID =  )
#gSht_info <- gSht_info %>% add_row(FN = as.character(NA), F_ID = as.character(NA), SN = as.character(NA), S_GID = as.numeric(NA) )

gSht_auth_email <- "<EMAIL>"

gDrv_mainURL <-"https://drive.google.com/drive/folders/1bsj3X2ug0Hp4lQ-6BpZXDlYtGxjHVtzh" #
gDrv_oldfilefoldername <- 'Processed Files'
gDrv_mainURL_email <- 'https://drive.google.com/drive/u/0/folders/19-z8UnvNk1LpRhW0JEhpLjYZb_RNmWOe'
gDrv_folder_prefix <- 'https://drive.google.com/drive/folders/' #append ID (for use when not directory owner: <EMAIL>)


# email parameters: recipient(s) of warning emails and signatures

warn_recip <- c("Steve Olson<<EMAIL>>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("Onboarding<<EMAIL>>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("Steve Olson<<EMAIL>>")
sig_logo <- FALSE

test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
if(Sys.getenv("COMPUTERNAME") %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- file.path(logpath, rptfolder)


### define some functions ###

# ROracle connection
Sys.setenv(TZ='America/Chicago')
Sys.setenv(ORA_SDTZ='America/Chicago')
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "steve", password =  key_get("Oracle", "steve"), dbname = connect.string)
#myOracleDB_deanna <- dbConnect(drv, username = "deanna", password =  key_get("Oracle", "deanna"), dbname = connect.string)



###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)


mySchema_ORA <- "STEVE"
myTable_ORA <- "MP_OTU_CODES"
myTableName_ORA <- paste(mySchema_ORA, myTable_ORA, sep = ".")

mySchema_SF <- "CORPORATE"
myTable_SF <- "MP_OTU_CODES"
myTableName_SF <- paste(mySchema_SF, myTable_SF, sep = ".")


#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

HVSigPath <- file.path(mainpath,"HTML_signatures.csv")

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get normal email signature###
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'HV Normal')],
    Name = 'Steve Olson',
    Title = 'Sr. Analytics Mgr.',
    Email = '<EMAIL>',
    Phone = '(*************'
  )
}

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}



check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)

getExtension <- function(file){
  tmp_results <- strsplit(file, ".", fixed=T)[[1]][-1]
  results <- tmp_results[[length(tmp_results)]]
  return(results)
}


has_class_v1 <- function(df, cls = "numeric") {
  names(df)[sapply(df, function(vec, clss) class(vec) %in% clss, clss = cls)]
}

read_OTU_file <- function(read_FN){
  my_result <- "No Data"
  if(endsWith(toupper(read_FN), ".CSV")){
    my_result <- read.csv(read_FN, header = FALSE, colClasses = "character")
    names(my_result) <- gsub("\\."," ",names(my_result))
  }else if(endsWith(toupper(read_FN), ".XLS") || endsWith(toupper(read_FN), ".XLSX")){
    my_result <- read_excel(read_FN, col_names = FALSE, col_types = "text")
  }else if(endsWith(toupper(read_FN), ".TXT")){
    my_result <- read.table(read_FN, header = FALSE, colClasses = "character")
    names(my_result) <- gsub("\\."," ",names(my_result))
  }
  if(is.data.frame(my_result)){
    if( (which.max(rowSums(is.na(my_result)) == ncol(my_result)) - 1) != 0){
      #trim trailing blank or unneeded rows at end of df
      my_result <- my_result[1:(which.max(rowSums(is.na(my_result)) == ncol(my_result)) - 1), ]
    }
  }
  return(my_result)
}


Convert_to_ColClass <- function(mydata, names_class_df){
  classes <- names_class_df$ColClass %>% unique(.) %>% toupper(.)
  myresult <- mydata
  if(length(classes)>=1){
    myresult <- mydata
    for(i in 1:length(classes)){
      curr_class <- classes[i]
      convert_cols <- names_class_df$NewName[which(toupper(names_class_df$ColClass) == curr_class)]
      if(curr_class == 'INTEGER'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.integer))}
      if(curr_class == 'NUMERIC'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.numeric))}
      if(curr_class == 'DATE'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.Date))}
      if(curr_class == 'CHARACTER'){myresult <- myresult %>% mutate(across(all_of(convert_cols), as.character))}
    }
  }
  
  
  return(myresult)
}

# Functions to retrieve email addresses and IDs of permissions (read/write) --------
get_permissions_df <- function(permission_list){
  map_df(permission_list, ~{
    if (!is.null(.$emailAddress)){
      tibble(user_email = .$emailAddress, user_role = .$role)
    } else {
      tibble(user_email = NA, user_role = NA)
    }
  })
}

get_permissions_df_IDs <- function(permission_list){
  map_df(permission_list, ~{
    if (!is.null(.$emailAddress)){
      tibble(user_email = .$emailAddress, user_role = .$role, permission_id = .$id)
    } else {
      tibble(user_email = NA, user_role = NA, permission_id = NA)
    }
  })
}

get_owners_df <- function(permission_list){
  map_df(permission_list, ~{
    if (!is.null(.$displayName)){
      tibble(displayName = .$displayName, me = .$me)
    } else {
      tibble(displayName = NA, me = NA)
    }
  })
}





###----------------------------------------------------------------------------------------###
# auth googledrive and googlesheets4, check if supplied folder URL is valid and files present#
###----------------------------------------------------------------------------------------###
if(okaytocontinue){
  isFolder <- FALSE
  filesFound <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    #auth okay, check if ID was for folder
    drv_get_main <- drive_get(id = as_id(gDrv_mainURL))
    isFolder <- drv_get_main$drive_resource[[1]]$mimeType == drive_mime_type("folder")
    if(!isFolder){
      okaytocontinue <- FALSE
    }else{
      #was folder
      # Get a tidy form of all shares and permissions of subfolders
      #tidy_permissions <- folder_contents %>% 
      tidy_permissions <- drv_get_main %>%
        mutate(new_creds = 
                 map(drive_resource, 
                     ~{get_permissions_df(.$permissions)})
        ) %>% 
        select(name, new_creds) %>% 
        unnest(new_creds) %>% 
        filter(!is.na(user_email)
        )
      folder_users <- tidy_permissions$user_email[tidy_permissions$user_email %notin% '<EMAIL>']
      
      # check if directory folder present, create if not
      #mySearch <- drive_ls(drv_get_main$id, recursive = TRUE) #dribble of drv_get_main contents
      mySearch <- drive_ls(drv_get_main$id, recursive = FALSE) #dribble of drv_get_main contents
      myFolders <- mySearch %>% 
        hoist(drive_resource, "mimeType") %>% 
        filter(mimeType == drive_mime_type("folder")) #%>% filter(name %in% gDrv_oldfilefoldername)
      myFolder_processed <- myFolders %>% filter(name %in% gDrv_oldfilefoldername)
      if(nrow(myFolder_processed) == 0){
        #myFolders_ToProcess <- myFolders
        #create folder
        myFolder_processed <- drive_mkdir(
          gDrv_oldfilefoldername, 
          path = drv_get_main$id[[1]], 
          description = paste0("Folder for previously processed onboarding report files")
        )
      }else{
        #look for old files to delete
        q_text <- paste0("createdTime < '", old_dates_delete, "'")
        myFiles_processed <- drive_ls(as_id(myFolder_processed), q = q_text)
        if(nrow(myFiles_processed)>0){
          #get owner
          myFiles_processed <- myFiles_processed %>%
            hoist(drive_resource, 
                  "mimeType",
                  #parent = list("parents", 1L), #not needed when folders not being used to control 'channel'
                  created = "createdTime"
            ) %>% 
            filter(mimeType != drive_mime_type("folder"))
          myFiles_to_delete <- myFiles_processed %>%
            mutate(new_creds = 
                     map(drive_resource, 
                         ~{get_owners_df(.$owners)})
            ) %>% 
            select(name, id, new_creds) %>% 
            unnest(new_creds) %>% 
            filter(!is.na(displayName)) %>%
            filter(me)
        }
      }
      #get current files in folder
      #mySearch <- drive_ls(drv_get_main$id, recursive = FALSE) #dribble of drv_get_main contents
      myFiles <- mySearch %>% 
        hoist(drive_resource, 
              "mimeType",
              #parent = list("parents", 1L), #not needed when folders not being used to control 'channel'
              created = "createdTime",
              link = "webViewLink"
              ) %>% 
        filter(mimeType != drive_mime_type("folder")) %>%
        filter(mimeType != "application/vnd.google-apps.spreadsheet")
        #filter(parent %notin% myFolder_processed$id)
      myFiles$created <- as_date(myFiles$created)
      myFiles_Current <- myFiles %>% filter(created >= process_dates_begin)
      #add column with ucase channel from filename
      myFiles_Current$channel <- stringr::word(myFiles_Current$name, 1, sep = OTU_channel_delimiter) %>% toupper()
      myFiles_temperrors <- myFiles_Current %>%
        filter(toupper(name) == channel) %>%
        select(name, link, channel)
      if(nrow(myFiles_temperrors)>0){
        myIssue <- paste0("CODES IN THIS FILE NOT LOADED! Channel not properly ",
                          "delimited using the '", OTU_channel_delimiter, "' ",
                          "character in the filename."
                          
                          )
        myFiles_temperrors$issue <- myIssue
        myFiles_errors <- rbind(myFiles_errors, myFiles_temperrors)
        #remove channel errors from files to be processed
        myFiles_Current <- myFiles_Current %>%
          filter(link %notin% myFiles_temperrors$link)
        rm(myFiles_temperrors)
      }
      #if(nrow(myFiles) == 0){
      if(nrow(myFiles_Current) == 0){
        okaytocontinue <- FALSE #files not found
      }else{
        #files are present, populated found file info in needed_files
        filesFound <- TRUE
        #filter folders list to ones containing current files found
        #myFolders_ToProcess <- myFolders %>% 
        #  filter(name %notin% gDrv_oldfilefoldername) %>%
        #  filter(id %in% unique(myFiles_Current$parent))
        MyFiles_owner <- myFiles_Current %>%
          mutate(new_creds = 
                   map(drive_resource, 
                       ~{get_owners_df(.$owners)})
          ) %>% 
          select(name, id, new_creds) %>% 
          unnest(new_creds) %>% 
          filter(!is.na(displayName))
      }
    }
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue & nrow(myFiles_errors)>0){
    #write file errors to file if present
    fn <- file.path(myReportPath, FN_fileerrors)
    write.csv(myFiles_errors, file = fn, row.names=FALSE, na = "")
    myemailfiles <- c(myemailfiles, fn)
    
    #email failure or abort reason
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google, the needed Drive folder or ",
                       "issues with the files for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Folder URL resolved: ", isFolder, "</li>",
                       "<li>Current Files Found: ", filesFound, "</li>",
                       "<li>File Issues Found (see attached .csv): ", nrow(myFiles_errors), "</li>",
                       "</ul></p>",
                       if(exists("bodytable")){
                         paste0(
                           "<br><p><b>Report files needed vs. filenames found in Drive folder:</b><br>",
                           print(xtable(bodytable, 
                                        digits = rep(0,ncol(bodytable)+1),
                           ),
                           html.table.attributes = "border=2 cellspacing=1",
                           type = "html",
                           caption.placement = "top",
                           include.rownames=FALSE
                           ),
                           "</p>"
                         )
                       },
                       "<br>",
                       warn_sig
    )
    #send mail
    mailsend(recipient = folder_users,
             subject = paste0(myReportName, ": Google or File Issues"),
             body = bodytext,
             attachment = myemailfiles,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }else{
    #routine will continue, okay to remove older processed file copies
    drv_get_old <- drive_get(id = as_id(myFolder_processed$id))
    mySearch <- drive_ls(drv_get_old$id, recursive = FALSE) #dribble of drv_get_old contents
    if(nrow(mySearch)>0){
      myFilesOld <- mySearch %>% 
        hoist(drive_resource, "mimeType") %>% 
        filter(mimeType != drive_mime_type("folder"))
      if(nrow(myFilesOld)>0){
        for(i in 1:nrow(myFilesOld)){
          if(substr(myFilesOld$name[i], 1, 8) %in% old_dates_delete){
            #remove old file
            drive_trash(file = as_id(myFilesOld$id[i]))
            Sys.sleep(2)
          }
        }
      }
    }
  }
}


###----------###
#Read OTU files#
###----------###
if(okaytocontinue){
  myChannels <- myFiles_Current$channel %>% unique()
  myCurrLoad <- data.frame(CODE_RAW=character(), CHANNEL=character(), CODE_REPORTING=character())
  #for(i in 1:nrow(myFolders_ToProcess)){
  for(Channel in myChannels){
    #currFolderName <- myFolders_ToProcess$name[i]
    #currFolderID <- myFolders_ToProcess$id[i]
    curr_Files <- myFiles_Current[which(myFiles_Current$channel == Channel),]
    #read files
    for(file_id in curr_Files$id){
      FN <- curr_Files$name[which(curr_Files$id == file_id)]
      #download
      #dl_file <- googledrive::drive_download(file = as_id(file_id), overwrite = TRUE)
      dl_file <- drive_download(file = as_id(file_id), overwrite = TRUE)
      currOwnerIsMe <- MyFiles_owner$me[which(MyFiles_owner$id == file_id)]
      currOwnerName <- MyFiles_owner$displayName[which(MyFiles_owner$id == file_id)]
      currData <- read_OTU_file(read_FN = FN)
      if(nrow(currData)>0){
        currData <- data.frame(currData[,1])
        currData <- data.frame(currData[which(currData[,1] %notin% c("RECEIVED: ", "Coupon Number")),])
        colnames(currData)[1] <- "CODE_RAW"
        currData$CHANNEL <- Channel
        #format in MOMS reporting style
        currData$CODE_REPORTING <- paste0('SUC_',gsub("-","",tolower(currData$CODE_RAW)))
        myCurrLoad <- rbind(myCurrLoad, currData)
        myCurrLoad <- unique(myCurrLoad)
        #delete temp local file and copy in Drive to processed folder
        unlink(FN)
        #move Drive file to processed folder
        if(currOwnerIsMe){
          with_drive_quiet(
            drive_mv(
              file = as_id(file_id),
              path = as_id(myFolder_processed),
              overwrite = TRUE,
            )
          )
        }else{
          myFiles_temperrors <- curr_Files[which(curr_Files$id == file_id) ,c("name","link","channel")]
          myFiles_temperrors$issue <- paste0("Can't move file to '",
                               myFolder_processed$name,
                               "' folder because owner is '",
                               currOwnerName,
                               "'...transfer ownership to ",
                               gSht_auth_email,
                               " AND manually move to folder."
                               )
          myFiles_errors <- rbind(myFiles_errors, myFiles_temperrors)
        }
        
      }
      rm(currData)
    }

  }
  if(nrow(myCurrLoad)==0){okaytocontinue <- FALSE}
}


###-------------------------------###
#load data into Oracle and Snowflake#
###-------------------------------###
if(okaytocontinue){
  #myOracleDB
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName_ORA, "
      "
  )
  start_cnt <- dbGetQuery(myOracleDB, myquery_select)
  
  #attempt load of codes into ORACLE
  #rs_write <- dbWriteTable(myOracleDB, myTable_ORA, myCurrLoad, row.names = FALSE , append = TRUE, schema = mySchema_ORA)
  rs_write <- tryCatch(
    expr = {
      dbWriteTable(myOracleDB, myTable_ORA, myCurrLoad, row.names = FALSE , append = TRUE, schema = mySchema_ORA)
      print('Values written to database.')
    },
    error = function(cond) {
      err_msg <- paste0("<p>Here's the original error message: <em>",
                        conditionMessage(cond),
                        "</em></p>"
                        )
      #message("Error writing values to database! Here's the original error message:")
      #message(conditionMessage(cond))
      # Choose a return value in case of error
      print(err_msg)
    },
    warning = function(cond) {
      message(paste0("Warning: ", conditionMessage(cond)))
      # Choose a return value in case of warning
      NULL
    }
  )    
  if(substr(rs_write,1,8)=="<p>Error"){write_error <- rs_write}else{write_error <- ""}
  
  end_cnt <- dbGetQuery(myOracleDB, myquery_select)
  myload_numrows <- end_cnt[1,1] - start_cnt[1,1]
  mydata_numrows <- nrow(myCurrLoad)
  if(myload_numrows != mydata_numrows){
    #determine missing codes
    myquery <- paste0(
      "select
        CODE_RAW
      , CHANNEL
      , CODE_REPORTING
      from ", myTableName_ORA
    )
    tableData <- dbGetQuery(myOracleDB, myquery)
    myCurrLoad_diffs <- setdiff(myCurrLoad, tableData)
    FN_diffs <- paste0(myReportName,
                       " ORACLE - CODES NOT LOADED ",
                       report.date.txt,
                       " - ",
                       report.time.txt
                       , ".csv"
    )
    fn <- file.path(myReportPath, FN_diffs)
    write.csv(myCurrLoad_diffs, file = fn, row.names=FALSE, na = "")
    
    myemailfiles <- c(myemailfiles, fn)
    #write diffs to Excel and attach to warning email
    bodytext_loadissues_ORA <- paste0(
      "<hr>",
      "<h3>ORACLE LOAD COUNT ISSUE</h3>",
      "<p>The routine read ",
      mydata_numrows, " codes, but ", myload_numrows, " ended up being ",
      "loaded. It's possible this might be due to codes being present ",
      "in more than one source file (duplicates cause 'unique constraint' ",
      "errors if they are already in table from previous load)...if ",
      "that's the case those codes won't be present in attached file.</p> ",
      write_error,
      if(nrow(myCurrLoad_diffs)>0){
        paste0("<p><strong>The codes that didn't load are in the attached ",
        FN_diffs," file for manual loading.</strong></p>")
      }else{
        
      },
      "<hr>"
    )
    okaytocontinue <- FALSE #this will prevent deleting old 'processed' files until
    #it runs without error
  }
  
  
  
  
  
  
  
  
  #SNOWFLAKE LOADING SECTION
  myquery_select <- paste0(
    "
      select count(*)
      from ", myTableName_SF, "
      "
  )
  start_cnt <- dbGetQuery(mySfDB, myquery_select)
  
  #attempt load of codes into SNOWFLAKE
  #20241029: rs_write <- dbWriteTable(mySfDB, myTable_ORA, myCurrLoad, row.names = FALSE , append = TRUE, schema = mySchema_ORA)
  rs_write <- tryCatch(
    expr = {
      #20241029: dbWriteTable(myOracleDB, myTable_ORA, myCurrLoad, row.names = FALSE , append = TRUE, schema = mySchema_ORA)
      dbAppendTable(mySfDB, Id(schema = mySchema_SF, table = myTable_SF), myCurrLoad)
      print('Values written to database.')
    },
    error = function(cond) {
      err_msg <- paste0("<p>Here's the original error message: <em>",
                        conditionMessage(cond),
                        "</em></p>"
      )
      #message("Error writing values to database! Here's the original error message:")
      #message(conditionMessage(cond))
      # Choose a return value in case of error
      print(err_msg)
    },
    warning = function(cond) {
      message(paste0("Warning: ", conditionMessage(cond)))
      # Choose a return value in case of warning
      NULL
    }
  )    
  if(substr(rs_write,1,8)=="<p>Error"){write_error <- rs_write}else{write_error <- ""}
  
  #20241029: end_cnt <- dbGetQuery(myOracleDB, myquery_select)
  end_cnt <- dbGetQuery(mySfDB, myquery_select)
  myload_numrows <- end_cnt[1,1] - start_cnt[1,1]
  mydata_numrows <- nrow(myCurrLoad)
  if(myload_numrows != mydata_numrows){
    #determine missing codes
    myquery <- paste0(
      "select
        CODE_RAW
      , CHANNEL
      , CODE_REPORTING
      from ", myTableName_SF
    )
    #20241029: tableData <- dbGetQuery(myOracleDB, myquery)
    tableData <- dbGetQuery(mySfDB, myquery)
    myCurrLoad_diffs <- setdiff(myCurrLoad, tableData)
    FN_diffs <- paste0(myReportName,
                       " SNOWFLAKE - CODES NOT LOADED ",
                       report.date.txt,
                       " - ",
                       report.time.txt
                       , ".csv"
    )
    fn <- file.path(myReportPath, FN_diffs)
    write.csv(myCurrLoad_diffs, file = fn, row.names=FALSE, na = "")
    
    myemailfiles <- c(myemailfiles, fn)
    #write diffs to Excel and attach to warning email
    bodytext_loadissues <- paste0(
      "<hr>",
      "<h3>SNOWFLAKE LOAD COUNT ISSUE</h3>",
      "<p>The routine read ",
      mydata_numrows, " codes, but ", myload_numrows, " ended up being ",
      "loaded. It's possible this might be due to codes being present ",
      "in more than one source file (duplicates cause 'unique constraint' ",
      "errors if they are already in table from previous load)...if ",
      "that's the case those codes won't be present in attached file.</p> ",
      write_error,
      if(nrow(myCurrLoad_diffs)>0){
        paste0("<p><strong>The codes that didn't load are in the attached ",
               FN_diffs," file for manual loading.</strong></p>")
      }else{
        
      },
      "<hr>"
    )
    okaytocontinue <- FALSE #this will prevent deleting old 'processed' files until
    #it runs without error
  }
  
  
  
  
  
  
  
  if(nrow(myFiles_errors)>0){
    fn <- file.path(myReportPath, FN_fileerrors)
    write.csv(myFiles_errors, file = fn, row.names=FALSE, na = "")
    myemailfiles <- c(myemailfiles, fn)
    myFiles_errors_cnt <- nrow(myFiles_errors)
    bodytext_fileissues <- paste0(
      "<hr>",
      "<h3>FILE ISSUES</h3>",
      "<p>There are apparent file issues found in the ",
      "<a href=\"", gDrv_mainURL, "\">", drv_get_main$name[1], "</a> ",
      " Google folder. ",
      myFiles_errors_cnt,
      " file", if(myFiles_errors_cnt>1){"s"},
      " reported issues most likely related to file ownership. See the ",
      "attached ", FN_fileerrors, " file for action needed. ",
      "Codes may or may not have imported as noted.</p>",
      "<hr>"
    )
    
  }
  
  if(exists("bodytext_fileissues")||exists("bodytext_loadissues")||exists("bodytext_loadissues_ORA")){
    bodytext <- paste0(
      "<html>
        <head></head>
        <body>",
      "<h2>", myReportName, " ISSUE</h2>",
      "<p>This is an automated email to inform you that it appears there ",
      "were issues in the '", myReportName, 
      "' routine as noted below.</p>",
      if(exists("bodytext_loadissues_ORA")){bodytext_loadissues_ORA}, #ORACLE
      if(exists("bodytext_loadissues")){bodytext_loadissues},
      if(exists("bodytext_fileissues")){bodytext_fileissues},
      warn_sig,
      "</body>
      </html>"
    )
    #send mail
    mailsend(recipient = folder_users,
             subject = paste0(myReportName, " Import Issues"),
             body = bodytext,
             attachment = myemailfiles,
             inline = sig_logo,
             test = testing_emails, testrecipient = test_recip
    )
  }
  
}



###----------------------###
#Delete old processed files#
###----------------------###
if(okaytocontinue & exists("myFiles_to_delete")){
  drive_rm(as_id(myFiles_to_delete$id))
}

dbDisconnect(myOracleDB)