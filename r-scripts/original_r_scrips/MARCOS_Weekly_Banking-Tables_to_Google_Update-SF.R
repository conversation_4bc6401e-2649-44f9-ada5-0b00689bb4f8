library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(openxlsx)
library(googledrive)
library(googlesheets4)
library(tidyr)
library(odbc)
library(DBI)
library(keyring)

# written by <PERSON> July 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version ********

### ******** change
### replaced mailR with gmailr package
### gmailr masks base message() function, replaced with explicit base::message()


### ******** change:
### moved to Snowflake DB and a some cleanup of unneeded VARS

### ******** change:
### new file


# Parameters

okaytocontinue <- TRUE

myReportName <- "<PERSON>'s Weekly Banking-Tables to Google"
rptfolder <- "MARCOS_Weekly_Banking"

msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message()

#NOTE, first URL above is a native Google Sheet, the second URL is <PERSON>'s file that is in
#drive as a .xlsx file that can't be populated via the googlesheets4 package. There is
gSht_auth_email <- "<EMAIL>"
gSht_id <-'1jC1CUm3UmGcCUebi_R78rDXONrx248UTA6M0VmwoRbQ' #file URL
gSht_mainURL_email <- paste0('https://docs.google.com/spreadsheets/d/',gSht_id,"/")
#gDrv_folder_prefix <- 'https://drive.google.com/drive/u/0/folders/' #append ID (for use when not directory owner: <EMAIL>)
mySheets <- c("MP_BANK_ID_MASTER", "MP_BANK_ID_LOCAL", "LOCAL join MASTER")

#SSMS connection
#mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

# email parameters: recipient(s) of warning emails and signatures
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this must match email in the mailsend function
gm_auth(email = gMail_auth_email)

warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

#********: 
#test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
test_computers <- c("STEVEANDJENYOGA")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Steve's PC instead of R/Tableau PC
}else{
  testing_pc <- FALSE
}
if(testing_pc){
  # Steve PC testing paths, replace above when testing_pc is TRUE, uses files on Central server vs local for PROD
  logpath <- file.path("//*************","public","steveo","R Stuff","ReportFiles",rptfolder)
}else if(this_computer == "DESKTOP-TABLEAU"){
  logpath <- file.path("C:","Users","table","Documents","ReportFiles",rptfolder)
}else if(this_computer == "STEVEO-PLEX7010"){
  logpath <- file.path("C:","Users","steve","Documents","ReportFiles",scriptfolder)
}

myReportPath <- logpath
HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")

if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  norm_sig <- paste0(norm_sig,
                         '<img src="',HVSigLogopath,'" width="420"> ')
  warn_sig <- paste0(warn_sig, "<br/>",
                     '<img src="',HVSigLogopath,'" width="420"> ')
}



# date and time variables
query.date <- format(Sys.Date(), "%d-%b-%y")
#query.date <- "16-AUG-22"
query.days <- 13
query.startdate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) - (query.days - 2), "%d-%b-%y")# Oracle date format for start date of reporting
query.enddate <- format(as.Date(cut(as.Date(query.date, "%d-%b-%y"), "week", start.on.monday = FALSE)) + 2, "%d-%b-%y")# Oracle date format for end date of reporting


### define some functions ###

#********: 
###STAGE Snowflake versions###
#Sf_DB <- "STAGE_CSM_DB"
#Sf_schema <- "MOMS"
#Sf_wh <- "STAGE_DATA_ANA_WH"
##Sf_role <- "FR_STAGE_ANA_USERS"
#Sf_role <- "AR_STAGE_CONSUMPTION_RO"
#Sf_user <- key_get("SfHV", "tableau_ID_prod")
#Sf_pw <- key_get("SfHV", "tableau_PW_prod")
###PROD Snowflake versions###
Sf_DB <- "PROD_CSM_DB"
Sf_schema <- "CORPORATE"
Sf_wh <- "PROD_DATA_ANA_WH"
Sf_role <- "AR_PROD_CONSUMPTION_RW" #this should probably be AR_PROD_CONSUMPTION_RO (read-only), but that role isn't set up fully as of ********
Sf_user <- key_get("SfHV", "tableau_ID_prod")
Sf_pw <- key_get("SfHV", "tableau_PW_prod")

mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role
                         #,authenticator = 'externalbrowser'
)
rm(Sf_user,Sf_pw)
Sys.setenv(TZ='America/Chicago')
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

mySchema <- Sf_schema

ColNamesBlacklist <- c(
  "_DLT_LOAD_ID",
  "_DLT_ID",
  "_DLT_LOAD_ID.1",
  "_DLT_ID.1"
) #remove these columns (test Snowflake cols) from results before writing to Google

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <<EMAIL>>")
  myreplyto <- myemail #change this if you want replies to go to someone other than sender email addy
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}


check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}


`%notin%` <- Negate(`%in%`)


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, 
                              colnames = TRUE, colwidths = NULL,  writeover = TRUE,
                              hyperlinks = NULL, hypercols = NULL){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  #create hyperlinks
  #myLinks <- RptDF[, 4]
  #names(myLinks) <- RptDF[,6]
  #class(myLinks) <- "hyperlink"
  
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  #write hyperlinks
  if(class(hyperlinks) == 'hyperlink' & !is.null(hypercols)){
    writeData(wb = myWB, sheet = sname, x = hyperlinks, startRow = 2, startCol = hypercols)
  }
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             bodytext
    )
  }
}



# auth googledrive and googlesheets4 and check if supplied folder URL is valid
if(okaytocontinue){
  isFolder <- FALSE
  tk <- drive_auth(email = gSht_auth_email)
  gs4_auth(token = drive_token())
  
  if (gs4_has_token()) {
    gSht_get <- gs4_get(as.character(gSht_id))
    if(length(gSht_get) > 2){
      gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
      gSht_URL <- gSht_get$spreadsheet_url[[1]]
      gSht_Sheets_num <- length(gSht_Sheets)
      test_compare <- mySheets %in% gSht_Sheets
      mySheets_present <- mySheets[test_compare]
      mySheets_notpresent <- mySheets[!test_compare]
      if(length(mySheets_notpresent) > 0){
        #gSht_mainURL_email
        bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                           "an error in the ", myReportName, " routine!</p>",
                           "<p>The following sheets where expected but NOT found in the ",
                           "<a href=\"", gSht_mainURL_email, "\">", gSht_get$name, "</a> ",
                           " workbook:<br>", 
                           paste(mySheets_notpresent, collapse = "<br> "),
                           "<p>The routine will continue <B>BUT WILL NOT UPDATE ",
                           "THE SHEETS LISTED ABOVE.</B></p> ",
                           warn_sig
        )
        #send mail
        mailsend(warn_recip,
                 paste0(myReportName, " Issue: Missing one or more expected sheets"),
                 bodytext,
                 attachment = NULL,
                 test = testing_emails, testrecipient = test_recip
        )
      }
    }else{
      okaytocontinue <- FALSE
    }
    
  }else{
    #token not available, abort and warn
    okaytocontinue <- FALSE
  }
  if(!okaytocontinue){
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there ",
                       "may have been an error accessing Google or the file folder for the ",
                       myReportName, " routine! ",
                       "<p>The routine is aborting without an update</p> ",
                       "<b>Google Access Statuses:</b><ul>",
                       "<li>Googledrive package token: ", drive_has_token(), "</li>",
                       "<li>Googlesheets4 package token: ", gs4_has_token(), "</li>",
                       "<li>Sheet ID found: ", okaytocontinue, "</li>",
                       "</ul></p>",
                       warn_sig
    )
    #send mail
    mailsend(warn_recip,
             paste0(myReportName, " Issue: Google Access Issue"),
             bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
  }
}



### Populate sheets in Google
if(okaytocontinue){
  #loop through sheets to populate
  for(i in 1:length(mySheets_present)){
    gSht_Sheet_curr <- mySheets_present[[i]]
    myquery <- case_when(
      gSht_Sheet_curr == "MP_BANK_ID_MASTER" ~ paste0(
        "
          SELECT
              M.*
          FROM ", mySchema, ".MP_BANK_ID_MASTER M
          WHERE 
            M.ACTIVE = 1
          order by M.NAME_LONG, M.B_ID_MASTER
          "
      ),
      gSht_Sheet_curr == "MP_BANK_ID_LOCAL" ~ paste0(
        "
          SELECT
              L.*
          FROM ", mySchema, ".MP_BANK_ID_LOCAL L
          WHERE 
            (L.E_DATE IS NULL OR L.E_DATE >= to_date('", query.startdate, "','DD-MON-YY'))
          order by  L.LOC_NUM, L.B_ID_MASTER, L.S_DATE
        "
      ),
      gSht_Sheet_curr == "LOCAL join MASTER" ~ paste0(
        "
          SELECT
              L.*
          ,   M.*
          FROM ", mySchema, ".MP_BANK_ID_LOCAL L
          JOIN ", mySchema, ".MP_BANK_ID_MASTER M
          ON L.B_ID_MASTER = M.B_ID_MASTER
          WHERE 
            (L.E_DATE IS NULL OR L.E_DATE >= to_date('", query.startdate, "','DD-MON-YY'))
          order by  L.LOC_NUM, L.B_ID_MASTER
        "
      ),
      TRUE ~ "skip" #unknown
    )
    if(myquery != "skip"){
      #********: mydata <- dbGetQuery(myOracleDB, myquery)
      mydata <- dbGetQuery(mySfDB, myquery)
      mydata <- mydata[ , names(mydata) %notin% ColNamesBlacklist] #remove temp extra cols added in Snowflake implementation
      mydata_status <- check_mydf_rows(mydf = mydata, MinNumRows = 0, ReportName = myReportName)
      if(mydata_status[[1]] == TRUE){
        #replace headers to replace underscores with spaces
        #names(mydata) <- gsub("_"," ",names(mydata))
        #convert POSIXct dates to match normal Oracle output
        mydata[] <- lapply(mydata[], function(x) if(inherits(x, "POSIXct")) as.Date(x) else x)
        #UPDATE sheet
        #clear existing data in sheet
        range_clear(gSht_get$spreadsheet_id, sheet = mySheets_present[[i]], range = NULL, reformat = FALSE)
        #write new data
        sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = mySheets_present[[i]])
        Sys.sleep(2)
      }
    }else{
      #query not specified for the sheetname
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         "<p>The routine didn't have a query associated with the ",
                         "sheet named '", mySheets_present[[i]], "' ",
                         "in the '", gSht_get$name, "' workbook.</p>",
                         "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                         warn_sig
      )
      #send mail
      mailsend(warn_recip,
               paste0(myReportName, " Issue: Unknown Query"),
               bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
}


#********: DBI::dbDisconnect(myOracleDB)
DBI::dbDisconnect(mySfDB)
