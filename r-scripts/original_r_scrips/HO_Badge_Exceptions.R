library(xtable)
library(data.table)
library(dplyr)
library(lubridate)
library(formattable)
library(rJava)
library(mailR)
library(readr)
library(stringr)
library(openxlsx)
library(utils)
library(keyring)
library(DBI)
library(ROracle)

#Written 6/4/2021 by <PERSON>
# Version ********

### ******** change:
### deleted Family Vet section

### ******** change:
### removed home gym in office check and added expiration date
### this allows it to work for all offices using those ATIDs

### ******** change:
### Removed on_hold fobs from report

### ******** change:
### updated mailsend to use keyring

### ******** version changes:
### added keyring package, added routine to check expiring Home Office or
### Family Vet accounts that might expire soon and need a manual payment
### pushed to extend their fob expiration dates out further (parameters added
### to control which account AIDs and how far out)

### ******** Version changes:
### excluded CANCELLED badges from active list

### ******** Version changes:
### fixed path error saving report file and added append ability to file save routine

### ******** Version changes:
### added VET section

### ******** Version changes:
### new script

testing_emails <- FALSE  #NORMAL, next line over-rides & should be disabled in PRODUCTION instance
#testing_emails <- TRUE

testing_pc <- FALSE  #NORMAL, next line over-rides and should be disabled in PRODUCTION instance
if(Sys.getenv("COMPUTERNAME") == "STEVEO-PLEX7010" || Sys.getenv("COMPUTERNAME") == "LAPTOPTOSHIBA13"){
  testing_pc <- TRUE  #TESTING portability, changes some paths to Central instead of local R/Tableau PC
}

# Vector of AID numbers to check for H.O. or FVG accounts that expire soon
# note, if the cancel_date in the FIT_ACCOUNTS table is populated and past,
# then the AIDs below will be ignored by the SQL as the location has been shut off
#myExpireAID_whitelist <- c(1803, 1786708, 1786748)
#1797988
myExpireAID_whitelist <- c(1803, 1797988)
myExpire_daystoexpire <- 22

query.date <- format(Sys.Date(), "%d-%b-%y")
query.startdate <- query.date
query.enddate <- query.date
report.time <- format(Sys.time(), "%H%M%S")
report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")
report.hour <- lubridate::hour(Sys.time())
report.enddate <- query.enddate %>% as.Date("%d-%b-%y") %>% format("%y%m%d")  # YYMMDD format of date for saves of report files


HVSigLogopath <- file.path("C:","Users","Family Video","Documents","ReportFiles","HV Logo Email Signature.png")
rptpath_HO_Badge_Except <- file.path("C:","Users","table","Documents","ReportFiles","HV_Badges","Reports")


if(testing_pc){
  #alter parameters to reflect different paths as needed
  rptpath_HO_Badge_Except <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV_Badges","Reports")
  # Steve PC testing paths, replace above when testing_pc is TRUE
  #HVSigLogopath <- file.path("C:","Users","SteveO","Documents","R Scripts","TestScripts","HV Logo Email Signature.png")
  HVSigLogopath <- file.path("//*************","public","steveo","R Stuff","ReportFiles","HV Logo Email Signature.png")
}

okaytocontinue <- TRUE

### ROracle connection
drv <- dbDriver("Oracle")
connect.string <- paste0(
  "(DESCRIPTION=",
  "(ADDRESS=(PROTOCOL=tcp)(HOST=", "************", ")(PORT=", 1531, "))",
  "(CONNECT_DATA=(SID=", "fvpa", ")))"
)
myOracleDB <- dbConnect(drv, username = "php", password = key_get("Oracle", "php"), dbname = connect.string)

### email parameters: recipient(s) of warning emails and signatures
#warn_recip <- c("<EMAIL>","<EMAIL>")
warn_recip <- c("<EMAIL>")
norm_recip <- c("<EMAIL>")
corp_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> Sr. Analytics Manager<br/><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_st_from <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                       "Sr. Analytics Mgr.<br/>",
                       "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                       "2500 Lehigh Ave.<br/>",
                       "Glenview, IL 60026<br/>",
                       "Ph: 847/904-9043<br/></span></font>")

test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>","<EMAIL>")
if(file.exists(HVSigLogopath)){
  #append signature logo to norm_st_from
  norm_st_from <- paste0(norm_st_from,
                         '<img src="',HVSigLogopath,'" width="600"> ')
  warn_sig <- paste0(warn_sig, "<br/>",
                     '<img src="',HVSigLogopath,'" width="600"> ')
}

### define some functions ###
mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE){
  library(mailR)
  sender <- "H.O. Badge Exceptions <<EMAIL>>"
  recipients <- recipient
  rs <- send.mail(from = sender,
                  to = recipients,
                  replyTo = "<EMAIL>",
                  subject = subject,
                  body = body,
                  smtp = list(host.name = "smtp.gmail.com", 
                              port = 465, 
                              user.name = "<EMAIL>",            
                              passwd = key_get("GMail", "steve"),
                              ssl = TRUE),
                  authenticate = TRUE,
                  attach.files = attachment,
                  html = TRUE,
                  inline = inline,
                  send = TRUE)
  return(rs)
}


queryfailure.mailsend <- function(recip, reportname){
  #send warning email
  
  # create body of warning email
  bodytext <- paste0("This is an automated email to inform you that it appears the main Oracle query ",
                     "needed for the <b>", reportname, "</b> report failed in the Badge Exceptions script.<br/><br/>",
                     warn_sig
  )
  mailsend(recip,
           paste0("Badge Exceptions Issue: ", toupper(reportname), " QUERY FAILURE"),
           bodytext,
           inline = TRUE
  )
}


writeCSVreport <- function(dirpath, fname, RptDF, colnames = TRUE, writeover = TRUE){
  myFN <- file.path(dirpath, fname)
  mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    write_excel_csv(RptDF, myFN, na="", col_names = colnames, append = !writeover)
    #write.xlsx(RptDF, myFN, sheetName=mySN, row.names=FALSE, showNA=FALSE)
  }else{
    #try appending report time to filename
    myNewFN <- gsub(report.enddate, paste0(report.enddate,"-",report.time), myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      write_excel_csv(RptDF, myNewFN, na="", col_names = colnames, append = !writeover)
      #write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>Badge Exception</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>Badge Exception</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(warn_recip,
             "Badge Exception Reports Issue: REPORT FILE SAVING ERROR",
             bodytext
    )
  }
}


file.opened <- function(path) {
  suppressWarnings(
    "try-error" %in% class(
      try(file(path, 
               open = "w"), 
          silent = TRUE
      )
    )
  )
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



#--Report Query: H.O. Badge Exceptions--#
# ONLY run on Friday
if(okaytocontinue & format(Sys.Date(),"%a") == 'Fri'){
  #EMAIL RESULT <NAME_EMAIL>
  #don't create a result file like the adds routine does, just embed results in body of email instead
  
  myReportName <- "Home Office Badge Exceptions"
  
  #report specific parameters
  myFN <- paste0("HO_Badge_Exceptions", ".csv")
  #myExcelFN <- paste0("HO_Badge_Exceptions", report.enddate, ".xlsx")
  myReportPath <- rptpath_HO_Badge_Except
  #rename column parameters for final output
  OldNames <- c("QUERYSTARTDATE",
                "QUERYENDDATE",
                "INFOTEXT")
  NewNames <- c(query.startdate %>% as.Date("%d-%b-%y") %>% format("%m/%d/%Y"),
                query.enddate %>% as.Date("%d-%b-%y") %>% format("%m/%d/%Y"), 
                "()")
  
  myquery <- paste0(
    "
      select
        a.paynum, 
        a.status,
        a.t_date,
        a.fname as emp_fname,
        a.lname as emp_lname,
        b.fname as badge_fname,
        b.lname as badge_lname,
        b.b_uid,
        b.bid,
        b.exp_date
      from ab_employees a
      right join
      (
        select emp_paynum, b_uid, fname, lname, bid, exp_date
        from fit_badges 
        --where home_gym = 981 
        --  and atid in (-2, -5, -6, -8)
        /* two lines above replaced with next two lines 3/17/23 */
        where atid in (-2, -5, -6, -8)
          and (exp_date is null or trunc(exp_date) >= trunc(sysdate))
          and cancel_date is NULL
          and on_hold = 0
      ) b
      on a.paynum = b.emp_paynum
      where (a.status not in ('A','L') and UPPER(a.lname) <> 'HOOGLAND')
        or (b.emp_paynum is null and UPPER(b.lname) <> 'HOOGLAND' )
        or (upper(a.fname) <> upper(b.fname) and upper(a.lname) <> upper(b.lname))
        --and (b.exp_date is NULL or trunc(b.exp_date) >= trunc(sysdate))
      order by t_date, a.paynum, b_uid
    "
  )
  
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 0, ReportName = myReportName)
  okaytocontinue <- mydata_status[[1]]
  
  if(!okaytocontinue){
    # send warning email
    #print(MyErrorLog[1,"QUERY_STATUS"])
    queryfailure.mailsend(recip = warn_recip, reportname = myReportName)
  }else{
    if(nrow(mydata)>0){
      # rename columns (if needed)
      setnames(mydata, 
               old = OldNames, 
               new = NewNames,
               skip_absent=TRUE)
      # convert POSIXct dates to match previous Oracle output
      mydata[] <- lapply(mydata[], function(x) if(inherits(x, "POSIXct")) format(x, '%d-%b-%y') else x)
      # create csv report
      writeCSVreport(dirpath = myReportPath, fname = myFN, RptDF = mydata, colnames = TRUE)
      #myemailfiles <- file.path(myReportPath, myFN)
      myemailfiles <- NA
      
      # create email
      bodytext <- paste0(print(xtable(mydata, 
                                caption = myReportName,
                                digits = rep(0, ncol(mydata) + 1),
                                align = c(rep("l",1), rep("c", ncol(mydata) - 0)),
                           ),
                           html.table.attributes = "border=2 cellspacing=1",
                           type = "html",
                           caption.placement = "top",
                           include.rownames=FALSE
                         ),
                         "<br/><br/>",
                         #"The attached file, if present, contains more details in order to assist in ",
                         #"removing the email from groups and to notify Matt McCormick and the ",
                         #"immediate supervisor of the email removal.<br/><br/>",
                         #"<p>The attached file, if present, contains the same data in .csv format.</p>",
                         "<p>Review and act on accordingly. Info on the left is from the ab_employees table ",
                         "and info on the right is from the fit_badges table.</p>",
                         "Active badges/fobs for Home Offices show up here if:",
                         "<ul>",
                         "<li>The badge is active but the paynum does NOT have active status (e.g. is terminated)</li>",
                         "<li>The first <b>AND</b> last names don't match employee records (both have to differ)</li>",
                         "<li>The badge does NOT have a paynum populated for it (excludes Hooglands)</li>",
                         "</ul></p>",
                         norm_st_from
      )
      this_recip <- c("<EMAIL>","<EMAIL>")
      #if(lubridate::hour(Sys.time()) < 10){
      #  this_recip <- c("<EMAIL>","<EMAIL>")
      #}else{
      #  this_recip <- c("<EMAIL>")
      #}
      if(testing_emails){
        bodytext <- paste0("<p><b>TEST SEND (normal recipient: ",
                           paste(this_recip, collapse = "; "), ")</b></p>",
                           bodytext)
        this_recip <- test_recip
      }
      rs <- mailsend(recipient = this_recip,
                     subject = paste0(myReportName),
                     body = bodytext,
                     if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                     inline = TRUE
      )
    }
  }
}




#--Report Query: Expiring H.O. or Vet accounts needing payment pushed--#
if(okaytocontinue){
  #EMAIL RESULT <NAME_EMAIL>
  myExpireAID <- paste0(myExpireAID_whitelist, collapse = ",")
  myReportName <- "H.O. or FVG Account Expire Date Soon"
  
  #report specific parameters
  myFN <- paste0("HO_or_FVG_Account_Expire_Date_Soon", ".csv")
  #myExcelFN <- paste0("HO_Badge_Exceptions", report.enddate, ".xlsx")
  myReportPath <- rptpath_HO_Badge_Except
  #rename column parameters for final output
  OldNames <- c("QUERYSTARTDATE",
                "QUERYENDDATE",
                "INFOTEXT")
  NewNames <- c(query.startdate %>% as.Date("%d-%b-%y") %>% format("%m/%d/%Y"),
                query.enddate %>% as.Date("%d-%b-%y") %>% format("%m/%d/%Y"), 
                "()")
  
  myquery <- paste0(
    "
      SELECT DISTINCT
          FA.FNAME||' '||FA.LNAME AS NAME_ON_ACCOUNT
      ,   STATS_MODE(FB.HOME_GYM) AS LOCATION_NUM
      ,   STATS_MODE(FB.EXP_DATE) AS EXP_DATE
      ,   STATS_MODE(FB.EXP_DATE) - TRUNC(SYSDATE) AS DAYS_UNTIL_EXPIRE
      ,   FA.AID
      ,   'https://pos.stayfit24.com/view_account.php?aid='||FB.AID AS ACCOUNT_URL
      FROM FAMVDBA.FIT_BADGES FB
      JOIN FAMVDBA.FIT_ACCOUNTS FA ON FB.AID = FA.AID
      WHERE FB.AID IN (",myExpireAID,")
          AND FB.ATID < 0
          AND (FB.CANCEL_DATE IS NULL OR FB.CANCEL_DATE > SYSDATE)
          AND (FB.ON_HOLD IS NULL OR FB.ON_HOLD = 0)
          AND (FA.CANCEL_DATE IS NULL OR FA.CANCEL_DATE > SYSDATE)
          AND TRUNC(FB.EXP_DATE) - TRUNC(SYSDATE) <= ", myExpire_daystoexpire, "
      GROUP BY
          --FB.HOME_GYM
          FA.AID
      ,   FA.FNAME||' '||FA.LNAME
      ,   'https://pos.stayfit24.com/view_account.php?aid='||FB.AID
    "
  )
  
  mydata <- dbGetQuery(myOracleDB, myquery)
  mydata_status <- check_mydata_rows(MinNumRows = 0, ReportName = myReportName)
  okaytocontinue <- mydata_status[[1]]
  
  if(!okaytocontinue){
    # send warning email
    #print(MyErrorLog[1,"QUERY_STATUS"])
    queryfailure.mailsend(recip = warn_recip, reportname = myReportName)
  }else{
    if(nrow(mydata)>0){
      # rename columns (if needed)
      setnames(mydata, 
               old = OldNames, 
               new = NewNames,
               skip_absent=TRUE)
      # replace _ in column names with space
      my_headers <- mydata[0,]
      colnames(mydata) <- gsub("_"," ",colnames(my_headers))
      
      # convert POSIXct dates to match previous Oracle output
      mydata[] <- lapply(mydata[], function(x) if(inherits(x, "POSIXct")) format(x, '%d-%b-%y') else x)
      # create csv report
      writeCSVreport(dirpath = myReportPath, fname = myFN, RptDF = mydata, colnames = TRUE)
      #myemailfiles <- file.path(myReportPath, myFN)
      myemailfiles <- NA
      
      # create email
      bodytext <- paste0(print(xtable(mydata, 
                                      caption = myReportName,
                                      digits = rep(0, ncol(mydata) + 1),
                                      align = c(rep("l",1), rep("c", ncol(mydata) - 0)),
        ),
        html.table.attributes = "border=2 cellspacing=1",
        type = "html",
        caption.placement = "top",
        include.rownames=FALSE
        ),
        "<br/>",
        #"The attached file, if present, contains more details in order to assist in ",
        #"removing the email from groups and to notify Matt McCormick and the ",
        #"immediate supervisor of the email removal.<br/><br/>",
        #"<p>The attached file, if present, contains the same data in .csv format.</p>",
        "<p>Review and act on accordingly. The above are Home Office or Family Vet ",
        "accounts in the StayFit24 POS that appear to be expiring soon.<p/>",
        "<p><b>Process one or more payments in the account using the 'MAKE PAYMENT' ",
        "button (should be $0) to push the expiration date of the fobs/badges ",
        "further out into the future.</b></p>",
        "Locations are included above if ALL the criteria below are met:",
        "<ul>",
        "<li>The AID in the FIT_ACCOUNTS table is in: ", paste0(myExpireAID_whitelist, collapse = ", "),"</li>",
        "<li>The mode (most common) expire date of fobs in the account is less than ",
        myExpire_daystoexpire, " days out.</li>",
        "<li>The AID in the FIT_ACCOUNTS table does not have a CANCEL_DATE in the past.</li>",
        "</ul></p>",
        norm_st_from
      )
      this_recip <- c("<EMAIL>","<EMAIL>")
      #if(lubridate::hour(Sys.time()) < 10){
      #  this_recip <- c("<EMAIL>","<EMAIL>")
      #}else{
      #  this_recip <- c("<EMAIL>")
      #}
      if(testing_emails){
        bodytext <- paste0("<p><b>TEST SEND (normal recipient: ",
                           paste(this_recip, collapse = "; "), ")</b></p>",
                           bodytext)
        this_recip <- test_recip
      }
      rs <- mailsend(recipient = this_recip,
                     subject = paste0(myReportName),
                     body = bodytext,
                     if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                     inline = TRUE
      )
    }
  }
}



#END