#********: library(RODBC) #replaced by odbc package for Snowflake conn
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#********: library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(utils)
library(googledrive)
library(googlesheets4)
library(keyring)
library(DBI)
library(odbc)

# written by <PERSON> July 2022


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE


# Version ********

### ******** change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### Updated signature for emails to standard requested by <PERSON> in March 2024


### 20220708 change:
### new file, based on L<PERSON>ACY_PROPERTY_ASSIGMENTS_update.R script


# Parameters
okaytocontinue <- TRUE

scriptfolder <- "LEGACY_MRI_Sprinkler_Notes"
myReportName <- "LCP MRI Sprinkler Notes to Google"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
Sys.sleep(1.5)
#https://docs.google.com/spreadsheets/d/13fQzj-LEoLGvKen_BuGgZ_dWOMXG19g4uePZsR1ZmDE/edit#gid=0
#https://docs.google.com/spreadsheets/d/1Puc3bBE-YdHRORKoiTlqN0zT0hscM7AB/edit#gid=819686871
#NOTE, first URL above is a native Google Sheet, the second URL is Legacy's file that is in
#drive as a .xlsx file that can't be populated via the googlesheets4 package. There is
gSht_id <- "13fQzj-LEoLGvKen_BuGgZ_dWOMXG19g4uePZsR1ZmDE"
mySheets <- c("MRI SPKRBLD Notes")


#Oracle connection
#********: mydb <- odbcConnect("FVPA64", "steve", key_get("Oracle", "steve"))
#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
norm_recip <- c("<EMAIL>")
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time.txt <- format(Sys.time(), "%H:%M:%S %Z")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- logpath
sig_logo <- FALSE


### define some functions ###
#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}



check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}



# check google sheet status
if(okaytocontinue){
  
  #MyErrorLog[1,"PROGRESS"] <- "GSHT STATUS"
  #MyErrorLog[1,"GSHT_STATUS"] <- paste0("CHECKING OAUTH")
  #writelog(MyErrorLog)
  
  gs4_auth(email = "<EMAIL>")
  
  #Is it OK to cache OAuth access credentials in the folder 'C:/Users/<USER>/.R/gargle/gargle-oauth' between R sessions?
  #if using googledrive along with googlesheets4,
  #do the auth with googledrive package first, then use the same token
  #in googlesheets4 something like this:
  #tk <- drive_auth()
  #gs4_auth(token = drive_token())
  #gSht_Closings <- sheets_get('1xoLPaRKdPvDdwl9wvCEiBp8ABrGgxhff9GSn55_yArc')
  #above was deprecated as of googlesheets4 0.2.0
  
  if (gs4_has_token()) {
    gSht_get <- gs4_get(as.character(gSht_id))
  }else{
    #token not available
    gSht_get <- c("")
  }
  
  #if(nrow(gSht_Orig) >= 1){
  if(length(gSht_get) > 2){
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH OKAY")
    #MyErrorLog[1,"QUERY_STATUS"] <- "COMPLETE"
    #writelog(MyErrorLog)
    #read data in from desired sheet
    #gSht_Orig <- read_sheet(gSht_get$spreadsheet_id, sheet = "RE Taxes Data")
    
    #Get number of sheets like '% P&L Full Sort' sheets
    #gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %ilike% mySheetsLike)]
    gSht_Sheets <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
    gSht_Sheets_num <- length(gSht_Sheets)
    #check that at least ONE sheet found
    if(gSht_Sheets_num == 0){
      bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                         "an error in the ", myReportName, " routine!</p>",
                         #"<p>There weren't any sheets named like '", mySheets, "' ",
                         "<p>There weren't any sheets with the expected name(s) (", 
                         paste(mySheets, collapse = "; "),
                         ") found in the '", gSht_get$name, "' workbook.",
                         "<p>The routine is aborting without an update</p> ",
                         warn_sig
      )
      #send mail
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " Issue: No sheets with expected names"),
               body = bodytext,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
      
      okaytocontinue <- FALSE
    }
  }else{
    #MyErrorLog[1,"GSHT_STATUS"] <- paste0("OAUTH FAIL")
    #MyErrorLog[1,"PROGRESS"] <- "FAILURE"
    #writelog(MyErrorLog)
    #email failure
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there may ",
                       "have been an error reading the Google Sheet for the ", myReportName, " routine! ",
                       "The file may be missing or there was an issue accessing it.</p>",
                       "<p>The routine is aborting without an update</p> ",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Google Sheet Access Issue"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
    okaytocontinue <- FALSE
  }
}


### Populate MRI data sheet in Google

if(okaytocontinue){
  # verify myMRISheet is present
  gSht_Sheet_MRI <- gSht_get$sheets$name[which(gSht_get$sheets$name %in% mySheets)]
  gSht_Sheet_MRI_num <- length(gSht_Sheet_MRI)
  if(gSht_Sheet_MRI_num > 0){
    #sheet found, continue
    
    #query MRI
    myquery <- paste0(
      "
          SELECT
          	BLDG.BLDGID,
          	BLDG.INACTIVE,
          	TO_DATE(NOTB.NOTEDATE) AS NOTEDATE,
          	NOTB.REF1,
          	NOTB.REF2,
              NOTB.NOTETEXT
          FROM MRI.BLDG
          LEFT JOIN MRI.NOTB
          ON BLDG.BLDGID = NOTB.BLDGID AND (NOTB.REF1 = 'SPKRBLD' OR NOTB.REF2 = 'SPKRBLD')
          WHERE
          	(INACTIVE IS NULL OR INACTIVE <> 'Y')
              AND TRY_CAST(BLDG.BLDGID AS INT) IS NOT NULL
          ORDER BY BLDG.BLDGID
      "
    )
    #******** mydata <- sqlQuery(mySSdb, myquery, stringsAsFactors = FALSE)
    mydata <- DBI::dbGetQuery(mySfDB, myquery, stringsAsFactors = FALSE)
    mydata_status <- check_mydata_rows(MinNumRows = 5, ReportName = myReportName)
    if(mydata_status[[1]] == TRUE){
      #UPDATE MRI sheet
      #clear existing data in MRI sheet
      range_clear(gSht_get$spreadsheet_id, sheet = mySheets[[1]], range = NULL, reformat = FALSE)
      #write new data
      sheet_write(mydata, ss = gSht_get$spreadsheet_id, sheet = mySheets[[1]])
      #replace headers to replace underscores with spaces
      #my_headers <- data.frame(gsub("_"," ",mydata[0,]))
      Sys.sleep(2)
    }
    
    
  }else{
    #MRI sheet not found, warn
    bodytext <- paste0("<p>This is an automated email to inform you that it appears there is ",
                       "an error in the ", myReportName, " routine!</p>",
                       "<p>There weren't any sheets named like '", mySheets[[1]], "' ",
                       #"<p>There weren't any sheets with the expected names (", 
                       #paste(mySheets, collapse = "; "),
                       " found in the '", gSht_get$name, "' workbook.",
                       "<p>The routine will not update this sheet, but will continue otherwise.</p> ",
                       warn_sig
    )
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " Issue: Missing expected sheet"),
             body = bodytext,
             attachment = NULL,
             test = testing_emails, testrecipient = test_recip
    )
    
  }
}

#********:
DBI::dbDisconnect(mySfDB)


