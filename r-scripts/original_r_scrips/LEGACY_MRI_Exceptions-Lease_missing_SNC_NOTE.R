library(lubridate)
library(formattable)
library(data.table)
#library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(xtable)
library(utils)
library(mime)
library(googledrive)
library(googlesheets4)
library(tidyverse)
library(readxl)
library(openxlsx)
library(DBI)
library(odbc)
library(keyring)
library(jsonlite)

testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20250304

### 20250304 change:
### reference types finalized
### fixed minor glitch in email subject

### 20250217 change:
### make changes to the reference types included in the query, also send exceptions
### when the note is present, but NOTETEXT is blank

### 20241106 change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail
### updated norm_sig to standard requested by <PERSON> March 2024
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present

### 20240708 change:
### new file

# Parameters
options(stringsAsFactors = FALSE)

myReportName <- "Legacy SNC Lease Note Exceptions"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
Sys.sleep(2)
scriptfolder <- "LEGACY_MRI_Exceptions-Leases"



#2025-02-17: MRI_noteRef <- c('COO','DELIVER','ELEC','GAS','INSURNC','LCDA','LEASDEP','OPEN','PLANS','SECDEP','SIGNS','WTRSWR')
#2025-02-17: use SNCUTIL (create) or existing 
#MRI_noteRef <- c('COO','DELIVER','LCDA','LEASDEP','OPEN','PLANS','SECDEP','SIGNS','utility above','COI')
#2025-03-04: finalized list, use 'UTILITY' in place of 'ELEC','GAS','WTRSWR'
MRI_noteRef <- c('COO','DELIVER','LCDA','LEASDEP','OPEN','PLANS','SECDEP','SIGNS','UTILITY','COI')

MRI_email_blacklist <- c(
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
SNC_note_missing_cols <- c(
  "Manager Email",
  "Manager Name",
  "Lease ID",
  "Bldg ID",
  "Suite ID",
  "City",
  "State",
  "Occupant Name",
  "Execution",
  "Beginning",
  "Rent Start",
  "Gen Code",
  "Status",
  "REF2"
)
SNC_note_blank_cols <- c(
  "Manager Email",
  "Manager Name",
  "Lease ID",
  "Bldg ID",
  "Suite ID",
  "City",
  "State",
  "Occupant Name",
  "Execution",
  "Beginning",
  "Rent Start",
  "Gen Code",
  "Status",
  "REF2",
  "SNC Note Text"
)
#Email_body columns below must exist in the corresponding SNC_note list above
#with exception where a name above is changed (i.e. "REF2" to "SNC Note Reference 2 Type ID(s) Missing")
#to another name later in the code
Email_body_missing_cols <- c(
  "Lease ID",
  "Bldg ID",
  "Suite ID",
  "Occupant Name",
  "Execution",
  "SNC Note Reference 2 Type ID(s) Missing"
)
Email_body_blank_cols <- c(
  "Lease ID",
  "Bldg ID",
  "Suite ID",
  "Occupant Name",
  "Execution",
  "Reference 2 ID(s) with Blank Note"
)

rptfolder <- "reports"
emoji_monocle <- "=?UTF-8?B?8J+nkA==?="
emoji_eyes <- "=?UTF-8?B?8J+RgA==?="
emoji_stopsign <- "=?UTF-8?B?8J+bkQ==?="

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts

logpath <- file.path("C:","Users","table","Documents","ReportFiles",scriptfolder)
logname <- paste0(myReportName," - LOG.csv")
report.loglimit <- format(floor_date(Sys.Date(), "month") - months(2), "%Y%m%d 000000") #only log rows >= this are retained to prevent log bloat
report.logruntime <- format(Sys.time(), "%Y%m%d %H%M%S")
date.header.text <- paste0("Updated ", format(Sys.Date(), "%m-%d-%Y"))


okaytocontinue <- TRUE

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath, scriptfolder)
myReportPath <- file.path(logpath, rptfolder)
sig_logo <- FALSE


###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("Steve Olson<<EMAIL>>")
test_cc_recip <- c("<EMAIL>")

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}



check_mydf_rows <- function(mydf, MinNumRows, ReportName = NULL){
  if(is.data.frame(mydf)){
    if(nrow(mydf) >= MinNumRows ){
      error_status <- paste0(ReportName, ": OKAY")
      tempnrow <- nrow(mydf)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydf)
      error_status <- paste0(ReportName, ": INCOMPLETE")
    }
  }else{
    #problem, not data.frame as expected
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": ERROR")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


nullToNA <- function(x) {
  x[sapply(x, is.null)] <- NA
  return(x)
}

`%notin%` <- Negate(`%in%`)

writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext
    )
  }
}


###Get SNC Leases with desired notes###
if(okaytocontinue){
  myquery_SNC <- paste0(
    '
      SELECT 
      	MNGR.MNGRNAME AS "Manager Name"
      ,	MNGR.EMAIL AS "Manager Email"
      ,	LEAS.LEASID AS "Lease ID"
      ,	LEAS.BLDGID AS "Bldg ID"
      --,	LEAS.MOCCPID AS "Occp ID"
      ,	BLDG.CITY AS "City"
      ,	BLDG.STATE AS "State"
      ,	LEAS.SUITID AS "Suite ID"
      --,	SUIT.SUITSQFT AS "Suite SQFT"
      --,	SQF.SQFT AS "Suite Details SQFT"
      ,	rtrim(LEAS.OCCPNAME) AS "Occupant Name"
      ,	CAST(LEAS.EXECDATE AS DATE) AS "Execution"
      ,	CAST(LEAS.BEGINDATE AS DATE) AS "Beginning"
      ,	CAST(LEAS.RENTSTRT AS DATE) AS "Rent Start"
      ,	CAST(LEAS.EXPIR AS DATE) AS "Expiration"
      ,	LEAS.GENCODE AS "Gen Code"
      ,	LEAS.GENERATION  AS "Generation"
      ,	LEAS.OCCPSTAT AS "Status"
      ,	CASE WHEN UPPER(LEAS.CONTINGENT) != \'Y\' OR LEAS.CONTINGENTDT < CURRENT_DATE OR LEAS.CONTINGENTDT IS NULL THEN NULL ELSE \'Y\' END AS "Contingent"
      ,	CAST(LEAS.CONTINGENTDT AS DATE) AS "Contingency Date"
      ,	LEASEAG.LEASEAGENT AS "Lease Agent"
      ,	ASSETMGR.NAME AS "Asset Manager"
      ,	SNC_NOTES.REF2
      , RTRIM(SNC_NOTES.NOTETEXT) AS "SNC Note Text"
    ',"
      FROM MRI.LEAS
      JOIN MRI.BLDG ON LEAS.BLDGID = BLDG.BLDGID
      JOIN MRI.SUIT ON LEAS.BLDGID = SUIT.BLDGID AND LEAS.SUITID = SUIT.SUITID
      LEFT JOIN 
      (
      		SELECT *
      		FROM MRI.SSQF
      		WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE)
      								FROM MRI.SSQF I
      								WHERE I.BLDGID = SSQF.BLDGID
      								AND I.SUITID = SSQF.SUITID
      								AND I.EFFDATE <= CURRENT_DATE
      								)
      ) SQF
      ON SUIT.BLDGID = SQF.BLDGID
      	AND SUIT.SUITID = SQF.SUITID
      	AND UPPER(SQF.SQFTTYPE) = 'GLA'
      LEFT JOIN
      (
      	SELECT
      		NOTE.BLDGID
      	,	NOTE.LEASID
      	,	LISTAGG(CONCAT(NOTE.NOTETEXT, ' (', TO_CHAR(NOTE.NOTEDATE, 'MM/dd/yyyy'), ')'), '; ') WITHIN GROUP (ORDER BY NOTE.NOTEDATE DESC) AS LEASEAGENT
      	FROM MRI.NOTE
      	WHERE (NOTE.REF1 = 'LEASEAG' OR NOTE.REF2 = 'LEASEAG')
      	GROUP BY NOTE.BLDGID
      	, NOTE.LEASID
      ) LEASEAG
      ON LEAS.BLDGID = LEASEAG.BLDGID
      	AND LEAS.LEASID = LEASEAG.LEASID
      LEFT JOIN MRI.MNGR
      ON BLDG.MNGRID = MNGR.MNGRID
      LEFT JOIN MRI.ENTITY
      ON BLDG.ENTITYID = ENTITY.ENTITYID
      LEFT JOIN MRI.PROJ
      ON ENTITY.PROJID = PROJ.PROJID
      LEFT JOIN MRI.ASSETMGR
      ON PROJ.ASSETMGR = ASSETMGR.ASSETMGR
      LEFT JOIN 
      (
      	SELECT *
      	FROM MRI.NOTE
      	WHERE UPPER(REF1) = 'SNC'
      	AND UPPER(REF2) IN (", paste0("'",MRI_noteRef,"'", collapse = "," ), ")
      ) SNC_NOTES
      ON LEAS.LEASID = SNC_NOTES.LEASID AND LEAS.BLDGID = SNC_NOTES.BLDGID
      WHERE 
        (BLDG.INACTIVE = 'N' or BLDG.INACTIVE is NULL)
      	AND UPPER(LEAS.GENCODE) = 'SNC'
      	AND UPPER(LEAS.OCCPSTAT) != 'I'
      	AND COALESCE(LEAS.EXECDATE, LEAS.RENTSTRT) >= '2024-07-04' /* Exclude leases originating prior to this first implentation date (2024-07-04) */
      ORDER BY 
      	MNGR.MNGRNAME 
      ,	LEAS.BLDGID
      ,	LEAS.LEASID
    "
  )
  #20241106: mydata_SNC <- dbGetQuery(mySSdb, myquery_SNC)
  mydata_SNC <- dbGetQuery(mySfDB, myquery_SNC)
  #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
  mydata_SNC[] <- lapply(mydata_SNC[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
  mydata_status <- check_mydf_rows(mydata_SNC, MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]]){
    SNC_leasid <- unique(mydata_SNC$`Lease ID`)
  }else{
    #no results, exit rest of routine
    okaytocontinue <- FALSE
  }
}


###---------------------------###
###Create 'Missing' data.frame###
###---------------------------###
if(okaytocontinue){
  SNC_note_missing <- mydata_SNC[0,SNC_note_missing_cols]
  for(i in 1:length(SNC_leasid)){
    curr_leas_ref2 <- mydata_SNC$REF2[which(mydata_SNC$`Lease ID` == SNC_leasid[i])] %>% unique()
    #identify which note references are NOT in this lease ID
    curr_missing <- setdiff(MRI_noteRef, curr_leas_ref2)
    if(length(curr_missing)>0){
      SNC_note_missing <- SNC_note_missing %>% add_row()
      SNC_note_missing_row <- nrow(SNC_note_missing)
      for(x in 1:length(SNC_note_missing_cols)){
        #populate desired columns
        curr_col <- SNC_note_missing_cols[x]
        if(curr_col!="REF2"){
          SNC_note_missing[SNC_note_missing_row, curr_col] <- mydata_SNC[which(mydata_SNC$`Lease ID` == SNC_leasid[i]), curr_col] %>% 
            unique()
        }
      }
      #add notes that are missing
      SNC_note_missing[SNC_note_missing_row, "REF2"] <- paste0(curr_missing, collapse = "; ")
    }
  }
  #rename REF2 column
  SNC_note_missing <- SNC_note_missing %>% dplyr::rename("SNC Note Reference 2 Type ID(s) Missing" = "REF2")
}


#20250217:
###-------------------------###
###Create 'Blanks' data.frame###
###-------------------------###
if(okaytocontinue){
  #create empty df with desired headers, append needed rows to this inside loop
  SNC_note_blank <- mydata_SNC[0,SNC_note_blank_cols]
  for(i in 1:length(SNC_leasid)){
    #get blank note text rows
    curr_leas_ref2 <- mydata_SNC$REF2[which(mydata_SNC$`Lease ID` == SNC_leasid[i] & stringr::str_squish(dplyr::coalesce(mydata_SNC$`SNC Note Text`,'')) == '' )] %>% unique()
    #identify which blanks identified above are in the relevant ref2 list
    curr_blank <- curr_leas_ref2[curr_leas_ref2 %in% MRI_noteRef]
    if(length(curr_blank)>0){
      SNC_note_blank <- SNC_note_blank %>% add_row()
      SNC_note_blank_row <- nrow(SNC_note_blank)
      for(x in 1:length(SNC_note_blank_cols)){
        #populate desired columns
        curr_col <- SNC_note_blank_cols[x]
        if(curr_col!="REF2"){
          SNC_note_blank[SNC_note_blank_row, curr_col] <- mydata_SNC[which(mydata_SNC$`Lease ID` == SNC_leasid[i]), curr_col] %>% 
            unique()
        }
      }
      #add notes that are missing
      SNC_note_blank[SNC_note_blank_row, "REF2"] <- paste0(curr_blank, collapse = "; ")
    }
  }
  #rename REF2 column to desired final name
  SNC_note_blank <- SNC_note_blank %>% dplyr::rename("Reference 2 ID(s) with Blank Note" = "REF2")
}





###----------------###
###Email exceptions###
###----------------###
if(okaytocontinue){
  if(length(c(SNC_note_missing$`Manager Email`, SNC_note_blank$`Manager Email`)) > 0){
    email_addresses <- c(SNC_note_missing$`Manager Email`, SNC_note_blank$`Manager Email`) %>% unique()
    myemailfiles <- c()
    skipped_email <- c()
    myquery_RefDesc <- paste0(
      "
      select
        'SNC' as \"Reference 1\"
      , rtypid as \"Reference 2\"
      , descrptn as \"Reference 2 ID Description\"
      from MRI.RTYP 
      where RTYPID in (", paste0("'",MRI_noteRef,"'", collapse = "," ), ")
      --where RTYPID in ('COO','DELIVER','ELEC','GAS','INSURNC','LCDA','LEASDEP','OPEN','PLANS','SECDEP','SIGNS','WTRSWR') /* test line, not up to date */
      order by RTYPID
    "
    )
    #20241106: mydata_RefDesc <- dbGetQuery(mySSdb, myquery_RefDesc)
    mydata_RefDesc <- dbGetQuery(mySfDB, myquery_RefDesc)
    #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
    mydata_RefDesc[] <- lapply(mydata_RefDesc[], function(x) if(inherits(x, "character")) trimws(x, which = "right") else x)
    mydata_status <- check_mydf_rows(mydata_RefDesc, MinNumRows = 1, ReportName = myReportName)
    #iterate through all present email
    for(i in 1:length(email_addresses)){
      curr_email <- email_addresses[i]
      if(curr_email %notin% MRI_email_blacklist & !is.na(curr_email)){
        
        #create Excel file and body table for missing
        email_missing <- SNC_note_missing[which(SNC_note_missing$`Manager Email` == curr_email),]
        curr_name <- email_missing$`Manager Name`[1]
        if(nrow(email_missing)>0){
          myXLSXColWidths <- data.frame (
            colname  = c(
              "Manager Name",
              "Lease ID",
              "Bldg ID",
              "Suite ID",
              "SNC Note Reference 2 Type ID(s) Missing",
              "City",
              "State",
              "Occupant Name",
              "Execution",
              "Beginning",
              "Rent Start",
              "Gen Code",
              "Status"
              #"",
            )
            ,
            width = c(
              if(max(nchar(na.omit(email_missing[,"Manager Name"]))) > 15){
                min(60, max(nchar(na.omit(email_missing[,"Manager Name"]))))}else{17},
              10,
              10,
              10,
              if(max(nchar(na.omit(email_missing[,"SNC Note Reference 2 Type ID(s) Missing"]))) > 20){
                min(100, max(nchar(na.omit(email_missing[,"SNC Note Reference 2 Type ID(s) Missing"]))))}else{24},
              if(max(nchar(na.omit(email_missing[,"City"]))) > 15){
                min(60, max(nchar(na.omit(email_missing[,"City"]))))}else{16},
              8,
              if(max(nchar(na.omit(email_missing[,"Occupant Name"]))) > 25){
                min(80, max(nchar(na.omit(email_missing[,"Occupant Name"]))))}else{28},
              10.5,
              10.5,
              10.5,
              8.5,
              8.5
            )
            , stringsAsFactors = FALSE
          ) #myXLSXColWidths
          #re-order and subset only desired column names
          email_missing <- email_missing[,myXLSXColWidths$colname]
          myFN <- paste0("SNC Lease Notes Missing Ref2-",curr_name,".xlsx")
          mySN <- "Missing Ref2"
          writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  
                    RptDF = email_missing, colnames = TRUE, colwidths = myXLSXColWidths, 
                    writeover = TRUE)
          myemailfiles <- c(myemailfiles, file.path(myReportPath, myFN))
          
          #create email table (missing)
          email_tbl <- email_missing[,Email_body_missing_cols]
          email_tbl$Execution <- format(email_tbl$Execution,  "%m/%d/%Y")
          body_Missing_tbl <- print(
            xtable(
              email_tbl,
              #caption = paste0(this_ReportName, " (", query.date, ")"),
              digits = rep(0,ncol(email_tbl)+1)
            ),
            #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
            html.table.attributes = "border=2 cellspacing=1",
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          )
        }
        
        
        #create Excel file for blank
        email_blank <- SNC_note_blank[which(SNC_note_blank$`Manager Email` == curr_email),]
        if(nrow(email_blank)>0){
          curr_name <- email_blank$`Manager Name`[1]
          myXLSXColWidths <- data.frame (
            colname  = c(
              "Manager Name",
              "Lease ID",
              "Bldg ID",
              "Suite ID",
              "Reference 2 ID(s) with Blank Note",
              "City",
              "State",
              "Occupant Name",
              "Execution",
              "Beginning",
              "Rent Start",
              "Gen Code",
              "Status"
              #"",
            )
            ,
            width = c(
              if(max(nchar(na.omit(email_blank[,"Manager Name"]))) > 15){
                min(60, max(nchar(na.omit(email_blank[,"Manager Name"]))))}else{17},
              10,
              10,
              10,
              if(max(nchar(na.omit(email_blank[,"Reference 2 ID(s) with Blank Note"]))) > 20){
                min(100, max(nchar(na.omit(email_blank[,"Reference 2 ID(s) with Blank Note"]))))}else{24},
              if(max(nchar(na.omit(email_blank[,"City"]))) > 15){
                min(60, max(nchar(na.omit(email_blank[,"City"]))))}else{16},
              8,
              if(max(nchar(na.omit(email_blank[,"Occupant Name"]))) > 25){
                min(80, max(nchar(na.omit(email_blank[,"Occupant Name"]))))}else{28},
              10.5,
              10.5,
              10.5,
              8.5,
              8.5
            )
            , stringsAsFactors = FALSE
          ) #myXLSXColWidths
          #re-order and subset only desired column names
          email_blank <- email_blank[,myXLSXColWidths$colname]
          myFN <- paste0("SNC Lease Notes Blank Text-",curr_name,".xlsx")
          mySN <- "Blank Note Text"
          writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  
                    RptDF = email_blank, colnames = TRUE, colwidths = myXLSXColWidths, 
                    writeover = TRUE)
          myemailfiles <- c(myemailfiles, file.path(myReportPath, myFN))
          
          #create email table (blank)
          email_tbl <- email_blank[,Email_body_blank_cols]
          email_tbl$Execution <- format(email_tbl$Execution,  "%m/%d/%Y")
          body_Blank_tbl <- print(
            xtable(
              email_tbl,
              #caption = paste0(this_ReportName, " (", query.date, ")"),
              digits = rep(0,ncol(email_tbl)+1)
            ),
            #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
            html.table.attributes = "border=2 cellspacing=1",
            type = "html",
            caption.placement = "top",
            include.rownames=FALSE
          )
        }
        
        
        #create email Reference Key
        body_RefKey_tbl <- print(
          xtable(
            mydata_RefDesc, 
            #caption = paste0(this_ReportName, " (", query.date, ")"),
            digits = rep(0,ncol(mydata_RefDesc)+1)
          ),
          #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
          html.table.attributes = "border=1 cellspacing=1",
          type = "html",
          caption.placement = "top",
          include.rownames=FALSE
        )
        
        #create email body
        body_html <- paste0(
          "<html><head></head><body>",
          "<h3>",myReportName, " - ", curr_name, "</h3>",
          if(exists("body_Missing_tbl")){
            paste0(
              "<p>",
              "The following leases appear to be <strong>MISSING</strong> one or more SNC lease note ",
              "entries as indicated by the reference 2 IDs mentioned:",
              "</p>",
              body_Missing_tbl,
              "<br>"
            )
          },
          if(exists("body_Blank_tbl")){
            paste0(
              "<p>",
              "The following leases appear to have one or more SNC lease notes ",
              "entered, but still <strong>BLANK</strong>, for the reference 2 IDs mentioned:",
              "</p>",
              body_Blank_tbl,
              "<br>"
            )
          },
          "<p>",
          "<em>The following key describes the desired SNC lease notes. ",
          "These should be entered with Reference 1 being the 'SNC' ID and ",
          "Reference 2 being the specific ID for each requirement:</em>",
          "</p>",
          body_RefKey_tbl,
          "<br><br>",
          norm_sig,
          "</body></html>"
        )
        
        #send email
        mailsend(recipient = curr_email,
                 subject = paste0(myReportName, "-", curr_name),
                 body = body_html,
                 attachment = myemailfiles,
                 test = testing_emails, testrecipient = test_recip
        )
        
      }else{
        #add skipped email address to warning email
        skipped_email <- c(skipped_email, curr_email)
      }
    }
    if(length(skipped_email)>0){
      #email that some leases were skipped
      body_html <- paste0(
        "<html><head></head><body>",
        "<h3>",myReportName, " - SKIPPED EMAILS</h3>",
        "<p>",
        "The following email addresses appeared in the ", myReportName, 
        " routine. They were apparently in the blacklisted emails.  ",
        "Investigate as needed.",
        "</p>",
        paste0(skipped_email, collapse = "; "),
        "<br><br>",
        norm_sig,
        "</body></html>"
      )
      
      #send email
      mailsend(recipient = warn_recip,
               subject = paste0(myReportName, " - Skipped email"),
               body = body_html,
               attachment = NULL,
               test = testing_emails, testrecipient = test_recip
      )
    }
  }
}


