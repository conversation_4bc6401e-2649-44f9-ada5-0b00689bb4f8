library(rJava)
library(xtable)
library(reshape2)
library(dplyr)
library(RDCOMClient)
library(lubridate)
library(formattable)
library(data.table)
#********: library(mailR) #replaced by gmailr package
library(gmailr)
library(purrr) #used for attachments to gmailr emails
library(stringr)
library(readr)
library(openxlsx)
library(utils)
library(keyring)
#********: library(RODBC) #replaced by odbc and DBI packages for Snowflake conn
library(DBI)
library(odbc)


testing_emails <- FALSE  #NORMAL, next line over-rides & should normally be disabled in PRODUCTION instance
#testing_emails <- TRUE

# Version 20241113

### 20241113 change:
### switched to DBI::dbDisconnect(mySfDB) to close connection at end of script

### ******** change:
### converted SQL queries to Snowflake DBs
### converted from mailR package (SMTP), to gmailr (OAuth) ahead of 20240930 SMTP deprecation in GMail (IF IP IS IN RANGE, STILL USES MAILR FOR SMTP RELAY!!!!)
### message() was being masked by gmailr where it's deprecated, replaced with explicit base::message() if present
### Updated signature for emails to standard requested by <PERSON> in March 2024

### 20230320 change:
### Updated 'internal' and 'external' tenant logic in query to reflect
### changes when Legacy changed usage of the leas.tentcat column

### 20220427 change:
### New file


# Parameters
query.date <- format(Sys.Date(), "%d-%b-%y")
# next lines are test lines that replace line above for testing purposes only
#query.date <- format(as.Date("04-FEB-20","%d-%b-%y"),"%d-%b-%y")
#query.date <- format(Sys.Date() + 7, "%d-%b-%y")

okaytocontinue <- TRUE

scriptfolder <- "LEGACY_MRI_Exceptions-Suites"
myReportName <- "MRI Suite Exceptions-Excluded suite has active lease"
msg_text <- paste0("Beginning '", myReportName, "' routine")
base::message(msg_text)
Sys.sleep(1.5)
rptFN <- paste0("MRI_Suite_Exceptions-E_suite_active_lease", ".xlsx")
myReportCriteria <- paste0("<p><b>Criteria for exceptions:</b><ul>",
                           "<li>Suite Type Area Usage is 'E' (Exclude), but 'Current' lease (w/rent > $0)</li>",
                           "</ul></p><br/>"
                           )


#SSMS connection
#********: mySSdb <- odbcConnect("SQLServer", "SteveO_ro", key_get("MRI_bak", "SteveO_ro"))

###Snowflake Connection
#Sf_environ <- "STAGE"
Sf_environ <- "PROD"
if(Sf_environ == "STAGE"){
  ###STAGE Snowflake versions###
  Sf_DB <- "STAGE_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "STAGE_DATA_ANA_WH"
  Sf_role <- "AR_STAGE_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_stage")
  Sf_pw <- key_get("SfHV", "tableau_PW_stage")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}else{
  ###PROD Snowflake versions###
  Sf_DB <- "PROD_CSM_DB"
  Sf_schema <- "CORPORATE"
  Sf_wh <- "PROD_DATA_ANA_WH"
  Sf_role <- "AR_PROD_CONSUMPTION_RW"
  Sf_user <- key_get("SfHV", "tableau_ID_prod")
  Sf_pw <- key_get("SfHV", "tableau_PW_prod")
  Sf_auth <- '' #for Tableau service account
  #Sf_auth <- 'externalbrowser' #for MS Entra SSO
}
# create a connection
mySfDB <- DBI::dbConnect(odbc::odbc(), 
                         dsn="Snowflake HV", 
                         Database=Sf_DB,
                         SCHEMA=Sf_schema,
                         uid=Sf_user, 
                         pwd=Sf_pw,
                         WAREHOUSE=Sf_wh,
                         ROLE=Sf_role,
                         authenticator = Sf_auth
)
rm(Sf_user,Sf_pw)
#Sys.setenv(TZ="GMT")
Sys.setenv(TZ="America/Chicago")
dbBegin(mySfDB)
myquery <- "ALTER SESSION SET TIMEZONE = 'America/Chicago'"
rs <- dbSendQuery(mySfDB, myquery)
dbCommit(mySfDB)
dbClearResult(rs)

# email parameters: recipient(s) of warning emails and signatures
#norm_recip <- c("<EMAIL>")
#norm_recip <- c("<EMAIL>","<EMAIL>")
norm_recip <- c("<EMAIL>")
norm_sig <- paste0("<b><span style='font-weight:bold'>Steve Olson</span></b><br/>",
                   "Sr. Analytics Mgr.<br/>",
                   "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>",
                   "2500 Lehigh Ave.<br/>",
                   "Glenview, IL 60026<br/>",
                   "Ph: 847/904-9043<br/></span></font>")
warn_recip <- c("<EMAIL>")
warn_sig <- "<br/><b> Steve Olson </b><br/> (847)904-9043 Office<br/> (715)379-8525 Cell"
test_recip <- c("<EMAIL>")
test_cc_recip <- c("<EMAIL>")

report.time <- format(Sys.time(), "%Y%m%d-%H%M%S%Z")

centralPath <- file.path("//*************","public","steveo","R Stuff","ReportFiles")
tableauPath <- file.path("C:","Users","table","Documents","ReportFiles") #Tableau PC local directory for R scripts
test_computers <- c("STEVEO-PLEX7010","LAPTOPTOSHIBA13","STEVEANDJENYOGA")
prod_computers <- c("DESKTOP-TABLEAU")
this_computer <- Sys.getenv("COMPUTERNAME")
if(this_computer %in% test_computers){
  testing_pc <- TRUE  #TESTING, changes some paths to Shared Drive instead of R/Tableau PC
  mainpath <- centralPath
}else{
  testing_pc <- FALSE
  mainpath <- tableauPath
}

logpath <- file.path(mainpath,scriptfolder)
myReportPath <- logpath
sig_logo <- FALSE

### define some functions ###

#Email OAuth dance (each distinct email used in auth will require OAuth dance to be completed)
gMail_auth_email <- "<EMAIL>" #this is the email from account used in the gmailr mailsend function
gm_auth(email = gMail_auth_email)
gMail_reply_to <- "<EMAIL>" #use if you want alternate reply-to email address, comment out to use gMail_auth_email

mailsend <- function(recipient, subject, body, attachment = NULL, inline = FALSE, 
                     sender = gMail_auth_email, test = FALSE, testrecipient = NULL, reportname = myReportName){
  email_regex <- "([_a-z0-9-]+(\\.[_a-z0-9-]+)*@[a-z0-9-]+(\\.[a-z0-9-]+)*(\\.[a-z]{2,4}))"
  myemail <- unlist(regmatches(sender, gregexpr(email_regex, sender)))[[1]]
  sender <- paste0(reportname, " <", myemail, ">")
  myreplyto <- myemail #change this using reply_to_addy parameter if you want replies to go to someone other than sender email addy
  if(exists("gMail_reply_to")){
    if(nchar(gMail_reply_to)>0){
      myreplyto <- unlist(regmatches(gMail_reply_to, gregexpr(email_regex, gMail_reply_to)))[[1]]
    }
  }
  
  if(test){
    recipients <- testrecipient
    body <- paste0("<p><b>TEST SEND (normal recipient: ",
                   paste(recipient, collapse = "; "), ")</b></p>",
                   body)
  }else{
    recipients <- recipient
  }
  
  msg <- gm_mime( 
    To = recipients,
    From = sender,
    'Reply-To' = myreplyto,
    #Sender = sender,
    Subject = subject
  ) %>% 
    gm_html_body(body)
  
  #attach file
  if(!is.null(attachment)){
    attach_multiple <- function(mime, attachment, ...) {
      mime %>% 
        gmailr::gm_attach_file(attachment, ...)
    }
    msg <- msg %>% purrr::reduce(.init = ., .x = attachment, .f = attach_multiple)
  }
  gm_send_message(msg)
}

get_Signature <- function(Template_HTML, Name = '', Title = '', Email = '', Phone = ''){
  sig <- Template_HTML %>%
    {gsub("\\[NAME\\]", Name, .)} %>%
    {gsub("\\[TITLE\\]", Title, .)} %>%
    {gsub("\\[EMAIL_FULL\\]", Email, .)} %>%
    {gsub("\\[TEL \\(000\\) 000-0000\\]", Phone, .)}
  return(sig)
}

###Get email signature###
HVSigPath <- file.path(mainpath,"HTML_signatures.csv")
if(file.exists(HVSigPath)){
  #read signature template and sub in desired values
  HTML_signatures <- read.csv(HVSigPath, stringsAsFactors = FALSE)
  norm_sig <- get_Signature(
    Template_HTML = HTML_signatures$HTML[which(HTML_signatures$Desc == 'LCP Reporting')], #LCP Reporting doesn't use any personal info substitutions
    Name = 'NA',
    Title = 'NA',
    Email = 'NA',
    Phone = '(*************'
  )
}


check_mydata_rows <- function(MinNumRows, ReportName = NULL){
  if(exists('mydata') && is.data.frame(get('mydata'))){
    if(nrow(mydata) >= MinNumRows ){
      error_status <- paste0(ReportName, ": COMPLETE")
      tempnrow <- nrow(mydata)
      tempbool <- TRUE
    }else{
      tempbool <- FALSE
      tempnrow <- nrow(mydata)
      error_status <- paste0(ReportName, ": INCOMPLETE RESULTS")
    }
  }else{
    #problem with data load. Log, send email and abort
    tempbool <- FALSE
    tempnrow <- 0
    error_status <- paste0(ReportName, ": NO RESULTS")
  }
  output <- list(tempbool, tempnrow, error_status)
  return(output)
}


writeXLSX <- function(dirpath, fname, sname = "Sheet1", RptDF, colnames = TRUE, colwidths = NULL,  writeover = TRUE){
  #passed colwidths should be a data frame where 1st column is Column Name (in df) and 2nd column is desired Width
  myFN <- file.path(dirpath, fname)
  
  #mySN <- substr(fname, 1, (regexpr("\\.[^\\.]*$", fname) - 1))
  #myWB <- buildWorkbook(RptDF)
  hs <- createStyle(
    textDecoration = "BOLD", fontColour = "#000000", fontSize = 12,
    fontName = "Arial Narrow", fgFill = "#D6D6D6", wrapText = TRUE,
    valign = "center"
  )
  RptDF[] <- lapply(RptDF[], function(x) if(inherits(x, "POSIXct")) as.Date(x, tz="") else x)
  myWB <- buildWorkbook(RptDF, asTable = FALSE, sheetName = sname, headerStyle = hs)
  freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1)
  addFilter(wb = myWB, sheet = sname, row = 1, cols = 1:ncol(RptDF))
  
  
  if(!is.null(colwidths)){
    #set column widths for specified columns
    if(is.data.frame(colwidths) & nrow(colwidths) > 0){
      for(i in 1:nrow(colwidths)){
        mycols <- which(names(RptDF) == colwidths[i, 1] )
        setColWidths(wb = myWB, sheet = sname,
                     cols = mycols,
                     widths = rep(colwidths[i, 2],length(mycols)))
      }
    }
  }
  
  
  if(!dir.exists(dirpath)){
    #report path not present, create it
    dir.create(dirpath)
    
  }
  if (dir.exists(dirpath)) {
    #save file
    #write_excel_csv(RptDF, myFN, na="", col_names = colnames)
    #write.xlsx(myWB, 
    #           myFN, 
    #           sheetName=sname, 
    #           row.names=FALSE, 
    #           showNA=FALSE, 
    #           overwrite = writeover, 
    #           freezePane(wb = myWB, sheet = sname, firstActiveRow = 2, firstActiveCol = 1))
    openxlsx::saveWorkbook(myWB, file = myFN, overwrite = writeover)
  }else{
    #try prepending report time to filename to create unique name
    myNewFN <- paste0(report.time, "-", myFN)
    if (dir.exists(dirpath) & file.opened(myNewFN) == FALSE) {
      #write_excel_csv(RptDF, myNewFN, na="", col_names = colnames)
      oldOpt <- options()
      options(xlsx.date.format="MM/dd/yyyy")
      write.xlsx(RptDF, myNewFN, sheetName=mySN, row.names=FALSE, showNA=FALSE, overwrite = writeover)
      options(oldOpt)
      #ALT FILENAME error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS SAVED ",
                         "WITH AN ALTERNATE FILENAME</b> during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myNewFN),
                         "<br/><br/>It appears that the original filename (", fname, ") was open in another process or locked.",
                         "<br/><br/>The routine should continue.<br/> <br/>",
                         warn_sig
      )
    }else{
      #FAILED SAVE error msg
      bodytext <- paste0("This is an automated email to inform you that it appears <b>the following file WAS NOT SAVED</b> ",
                         "during the <b>", myReportName, "</b> routine.<br/><br/>",
                         as.character(myFN),
                         "<br/><br/>Either the path wasn't accessible or the file was open in another process.",
                         "<br/><br/>The routine should continue without saving this file.<br/> <br/>",
                         warn_sig
      )
    }
    #send mail
    mailsend(recipient = warn_recip,
             subject = paste0(myReportName, " : REPORT FILE SAVING ERROR"),
             body = bodytext
    )
  }
}





### Find Exceptions and email results
if(okaytocontinue){
  
  myFN <- rptFN
  this_recip <- c(norm_recip)
  this_ReportName <- myReportName
  
  myquery_exceptions <- paste0(
    "
      SELECT
      	'Suite Type Usage = ''E (Exclude)'' with active lease' as ISSUE
    ",'
      ,	B.BLDGID AS BLDGID
      ,	SANDL.SUITID
      ,	SANDL.SUITETYPE_MRI AS "SUITETYPE MRI"
      ,	SANDL.SUITE_TYPE_AREA_USAGE AS "SUITE TYPE AREA USAGE"
      ,	SANDL.COUNT_AS_OCCUPIED AS "COUNT AS OCCUPIED"
      ,	SANDL.LEASID
      ,	SANDL.OCCUPANT_STATUS AS "OCCUPANT STATUS"
      ,	SANDL.ACTIVE_CONTINGENCY AS "ACTIVE CONTINGENCY"
      ,	SANDL.GENERATION_CODE AS "GEN CODE"
      ,	SANDL.TENANT_NAME AS "TENANT NAME"
      ,	SANDL.RENT_START AS "RENT START"
      ,	SANDL.STOP_BILL_DATE AS "STOP BILL DATE"
      ,	SANDL.VACATE_DATE AS "VACATE DATE"
      ,	SANDL.EXPIRE_DATE AS "EXPIRE DATE"
      ,	SANDL.THIRD_PARTY_RENT AS "3RD PARTY RENT"
      ,	SANDL.INTERNAL_RENT AS "INTERNAL RENT"
    ',"
      FROM MRI.BLDG B
      LEFT JOIN
      ( /* Suites and Leases */
      	select 
      		S.BLDGID
      	,	S.SUITID
      	,	S.SUITETYPE_MRI
      	,	S.SUITSQFT AS SUITE_SQ_FEET
      	,	CONCAT(TB_CM_SUITETYPE.SUITETYPEUSAGE,CASE WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'I' THEN ' (Include)' 
      						WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'N' THEN ' (Include Not Counted)' 
      						WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'E' THEN ' (Exclude)' END) AS SUITE_TYPE_AREA_USAGE
      	,	SSQF_TYPE.SQFTTYPE
      	,	(CASE WHEN TB_CM_SUITETYPE.SUITETYPEUSAGE = 'I' OR TB_CM_SUITETYPE.SUITETYPEUSAGE IS NULL THEN 1 END) AS COUNT_AS_SUITE /* Exclude N and E, sometimes Legacy counts NULL sometimes not */
      	--,	(CASE WHEN LEASED.OCCPSTAT ='C' THEN 1 END) AS COUNT_AS_OCCUPIED /* Steve best guess. prior to 10/13/2021 Patrick M and Shaunti A meeting */
      	,	(CASE WHEN LEASED.OCCPSTAT ='C' AND LEASED.GENCODE <> 'SNC' AND (S.SUITETYPE_MRI <> 'KIOSK' OR S.SUITETYPE_MRI IS NULL) THEN 1
      			WHEN LEASED.OCCPSTAT ='C' AND LEASED.GENCODE = 'SNC' AND (S.SUITETYPE_MRI <> 'KIOSK' OR S.SUITETYPE_MRI IS NULL) AND LEASED.RENT_START <= Cast(GetDate() AS date) AND (LEASED.ACTIVE_CONTINGENCY IS NULL OR LEASED.ACTIVE_CONTINGENCY = 'N')  THEN 1 END) AS COUNT_AS_OCCUPIED /* Steve best guess. Starting 10/13/2021 per Patrick M and Shaunti A meeting */
      	,	IFNULL(SSQF_TYPE.SQFT,0) AS LEASABLE_SQFT
      	,	IFNULL((CASE WHEN LEASED.ACTIVE_CONTINGENCY = 'Y' THEN 0 ELSE SSQF_TYPE.LSDSQFT END),0) AS LEASED_SQFT
      	,	LEASED.LEASID
      	,	LEASED.ACTIVE_CONTINGENCY
      	,	LEASED.GENCODE AS GENERATION_CODE
      	,	LEASED.OCCPNAME AS TENANT_NAME
      	,	LEASED.OCCUPANT_STATUS AS OCCUPANT_STATUS
      	,	LEASED.RENT_START
      	,	LEASED.STOPBILLDATE AS STOP_BILL_DATE
      	,	LEASED.VACATEDATE AS VACATE_DATE
      	,	LEASED.EXPIRDATE AS EXPIRE_DATE
      	--,	LEASED.END_DATE AS END_DATE
      	--,	LEASED.EFFDATE
      	--,	LEASED.LAST_BILL_DATE AS LAST_BILL_DATE
      	,	LEASED.THIRD_PARTY_RENT
      	,	LEASED.INTERNAL_RENT
      	FROM MRI.SUIT S
      	JOIN MRI.BLDG B
      	ON B.BLDGID = S.BLDGID
      	LEFT JOIN MRI.TB_CM_SUITETYPE
      	ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID
      	LEFT JOIN 
      	(
      		SELECT *
      		FROM MRI.SSQF
      		WHERE SSQF.EFFDATE = (
      			SELECT MAX(I.EFFDATE) FROM MRI.SSQF I WHERE I.BLDGID = SSQF.BLDGID AND I.SUITID = SSQF.SUITID AND I.EFFDATE <= CURRENT_DATE
      			)
      	) SSQF_TYPE
      	ON S.SUITID = SSQF_TYPE.SUITID
      	 AND S.BLDGID = SSQF_TYPE.BLDGID
      	LEFT JOIN
      	(
      		SELECT 								
      			L.BLDGID,
      			TRIM(P.PORTID) AS PORTFOLIO,
      			L.SUITID,							
      			(CASE WHEN L.TENTCAT != 'INTER' THEN '3rd Party Base Rent' ELSE 'Internal Base Rent' END) AS RENT_TYPE,
      			L.LEASID,
      			L.OCCPNAME,							
      			TO_DATE(L.RENTSTRT) AS RENT_START,							
      			CAST(S.SUITSQFT AS INT) AS SUITE_SQFT, /* NOT ACCURATE IF 'Rent Roll Update SF' has not been run recently */							
      			CAST(IFNULL(SQF.SQFT,0) AS INT) AS SSQF_SQFT,							
      			TRIM(SQF.SQFTTYPE) AS SQFT_TYPE,														
      			L.OCCPSTAT,
      			CL.CODEDESC AS OCCUPANT_STATUS,
      			L.TENTCAT AS TENANT_CATEGORY_ID,
      			TCAT.TENTDESC AS TENANT_CATEGORY_DESCRIPTION,	
      			TO_DATE(L.STOPBILLDATE) AS STOPBILLDATE,							
      			TO_CHAR(L.VACATE, 'MM/dd/yyyy') AS VACATEDATE,							
      			TO_CHAR(L.EXPIR,'MM/dd/yyyy') as EXPIRDATE,														
      			--(CASE WHEN L.TENTCAT != 'INTER' THEN IFNULL(RENT.AMOUNT,0) ELSE 0 END) AS THIRD_PARTY_RENT, /* deprecated when Legacy had MRI delete values in this column */
				    (CASE WHEN L.COMPANYGRPID = 2 THEN 0 ELSE IFNULL(RENT.AMOUNT,0) END) AS THIRD_PARTY_RENT, /* new 20220720 */
      			--(CASE WHEN L.TENTCAT != 'INTER' THEN 0 ELSE IFNULL(RENT.AMOUNT,0) END) AS INTERNAL_RENT, /* deprecated when Legacy had MRI delete values in this column */
				    (CASE WHEN L.COMPANYGRPID = 2 THEN IFNULL(RENT.AMOUNT,0) ELSE 0 END) AS INTERNAL_RENT,
      			S.SUITETYPE_MRI,							
      			TB_CM_SUITETYPE.SUITETYPEUSAGE,							
      			TB_CM_SUITETYPE.DESCRIPTION AS SUITETYPEDESCRIPTION,
      			L.GENCODE,
      			L.CONTINGENT,
      			L.CONTINGENTDT,
      			case when L.CONTINGENT = 'Y' AND L.CONTINGENTDT >= Cast(GetDate() AS date) THEN 'Y' else 'N' end as ACTIVE_CONTINGENCY,
				case when L.COMPANYGRPID = 2 THEN 'INT' ELSE 'EXT' END AS INTEXT /* new 20220720 */							
      		FROM MRI.SUIT S
      		LEFT JOIN MRI.LEAS L
      		 ON S.SUITID = L.SUITID
      			AND S.BLDGID = L.BLDGID
      		left join								
      		(								
      			SELECT CMRECC.LEASID
      			,	SUM(CMRECC.AMOUNT) AS AMOUNT
      			FROM MRI.CMRECC
      			JOIN MRI.LEAS ON CMRECC.LEASID = LEAS.LEASID
      			LEFT JOIN MRI.INCH_LCP_REPORTS
				ON CMRECC.INCCAT = INCH_LCP_REPORTS.INCCAT
      			WHERE CMRECC.EFFDATE = (
      						SELECT MAX(IC.EFFDATE) AS EFFDATE
      						FROM MRI.CMRECC IC 
      						WHERE TO_DATE(LEAS.RENTSTRT) < CURRENT_DATE
      						AND IC.BLDGID=CMRECC.BLDGID 
      						AND IC.LEASID=CMRECC.LEASID 
      						AND IC.INCCAT=CMRECC.INCCAT 
      						--AND IC.EFFDATE <= CURRENT_TIME /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
      						--AND (IC.ENDDATE IS NULL OR IC.ENDDATE >= TO_DATE( CURRENT_TIME)) /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
      						AND IC.INEFFECT = 'Y' /* ONLY USED IF LOOKING FOR HISTORICAL OR FUTURE RATES, OTHERWISE USE INEFFECT COL */
      				)
      				--AND CMRECC.INCCAT IN ('RNT','RN2','PRK','PPR','RFV','RHW','RMP','RSF','RVT') /*REPLACED BY LINE BELOW 2024-11-11*/
      				AND (INCH_LCP_REPORTS.RPT_TYPE IN ('BASE RENT') OR CMRECC.INCCAT IN ('PPR') )
      				/* TEST */ --AND CMRECC.LEASID IN ('001557','002123') /*'001557','002123'*/
      			GROUP BY
      				CMRECC.LEASID					
      		) RENT	
      		ON L.LEASID = RENT.LEASID								
      		INNER JOIN MRI.BLDG B								
      		ON S.BLDGID = B.BLDGID	
      		LEFT JOIN MRI.TCAT ON L.TENTCAT = TCAT.TENTCAT
      		LEFT JOIN (								
      			SELECT * 							
      			FROM MRI.SSQF 							
      			WHERE SSQF.EFFDATE = (SELECT MAX(I.EFFDATE) 
      					FROM MRI.SSQF I 
      					WHERE I.BLDGID = SSQF.BLDGID 
      					AND I.SUITID = SSQF.SUITID 
      					AND TO_DATE(I.EFFDATE) <= CURRENT_DATE 
      				)							
      		) SQF								
      		ON S.BLDGID = SQF.BLDGID								
      			AND S.SUITID = SQF.SUITID							
      		LEFT JOIN MRI.TB_CM_SUITETYPE								
      		ON S.SUITETYPE_MRI = TB_CM_SUITETYPE.SUITETYPEID								
      		LEFT JOIN MRI.ENTITY E								
      		ON B.ENTITYID = E.ENTITYID								
      		LEFT JOIN MRI.PROJ P								
      		ON E.PROJID = P.PROJID																
      		LEFT JOIN MRI.CODELIST CL								
      		ON L.OCCPSTAT = CL.CODEVAL AND CL.CODETYPE = 'OCCPSTAT'								
      		WHERE L.OCCPSTAT NOT IN ('P', 'I') 
      			AND (							
      					(							
      						TO_DATE(L.RENTSTRT) <= CURRENT_DATE				
							AND (				
									L.STOPBILLDATE IS NULL 		
									OR TO_DATE(L.STOPBILLDATE) >= CURRENT_DATE		
									OR (		
											TO_DATE(L.STOPBILLDATE) < CURRENT_DATE 
											AND COALESCE(L.VACATE,L.EXPIR) >= CURRENT_DATE
										)	
								)			
							AND COALESCE(L.VACATE,L.EXPIR) >= CURRENT_DATE 
      					)					
      					OR 						
      					(((TO_DATE(L.EXPIR) <= CURRENT_DATE OR L.EXPIR IS NULL) AND (TO_DATE(L.VACATE) >= CURRENT_DATE OR L.VACATE IS NULL))) 					
						AND (L.RENTSTRT IS NOT NULL AND L.OCCPSTAT<>'I')	
      				)
      	)LEASED
      	ON S.BLDGID = LEASED.BLDGID
      		AND S.SUITID = LEASED.SUITID
      	WHERE (B.INACTIVE <> 'Y' or B.INACTIVE IS NULL)
      		AND TB_CM_SUITETYPE.SUITETYPEUSAGE = 'E'
      ) SANDL
      on B.BLDGID = SANDL.BLDGID
      WHERE SANDL.LEASID IS NOT NULL
      	AND (SANDL.THIRD_PARTY_RENT > 0 OR SANDL.INTERNAL_RENT > 0)
      ORDER BY B.BLDGID, SANDL.SUITID
    "
  )
  #********: mydata <- sqlQuery(mySSdb, myquery_exceptions, stringsAsFactors = FALSE)
  mydata <- DBI::dbGetQuery(mySfDB, myquery_exceptions, stringsAsFactors = FALSE)
  mydata_test <- mydata
  #remove trailing spaces to avoid using 'trim' in multiple SELECT columns
  mydata_test[] <- lapply(mydata[], function(x) if(inherits(x, "character")) trimws(x, "r") else x)
  mydata_status <- check_mydata_rows(MinNumRows = 1, ReportName = myReportName)
  if(mydata_status[[1]] == TRUE){
    #exceptions found, create Excel file and email it
    
    #specify report column widths where alternate width desired
    myXLSXColWidths <- data.frame (colname  = c("ISSUE",
                                                "BLDGID",
                                                "SUITID",
                                                "SUITETYPE MRI",
                                                "SUITE TYPE AREA USAGE",
                                                "COUNT AS OCCUPIED",
                                                "LEASID",
                                                "OCCUPANT STATUS",
                                                "ACTIVE CONTINGENCY",
                                                "GEN CODE",
                                                "TENANT NAME",
                                                "RENT START",
                                                "STOP BILL DATE",
                                                "VACATE DATE",
                                                "EXPIRE DATE",
                                                "3RD PARTY RENT",
                                                "INTERNAL RENT"
                                                #"",
                                                )
                                   ,
                                   width = c(43.5,
                                             8,
                                             8,
                                             10.25,
                                             10.25,
                                             10.25,
                                             8,
                                             9.5,
                                             7.25,
                                             6,
                                             20,
                                             10.5,
                                             10.5,
                                             10.5,
                                             10.5,
                                             8,
                                             9.5
                                             )
                                   ,
                                   stringsAsFactors = FALSE
    ) #myXLSXColWidths
    mySN <- query.date
    writeXLSX(dirpath = myReportPath, fname = myFN, sname = mySN,  RptDF = mydata, colnames = TRUE, colwidths = myXLSXColWidths, writeover = TRUE)
    myemailfiles <- file.path(myReportPath, myFN)
    # create email
    myemailbodycols <- c(1,2,3,5,11,16,17)
    mydata_emailbody <- mydata[,myemailbodycols]
    mydata_emailbody[] <- lapply(mydata_emailbody[], function(x) if(inherits(x, "Date")) format(x, "%m/%d/%y") else x)
    if(nrow(mydata_emailbody)<=20){
      bodytable <- paste0("<p>",
                          print(xtable(mydata_emailbody, 
                                       #caption = paste0(this_ReportName, " (", query.date, ")"),
                                       digits = rep(0,ncol(mydata_emailbody)+1)
                                ),
                                #align = c(rep("l",2), rep("c", ncol(MyErrorLog) - 1)),
                                html.table.attributes = "border=2 cellspacing=1",
                                type = "html",
                                caption.placement = "top",
                                include.rownames=FALSE
                          ),
                          "</p>"
      )
    }else{
      bodytable <- paste0("<p>There are ", nrow(mydata_emailbody), 
                          " results, see attached file for all.",
                          "</p>"
      )
    }
    bodytext <- paste0("<p><b>REPORT: ", this_ReportName, "</b>",
                       "</p>",
                       myReportCriteria,
                       "<p>The info below contains MRI data (from yesterday) that appears to be an exception. <b>See attached Excel file for more details.</b> ",
                       "</p>",
                       bodytable,
                       "<br/>",
                       norm_sig
    )
    rs <- mailsend(recipient = this_recip,
                   subject = paste0(this_ReportName),
                   body = bodytext,
                   if(is.na(myemailfiles)){attachment = NULL}else{attachment = myemailfiles},
                   test = testing_emails, testrecipient = test_recip
    )
    myemailfiles <- NA
    #rm(mydata)
    
  }
}


DBI::dbDisconnect(mySfDB)




