from posixpath import basename
import smtplib
import os
import tempfile
# import pysftp
# import tzlocal
# import paramiko
from enum import Enum
from email import encoders
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
 
class EmailBodyType(Enum):
 
        TEXT = 'text'
        HTML = 'html'
 
 
def sendEmail(smtphost: str,
                  smtpport: str,
                  subject: str,
                  body: str,
                  recipients: list,
                  sender: str,
                  password: str,
                  bodytype=EmailBodyType.TEXT,
                  attachments: list=[],
                  replyto: str=None):
 
        message = MIMEMultipart()
        message['Subject'] = subject
        message['From'] = sender
        message['To'] = ', '.join(recipients)
        if replyto:
            message['Reply-To'] = replyto
        #message['ReplyTo'] = 'donotreply'
        html_part = MIMEText(body, bodytype.value)
        message.attach(html_part)
 
        part = None
   
        for attachment in attachments:
            with open(attachment, "rb") as attachmentFile:
             # Add the attachment to the message
                part = MIMEBase("application", "octet-stream")
                part.set_payload(attachmentFile.read())
                encoders.encode_base64(part)
                part.add_header("Content-Disposition", f"attachment; filename={basename(attachment)}",)
                part.add_header("Content-ID", f"<{basename(attachment)}>") # added by Jorge on 3/12/25 to embed images
                message.attach(part)
 
        if 'office365.com' in smtphost.lower(): # MS 365
            # Office 365 SMTP configuration
            smtp_server = smtplib.SMTP(smtphost, int(smtpport))
            try:
                # Explicitly set hostname for EHLO command
                smtp_server.ehlo('localhost')  # Use a valid hostname
                smtp_server.starttls()  # Enable TLS
                smtp_server.ehlo('localhost')  # Re-identify after STARTTLS
                smtp_server.login(sender, password)
                smtp_server.sendmail(sender, recipients, message.as_string())
            finally:
                smtp_server.quit()
        else: # gmail
            with smtplib.SMTP_SSL(smtphost, int(smtpport)) as smtp_server:
                smtp_server.login(sender, password)
                # smtp_server.starttls()  # Enable TLS
                smtp_server.sendmail(sender, recipients, message.as_string())
            # print("Message sent!")
 
def send_email(recipient, subject, body, attachments =[], test=None, test_recipient= None, replyto=None, override_email_recipients=False):
    """ custom email sender for HV """
    smtphost = os.environ["EMAIL_HOST"]
    smtpport = os.environ["EMAIL_PORT"]
    password =  os.environ["EMAIL_PASSWORD"]
    senders =  os.environ["EMAIL_SENDER"]
    recipients = recipient
 
    # convert to list if string
    if not isinstance(recipients, list):
        recipients = [recipient]
 
    if override_email_recipients:
        # @todo remove after testing
        recipients = [
            # '<EMAIL>',
            '<EMAIL>'
 
        ]
   
    # subject = f"Test 5 python email sender for HV"
    # body = "Python Email test from HV gmail"
    # bodytype = EmailBodyType.TEXT
    bodytype = EmailBodyType.HTML
 
    # recipient_emails = self.get_config_parameter_value_from_db(parameter_key='ONBOARDING_NOTIFICATIONS_EMAIL_RECIPIENTS')
    # recipients=jgutils.get_array_list_from_string(self.get_config_parameter_value_from_db(parameter_key='ONBOARDING_NOTIFICATIONS_EMAIL_RECIPIENTS'))
 
    if len(recipients) < 1:
        print(f"WARNING: No email addresses found for key in  *.CORPORATE.AC_COMMON_PARAMETERS table")
        # self.log_audit_in_db(log_msg=f"WARNING: No email addresses found for key in  *.CORPORATE.AC_COMMON_PARAMETERS table", log_type='warning')
        return
 
 
    # attachments = attachments
    if not isinstance(attachments, list):
        attachments = [attachments]
 
    # @todo remove after testing
    if override_email_recipients:
        override_msg = f"\tSending email to original {recipient}, overridden with {recipients} with subject: {subject}\n\n"
        print(override_msg)
        body = f"{body}\n\n{override_msg}"
 
    sendEmail(smtphost,smtpport,subject,body,recipients,senders, password, bodytype, attachments=attachments, replyto=replyto)    
 
def send_email_ms365(recipient, subject, body, attachments =[], test=None, test_recipient= None, replyto=None):
    """ custom email sender for HV """
    smtphost = os.environ["EMAIL_HOST_MS"]
    smtpport = os.environ["EMAIL_PORT_MS"]
    password =  os.environ["EMAIL_PASSWORD_MS"]
    senders =  os.environ["EMAIL_SENDER_MS"]
    recipients = recipient
 
    recipients = [
        # '<EMAIL>',
        '<EMAIL>'
 
    ]
   
    # subject = f"Test 5 python email sender for HV"
    # body = "Python Email test from HV gmail"
    # bodytype = EmailBodyType.TEXT
    bodytype = EmailBodyType.HTML
 
    # recipient_emails = self.get_config_parameter_value_from_db(parameter_key='ONBOARDING_NOTIFICATIONS_EMAIL_RECIPIENTS')
    # recipients=jgutils.get_array_list_from_string(self.get_config_parameter_value_from_db(parameter_key='ONBOARDING_NOTIFICATIONS_EMAIL_RECIPIENTS'))
 
    if len(recipients) < 1:
        print(f"WARNING: No email addresses found for key in  *.CORPORATE.AC_COMMON_PARAMETERS table")
        # self.log_audit_in_db(log_msg=f"WARNING: No email addresses found for key in  *.CORPORATE.AC_COMMON_PARAMETERS table", log_type='warning')
        return
 
 
    attachments = attachments
 
    print(f"\tSending email to original {recipient}, overridden with {recipients} with subject: {subject}\n\n")
    print(f"smtphost: {smtphost}\tsmtpport: {smtpport}\npassword: {password}\nbodytype: {bodytype}\nattachments: {attachments}\nreplyto: {replyto}")
 
    sendEmail(smtphost,smtpport,subject,body,recipients,senders, password, bodytype, attachments=attachments, replyto=replyto)    
# sample code
 
 
# smtphost = os.environ['SMTP_HOST']
# smtpport = os.environ['SMTP_PORT']
# subject = f"Some Subject"
# body = "Some text"
# bodytype = EmailBodyType.TEXT
# senders = os.environ['DATA_ADMIN_EMAIL_ACCOUNT']
# recipients = ["<EMAIL>"]
# password = os.environ['DATA_ADMIN_EMAIL_PASSWORD']
 
# sendEmail(smtphost,smtpport,subject,body,recipients,senders, password, bodytype)
 
if __name__ == '__main__':
    send_email_ms365(recipient='<EMAIL>',
                    subject='Test 5 python email sender for HV',
                    body='Python Email test from HV mdfkmasmkfksmd',
                    attachments=[],
                    test=None,
                    test_recipient=None
                    )
    print('ara ara')