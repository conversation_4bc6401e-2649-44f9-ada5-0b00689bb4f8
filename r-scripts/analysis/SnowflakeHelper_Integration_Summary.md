# MRI Lease Exceptions - Production Deployment Guide

## Overview

**Script:** `LEGACY_MRI_exceptions_least_inactive_dates.py`
**Purpose:** Finds MRI leases marked as "Current" but past critical dates, emails Excel report to property management.

**What it does:**
1. Queries Snowflake for lease exceptions (past stop billing, vacate, or expire dates)
2. Creates Excel report with lease details
3. Emails <NAME_EMAIL> (production) or test recipients
4. Logs execution details

## Database Dependencies

**Snowflake Tables:**
- `MRI.LEAS` - Main lease data
- `MRI.MOCCP` - Occupancy details
- `MRI.BLDG` - Building information
- `MRI.MNGR` - Property managers
- `BATCH_AUDIT.MOMS_EXECUTION_LOGS` - Audit logs (optional)

**Connection Requirements:**
- Database: `PROD_CSM_DB`
- Schema: `CORPORATE`
- Warehouse: `PROD_DATA_ANA_WH`
- Role: `AR_PROD_CONSUMPTION_RW`


## Requirements

**Python:** 3.10+

**Dependencies:** Install from requirements.txt
```bash
pip install -r requirements.txt
```


## Configuration

**Production Settings** (edit in script):
```python
# Line ~941
testing_emails = False    # Use production recipients

# Line ~40
LOG_TO_DB = True         # Enable database logging

# Recipients are already configured:
# NORM_RECIPIENTS = ['<EMAIL>']
```

## Deployment

**Running the Script**
- The script is ran via the runner.sh file.

**Log Files:**
- `logs/MRI_lease_exceptions_YYYYMMDD_HHMMSS.log` - Application logs
- `query_results_log.txt` - Debug output

**Common Issues:**
- **Snowflake Connection:** Check credentials and network access
- **Email Sending:** Verify Office 365 SMTP settings
- **File Permissions:** Ensure write access to logs/ and reports/

**Health Checks:**
```bash
# Test Snowflake
python -c "from libs.snowflake_helper import SnowflakeHelper; SnowflakeHelper()"

# Test Email
python -c "from libs import email_client; email_client.send_email(['<EMAIL>'], 'Test', 'Test body')"
```

## Output

**Excel Report:**
- Filename: `MRI_Lease_Exceptions-Current_Past_Exp_Date.xlsx`
- Contains lease details with issue types, dates, and property manager info

**Email:**
- Subject: "MRI Lease Exceptions-Current But Past Expiration Date"
- HTML format with summary table (≤20 records) or record count
- Excel attachment with complete data

