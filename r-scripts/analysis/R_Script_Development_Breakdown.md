# High-Level Development Breakdown: LEGACY_MRI_Exceptions-Lease_C_past_inactive_dates.R

## 📋 **Script Overview**
**Purpose**: Identifies MRI lease records marked as "Current" but have passed critical dates (stop billing, vacate, or expiration dates), then emails an Excel report to property management.

**Version**: 20241106 (Latest update converted to Snowflake and gmailr OAuth)

---

## 🏗️ **Architecture & Flow**

### **1. Initialization & Configuration**
- **Environment Setup**: Sets timezone to America/Chicago
- **Database Environment**: Configurable STAGE/PROD Snowflake environments
- **Path Management**: Dynamic path selection based on computer name (test vs production)
- **Email Configuration**: Multiple recipient lists (normal, warning, test)

### **2. Database Connection**
- **Snowflake Integration**: Uses ODBC connection with role-based access
- **Credential Management**: Leverages `keyring` package for secure credential storage
- **Environment-Aware**: Separate configurations for STAGE and PROD environments

### **3. Core Business Logic**
- **Query Execution**: Complex SQL query to identify lease exceptions
- **Data Processing**: Cleans and formats query results
- **Exception Detection**: Identifies leases with past critical dates

### **4. Report Generation**
- **Excel Creation**: Generates formatted Excel files with styling
- **Dynamic Formatting**: Adjusts column widths based on content
- **Professional Styling**: Headers, filters, freeze panes

### **5. Email Distribution**
- **HTML Email**: Rich formatting with embedded tables
- **Attachment Handling**: Excel file attachments
- **Test Mode**: Configurable test email functionality

---

## 📦 **R Package Dependencies**

### **Core Data Processing**
- `dplyr` - Data manipulation and transformation
- `data.table` - High-performance data operations
- `lubridate` - Date/time handling
- `stringr` - String manipulation
- `readr` - Data reading utilities

### **Database Connectivity**
- `DBI` - Database interface abstraction
- `odbc` - ODBC database connections
- `keyring` - Secure credential storage

### **Excel & Reporting**
- `openxlsx` - Excel file creation and formatting
- `xtable` - HTML table generation for emails
- `formattable` - Data formatting utilities

### **Email & Communication**
- `gmailr` - Gmail API integration (OAuth)
- `purrr` - Functional programming for attachments

### **Legacy/Utility**
- `rJava` - Java integration (legacy)
- `RDCOMClient` - COM client (legacy Windows integration)
- `reshape2` - Data reshaping
- `utils` - Base utilities

---

## 🔧 **External Resources & Dependencies**

### **Database Resources**
- **Snowflake Data Warehouse**
  - PROD_CSM_DB / STAGE_CSM_DB databases
  - CORPORATE schema
  - PROD_DATA_ANA_WH / STAGE_DATA_ANA_WH warehouses
  - Role-based access: AR_PROD_CONSUMPTION_RW / AR_STAGE_CONSUMPTION_RW

### **Data Sources**
- **MRI Tables**:
  - `MRI.LEAS` - Lease information (primary table)
  - `MRI.MOCCP` - Occupancy details
  - `MRI.BLDG` - Building information
  - `MRI.MNGR` - Property manager details

### **File System Resources**
- **Network Paths**:
  - `//*************/public/steveo/R Stuff/ReportFiles` (shared drive)
  - `C:/Users/<USER>/Documents/ReportFiles` (Tableau PC local)

### **Authentication Systems**
- **Keyring Credentials**:
  - `SfHV` service with tableau_ID_prod/stage
  - `SfHV` service with tableau_PW_prod/stage
- **Gmail OAuth**: <EMAIL>

### **Email Infrastructure**
- **Gmail API**: OAuth-based authentication
- **Reply-to Configuration**: <EMAIL>
- **Recipients**:
  - Normal: <EMAIL>
  - Warnings: <EMAIL>
  - Testing: <EMAIL>

---

## 🖥️ **Environment Dependencies**

### **Computer-Specific Configurations**
- **Test Computers**: STEVEO-PLEX7010, LAPTOPTOSHIBA13, STEVEANDJENYOGA
- **Production Computer**: DESKTOP-TABLEAU
- **Path Switching**: Automatic based on COMPUTERNAME environment variable

### **ODBC Configuration**
- **DSN**: "Snowflake HV" - Must be configured in Windows ODBC settings
- **Driver**: Snowflake ODBC driver required

---

## 🔍 **Key Business Rules**

### **Lease Exception Criteria**
Identifies leases where:
- Lease status = 'C' (Current)
- AND lease status NOT IN ('P', 'I') (Not Pending or Inactive)
- AND one or more critical dates have passed:
  - Stop Billing Date < Current Date
  - Vacate Date < Current Date  
  - Expire Date < Current Date

### **Complex Logic**
- Handles NULL date scenarios with COALESCE
- Considers multiple date precedence rules
- Filters for active leases that should be updated

---

## ⚠️ **Potential Issues & Considerations**

### **Security Concerns**
- Credentials stored in Windows Keyring (single point of failure)
- Network path dependencies (//*************)
- Computer name-based environment detection

### **Scalability Limitations**
- Hardcoded file paths
- Manual ODBC DSN configuration required
- Gmail API rate limits for email sending

### **Maintenance Requirements**
- Regular OAuth token refresh for Gmail
- Snowflake credential rotation
- Network path availability monitoring

---

## 🔄 **Conversion Considerations for Python**

### **Replaced Components**
- `gmailr` → `EmailClient` (Microsoft-compatible)
- `openxlsx` → `openpyxl` 
- `DBI/odbc` → `SnowflakeHelper`
- `keyring` → Environment variables or Azure Key Vault
- Network paths → SharePoint integration

### **Enhanced Features in Python Version**
- Class-based architecture for better organization
- Comprehensive error handling and logging
- Configurable testing modes
- Modern email infrastructure compatibility
