# 🧪 TEST Script for Learning MRI Lease Exceptions

## 📋 **What This Is**

This is a **SAFE TESTING VERSION** of the MRI Lease Exceptions script that you can use to:
- 🎓 **Learn** how the script works
- 🧪 **Experiment** with different settings
- 🔧 **Test** functionality without affecting production
- 📊 **See** real Excel output with mock data

## 📁 **Files**

- **`TEST_MRI_exceptions_lease_dates_LEARNING.py`** - The test script
- **`LEGACY_MRI_exceptions_least_inactive_dates.py`** - The production script
- **`TEST_SCRIPT_README.md`** - This guide

---

## 🚀 **Quick Start**

### **1. Install Requirements**
```bash
pip install pandas xlsxwriter
```

### **2. Run the Test Script**
```bash
python TEST_MRI_exceptions_lease_dates_LEARNING.py
```

### **3. Check the Results**
- Look for the Excel file in `test_reports/TEST_MRI_Exceptions-Leases/`
- Open the Excel file to see the formatted results!

---

## 🔧 **Configuration Options**

Edit these settings in the `test_main()` function to experiment:

```python
# 🔧 CONFIGURATION - MODIFY THESE TO EXPERIMENT!
testing_emails = True      # Keep True for safety
enable_sharepoint = False  # Set to True to test SharePoint (if available)
use_mock_data = True      # Set to False to test with real database (if available)
```

### **Settings Explained**

| Setting | What It Does | Recommended for Learning |
|---------|-------------|-------------------------|
| `testing_emails = True` | Prevents real emails from being sent | ✅ Keep True |
| `enable_sharepoint = False` | Disables SharePoint upload | ✅ Keep False initially |
| `use_mock_data = True` | Uses fake data instead of database | ✅ Keep True initially |

---

## 🎭 **Mock Data Mode (Recommended for Learning)**

When `use_mock_data = True`, the script creates realistic fake data:
- 5 sample lease exception records
- Different types of issues (Stop Bill Date Past, Vacate Date Past, etc.)
- Realistic building IDs, tenant names, and dates
- Perfect for learning without needing database access

### **Sample Mock Data**
```
BLDGID  SUITID  TENANT NAME    ISSUE
BLDG001 101     ABC Corp       Stop Bill Date Past; Expire Date Past
BLDG002 205     XYZ LLC        Vacate Date Past
BLDG001 102     Test Company   Stop Bill Date Past
```

---

## 📊 **What the Script Does**

### **Step-by-Step Process**
1. **🔌 Initialize** - Sets up connections (or mock mode)
2. **🔍 Get Data** - Retrieves lease exceptions (real or mock)
3. **📊 Create Excel** - Generates professionally formatted Excel file
4. **☁️ SharePoint** - Optionally uploads to SharePoint
5. **📧 Email** - Would send email (disabled in test mode)
6. **📝 Log** - Records all activities

### **Excel Output Features**
- ✅ **Professional formatting** with gray headers
- ✅ **Auto-sized columns** for readability
- ✅ **Frozen header row** stays visible when scrolling
- ✅ **Auto-filter** allows filtering by clicking headers
- ✅ **Multiple sheets** if needed

---

## 🎓 **Learning Exercises**

### **Beginner**
1. **Run with defaults** - See how mock data works
2. **Open the Excel file** - Examine the formatting
3. **Change mock data** - Edit `create_mock_data()` method
4. **Modify email recipients** - Change the test email addresses

### **Intermediate**
1. **Add new columns** - Modify the mock data structure
2. **Change Excel formatting** - Adjust colors, fonts, column widths
3. **Add data validation** - Create checks for data quality
4. **Experiment with dates** - Change which dates are "past due"

### **Advanced**
1. **Enable SharePoint** - Test with real SharePoint (if available)
2. **Connect to database** - Set `use_mock_data = False` (if database available)
3. **Add new features** - Create additional report types
4. **Modify the query** - Adjust the SQL logic

---

## 🔍 **Understanding the Code**

### **Key Classes and Methods**

| Component | Purpose | Learning Focus |
|-----------|---------|----------------|
| `TestMRILeaseExceptionsPastInactiveDates` | Main class | Object-oriented design |
| `create_mock_data()` | Generates test data | Data structure creation |
| `create_excel_report()` | Makes Excel files | File generation and formatting |
| `execute_lease_exceptions_query()` | Gets data | Database interaction concepts |
| `run_test_report()` | Orchestrates everything | Process flow and error handling |

### **Important Concepts**
- **🏗️ Class-based design** - Everything organized in a class
- **📝 Logging** - Every action is logged for debugging
- **🛡️ Error handling** - Graceful failure with helpful messages
- **🔧 Configuration** - Easy to modify behavior
- **🎭 Mock data** - Testing without external dependencies

---

## 🛡️ **Safety Features**

### **Built-in Protections**
- ✅ **No database writes** - `LOG_TO_DB = False`
- ✅ **Test email recipients only** - Won't spam production users
- ✅ **Separate test directories** - Won't overwrite production files
- ✅ **Mock data by default** - No accidental database queries
- ✅ **Clear test labeling** - All outputs marked as "TEST"

### **What's Safe to Modify**
- ✅ Mock data content and structure
- ✅ Excel formatting and styling
- ✅ File names and paths
- ✅ Email recipients (test addresses only)
- ✅ Configuration settings

### **What to Be Careful With**
- ⚠️ Database connection settings
- ⚠️ SharePoint URLs and credentials
- ⚠️ Production email addresses
- ⚠️ File paths outside test directories

---

## 🔧 **Troubleshooting**

### **Common Issues**

| Problem | Solution |
|---------|----------|
| "pandas not found" | Run `pip install pandas xlsxwriter` |
| "No Excel file created" | Check the `test_reports/` directory |
| "Import errors" | Set `use_mock_data = True` and ignore lib imports |
| "Permission denied" | Make sure you have write access to the directory |

### **Getting Help**
1. **Check the console output** - Detailed logging shows what's happening
2. **Look for error messages** - They usually explain the problem
3. **Try mock data mode** - Eliminates external dependencies
4. **Check file permissions** - Make sure you can write to the directory

---

## 🎯 **Next Steps**

### **After Learning with Test Script**
1. **Understand the production script** - Compare with the real version
2. **Test with real data** - When ready, try `use_mock_data = False`
3. **Configure for production** - Set up real email and SharePoint
4. **Schedule execution** - Set up automated runs

### **Production Deployment**
- Use `LEGACY_MRI_exceptions_least_inactive_dates.py` for production
- Configure real email recipients
- Set up SharePoint integration
- Enable database logging
- Schedule regular execution

---

## 🎉 **Have Fun Learning!**

This test script is designed to be a safe, educational environment where you can:
- **Experiment freely** without breaking anything
- **Learn by doing** with real code and real outputs
- **Build confidence** before working with production systems
- **Understand the process** step by step

**Happy learning! 🚀**
