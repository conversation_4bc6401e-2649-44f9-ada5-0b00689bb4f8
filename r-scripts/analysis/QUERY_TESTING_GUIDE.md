# 🔍 Snowflake Query Testing Guide

## 🎯 **Purpose**

This guide shows you how to test and debug the Snowflake query in the MRI Lease Exceptions script. Perfect for:
- 🔍 **Seeing the actual SQL query**
- 📊 **Testing query results**
- 🐛 **Debugging query issues**
- 📝 **Analyzing data patterns**

---

## 🚀 **Quick Start - Query Testing**

### **1. Test Query with Mock Data (Safe)**
```python
# In test_main() function, set:
use_mock_data = True      # Use fake data
test_query = False        # Don't run real query
save_query_log = True     # Save query to log file
```
**Result**: Shows you the SQL query and logs it with mock data

### **2. Test Actual Snowflake Query**
```python
# In test_main() function, set:
use_mock_data = False     # Use real database
test_query = True         # Run actual query
save_query_log = True     # Save detailed results
```
**Result**: Runs the real query against Snow<PERSON>lake and logs everything

---

## 📝 **What Gets Logged**

### **Console Output**
- 🔍 **Full SQL query** displayed
- 📊 **Row count** and column information
- 🏢 **Building analysis** (which buildings have issues)
- 👤 **Property manager breakdown**
- 🚨 **Issue type analysis** (Stop Bill, Vacate, Expire dates)
- 📋 **Sample data** (first 3 rows)

### **Log File Output**
- 📁 **Location**: `test_reports/TEST_MRI_Exceptions-Leases/query_logs/`
- 📝 **Filename**: `snowflake_query_test_[date]_[time].log`
- 📊 **Contents**: 
  - Complete SQL query
  - Full result set
  - Data summary statistics
  - Column information
  - Error details (if any)

---

## 🔧 **Configuration Options**

### **Query Testing Modes**

| Mode | use_mock_data | test_query | What Happens |
|------|---------------|------------|--------------|
| **Safe Learning** | `True` | `False` | Shows query + uses mock data |
| **Query Preview** | `True` | `True` | Shows query + uses mock data |
| **Real Test** | `False` | `True` | Runs actual Snowflake query |
| **Production** | `False` | `False` | Normal production mode |

### **Recommended Testing Sequence**
1. **Start Safe**: `use_mock_data=True, test_query=False`
2. **See Query**: Check the log file for the SQL
3. **Test Real**: `use_mock_data=False, test_query=True`
4. **Analyze Results**: Review the detailed output

---

## 📊 **Understanding the Query**

### **What the Query Does**
The SQL query finds MRI lease records where:
- ✅ **Lease Status = 'C'** (Current)
- ❌ **BUT** one or more critical dates have passed:
  - Stop Billing Date < Today
  - Vacate Date < Today  
  - Expire Date < Today

### **Key SQL Components**

```sql
-- Creates a list of issues for each lease
ARRAY_TO_STRING(ARRAY_CONSTRUCT_COMPACT(
    CASE WHEN LEAS.STOPBILLDATE < CURRENT_DATE THEN 'Stop Bill Date Past' END,
    CASE WHEN LEAS.VACATE < CURRENT_DATE THEN 'Vacate Date Past' END,
    CASE WHEN LEAS.EXPIR < CURRENT_DATE THEN 'Expire Date Past' END
),'; ') AS ISSUE
```

```sql
-- Main filtering logic
WHERE LEAS.OCCPSTAT = 'C'  -- Current leases only
 AND LEAS.OCCPSTAT NOT IN ('P', 'I')  -- Not Pending or Inactive
 AND (
    -- Complex date logic to find past-due leases
    COALESCE(LEAS.VACATE,LEAS.EXPIR) < CURRENT_DATE
 )
```

### **Tables Joined**
- **MRI.LEAS** - Main lease information
- **MRI.MOCCP** - Occupancy details  
- **MRI.BLDG** - Building information
- **MRI.MNGR** - Property manager details

---

## 🔍 **Sample Output Analysis**

### **Console Output Example**
```
🔍 SNOWFLAKE QUERY TEST RESULTS
=====================================
⏰ Timestamp: 2025-01-02 14:30:15
🎯 Query Purpose: Find MRI lease exceptions

📊 QUERY SUCCESS:
   📊 Rows returned: 23

🔍 DATA ANALYSIS:
   📊 Total records: 23
   🚨 Issue breakdown:
      - Stop Bill Date Past: 15 records
      - Vacate Date Past: 8 records
      - Expire Date Past: 12 records
   🏢 Top buildings with issues:
      - BLDG001: 5 records
      - BLDG002: 3 records
   👤 Property managers affected:
      - John Smith: 8 records
      - Jane Doe: 6 records
```

### **What This Tells You**
- **23 leases** need attention
- **Stop Billing** is the most common issue
- **BLDG001** has the most problems
- **John Smith** manages the most problematic leases

---

## 🐛 **Troubleshooting**

### **Common Issues**

| Problem | Likely Cause | Solution |
|---------|-------------|----------|
| "No database connection" | SnowflakeHelper not configured | Check environment variables |
| "Query timeout" | Query too complex/large dataset | Add LIMIT clause for testing |
| "Permission denied" | Database access issues | Check user permissions |
| "No results found" | Query filters too restrictive | Good news - no exceptions! |

### **Environment Variables Needed**
```bash
DATABASE_RAW_DATABASE=PROD_CSM_DB
DATABASE_ENVM=PROD
DATABASE_CSM_DATABASE=PROD_CSM_DB
```

### **Testing Steps**
1. **Check Connection**: Script tests connection first
2. **Run Simple Query**: Tests with basic SELECT
3. **Execute Main Query**: Runs the lease exceptions query
4. **Log Everything**: Saves detailed results

---

## 📁 **File Locations**

### **Query Log Files**
- **Directory**: `test_reports/TEST_MRI_Exceptions-Leases/query_logs/`
- **Format**: `snowflake_query_test_[DD-MMM-YY]_[HHMMSS].log`
- **Example**: `snowflake_query_test_02-Jan-25_143015CST.log`

### **Excel Output**
- **Directory**: `test_reports/TEST_MRI_Exceptions-Leases/`
- **Format**: `TEST_MRI_Lease_Exceptions-[DD-MMM-YY].xlsx`

---

## 🎓 **Learning Exercises**

### **Beginner**
1. **Run with mock data** - See the query structure
2. **Read the log file** - Understand the SQL
3. **Modify date filters** - Change which dates are considered "past"

### **Intermediate**  
1. **Test with real data** - Run actual Snowflake query
2. **Analyze results** - Look for patterns in the data
3. **Add LIMIT clause** - Test with smaller result sets

### **Advanced**
1. **Modify the query** - Add new filters or columns
2. **Performance testing** - Measure query execution time
3. **Data validation** - Verify results make business sense

---

## 🚨 **Safety Notes**

### **Safe Practices**
- ✅ **Start with mock data** - Learn the structure first
- ✅ **Use test mode** - Keep `testing_emails=True`
- ✅ **Check permissions** - Ensure you have read access
- ✅ **Review queries** - Understand what you're running

### **What's Safe**
- ✅ **SELECT queries** - Read-only operations
- ✅ **Query logging** - Saves to local files
- ✅ **Mock data mode** - No database impact
- ✅ **Test directories** - Separate from production

### **Be Careful With**
- ⚠️ **Large result sets** - May take time/resources
- ⚠️ **Production data** - Sensitive information
- ⚠️ **Network usage** - Large queries use bandwidth
- ⚠️ **Database load** - Don't run too frequently

---

## 🎯 **Next Steps**

### **After Testing Query**
1. **Understand the results** - What do the exceptions mean?
2. **Validate the logic** - Do the filters make sense?
3. **Check data quality** - Are the dates accurate?
4. **Plan improvements** - How could the query be enhanced?

### **Production Readiness**
1. **Query works correctly** ✅
2. **Results make business sense** ✅  
3. **Performance is acceptable** ✅
4. **Error handling works** ✅

**Happy query testing! 🔍**
