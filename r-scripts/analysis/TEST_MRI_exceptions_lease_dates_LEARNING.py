"""
🧪 TEST VERSION - MRI Lease Exceptions Past Inactive Dates
Original R-Script: LEGACY_MRI_Exceptions-Lease_C_past_inactive_dates.R

🎯 PURPOSE: Learning and testing version of the MRI lease exceptions script
📚 SAFE TO EXPERIMENT: This is a copy for learning - modify freely!

Identifies MRI lease records that are marked as "Current" but have passed critical dates 
(stop billing, vacate, or expiration dates), then emails an Excel report to property management.

🔧 MODIFICATIONS FOR TESTING:
- Safe test mode enabled by default
- Mock data option for testing without database
- Detailed logging for learning
- Comments explaining each step
- Test email recipients only

- <PERSON> - 6/26/2025 (Test Version)
"""

import os
import pandas as pd
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

# 🔧 TESTING: Comment out these imports if you want to test without the libs
try:
    from libs.snowflake_helper import SnowflakeHelper
    from libs.email_client import EmailClient
    from libs.sharepoint_helper import SharePointClient
    from libs.excel_helper import SharePointExcelOnline
    LIBS_AVAILABLE = True
    print("✅ All libs imported successfully")
except ImportError as e:
    print(f"⚠️  Some libs not available: {e}")
    print("🧪 Running in MOCK MODE for testing")
    LIBS_AVAILABLE = False


@dataclass
class ReportResult:
    """Data class to hold report generation results"""
    success: bool
    row_count: int
    file_path: Optional[str] = None
    error_message: Optional[str] = None


class TestMRILeaseExceptionsPastInactiveDates:
    """
    🧪 TEST VERSION: Class to handle MRI lease exceptions for current leases past inactive dates.
    
    This is a SAFE TESTING VERSION - experiment freely!
    
    Key Features for Learning:
    - Mock data mode for testing without database
    - Detailed step-by-step logging
    - Safe test email recipients
    - Comments explaining each operation
    """
    
    # 📋 PROCESS CONFIGURATION
    PROCESS_NAME = '🧪 TEST: MRI Lease Exceptions-Current But Past Expiration Date'
    PROCESS_TYPE = 'Exception Report (TEST)'
    LOG_TO_DB = False  # 🔒 SAFE: Disabled for testing
    
    R_SCRIPT_NAME = 'LEGACY_MRI_Exceptions-Lease_C_past_inactive_dates.R'
    
    # 🗄️ DATABASE CONFIGURATION (for when you're ready to test with real data)
    LOG_SCHEMA = 'BATCH_AUDIT'
    LOG_TABLE = 'MOMS_EXECUTION_LOGS'
    
    # 📧 EMAIL CONFIGURATION - SAFE TEST RECIPIENTS ONLY
    NORM_RECIPIENTS = ['<EMAIL>']  # 🔧 CHANGE THIS TO YOUR EMAIL
    WARN_RECIPIENTS = ['<EMAIL>']  # 🔧 CHANGE THIS TO YOUR EMAIL
    TEST_RECIPIENTS = ['<EMAIL>']  # 🔧 CHANGE THIS TO YOUR EMAIL
    TEST_CC_RECIPIENTS = ['<EMAIL>']  # 🔧 CHANGE THIS TO YOUR EMAIL
    
    # 📁 REPORT CONFIGURATION
    REPORT_FOLDER = 'TEST_MRI_Exceptions-Leases'
    
    # ☁️ SHAREPOINT CONFIGURATION (optional)
    SHAREPOINT_ENABLED = False  # 🔒 SAFE: Disabled for testing
    SHAREPOINT_SITE_URL = 'https://highlandventuresltd442.sharepoint.com/sites/dev'
    SHAREPOINT_REPORTS_FOLDER = 'Shared Documents/MRI-Exceptions-Report/TEST'
    
    def __init__(self, testing_emails: bool = True, enable_sharepoint: bool = False, use_mock_data: bool = True,
                 test_query: bool = False, save_query_log: bool = True):
        """
        🧪 Initialize the TEST MRI lease exceptions processor.

        Args:
            testing_emails: Whether to send test emails only (DEFAULT: True for safety)
            enable_sharepoint: Whether to enable SharePoint integration (DEFAULT: False for safety)
            use_mock_data: Whether to use mock data instead of real database (DEFAULT: True for safety)
            test_query: Whether to test the actual Snowflake query (DEFAULT: False for safety)
            save_query_log: Whether to save query results to log file (DEFAULT: True)
        """
        print("🧪 Initializing TEST version of MRI Lease Exceptions processor...")
        print(f"📧 Testing emails: {testing_emails}")
        print(f"☁️  SharePoint enabled: {enable_sharepoint}")
        print(f"🎭 Mock data mode: {use_mock_data}")
        print(f"🔍 Test query mode: {test_query}")
        print(f"📝 Save query log: {save_query_log}")

        # 🔧 CONFIGURATION
        self.testing_emails = testing_emails
        self.enable_sharepoint = enable_sharepoint
        self.use_mock_data = use_mock_data
        self.test_query = test_query
        self.save_query_log = save_query_log
        self.log_buffer = []
        
        # 🗄️ DATABASE SETUP (only if libs available and not using mock data)
        self.sf = None
        self.cursor = None
        self.conn = None
        
        if LIBS_AVAILABLE and not use_mock_data:
            try:
                print("🔌 Connecting to Snowflake...")
                self.sf = SnowflakeHelper()
                self.cursor = self.sf.cs  # Snowflake cursor for direct SQL operations
                self.conn = self.sf.conn  # Snowflake connection object
                print("✅ Snowflake connection established")
            except Exception as e:
                print(f"⚠️  Snowflake connection failed: {e}")
                print("🎭 Falling back to mock data mode")
                self.use_mock_data = True
        else:
            print("🎭 Using mock data mode (no database connection)")
        
        # ☁️ SHAREPOINT SETUP (only if enabled and libs available)
        self.sp = None
        self.ex = None
        if self.enable_sharepoint and LIBS_AVAILABLE:
            try:
                print("☁️  Initializing SharePoint connection...")
                self.sp = SharePointClient(suppress_false_errors=True)
                self.sp.authenticate()
                self.ex = SharePointExcelOnline(sharepoint_client=self.sp)
                print("✅ SharePoint connection established")
            except Exception as e:
                print(f"⚠️  SharePoint initialization failed: {e}")
                print("📁 Continuing with local file storage only")
                self.enable_sharepoint = False
        
        # 🕐 TIMEZONE AND DATE SETUP
        os.environ['TZ'] = 'America/Chicago'
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.report_time = datetime.now().strftime("%H%M%S%Z")
        
        print(f"📅 Report date: {self.query_date}")
        print(f"🕐 Report time: {self.report_time}")
        
        # 📝 REPORT CONTENT SETUP
        self.report_criteria = (
            "<p><b>🧪 TEST REPORT - Criteria for exceptions,</b> lease status = 'C' but one or more apply:"
            "<ul>"
            "<li>Past Stop Billing Date</li>"
            "<li>Past Vacate Date</li>"
            "<li>Past Expire Date</li>"
            "</ul></p>"
        )
        
        # ✍️ EMAIL SIGNATURE
        self.norm_sig = (
            "<b><span style='font-weight:bold'>🧪 TEST SYSTEM</span></b><br/>"
            "Automated Testing<br/>"
            "<b><span style='font-weight:bold'>MRI Lease Exceptions Script</span></b><br/>"
            "Learning and Development<br/>"
            "This is a test email from the learning version<br/>"
        )
        
        # 📁 SETUP REPORT PATH
        self.setup_report_path()
        
        # 📝 LOG INITIALIZATION
        self.log_audit_in_db(
            log_msg=f"🧪 Beginning TEST '{self.PROCESS_NAME}' routine",
            print_msg=True
        )
        
        if self.sf:
            self.log_audit_in_db(
                log_msg=f"✅ SnowflakeHelper initialized. Connection: {type(self.conn).__name__}",
                print_msg=True
            )
        else:
            self.log_audit_in_db(
                log_msg="🎭 Running in mock data mode - no database connection",
                print_msg=True
            )
    
    def setup_report_path(self):
        """📁 Set up the report output path for test files"""
        print("📁 Setting up report directory...")
        
        # Create a test-specific directory
        base_path = os.path.join(os.getcwd(), 'test_reports')
        self.report_path = os.path.join(base_path, self.REPORT_FOLDER)
        
        # Create directory if it doesn't exist
        os.makedirs(self.report_path, exist_ok=True)
        
        print(f"📁 Report path: {self.report_path}")

        # 📝 Setup query log file if enabled
        if self.save_query_log:
            self.query_log_path = os.path.join(self.report_path, 'query_logs')
            os.makedirs(self.query_log_path, exist_ok=True)
            self.query_log_file = os.path.join(self.query_log_path, f'snowflake_query_test_{self.query_date}_{self.report_time}.log')
            print(f"📝 Query log file: {self.query_log_file}")
    
    def log_audit_in_db(self, log_msg: str, log_type: str = 'Info', print_msg: bool = False, 
                       print_data_list: bool = True, start_upload: bool = False,
                       process_type: Optional[str] = None, script_file_name: Optional[str] = None) -> bool:
        """
        📝 Store log messages in a buffer (TEST VERSION - no database upload)
        
        Args:
            log_msg: Message to log
            log_type: Type of log (Info, Warning, Error, etc.)
            print_msg: Whether to print the log message
            print_data_list: Whether to print the data list
            start_upload: Whether to upload the collected logs (DISABLED in test mode)
            process_type: Process type override
            script_file_name: Script name override
        
        Returns:
            Success status of the operation
        """
        try:            
            # 🖨️ Print log message if requested
            if print_msg:
                timestamp = datetime.now().strftime('%H:%M:%S')
                print(f"[{timestamp}] {self.PROCESS_NAME} - {log_type}: {log_msg}")
               
            # 📝 Create the log record and add to buffer
            uuid_str = str(uuid.uuid4())
            script_name = script_file_name or self.PROCESS_NAME
            process_type = process_type or self.PROCESS_TYPE
            rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            record = [uuid_str, script_name, log_type, process_type, log_msg, rec_ins_date]
            self.log_buffer.append(record)
 
            if print_data_list:
                print(f"📝 Added to log buffer. Current size: {len(self.log_buffer)}")
 
            # 🔒 SAFE: No database upload in test mode
            if not self.LOG_TO_DB:
                return True
           
            # 🗄️ Upload logs if requested and database available
            if start_upload and self.log_buffer and self.sf:
                columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE', 'PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']
               
                try:
                    self.sf.bulk_insert(
                        columns_list=columns_list,
                        data_list=self.log_buffer,
                        database=os.environ.get('DATABASE_RAW_DATABASE', 'PROD_CSM_DB'),
                        schema=self.LOG_SCHEMA,
                        table=self.LOG_TABLE
                    )
                    print(f"📤 Uploaded {len(self.log_buffer)} log entries in bulk")
                   
                    # Clear the buffer after successful upload
                    self.log_buffer = []
                    return True
                   
                except Exception as e:
                    print(f"❌ Error uploading logs for {self.PROCESS_NAME}: {e}")
                    return False
                   
            return True
           
        except Exception as e:
            print(f"❌ Error in log_audit_in_db for {self.PROCESS_NAME}: {e}")
            return False

    def log_query_details(self, query: str, results: Optional[pd.DataFrame] = None, error: Optional[str] = None):
        """
        📝 Log detailed query information to file and console for debugging.

        Args:
            query: The SQL query that was executed
            results: DataFrame with query results (if successful)
            error: Error message (if query failed)
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 🖨️ Print to console
        print("\n" + "="*80)
        print("🔍 SNOWFLAKE QUERY TEST RESULTS")
        print("="*80)
        print(f"⏰ Timestamp: {timestamp}")
        print(f"🎯 Query Purpose: Find MRI lease exceptions (Current status but past critical dates)")

        print("\n📋 SQL QUERY:")
        print("-" * 40)
        print(query)
        print("-" * 40)

        if error:
            print(f"\n❌ QUERY ERROR:")
            print(f"   {error}")
        elif results is not None:
            print(f"\n✅ QUERY SUCCESS:")
            print(f"   📊 Rows returned: {len(results)}")

            if len(results) > 0:
                print(f"\n📊 COLUMN INFORMATION:")
                for i, col in enumerate(results.columns):
                    print(f"   {i+1:2d}. {col}")

                print(f"\n📋 SAMPLE DATA (first 3 rows):")
                print(results.head(3).to_string(index=False))

                print(f"\n🔍 DATA ANALYSIS:")
                print(f"   📊 Total records: {len(results)}")

                # Analyze issue types
                if 'ISSUE' in results.columns:
                    issue_counts = {}
                    for issue in results['ISSUE']:
                        if pd.notna(issue):
                            for issue_type in str(issue).split('; '):
                                issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1

                    print(f"   🚨 Issue breakdown:")
                    for issue_type, count in issue_counts.items():
                        print(f"      - {issue_type}: {count} records")

                # Analyze by building
                if 'BLDGID' in results.columns:
                    building_counts = results['BLDGID'].value_counts()
                    print(f"   🏢 Top buildings with issues:")
                    for building, count in building_counts.head(5).items():
                        print(f"      - {building}: {count} records")

                # Analyze by property manager
                if 'PROP MGR' in results.columns:
                    mgr_counts = results['PROP MGR'].value_counts()
                    print(f"   👤 Property managers affected:")
                    for mgr, count in mgr_counts.head(5).items():
                        print(f"      - {mgr}: {count} records")
            else:
                print("   ℹ️  No records found (this might be good news - no exceptions!)")

        print("="*80)

        # 📝 Save to log file if enabled
        if self.save_query_log:
            try:
                with open(self.query_log_file, 'w', encoding='utf-8') as f:
                    f.write(f"SNOWFLAKE QUERY TEST LOG\n")
                    f.write(f"========================\n")
                    f.write(f"Timestamp: {timestamp}\n")
                    f.write(f"Process: {self.PROCESS_NAME}\n")
                    f.write(f"Query Purpose: Find MRI lease exceptions\n\n")

                    f.write(f"SQL QUERY:\n")
                    f.write(f"{'-'*40}\n")
                    f.write(f"{query}\n")
                    f.write(f"{'-'*40}\n\n")

                    if error:
                        f.write(f"QUERY ERROR:\n")
                        f.write(f"{error}\n\n")
                    elif results is not None:
                        f.write(f"QUERY RESULTS:\n")
                        f.write(f"Rows returned: {len(results)}\n\n")

                        if len(results) > 0:
                            f.write(f"COLUMNS:\n")
                            for i, col in enumerate(results.columns):
                                f.write(f"{i+1:2d}. {col}\n")
                            f.write(f"\n")

                            f.write(f"FULL DATA:\n")
                            f.write(results.to_string(index=False))
                            f.write(f"\n\n")

                            f.write(f"DATA SUMMARY:\n")
                            f.write(results.describe(include='all').to_string())
                            f.write(f"\n\n")
                        else:
                            f.write("No records found.\n\n")

                print(f"📝 Detailed log saved to: {self.query_log_file}")

            except Exception as e:
                print(f"⚠️  Could not save query log: {e}")

    def test_snowflake_connection(self) -> bool:
        """
        🔌 Test the Snowflake connection and basic functionality.

        Returns:
            True if connection works, False otherwise
        """
        if not self.sf:
            print("❌ No Snowflake connection available")
            return False

        try:
            print("🔌 Testing Snowflake connection...")

            # Test with a simple query first
            test_query = "SELECT CURRENT_DATE() as TODAY, CURRENT_USER() as USER, CURRENT_WAREHOUSE() as WAREHOUSE"

            print("🧪 Running connection test query...")
            test_results = self.sf.execute_snowflake_query(
                query=test_query,
                print_query=True,
                pull_only_one_record=False
            )

            if test_results:
                df = pd.DataFrame(test_results)
                print("✅ Snowflake connection test successful!")
                print("📊 Connection details:")
                print(df.to_string(index=False))
                return True
            else:
                print("❌ Snowflake connection test failed - no results")
                return False

        except Exception as e:
            print(f"❌ Snowflake connection test failed: {e}")
            return False

    def create_mock_data(self) -> pd.DataFrame:
        """
        🎭 Create mock data for testing without database connection.

        This simulates what the real query would return, perfect for learning!

        Returns:
            DataFrame with mock lease exception data
        """
        print("🎭 Creating mock data for testing...")

        # Create sample data that looks like real MRI lease exceptions
        mock_data = {
            'ISSUE': [
                'Stop Bill Date Past; Expire Date Past',
                'Vacate Date Past',
                'Stop Bill Date Past',
                'Expire Date Past; Vacate Date Past',
                'Stop Bill Date Past; Vacate Date Past; Expire Date Past'
            ],
            'BLDGID': ['BLDG001', 'BLDG002', 'BLDG001', 'BLDG003', 'BLDG002'],
            'SUITID': ['101', '205', '102', '301', '206'],
            'PROP MGR': ['John Smith', 'Jane Doe', 'John Smith', 'Bob Johnson', 'Jane Doe'],
            'LEASID': ['L001', 'L002', 'L003', 'L004', 'L005'],
            'OCCUPANCY STATUS': ['C', 'C', 'C', 'C', 'C'],
            'TENANT NAME': ['ABC Corp', 'XYZ LLC', 'Test Company', 'Sample Business', 'Demo Tenant'],
            'GEN CODE': ['RET', 'OFF', 'RET', 'SVC', 'OFF'],
            'RENT START': [
                datetime(2023, 1, 1),
                datetime(2023, 3, 15),
                datetime(2023, 2, 1),
                datetime(2023, 4, 1),
                datetime(2023, 1, 15)
            ],
            'OCCUPANCY DATE': [
                datetime(2023, 1, 1),
                datetime(2023, 3, 15),
                datetime(2023, 2, 1),
                datetime(2023, 4, 1),
                datetime(2023, 1, 15)
            ],
            'STOP BILL DATE': [
                datetime(2024, 12, 31),  # Past date
                datetime(2025, 6, 30),   # Future date
                datetime(2024, 11, 30),  # Past date
                datetime(2025, 8, 31),   # Future date
                datetime(2024, 10, 31)   # Past date
            ],
            'VACATE DATE': [
                datetime(2024, 12, 31),  # Past date
                datetime(2024, 12, 15),  # Past date
                datetime(2025, 6, 30),   # Future date
                datetime(2024, 11, 30),  # Past date
                datetime(2024, 12, 1)    # Past date
            ],
            'EXPIRE DATE': [
                datetime(2024, 12, 31),  # Past date
                datetime(2025, 6, 30),   # Future date
                datetime(2025, 5, 31),   # Future date
                datetime(2024, 11, 30),  # Past date
                datetime(2024, 11, 30)   # Past date
            ],
            'TENANT CATEGORY': ['Retail', 'Office', 'Retail', 'Service', 'Office']
        }

        df = pd.DataFrame(mock_data)

        print(f"🎭 Created {len(df)} mock lease exception records")
        print("📊 Sample data preview:")
        print(df[['BLDGID', 'SUITID', 'TENANT NAME', 'ISSUE']].head())

        return df

    def get_lease_exceptions_query(self) -> str:
        """
        📋 Get the SQL query to find lease exceptions.

        This is the REAL query converted from the R script.
        In test mode, we use mock data instead of running this query.

        Returns:
            SQL query string
        """
        return """
        SELECT /* SNOWFLAKE VERSION - TEST COPY */
        ARRAY_TO_STRING(ARRAY_CONSTRUCT_COMPACT(
            CASE WHEN LEAS.STOPBILLDATE < CURRENT_DATE THEN 'Stop Bill Date Past' END,
            CASE WHEN LEAS.VACATE < CURRENT_DATE THEN 'Vacate Date Past' END,
            CASE WHEN LEAS.EXPIR < CURRENT_DATE THEN 'Expire Date Past' END
        ),'; ') AS ISSUE
        , LEAS.BLDGID
        , LEAS.SUITID
        , MNGR.MNGRNAME AS "PROP MGR"
        , LEAS.LEASID
        , LEAS.OCCPSTAT AS "OCCUPANCY STATUS"
        , LEAS.OCCPNAME AS "TENANT NAME"
        , LEAS.GENCODE AS "GEN CODE"
        , TO_DATE(LEAS.RENTSTRT) AS "RENT START"
        , TO_DATE(LEAS.OCCUPNCY) AS "OCCUPANCY DATE"
        , TO_DATE(LEAS.STOPBILLDATE) AS "STOP BILL DATE"
        , TO_DATE(LEAS.VACATE) as "VACATE DATE"
        , TO_DATE(LEAS.EXPIR) as "EXPIRE DATE"
        , LEAS.TENTCAT AS "TENANT CATEGORY"
        FROM MRI.LEAS
        LEFT JOIN MRI.MOCCP ON LEAS.MOCCPID = MOCCP.MOCCPID
        LEFT JOIN MRI.BLDG ON LEAS.BLDGID = BLDG.BLDGID
        LEFT JOIN MRI.MNGR ON BLDG.MNGRID = MNGR.MNGRID
        WHERE LEAS.OCCPSTAT = 'C'
         AND LEAS.OCCPSTAT NOT IN ('P', 'I')
         AND (
            (
                (
                    LEAS.RENTSTRT <= CURRENT_DATE
                    OR LEAS.OCCUPNCY <= CURRENT_DATE
                    OR LEAS.EXECDATE <= CURRENT_DATE
                )
                AND (
                        LEAS.STOPBILLDATE IS NULL
                        OR LEAS.STOPBILLDATE < CURRENT_DATE
                        OR (
                                LEAS.STOPBILLDATE >= CURRENT_DATE
                                AND COALESCE(LEAS.VACATE,LEAS.EXPIR) < CURRENT_DATE
                            )
                )
                AND COALESCE(LEAS.VACATE,LEAS.EXPIR)< CURRENT_DATE
            )
        )
        ORDER BY LEAS.BLDGID, LEAS.SUITID, LEAS.LEASID
        """

    def execute_lease_exceptions_query(self) -> Optional[pd.DataFrame]:
        """
        🔍 Execute the lease exceptions query and return results.

        In test mode, this returns mock data instead of hitting the database.
        Perfect for learning without needing database access!

        Returns:
            DataFrame with query results or None if error
        """
        try:
            self.log_audit_in_db(
                log_msg="🔍 Executing lease exceptions query",
                print_msg=True
            )

            # 🎭 USE MOCK DATA IN TEST MODE (unless specifically testing query)
            if self.use_mock_data and not self.test_query:
                print("🎭 Using mock data instead of database query")
                mock_data = self.create_mock_data()

                # Still log the query for reference
                if self.save_query_log:
                    query = self.get_lease_exceptions_query()
                    self.log_query_details(query, mock_data)

                return mock_data

            # � REAL DATABASE QUERY MODE
            print("🔍 Testing actual Snowflake query...")

            if not self.sf:
                error_msg = "❌ No database connection available"
                print(error_msg)
                if self.save_query_log:
                    query = self.get_lease_exceptions_query()
                    self.log_query_details(query, error=error_msg)
                return None

            # 🔌 Test connection first
            if not self.test_snowflake_connection():
                error_msg = "❌ Snowflake connection test failed"
                print(error_msg)
                if self.save_query_log:
                    query = self.get_lease_exceptions_query()
                    self.log_query_details(query, error=error_msg)
                return None

            # 📋 Get and execute the actual query
            query = self.get_lease_exceptions_query()

            print("🚀 Executing MRI lease exceptions query...")
            print("⏳ This may take a moment...")

            # Execute query using SnowflakeHelper
            query_results = self.sf.execute_snowflake_query(
                query=query,
                print_query=True,
                pull_only_one_record=False
            )

            if not query_results:
                warning_msg = "⚠️  No data returned from lease exceptions query (this might be good - no exceptions found!)"
                print(warning_msg)
                empty_df = pd.DataFrame()

                # Log the query and empty results
                if self.save_query_log:
                    self.log_query_details(query, empty_df)

                return empty_df

            # Convert to DataFrame
            df = pd.DataFrame(query_results)

            # Clean up string columns (remove trailing spaces)
            for col in df.select_dtypes(include=['object']).columns:
                df[col] = df[col].astype(str).str.rstrip()

            print(f"✅ Query executed successfully! Found {len(df)} lease exceptions")

            # 📝 Log detailed query results
            if self.save_query_log:
                self.log_query_details(query, df)

            self.log_audit_in_db(
                log_msg=f"✅ Query returned {len(df)} rows",
                print_msg=True
            )

            return df

        except Exception as e:
            error_msg = f"❌ Error executing lease exceptions query: {str(e)}"
            print(error_msg)

            # Log the error
            if self.save_query_log:
                query = self.get_lease_exceptions_query()
                self.log_query_details(query, error=error_msg)

            self.log_audit_in_db(
                log_msg=error_msg,
                log_type='Error',
                print_msg=True
            )
            return None

    def create_excel_report(self, data: pd.DataFrame, filename: str) -> Optional[str]:
        """
        📊 Create an Excel report with the lease exceptions data.

        This creates a professionally formatted Excel file that you can open and examine!

        Args:
            data: DataFrame containing the lease exceptions
            filename: Name of the Excel file to create

        Returns:
            Full path to the created file or None if error
        """
        try:
            file_path = os.path.join(self.report_path, filename)

            self.log_audit_in_db(
                log_msg=f"📊 Creating Excel report: {filename}",
                print_msg=True
            )

            print(f"📊 Excel file will be saved to: {file_path}")

            # 📝 Create Excel writer with xlsxwriter engine for better formatting
            with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                # Write data to Excel
                data.to_excel(writer, sheet_name=self.query_date, index=False)

                # Get the workbook and worksheet objects for formatting
                workbook = writer.book
                worksheet = writer.sheets[self.query_date]

                # 🎨 Define header format (professional styling)
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'vcenter',
                    'align': 'center',
                    'fg_color': '#D6D6D6',  # Light gray background
                    'border': 1,
                    'font_name': 'Arial Narrow',
                    'font_size': 12
                })

                # 🎨 Apply header formatting
                for col_num, value in enumerate(data.columns.values):
                    worksheet.write(0, col_num, value, header_format)

                # 📏 Set column widths based on content (makes it readable)
                column_widths = {
                    'ISSUE': min(39, max(24, max(len(str(val)) for val in data.iloc[:, 0]) if len(data) > 0 else 24)),
                    'BLDGID': 8,
                    'SUITID': 8,
                    'PROP MGR': 22,
                    'LEASID': 8.5,
                    'OCCUPANCY STATUS': 7.5,
                    'TENANT NAME': min(36, max(21, max(len(str(val)) for val in data.iloc[:, 6]) if len(data) > 0 else 21)),
                    'GEN CODE': 7.5,
                    'RENT START': 10.5,
                    'OCCUPANCY DATE': 10.5,
                    'STOP BILL DATE': 10.5,
                    'VACATE DATE': 10.5,
                    'EXPIRE DATE': 10.5,
                    'TENANT CATEGORY': 9.5
                }

                # 📏 Apply column widths
                for col_idx, column in enumerate(data.columns):
                    if column in column_widths:
                        worksheet.set_column(col_idx, col_idx, column_widths[column])
                    else:
                        worksheet.set_column(col_idx, col_idx, 15)  # Default width

                # ❄️ Freeze the header row (so it stays visible when scrolling)
                worksheet.freeze_panes(1, 0)

                # 🔍 Add auto filter (allows filtering by clicking column headers)
                worksheet.autofilter(0, 0, len(data), len(data.columns) - 1)

            self.log_audit_in_db(
                log_msg=f"✅ Excel report created successfully: {file_path}",
                print_msg=True
            )

            print(f"✅ Excel file created! You can open it to see the results.")
            print(f"📁 Location: {file_path}")

            return file_path

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"❌ Error creating Excel report: {str(e)}",
                log_type='Error',
                print_msg=True
            )
            return None

    def run_test_report(self) -> ReportResult:
        """
        🧪 Main method to execute the TEST lease exceptions report.

        This is the main function that orchestrates everything:
        1. Gets the data (mock or real)
        2. Creates Excel file
        3. Optionally uploads to SharePoint
        4. Shows you the results

        Returns:
            ReportResult with execution status and details
        """
        try:
            print("\n" + "="*60)
            print("🧪 STARTING TEST REPORT EXECUTION")
            print("="*60)

            self.log_audit_in_db(
                log_msg="🚀 Starting TEST lease exceptions report execution",
                print_msg=True
            )

            # 🔍 STEP 1: Get the data
            print("\n📋 STEP 1: Getting lease exception data...")
            data = self.execute_lease_exceptions_query()

            # ✅ STEP 2: Check if we have valid data
            print("\n🔍 STEP 2: Validating data...")
            if data is None or len(data) == 0:
                message = "No lease exceptions found (this might be good news!)"
                print(f"ℹ️  {message}")
                self.log_audit_in_db(
                    log_msg=message,
                    log_type='Info',
                    print_msg=True
                )
                return ReportResult(
                    success=True,
                    row_count=0,
                    error_message="No exceptions found"
                )

            row_count = len(data)
            print(f"📊 Found {row_count} lease exceptions to report")

            self.log_audit_in_db(
                log_msg=f"✅ Found {row_count} lease exceptions",
                print_msg=True
            )

            # 📊 STEP 3: Create Excel file
            print("\n📊 STEP 3: Creating Excel report...")
            filename = f"TEST_MRI_Lease_Exceptions-{self.query_date}.xlsx"
            file_path = self.create_excel_report(data, filename)

            if not file_path:
                return ReportResult(
                    success=False,
                    row_count=row_count,
                    error_message="Failed to create Excel report"
                )

            # ☁️ STEP 4: Upload to SharePoint (if enabled)
            sharepoint_url = None
            if self.enable_sharepoint:
                print("\n☁️  STEP 4: Uploading to SharePoint...")
                # Note: SharePoint upload method would go here
                print("ℹ️  SharePoint upload not implemented in test version")
            else:
                print("\n☁️  STEP 4: SharePoint upload skipped (disabled)")

            # 📧 STEP 5: Email notification (disabled in test mode)
            print("\n📧 STEP 5: Email notification...")
            if self.testing_emails:
                print("📧 Email sending disabled in test mode for safety")
                print("📧 In production, this would email the Excel file to property management")

            # 📝 STEP 6: Finalize logging
            print("\n📝 STEP 6: Finalizing logs...")
            self.log_audit_in_db(
                log_msg="✅ TEST report execution completed successfully",
                print_msg=True,
                start_upload=False  # No database upload in test mode
            )

            # 🎉 SUCCESS!
            print("\n" + "="*60)
            print("🎉 TEST REPORT COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"📊 Records processed: {row_count}")
            print(f"📁 Excel file: {file_path}")
            if sharepoint_url:
                print(f"☁️  SharePoint: {sharepoint_url}")
            print("="*60)

            return ReportResult(
                success=True,
                row_count=row_count,
                file_path=file_path
            )

        except Exception as e:
            error_msg = f"❌ Error in run_test_report: {str(e)}"
            print(f"\n{error_msg}")
            self.log_audit_in_db(
                log_msg=error_msg,
                log_type='Error',
                print_msg=True,
                start_upload=False
            )

            return ReportResult(
                success=False,
                row_count=0,
                error_message=error_msg
            )


def test_main():
    """
    🧪 Main execution function for the TEST MRI lease exceptions report.

    This is your playground! Modify the settings below to experiment.
    """
    print("🧪 STARTING TEST VERSION OF MRI LEASE EXCEPTIONS SCRIPT")
    print("="*70)

    # 🔧 CONFIGURATION - MODIFY THESE TO EXPERIMENT!
    testing_emails = True      # Keep True for safety
    enable_sharepoint = False  # Set to True to test SharePoint (if available)
    use_mock_data = True      # Set to False to test with real database (if available)

    # 🔍 QUERY TESTING OPTIONS - NEW!
    test_query = False        # Set to True to test actual Snowflake query
    save_query_log = True     # Set to True to save detailed query logs

    print(f"📧 Testing emails: {testing_emails}")
    print(f"☁️  SharePoint integration: {enable_sharepoint}")
    print(f"🎭 Mock data mode: {use_mock_data}")
    print(f"� Test actual query: {test_query}")
    print(f"📝 Save query logs: {save_query_log}")
    print(f"�📚 Libs available: {LIBS_AVAILABLE}")
    print("-" * 70)

    # 🚨 SAFETY WARNING FOR QUERY TESTING
    if test_query and not use_mock_data:
        print("\n🚨 QUERY TESTING MODE ENABLED!")
        print("   This will run the actual Snowflake query against your database.")
        print("   Make sure you have:")
        print("   - ✅ Proper database access")
        print("   - ✅ SnowflakeHelper configured")
        print("   - ✅ Environment variables set")
        print("   - ✅ Network connectivity")
        print("-" * 70)

    try:
        # 🏗️ Create processor instance
        print("🏗️  Creating test processor...")
        processor = TestMRILeaseExceptionsPastInactiveDates(
            testing_emails=testing_emails,
            enable_sharepoint=enable_sharepoint,
            use_mock_data=use_mock_data,
            test_query=test_query,
            save_query_log=save_query_log
        )

        # 🚀 Run the test report
        print("\n🚀 Running test report...")
        result = processor.run_test_report()

        # 📋 Print final results
        print("\n" + "="*70)
        print("📋 FINAL RESULTS")
        print("="*70)

        if result.success:
            print("✅ Test completed successfully!")
            print(f"📊 Found {result.row_count} lease exceptions")
            if result.file_path:
                print(f"📁 Excel file created: {result.file_path}")
                print("💡 TIP: Open the Excel file to see the formatted results!")
        else:
            print("❌ Test failed!")
            print(f"📊 Row count: {result.row_count}")
            if result.error_message:
                print(f"❌ Error: {result.error_message}")

        print("="*70)
        print("🎓 LEARNING COMPLETE! Feel free to modify and re-run.")
        print("="*70)

        return result.success

    except Exception as e:
        print(f"\n💥 Fatal error in test execution: {str(e)}")
        print("🔧 Try modifying the configuration or check your setup")
        return False


if __name__ == "__main__":
    """
    🎯 Entry point for TEST script execution.

    Run this script to test and learn how the MRI lease exceptions process works!
    """
    print("🎯 Starting TEST script...")

    success = test_main()

    if success:
        print("\n🎉 Test script completed successfully!")
        print("💡 Now you can experiment with different settings and learn how it works!")
    else:
        print("\n⚠️  Test script encountered issues.")
        print("🔧 Check the error messages above and try adjusting the configuration.")

    print("\n👋 Thanks for testing! Happy learning!")

    # Exit with appropriate code
    import sys
    sys.exit(0 if success else 1)
