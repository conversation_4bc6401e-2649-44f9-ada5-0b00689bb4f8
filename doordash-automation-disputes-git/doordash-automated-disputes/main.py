import asyncio
import os
import subprocess
import time
import sys
import re
import zipfile
import pandas as pd
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright
from snowflake.connector.pandas_tools import write_pandas
from libs.snowflake_helper import SnowflakeHelper
import doordash_to_snowflake_helper as ddtsh


# print(f"[DEBUG] Python running from: {sys.executable}")

#?Dynamically load env variables
EMAIL = os.getenv("EMAIL")
PASSWORD = os.getenv("PASS")
URL = os.getenv("URL")
DATA_DIR = os.getenv("DOORDASH_DATA_DIR")


assert EMAIL and PASSWORD and URL, " Missing EMAIL, PASS, or URL environment variables!"


#?Clean column headers up to fit snowflake tables
def clean_column_name(col):
    col = col.strip()                          # Remove leading/trailing spaces
    col = re.sub(r"\s+", "_", col)             # Replace spaces/tabs/etc with underscores
    col = re.sub(r"\([^)]*\)", "", col)        # Remove anything in parentheses
    col = re.sub(r"_+", "_", col)              # Collapse multiple underscores
    col = col.strip("_")                       # Remove leading/trailing underscores
    return col


async def get_column_index_by_header(page, reference_text="Date created", header_name="Time frame"):
    # Locate the table containing the reference text
    table_locator = page.locator(f'//table[.//text()[contains(., "{reference_text}")]]')
    table = table_locator.first  # Use the first matching table

    if not await table.count():
        print(f"Could not find table containing '{reference_text}'")
        return None

    # Get the first header row (could be td or th)
    header_cells = await table.locator("xpath=.//tr[1]/*").all()
    headers = [await cell.text_content() for cell in header_cells]
    print("Scoped Table Headers:", headers)

    try:
        index = next(i for i, text in enumerate(headers) if header_name.lower() in text.lower())
        print(f"'{header_name}' found at index {index}")
        return index
    except StopIteration:
        print(f"Header '{header_name}' not found in scoped table!")
        return None


def clean_csv_headers(filepath):
    df = pd.read_csv(filepath)

    original_headers = df.columns.tolist()
    cleaned_headers = [clean_column_name(col) for col in original_headers]

    df.columns = cleaned_headers

    print("Original headers:")
    print(original_headers)
    print("\nCleaned headers:")
    print(cleaned_headers)

    return df



async def handle_download_and_unzip(page, download_button):
    #! Get script directory and set up data/ folder
    script_dir = Path(__file__).resolve().parent

    if DATA_DIR:
        output_dir = Path(DATA_DIR)
    else:
        output_dir = script_dir / "data"
        print(f"DATA_DIR environment variable not set, using default: {output_dir}")

    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        print("Waiting for download to start...")
        async with page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value

        zip_path = output_dir / download.suggested_filename
        await download.save_as(str(zip_path))
        print(f"Zip file saved to: {zip_path}")

        if zip_path.suffix.lower() == ".zip":
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(output_dir)
            print(f"Extracted contents to: {output_dir}")

            today = datetime.now().strftime("%Y-%m-%d")
            rename_map = {
                "avoidable": f"DOORDASH_AVOIDABLE_WAIT_ORDERS-{today}.csv",
                "cancelled": f"DOORDASH_CANCELLED_ORDERS-{today}.csv",
                "missing": f"DOORDASH_MISSING_INCORRECT_ORDERS-{today}.csv"
            }

            for file in output_dir.iterdir():
                if file.suffix.lower() == ".csv":
                    lowercase_name = file.name.lower()
                    for keyword, new_name in rename_map.items():
                        if keyword in lowercase_name:
                            new_path = output_dir / new_name
                            file.rename(new_path)
                            print(f"Renamed {file.name} → {new_name}")
                            break

            try:
                zip_path.unlink()
                print(f"Deleted ZIP file: {zip_path}")
            except Exception as e:
                print(f"Failed to delete zip: {e}")

            #  Clean only today's CSVs
            csv_files = list(output_dir.glob("*.csv"))
            for csv_file in csv_files:
                if today not in csv_file.stem:
                    print(f"⏩ Skipping old file: {csv_file.name}")
                    continue
                try:
                    df_cleaned = clean_csv_headers(csv_file)
                    df_cleaned.to_csv(csv_file, index=False)
                    print(f"✅ Cleaned headers in: {csv_file.name}")
                except Exception as e:
                    print(f"❌ Failed to clean headers in {csv_file.name}: {e}")

            return output_dir
        else:
            print("ℹDownloaded file is not a zip.")
            return zip_path

    except Exception as e:
        print(f"Download or unzip failed: {e}")
        return None





async def find_valid_report(page):
    time_frame_index = await get_column_index_by_header(page)
    if time_frame_index is None:
        return None

    #! Grab all report rows
    rows = await page.locator('//table[.//text()[contains(., "Date created")]]//tr[contains(@role, "row")]').all()
    print(f"Total report rows found: {len(rows)}")

    valid_reports = []

    for row in rows:
        text = await row.text_content()

        if "EXPIRED" in text:
            print("Skipping row marked as EXPIRED")
            continue

        valid_reports.append(row)
        if len(valid_reports) == 4:
            break

        inner_html = await row.inner_html()
        print(f"Row inner HTML:\n{inner_html}\n")

    print(f"Valid reports to evaluate: {len(valid_reports)}")

    reports = []

    inner_html = await row.inner_html()
    print(f"Row inner HTML:\n{inner_html}\n")

    for row in valid_reports:
        cells = await row.locator("td").all()
        cell_texts = [await cell.text_content() for cell in cells]
        print("Row contents:", cell_texts)

        # ✅ Check cell length before accessing time_frame_index
        if len(cell_texts) <= time_frame_index:
            print(f"Row missing expected number of columns — skipping. Contents: {cell_texts}")
            continue

        try:
            date_range_text = cell_texts[time_frame_index].strip()
            print(f"Time Frame column: {date_range_text}")

            if not date_range_text:
                print("No Time Frame value — skipping.")
                continue

            # Normalize spacing/dash variants
            date_range_text = date_range_text.replace('\u2009', ' ').replace('–', '-')

            if "-" in date_range_text:
                try:
                    start_str, end_str = map(str.strip, date_range_text.split("-", 1))

                    # Extract the year from the end string
                    match = re.search(r",?\s*(\d{4})", end_str)
                    if not match:
                        print("Couldn't find year — skipping.")
                        continue
                    year = match.group(1)

                    end_str_clean = re.sub(r",?\s*\d{4}", "", end_str).strip()

                    # ✅ If end_str is just a day (like "26"), use start_str's month
                    if re.match(r"^\d{1,2}$", end_str_clean):
                        start_month = start_str.split(" ")[0]
                        end_str_clean = f"{start_month} {end_str_clean}"

                    start_date = datetime.strptime(f"{start_str} {year}", "%b %d %Y")
                    end_date = datetime.strptime(f"{end_str_clean} {year}", "%b %d %Y")

                    if end_date < start_date:
                        end_date = end_date.replace(year=int(year) + 1)

                    days_diff = (end_date - start_date).days
                    print(f"Range: {start_date.date()} → {end_date.date()} ({days_diff} days)")

                    if 4 <= days_diff <= 8:
                        reports.append((end_date, row))
                        print("Valid 4–8 day report added.")
                    else:
                        print("Report not 4–8 days — skipping.")
                except Exception as e:
                    print(f"Date range parsing failed: {e}")
                    continue
            else:
                print(f"Not a date range: {date_range_text}")

        except Exception as e:
            print(f"Unexpected error while processing row: {e}")
            continue

    reports.sort(key=lambda x: x[0], reverse=True)

    if reports:
        print("Found Most Recent Valid 4–8 Day Report")
        return reports[0][1]
    else:
        print("No Valid Reports Found!!")
        return None



async def run_playwright(URL):
    print("Launching Playwright...")
    async with async_playwright() as p:
        print("Launching browser...")
        browser = await p.chromium.launch(
            headless=True,
            args=[
                "--disable-gpu",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--window-size=1920,1080"
            ],
            executable_path=p.chromium.executable_path
        )
        
        print("Browser launched. Creating new context...")
        
        context = await browser.new_context(
            no_viewport=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            viewport={"width": 1280, "height": 720},
            java_script_enabled=True,
            ignore_https_errors=True,
            bypass_csp=True
        )

        page = await context.new_page()
        await page.add_init_script("""Object.defineProperty(navigator, 'webdriver', { get: () => false })""")

        print("Navigating to login page...")
        try:
            await page.goto(URL, wait_until="domcontentloaded", timeout=60000)
        except Exception as e:
            content = await page.content()
            with open("headless_error_dump_.html", "w") as f:
                f.write(content)
            print("Saved headless error HTML to file.")
            raise e
      

        print("Filling in login credentials...")
        await page.fill('[data-anchor-id="IdentityLoginPageEmailField"]', f"{EMAIL}")
        await page.click("#merchant-login-submit-button")
        await page.fill('[data-anchor-id="IdentityLoginPagePasswordField"]', f"{PASSWORD}")
        await page.click('#login-submit-button')

        print("Waiting for post-login indicators...")
        try:
            await page.wait_for_selector("text=Reports", timeout=18000)
            print("Login successful — Reports link is visible.")
        except:
            error_text = await page.content()
            if "incorrect" in error_text.lower():
                print("Incorrect login credentials.")
            else:
                print("Login likely failed or page structure changed.")
                return

        await page.get_by_text("Reports").click()
        await page.get_by_role("button", name="Close All your DoorDash reports in one place").click()

        print("Scanning for valid reports...")
        row = await find_valid_report(page)

        if row:
            print("Attempting to download the selected report...")
            download_button = row.locator("button[aria-label='Download']")
            result_path = await handle_download_and_unzip(page, download_button)
            print(f"Download and extraction complete. Path: {result_path}")

            # ✅ ADDED: trigger Snowflake ingestion if the files were downloaded
            if result_path:
                print("🔄 Kicking off Snowflake ingestion...")
                ddtsh.main()
        else:
            print("No report to download.")

        await browser.close()




if __name__ == "__main__":
 asyncio.run(run_playwright(URL))
