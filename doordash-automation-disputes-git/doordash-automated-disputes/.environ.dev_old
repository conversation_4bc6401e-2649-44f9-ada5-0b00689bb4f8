export DATABASE_ENVM="DEV"
export DATABASE_USER="DEV_DATA_LOADER_USER"
export DATABASE_PASSWORD="BrTh4sndUcu3sY"
export DATABASE_ACCOUNT="bv18165.central-us.azure"
export DATABASE_RAW_DATABASE="DEV_RAW_DB"
export DATABASE_RAW_SCHEMA="MOMS_RAW"
export DATABASE_RAW_TEMP_SCHEMA="MOMS_RAW_TEMP"
export DATABASE_TRF_DATABASE="DEV_TRF_DB"
export DATABASE_TRF_SCHEMA="MOMS_FLTTN"
export DATABASE_CSM_DATABASE="DEV_CSM_DB"
export DATABASE_CSM_SCHEMA="MOMS"
export DATABASE_WAREHOUSE="DEV_DATA_LOADER_WH"
export DATABASE_ROLE="AR_DEV_ALL_ETL"
export DATABASE_SMTPHOST="smtp.gmail.com"
export DATABASE_SMTPPORT="465"
export DATABASE_SENDERS="<EMAIL>"
export DATABASE_MAIL_PASSWORD="zxzp jzrs zyzk bimd"
export BEARER_TOKEN_FPC="ApvpMlNqBTJKvf53CawImNIge6ZlAQAAAPk_CNwOAAAA"
export BEARER_TOKEN_STSSERVICECOOKIE="estsfd"
export BEARER_TOKEN_AUTHORIZATION="Basic ********************************************************************************************************"
export BEARER_TOKEN_CONTENT_TYPE="application/x-www-form-urlencoded"
export BEARER_TOKEN_GRANT_TYPE="client_credentials"
export BEARER_TOKEN_SCOPE="api://5cbf238e-ae31-4876-ba33-bcd851005afb/.default"
export BEARER_TOKEN_OAUTH="https://login.microsoftonline.com/82ccc3e9-1864-41dc-9e68-45088d5e0123/oauth2/v2.0/token"
export BEARER_TOKEN_TOKEN_MAX_TIME="1800"
export BEARER_TOKEN_TOTAL_API_COUNT="100"
export BEARER_TOKEN_API_MAX_TIME="60"
export BEARER_TOKEN_SLEEP_TIME="10"
export SQL_STORE="select CAST(HF AS INT) as STORE_NO,MKEY as APIKEY from {}.{}.AC_STORE_MATCH WHERE is_moms = 'Y' and '{}' between TO_DATE(MOMS_ACTIVATION_DATE) and coalesce(moms_closure_date::DATE,current_date)"
export SQL_DATE="select DATE_OVERRIDE,CUSTOM_START_DATE,CUSTOM_END_DATE from {}.{}.DATA_EXTRACTION_DATES WHERE SRC_NAME = 'MOMS'"
export SQL_TABLES="select SRC_NAME,OBJECT_NAME,ENDPOINT,ACTIVE from {}.{}.ALLOWED_NAMES_LIST where active='Y' AND SRC_NAME = 'MOMS'"
export SQL_DEL_INS="BEGIN DELETE FROM {envm}_raw_db.moms_raw.{table_name} WHERE (data_date,store_number) in (SELECT data_date,store_number FROM {envm}_raw_db.moms_raw_temp.{table_name}); INSERT INTO {envm}_raw_db.moms_raw.{table_name} SELECT * FROM {envm}_raw_db.moms_raw_temp.{table_name}; END;"
export SQL_COMMON_PARAMETERS="SELECT A.VALUE FROM {}.{}.AC_COMMON_PARAMETERS, LATERAL SPLIT_TO_TABLE(VALUE, ',') A  WHERE KEY='CUSTOM_MOMS_STORES_TO_PROCESS' AND (A.VALUE IS NOT NULL AND A.VALUE <> '') AND A.VALUE IN (SELECT TO_CHAR(HF) FROM {}.{}.AC_STORE_MATCH WHERE IS_MOMS='Y' )"
export SQL_CUSTOM_STORE="SELECT CAST(HF AS INT) AS STORE_NO, MKEY AS APIKEY FROM {}.{}.AC_STORE_MATCH WHERE is_moms = 'Y' and '{}' between TO_DATE(MOMS_ACTIVATION_DATE) and coalesce(moms_closure_date::DATE,current_date) AND TO_CHAR(HF) IN (SELECT TRIM(REPLACE(A.VALUE,'\"','')) FROM {}.{}.AC_COMMON_PARAMETERS, LATERAL SPLIT_TO_TABLE(VALUE, ',') A WHERE KEY='CUSTOM_MOMS_STORES_TO_PROCESS')"
export SQL_1_EXCEPTION="SELECT COUNT(*)  FROM {}.{}.EMPLOYEESRESULT_EXCEPTIONS where email_sent is null"
export SQL_2_EXCEPTION="select STORE_NUMBER,id,employee_number,processed_date from {}.{}.EMPLOYEESRESULT_EXCEPTIONS where processed_date = current_date and email_sent  is null"
export SQL_3_EXCEPTION="select STORE_NUMBER,id,employee_number,processed_date from {}.{}.EMPLOYEESRESULT_EXCEPTIONS where processed_date = current_date and email_sent is not null"
export SQL_4_EXCEPTION="update {}.{}.EMPLOYEESRESULT_EXCEPTIONS set email_sent = '{}' where email_sent is null"
export SQL_EMAIL_EMPLOYEES_NUMBER_EXCEPTION_TO_LIST="SELECT VALUE FROM {}.{}.AC_COMMON_PARAMETERS WHERE KEY='EMAIL_EMPLOYEES_NUMBER_EXCEPTION_TO_LIST'"
export TIME_DESIRED_TIMEZONE="America/Chicago"
export EMAIL="<EMAIL>"
export PASS="Pizza12345!"
export URL="https://identity.doordash.com/auth?client_id=1643580605860775164&redirect_uri=https%3A%2F%2Fmerchant-portal.doordash.com%2Fauth_callback&scope=*&prompt=none&response_type=code&layout=merchant_web_v2&state=f1394920-c566-4bb4-a2bb-64ee8841889d&allowRedirect=true&failureRedirect=%2Fmerchant%2Flogin"
export DD_FILEPATH=""
export MOMS_SCHEMA="MOMS"
export DATABASE_RAW_DATABASE="DEV_RAW_DB"
