# Snowflake connector helper utility

#--------------------------------------------------------------------------------------
# Rev. No     Date      Author   Description
#---------------------------------------------------------------------------------------
#  1.0    2/20/25   jgarifuna    Initial Version
#---------------------------------------------------------------------------------------

import pandas as pd

import snowflake.connector
from snowflake.connector import DictCursor
from snowflake.connector.pandas_tools import write_pandas
import os
import time 
import datetime 

class SnowflakeHelper:

    def __init__(self, my_sf_conn = None):
        
        if my_sf_conn is not None:  # use the provided SF connection
            self.conn = my_sf_conn
        else:                       # get a new SF connection
            self.conn = self.connect_to_snowflake()

        print(f'SF conn: {self.conn}')
        self.cs = self.conn.cursor()
        self.cs_dict = self.conn.cursor(DictCursor) 
        self.batch_audit_schema = 'BATCH_AUDIT'

    def connect_to_snowflake(self):
        """
        Connects to Snowflake using the configuration settings.
        """
        return snowflake.connector.connect(
            user=os.environ["DATABASE_USER"],
            password=os.environ["DATABASE_PASSWORD"],
            account=os.environ["DATABASE_ACCOUNT"],
            database=os.environ["DATABASE_RAW_DATABASE"],
            schema=os.environ["DATABASE_RAW_TEMP_SCHEMA"],
            warehouse=os.environ["DATABASE_WAREHOUSE"],
            role=os.environ["DATABASE_ROLE"]
        )    
    
    def execute_query(self, query):
        """
        Executes the SQL query.
        """
        # print("execute_query: {}\n".format(query))
        self.cs.execute(query)


    def execute_snowflake_query(self, query, print_query=False, pull_only_one_record=False):
        """ Executes update or insert or delete query in Snowflake """

        if print_query:
            print(f"Starting: execute_snowflake_query()") # \n\t{query}

        if "select " in query.lower():
            is_select = True
        else:
            is_select = False

        # cs = conn.cursor()   
        cs = self.conn.cursor(DictCursor) 

        if print_query:
            print(f'\tSF cursor: {cs}\n\nquery: {query}\n')

        # exit(1)

        cs.execute(query)


        if is_select:
            if print_query:
                print("Finished: execute_snowflake_query()")

            if pull_only_one_record: # only one pull record
                return cs.fetchone()
            else: # pull all records
                return cs.fetchall()
        
        if print_query:
            print("Finished: execute_snowflake_query()")


    def bulk_insert(self, columns_list, data_list, database, schema, table):
        """ Inserts records into Snowflake """

        start_time = time.time()
        start_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')

        print(f"\tStarting: bulk_insert({database}.{schema}.{table})\t[time: {start_now}]")

        df_to_insert = pd.DataFrame(columns=columns_list,data=data_list)
        write_pandas(conn=self.conn, df=df_to_insert, database=database, schema=schema,
                    table_name=table, quote_identifiers = False)
        
        # calculate duration
        end_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
        duration = self.get_duration(start_time)

        print(f"\tFinished: bulk_insert({database}.{schema}.{table})\t[time: {end_now}] [{duration}]\n")


    def get_duration(self, start_time, with_label = True, show_secs=False):
        """ gets duration in minutes """
        end_time = time.time()
        secs = (end_time - start_time)
        total_time_mins = round(secs / 60, 2)

        if with_label:
            if show_secs:
                secs = round(secs,2)
                return f"Duration: {total_time_mins} mins. {secs} secs."
            else:
                return f"Duration: {total_time_mins} mins"
        
        return total_time_mins

    def get_config_parameter_value_from_db(self, env, parameter_key, only_check_existance=False):
        """ get value of given parameter from *.CORPORATE.AC_COMMON_PARAMETERS table """

        if env is None or (env is not None and env.lower() not in ['dev', 'stage', 'prod']):
            raise ValueError("env value must be one of the following: dev, stage, prod")

        if parameter_key is None or (parameter_key is not None and len(str(parameter_key)) < 1):
            raise ValueError("parameter_key cannot be empty")
        
        query = f"""
            SELECT value from {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS where key='{parameter_key}';
            """
        value = ''
        result =  self.execute_snowflake_query(query=query, pull_only_one_record=True)

        # only check existance of key in table
        if only_check_existance:
            if result is None:
                return False
            else:
                return True
            
        if result is not None and 'VALUE' in result:
            value = result['VALUE']

        return value
    
    def create_new_config_parameter_in_db(self, env, parameter_key, parameter_value, description, created_by=None, print_output=True):
        """ create a new parameter in {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS """
        # key already created, no need to create
        if self.get_config_parameter_value_from_db(env=env, parameter_key=parameter_key, only_check_existance=True):
            if print_output:
                print(f"WARNING: create_new_config_parameter_in_db() - Parameter key, {parameter_key}, already exists in table: {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS. No need to create.")
            return True
        
        if created_by is None:
            created_by = 'snowflake_helper'

        rec_ins_date = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        columns_list=['KEY', 'VALUE', 'DESCRIPTION', 'CREATED_AT', 'CREATED_BY']
        data_list=[[parameter_key, parameter_value, description, rec_ins_date, created_by]]

        # create the new parameter
        self.bulk_insert(columns_list=columns_list, data_list=data_list, database=f'{env}_CSM_DB', schema='CORPORATE', table='AC_COMMON_PARAMETERS')

        if self.get_config_parameter_value_from_db(env=env, parameter_key=parameter_key, only_check_existance=True):
            if print_output:
                print(f"SUCCESS: create_new_config_parameter_in_db() - Parameter key, {parameter_key}, created in table: {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS.")
            return True
        else:
            if print_output:
                print(f"ERROR: create_new_config_parameter_in_db() - Parameter key, {parameter_key}, NOT created in table: {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS.")
            return False

    def update_config_parameter_in_db(self, env, parameter_key, parameter_value, print_output=True):
        """ Updates value of provided key in  {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS """

        if not self.get_config_parameter_value_from_db(env=env, parameter_key=parameter_key, only_check_existance=True):
            raise Exception(f"-1) ERROR: update_config_parameter_in_db({env}, {parameter_key}, {parameter_value}) -  Parameter key, {parameter_key}, Does not exist in {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS. Please create it and try again.\n\nQuitting.")
        
        if parameter_key is None:
            raise Exception(f"0) ERROR: update_config_parameter_in_db({env}, {parameter_key}, {parameter_value}) -  Parameter key, {parameter_key}, is empty. Please pass a valid parameter and try again.\n\nQuitting.")            

        if parameter_key is not None and len(parameter_key) < 1:
            raise Exception(f"1) ERROR: update_config_parameter_in_db({env}, {parameter_key}, {parameter_value}) -  Parameter key, {parameter_key}, is empty. Please pass a valid parameter and try again.\n\nQuitting.")            

        rec_ins_date = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        query = f""" 
            UPDATE {env}_CSM_DB.CORPORATE.AC_COMMON_PARAMETERS SET value='{parameter_value}', UPDATED_AT='{rec_ins_date}', UPDATED_BY='snowflake_helper' WHERE KEY='{parameter_key}'
        """

        self.execute_snowflake_query(query=query)

        # print output
        if print_output:
            print(f"SUCCESS: update_config_parameter_in_db({env}, {parameter_key}, {parameter_value})")

    def log_email_message_in_db(self, to_emails, subject, body, script_name):
        """ Logs email message to EMAILER table """

        start_time = time.time()
        start_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')

        print(f"Starting: log_email_message_in_db()\t[time: {start_now}]")

        if to_emails is None:
            print(f"\tEmail is empty quiting.")
            exit(1)

        if len(to_emails) < 1:
            print(f"\tEmail is empty quiting.")
            exit(1)

        if len(subject) < 1:
            print(f"\tSubject is empty quiting.")
            exit(1)

        if len(body) < 1:
            print(f"\tBody is empty quiting.")
            exit(1)

        body = f"{body}<hr>Automated email sent from {script_name}"

        # uuid_str = str(uuid.uuid4())
        script_name = os.path.basename(script_name)
        rec_ins_date = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')

        emailer_table = 'EMAILER'

        data_list = []
        data_list.append([to_emails,subject, body,script_name, rec_ins_date])
        # INSERT INTO STAGE_RAW_DB.BATCH_AUDIT.EMAILER(RECIPIENTS, SUBJECT,BODY, BODY_TYPE, SOURCE, ATTACHMENTS_PATHS) 
        df_to_insert = pd.DataFrame(columns=['RECIPIENTS', 'SUBJECT','BODY',  'SOURCE','RECORD_INSERTED_AT'],data=data_list)
        write_pandas(conn=self.conn, df=df_to_insert, database=os.environ["DATABASE_RAW_DATABASE"], schema=self.batch_audit_schema,
                    table_name=emailer_table, quote_identifiers = False)
        
        # calculate duration
        end_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
        duration = self.get_duration(start_time)

        print(f"Finished: log_email_message_in_db()\t[time: {end_now}] [{duration}]\n")   

        
        
