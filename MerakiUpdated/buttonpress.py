import requests
import json
import config


key = "7ce7dab5-b022-4a2e-acac-537bc26e54af"
doc_id = "6gRoLFuM-u"
table_id = "grid-fETr1XpfXS"
row_id = "i-Sa_i6o2h3w"
column_id = "c-UZf3bDVKUl"



#table-tCf9G024gf
#grid-fETr1XpfXS
def api_call(uri, params):
    headers = {'Authorization': f'Bearer {key}'}
    res = requests.get(uri, headers=headers, params=params)
    data = res.json()  # Extract JSON data from the response
    return data



def list_tables():
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables"
    params = {}
    tables = api_call(uri, params)
    print(json.dumps(tables, indent=4))

def list_rows():
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/rows"
    params = {}
    rows = api_call(uri, params)
    print(json.dumps(rows, indent=4))

def list_columns():
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/columns"
    params = {}
    columns = api_call(uri, params)
    print(json.dumps(columns, indent=4))

def get_table_info():
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}"
    params = {}
    table_info = api_call(uri, params)
    print(json.dumps(table_info, indent=4))

def get_row_info():
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/rows/{row_id}"
    params = {}
    row_info = api_call(uri, params)
    print(json.dumps(row_info, indent=4))

def push_button():
    headers = {'Authorization': f'Bearer {key}'}
    uri = f'https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/rows/{row_id}/buttons/{column_id}'
    try:
        req = requests.post(uri, headers=headers)
        req.raise_for_status()
        res = req.json()
        # Detailed logging of the request and response
        print("Request URI:", uri)
        print("Request Headers:", headers)
        print("Response JSON:", res)
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
        print(f"Response content: {req.content}")
    except Exception as err:
        print(f"Other error occurred: {err}")


def main():
    push_button()


main()


