import time
import re


def read_status() -> dict:
    # Regular expression to capture the store number (after "<PERSON>")
    store_pattern = re.compile(r'<PERSON>(\d+)')

    # Dictionary to store status by store number
    store_status = {}

    with open("uplink_logs.txt", "r") as file:
        line = file.readline()

        while line:
            # Check if the line contains either "WAN" or "Cellular"
            if "WAN" in line or "Cellular" in line:
                # Use regex to find the store number
                store_match = store_pattern.search(line)

                if store_match:
                    store_number = store_match.group(1)  # Extract the store number

                    # Determine if it's WAN or Cellular and the status
                    if "WAN" in line:
                        status_type = "WAN"
                    elif "Cellular" in line:
                        status_type = "Cellular"
                    elif "Wan2" in line:
                        status_type = "Wan2"


                    # Extract the status (active, connecting, etc.)
                    status_match = re.search(r'(active|failed|connecting|not connected)', line)
                    if status_match:
                        status = status_match.group(0)

                    # If the store doesn't exist in the dictionary, initialize it
                    if store_number not in store_status:
                        store_status[store_number] = {}

                    # Update the status in the store's dictionary
                    store_status[store_number][status_type] = status

            line = file.readline()

    # Validate the store_dict
    for store, info in store_status.items():
        if "WAN" not in info:
            print(f"Warning: 'WAN' information missing for store {store}")
        if "Cellular" not in info:
            print(f"Warning: 'Cellular' information missing for store {store}")

    print(store_status)
    return store_status

def main():
    read_status()
    return

if __name__ == "__main__":
    main()
