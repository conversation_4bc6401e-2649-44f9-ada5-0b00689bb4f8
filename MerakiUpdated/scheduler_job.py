from Uplink_Status import UplinkStatuses
from CodaUploader import CodaUploader

"""
Runs uplink-status.py into coda-updater.

uplink-status.py makes api call to meraki dashboard to grab network statuses. This info - as a list - is then written to file uplink_logs.txt for
use with coda-updater.py


"""

if __name__ == "__main__":
    try:
        processor = UplinkStatuses()
        processor.run()
        print(f"Successfully ran UplinkStatuses Processor")

    except Exception as e:
        print(f"Unable to run UplinkStatuses Processor due to Error: {e}")

    try:
        uploader = CodaUploader()
        uploader.run()
        print(f"Successfully ran CodaUploader Processor")

    except Exception as e:
        print(f"Unable to run CodaUploader Processor due to Error: {e}")