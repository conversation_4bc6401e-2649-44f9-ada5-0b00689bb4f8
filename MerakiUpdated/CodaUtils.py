import os
import requests
import json
from requests.exceptions import RequestException

class CodaUtils:
    API_KEY = "************************************"
    BASE_URL = "https://coda.io/apis/v1"

    @staticmethod
    def rate_limited_api_call(func, *args, **kwargs):
        return func(*args, **kwargs)

    @staticmethod
    def api_call(uri, params=None, method="GET", payload=None):
        headers = {'Authorization': f'Bearer {CodaUtils.API_KEY}'}
        url = f"{CodaUtils.BASE_URL}/{uri}"
        
        response = CodaUtils.rate_limited_api_call(
            requests.request,
            method,
            url,
            headers=headers,
            params=params,
            json=payload
        )
        response.raise_for_status()
        return response.json()

    @staticmethod
    def get_doc(doc_id):
        doc = CodaUtils.api_call(f"docs/{doc_id}")
        return doc["id"] if doc else None

    @staticmethod
    def get_table(doc_id, table_id):
        table_info = CodaUtils.api_call(f"docs/{doc_id}/tables/{table_id}")
        if table_info:
            print(f"Table info: {json.dumps(table_info, indent=4)}")
        return table_info

    @staticmethod
    def list_columns(doc_id, table_id):
        columns = CodaUtils.api_call(f"docs/{doc_id}/tables/{table_id}/columns")
        if columns:
            #print(f"Column info: {json.dumps(columns, indent=4)}")
            print("Found Columns")
            for item in columns["items"]:
                print(f'Column ID: {item["id"]}')
        return columns

    @staticmethod
    def verify_table_access(doc_id, table_id):
        table_info = CodaUtils.api_call(f"docs/{doc_id}/tables/{table_id}")
        if table_info:
            #print(f"Successfully verified access to table: {table_info['name']} (ID: {table_info['id']})")
            print("Table Access Verified")
            return True
        else:
            print(f"Failed to access table with ID: {table_id}")
            return False
        
