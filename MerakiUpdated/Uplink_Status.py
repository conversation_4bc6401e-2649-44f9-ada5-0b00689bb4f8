import requests
import logging
import json
import time
import re

# Configure logging to write to a file in overwrite mode
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='uplink_logs.txt',
    filemode='w'
)

class UplinkStatuses:
    def __init__(self):
        self.API_KEY = '44870573592543ee327ed0b969c95e47b241f7d7'
        self.ORG_ID = '1501819'

    @staticmethod
    def get_marcos_networks(API_KEY, organization_id)->list:
        url = f"https://api.meraki.com/api/v1/organizations/{organization_id}/networks"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Accept": "application/json"
        }
        try:
            logging.info(f"Fetching networks for organization ID: {organization_id}...")
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            networks = response.json()
            marcos_networks = [network for network in networks if '<PERSON>' in network['name']]
            logging.info(f"Found {len(marcos_networks)} networks with '<PERSON>' in their name.")
            return marcos_networks
        except requests.exceptions.RequestException as e:
            logging.error(f"Error fetching networks: {e}")
            return []
        
    @staticmethod
    def get_url(API_KEY, organization_id)->list:
        url = f"https://api.meraki.com/api/v1/organizations/{organization_id}/networks"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Accept": "application/json"
        }
        urls = []
        try:
            logging.info(f"Fetching urls for organization ID: {organization_id}...")
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            networks = response.json()
            marcos_networks = [network for network in networks if 'Marcos' in network['name']]
            logging.info(f"Found {len(marcos_networks)} networks with 'Marcos' in their name.")
            urls = {}
            for network in networks:
                url = network['url']
                match = re.search(r'Marcos\d{4}', url)
                if match:
                    marcos_part = match.group(0)
                    urls[marcos_part] = url

            print(f"Total url dicts are: {urls}")
            return urls
        except requests.exceptions.RequestException as e:
            logging.error(f"Error fetching networks: {e}")
            return {}

    @staticmethod
    def get_wan1_uplink_status(API_KEY, organization_id, retries=3, delay=5) -> list:
        url = f"https://api.meraki.com/api/v1/organizations/{organization_id}/appliance/uplink/statuses"
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Accept": "application/json"
        }
        
        for attempt in range(retries):
            try:
                logging.info(f"Fetching WAN 1 uplink statuses for organization ID: {organization_id} (Attempt {attempt + 1})...")
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                uplink_statuses = response.json()
                return uplink_statuses
            except requests.exceptionsRequestException as e:
                logging.error(f"Error fetching WAN 1 uplink statuses: {e}")
                if attempt < retries - 1:
                    logging.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                else:
                    logging.error("Max retries reached. Returning empty list.")
                    return []


    def run(self):
        logging.info("Starting the script...")
        marcos_networks = UplinkStatuses.get_marcos_networks(self.API_KEY, self.ORG_ID)
        if not marcos_networks:
            logging.error("No 'Marcos' networks found.")
            return
        wan1_uplinks = UplinkStatuses.get_wan1_uplink_status(self.API_KEY, self.ORG_ID)
        if not wan1_uplinks:
            logging.error("No WAN 1 uplink statuses available.")
            return
        
        urls = UplinkStatuses.get_url(self.API_KEY, self.ORG_ID)
        for network in marcos_networks:
            network_id = network['id']
            network_name = network['name']
            found_status = False
            i = 0

            for uplink_status in wan1_uplinks:
                if uplink_status['networkId'] == network_id:
                    for uplink in uplink_status['uplinks']:
                        if uplink['interface'] == 'wan1':
                            logging.info(f"WAN 1 Uplink status for {network_name}: {uplink['status']}")
                            print(f"WAN 1 Uplink status for {network_name}: {uplink['status']}")
                            found_status_wan1 = True
                            match = re.search(r'Marcos\d{4}', network_name)
                        if uplink['interface'] == 'cellular':
                            logging.info(f"Cellular Uplink status for {network_name}: {uplink['status']}")
                            print(f"Cellular Uplink status for {network_name}: {uplink['status']}")
                            found_status_cell = True
                            match = re.search(r'Marcos\d{4}', network_name)
                    if match:
                        try:
                            match_part = match.group(0)
                            store_url = urls[match_part]
                            print(f"Store url is: {store_url}")
                            logging.info(f"Store url is: {store_url}")
                        except:
                            logging.info(f"Match not found")
                            print(f"Match not found")
                    match = None


            if not found_status_wan1:
                logging.error(f"Could not retrieve WAN 1 uplink status for {network_name}.")
                print(f"Could not retrieve WAN 1 uplink status for {network_name}.")

            if not found_status_cell:
                logging.error(f"Could not retrieve Cellular uplink status for {network_name}.")
                print(f"Could not retrieve Cellular uplink status for {network_name}.")


if __name__ == '__main__':
    processor = UplinkStatuses()
    processor.run()