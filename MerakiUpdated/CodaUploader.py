import time
import config
import readfile
from requests.exceptions import RequestException
from CodaUtils import CodaUtils
from buttonpress import push_button

class CodaUploader:
    DOC_ID = "6gRoLFuM-u"
    TABLE_ID = "grid--jBYCrtfwS"
    CALLS = 5
    RATE_LIMIT = 10

    @staticmethod
    def preview_updates(store_dict):
        print("Preview of updates:")
        for store, store_info in store_dict.items():
            print(f"Store: {store}")
            print(f"  WAN: {store_info.get('WAN', 'N/A')}")
            print(f"  Cellular: {store_info.get('Cellular', 'N/A')}")
            print("---")

    @staticmethod
    def update_rows(doc_id, table_id, store_dict):
        uri = f"docs/{doc_id}/tables/{table_id}/rows"
        
        rows_to_upsert = []
        for store, store_info in store_dict.items():
            # Normalize store name: "Marcos 1234" → "Marcos1234"
            normalized_store = store.replace("<PERSON> ", "<PERSON>").strip()
            cells = [
                {"column": config.store_numb, "value": normalized_store},
                {"column": config.wan_one, "value": store_info.get("WAN", "N/A")},
                {"column": config.cellular, "value": store_info.get("Cellular", "N/A")}
            ]
            
            rows_to_upsert.append({
                "cells": cells,
                "keyColumns": [config.store_numb]
            })
        
        max_retries = 5
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                upsert_result = CodaUtils.api_call(uri, method="POST", payload={"rows": rows_to_upsert, "keyColumns": [config.store_numb]})
                if upsert_result:
                    print(f'Upserted {len(rows_to_upsert)} rows')
                    return True
                else:
                    print("Failed to upsert rows")
                    return False
            
            except RequestException as e:
                if e.response.status_code == 429:
                    if attempt < max_retries - 1:
                        print(f"Rate limit hit, retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        print(f"Failed to upsert rows after {max_retries} attempts")
                        return False
                else:
                    print(f"Error upserting rows: {e}")
                    return False
        
        return True

    def run(self):
        store_dict = readfile.read_status()
        
        doc_id = CodaUtils.get_doc(self.DOC_ID)
        if not doc_id:
            print("Failed to get document ID")
            return
        
        table_id = self.TABLE_ID
        if not table_id:
            print("Failed to get table ID")
            return
        
        #print(f"Document ID: {doc_id}")
        #print(f"Table ID: {table_id}")
        
        # Verify table access
        if not CodaUtils.verify_table_access(doc_id, table_id):
            print("Unable to access the selected table. Please check your permissions and table ID.")
            return
        
        # Verify table info
        table_info = CodaUtils.get_table(doc_id, table_id)
        if not table_info:
            print("Failed to retrieve table info")
            return
        
        # List columns
        columns = CodaUtils.list_columns(doc_id, table_id)
        if not columns:
            print("Failed to retrieve columns")
            return
        
        # Verify column IDs
        column_ids = [col['id'] for col in columns['items']]
        #if config.store_numb not in column_ids:
        #    print(f"Error: store_numb column ID {config.store_numb} not found in table")
        #    return
        #if config.wan_one not in column_ids:
        #    print(f"Error: wan_one column ID {config.wan_one} not found in table")
        #    return
        #if config.cellular not in column_ids:
        #    print(f"Error: cellular column ID {config.cellular} not found in table")
        #    return
        
        # self.preview_updates(store_dict)
        
        # Proceed with update without confirmation
        success = self.update_rows(doc_id, table_id, store_dict)
        if success:
            print("Update successful")
            push_button()
        else:
            print("Update failed")

if __name__ == "__main__":
    processor = CodaUploader()
    processor.run()