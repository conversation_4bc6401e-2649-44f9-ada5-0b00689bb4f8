import os
import json
import datetime

def split_list(my_list, chunk_size):
    """ splits list into number of chunks 
        eg:
            input for 5 chunk size: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
            output for 5 chunk size: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    """
    # Define a list of integers from 1 to 15
    # my_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

    # Define the chunk size for splitting the list
    # chunk_size = 5

    # Create a list of empty lists, one for each chunk
    split_list = [[] for _ in range(chunk_size)]

    # Iterate over the elements and their indices in my_list
    for index, value in enumerate(my_list):
        # Calculate the index of the sublist where the current value should go using modulo operation
        sublist_index = index % chunk_size
        
        # Append the current value to the corresponding sublist in split_list
        split_list[sublist_index].append(value)

    # Print the list of sublists after splitting my_list into chunks
    # print(split_list)    
    return split_list


def save_file(file_path, file_content, write_mode='w'):
        """
        Saves file
        """
        fp = open(file_path, write_mode)
        fp.write(file_content)
        fp.close()

        if os.path.exists(file_path) and os.path.isfile(file_path):
              print(f"File successfully saved to: {file_path}")
        return os.path.exists(file_path)

def read_file(file_path):
        """
        Reads file content
        """
        fp = open(file_path)
        file_content = fp.read()
        fp.close()

        return file_content

def create_directories(path):
    """
    Creates directories if they don't exist.
    """
    os.makedirs(path, exist_ok=True)

def isDriver(jobTitle, driving_opt_in=None):
    if jobTitle == "DRIVER" or (driving_opt_in is not None and driving_opt_in.lower() == 'y'):
        return True 
    else:
        return False
    
def is_only_digits(value):
    if isinstance(value, str) and value.isdigit():
        return True
    try:
        int(value)
        return True
    except ValueError:
        return False

def get_string_from_array_list(my_list, delimiter=',', enclosure="'"):
    """  
        takes array list. eg: ['314491','293824','199679'] 
        returns string separated by delimiter and enclosed by enclosure. eg:   '314491','293824','199679'
    """
    final_result = ''
    # final_result = "'" +"','".join(str(x) for x in my_list)+"'" # convert to string and concatinate by ,        
    if len(my_list) > 0:
        final_result = f"{enclosure}" +f"{enclosure}{delimiter}{enclosure}".join(str(x) for x in my_list)+f"{enclosure}" # convert to string and concatinate by ,        
    return final_result

def get_array_list_from_string( my_string, delimiter=',', remove_spaces = True):
    """  
        takes string. eg: '314491','293824','199679'
        takes array list. eg: ['314491','293824','199679'] 
        returns array list: ['314491','293824','199679'] 
    """

    my_list = []
    
    if my_string is not None and len(my_string) > 0:
        # remove spaces
        if remove_spaces and ' ' in my_string:
            my_string = my_string.replace(' ','')

        if delimiter in my_string:
            my_list = my_string.split(delimiter)
        else:
            my_list.append(my_string)

    return my_list

def load_json_file(json_file_path):
    """ loads JSON file from given path """

    with open(json_file_path) as f:
        json_content = json.load(f)
        # print(d)
        return json_content

def is_only_digits(value):
    if isinstance(value, str) and value.isdigit():
        return True
    try:
        int(value)
        return True
    except ValueError:
        return False

def is_valid_date(date_string, print_result=False):
    """ check valid date """
    try:
        # Attempt to create a date object from the string
        datetime.datetime.strptime(date_string, "%Y-%m-%d")
        if print_result:
            p(f"VALID: is_valid_date({date_string})")
        return True
    except ValueError:
        # If ValueError is raised, the date string is not valid
        try:
            # If it fails, try to parse as datetime
            datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
            if print_result:
                p(f"VALID: is_valid_date({date_string})")

            return True
        except ValueError:
            # If both fail, the string is invalid
            if print_result:
                p(f"INVALID: is_valid_date({date_string})")

            return False

    
def p(msg, print_log=True, kill_now=False):
    """ print utility """
    if print_log:
        start_now = datetime.datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')    
        print(f"{start_now}: {msg}")
    if kill_now:
        exit(1)