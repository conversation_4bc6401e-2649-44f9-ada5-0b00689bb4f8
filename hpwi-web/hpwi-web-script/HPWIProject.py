import requests
import json
import os
import time
import logging
from datetime import datetime
##from dotenv import load_dotenv
import asyncio
import HPWIWebScript



# Setup logging
log_dir = "HPWILog"
os.makedirs(log_dir, exist_ok=True)
log_filename = f"controller-automation-{datetime.now().strftime('%Y-%m-%d')}.log"
logging.basicConfig(
    filename=os.path.join(log_dir, log_filename),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Environment variables
API_KEY = os.getenv("HPWICALL")
DOC_ID = os.getenv("DOC_ID")
TABLE_ID = os.getenv("TABLE_ID")
REQUEST_COMP_COLUMN = os.getenv("REQUEST_COMP_COLUMN")
REFUNDED_COLUMN = os.getenv("REFUNDED_COLUMN")
DUPLICATE_COLUMN = os.getenv("DUPLICATE_COLUMN")
AMOUNT_LOST = os.getenv("AMOUNT_LOST")
EMAIL = os.getenv("EMAIL")
HPWI_USER = os.getenv("HPWI_USER")
HPWI_PASS = os.getenv("HPWI_PASS")


def api_call(uri, params=None):
    headers = {"Authorization": f"Bearer {API_KEY}"}
    try:
        res = requests.get(uri, headers=headers, params=params)
        res.raise_for_status()
        return res.json()
    except requests.exceptions.RequestException as e:
        logging.error(f"An error occurred: {e}")
        return None


def get_tables(doc_id):
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables"
    tables = api_call(uri)
    if tables:
        table_id = tables["items"][0]["id"]
        logging.info(f"Table ID: {table_id}")
        return table_id
    logging.error("Failed to fetch tables.")
    return None


def list_columns(doc_id, table_id):
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/columns"
    columns = api_call(uri)
    if columns:
        pretty_columns = json.dumps(columns, indent=4)
        logging.info(f"This is the columns: {pretty_columns}")
    else:
        logging.error("Failed to list columns.")


def get_rows(doc_id, table_id):
    uri = f"https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/rows"
    params = {"query": f'{REFUNDED_COLUMN}:"false"'}
    unfiltered_rows = api_call(uri, params)

    if not unfiltered_rows:
        return {}

    filtered_rows = {}

    for item in unfiltered_rows["items"]:
        amount_str = item["values"].get(AMOUNT_LOST, "$0.00") or "$0.00"
        amount = float(amount_str.replace("$", ""))

        if "," in amount_str:
            continue

        item["values"][AMOUNT_LOST] = 3 if amount > 3 else 2

        if (amount <= 7.50 and item["values"].get(REQUEST_COMP_COLUMN) == "Yes, I would like compensation"
                and item["values"].get(DUPLICATE_COLUMN) is False):
            filtered_rows[item["id"]] = item["values"]

    return filtered_rows


def put_info(filtered_rows):
    info_list = []
    for id, row_info in filtered_rows.items():
        email = row_info.get(EMAIL)
        amount = row_info.get(AMOUNT_LOST)
        info_list.append((email, amount, id))
    return info_list


def refund_confirm(row_id):
    uri = f"https://coda.io/apis/v1/docs/{DOC_ID}/tables/{TABLE_ID}/rows/{row_id}"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    payload = {
        "row": {
            "cells": [
                {
                    "column": REFUNDED_COLUMN,
                    "value": True
                }
            ]
        }
    }

    max_retries = 3
    retry_delay = 4

    for attempt in range(max_retries):
        try:
            logging.info(f"Attempting to update row {row_id} as refunded...")
            req = requests.put(uri, headers=headers, json=payload)
            req.raise_for_status()
            res = req.json()
            logging.info(f"Successfully updated row {res['id']}")
            return
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to update row {row_id} (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                logging.critical(f"All retry attempts failed for row {row_id}.")


async def main():
    list_columns(DOC_ID, TABLE_ID)

    filtered_rows = get_rows(DOC_ID, TABLE_ID)

    if not filtered_rows:
        logging.info("No rows to process.")
        return

    info_list = put_info(filtered_rows)
    for email, amount, id in info_list:
        logging.info(f"Processing refund for: {email} | Amount: {amount} | Row ID: {id}")
        success = await HPWIWebScript.main(email=email, amount=amount, id=id)

        if success:
            refund_confirm(id)
        else:
            logging.warning(f"Refund not confirmed for {id} due to previous failure.")


if __name__ == "__main__":
    asyncio.run(main())