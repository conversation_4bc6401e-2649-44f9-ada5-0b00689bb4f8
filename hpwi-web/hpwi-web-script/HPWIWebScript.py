import asyncio
import logging
import os
from playwright.async_api import async_playwright


HPWI_USER = os.getenv("HPWI_USER")
HPWI_PASS = os.getenv("HPWI_PASS")


async def login_to_website(page, username, password):
    try:
        await page.goto("https://www.mysmartice.com/ice2u/coupons/new")
        await page.wait_for_load_state("networkidle")  # wait for network to calm

        await page.locator("#user_username").fill(username)
        await page.locator("#user_password").fill(password)
        await page.locator('input[name="commit"]').click()
    except Exception as e:
        print(f"An error occurred during login: {e}")



async def fill_form(page, email, amount):
    try:
        page.set_default_timeout(120000)  # 2-minute timeout

        await page.locator("#ice2u_coupon_form_description").fill(
            f"This is good for {amount} 20lb Ice Vends! The code below never expires! "
            "We apologize for the inconvenience and are trying to resolve the issue with our machine. "
            "To check on the status of the machine, please look under the ICE2U app."
        )

        await page.locator("#ice2u_coupon_form_redeems_per_customer_limit").fill(str(amount))
        await page.locator("#ice2u_coupon_form_notification_type_email").click()
        await page.locator("#ice2u_coupon_form_notification_emails").fill(email)

        await page.locator('input[value="Create Coupon"]').click()

        # Wait for success message
        await page.locator("text=Coupon created. Notifications should be delivered within a few minutes.").wait_for(timeout=120000)

        logging.info("Coupon successfully created.")

    except Exception as e:
        logging.error(f"An error occurred while filling the form: {e}")



async def run(playwright, email, amount):
    browser = None
    context = None
    page = None
    try:
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        await login_to_website(page, HPWI_USER, HPWI_PASS)
        await fill_form(page, email, amount)

    except asyncio.CancelledError:
        logging.error("Script cancelled while waiting. Likely from a page or browser closure.")
        raise
    except Exception as e:
        logging.error(f"An error occurred during automation: {e}")
    finally:
        if page and not page.is_closed():
            await page.close()
        if context:
            await context.close()
        if browser:
            await browser.close()



async def main(email, amount, id):
    async with async_playwright() as playwright:
        await run(playwright=playwright, email=email, amount=amount)
        return True  #Returns back to the script
