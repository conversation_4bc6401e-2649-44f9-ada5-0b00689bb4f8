# src/main.py
import os
import pandas as pd
from datetime import datetime, timedelta
import time
from pathlib import Path
from libs.snowflake_helper import SnowflakeHelper
from snowflake.connector.pandas_tools import write_pandas
import libs.jgutils as jgutils # added 10/9/24


csm_database = os.getenv("DATABASE_CSM_DATABASE")
my_schema = os.getenv("MOMS_SCHEMA")

def load_doordash_csv_file_to_snowflake(sfconnection, filepath, table_name, columns_list, dry_run=True):
    
    try:
        ##sfconnection = sf.SnowflakeHelper() # snowflake helper    
        # Read CSV file into pandas DataFrame
        df = pd.read_csv(filepath)
        
        # Convert DataFrame to list of tuples
        csv_data_list = df.values.tolist()
        rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')

        counter = 0
        data_list = []
        dd_order_ids_list = []

        # Execute INSERT query for each row in the DataFrame
        for row in csv_data_list:
            row.append(rec_ins_date) # add current date to the row
            data_list.append(row) # append the row to the list

            dd_order_id = row[2] # assuming dd_order_id is at index 2
            if dd_order_id not in dd_order_ids_list:
                dd_order_ids_list.append(dd_order_id) # track dd_order_ids

            # print(f"Row {counter}: {row}")
            counter += 1
            # if counter == 10:
            #     break

        total_records = len(data_list)
        
        if dry_run:
            print(f"\n[DRY RUN] Would insert {total_records} rows into table: {table_name}")
            print(f"[DRY RUN] Columns: {columns_list}")
            print(f"[DRY RUN] Sample row: {data_list[0] if data_list else 'N/A'}")
            return
        
        if total_records> 0:
            start_time_ = time.time()
            #! Pass in the table name for the table I am trying to update
            my_table = table_name

            print(f"Deleting {len(dd_order_ids_list)} existing records from: {my_table}")
            # delete existing records for the same DD_ORDER_IDs to avoid duplicates
            dd_order_id_list_string = jgutils.get_string_from_array_list(my_list=dd_order_ids_list, enclosure="'")
            query = f"""
                DELETE FROM  {csm_database}.{my_schema}.{my_table} WHERE DD_ORDER_ID in({dd_order_id_list_string})
                """            
            sfconnection.execute_snowflake_query(query=query, pull_only_one_record=False, print_query=False)

            # print(f"query: {query}")
            # exit(1)

            ##columns_list = ['ORDER_DELIVERED_DATE','ORDER_DELIVERED_TIME','DD_ORDER_ID','CLIENT_ORDER_ID','CUSTOMER_NAME','STORE_ID','MERCHANT_SUPPLIED_ID','STORE_NAME','WAS_ASAP','CONFIRMED_FOOD_READY_TIME','DASHER_ARRIVAL_TIME','ORDER_PICK_UP_TIME','AVOIDABLE_WAIT_TIME','DELIVERED_AT_TIMESTAMP','TOTAL_DELIVERY_TIME','ORDER_SUBTOTAL','NET_PAYOUT','CURRENCY','RECORD_INSERTED_AT']
            # data_list.append(record)

            sfconnection.bulk_insert(columns_list=columns_list, data_list=data_list,database=csm_database, schema=my_schema, table=my_table)    

            end_now_ = datetime.now().strftime('%A, %Y-%m-%d %H:%M:%S')
            duration = sfconnection.get_duration(start_time_, show_secs=True)
            msg = f"Finished inserting {total_records} records to {my_table} table\t[time: {end_now_}] [{duration}] "
            print(f"\n\n{msg}\n")
            
        print("Data loaded successfully into Snowflake table.")
        
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # finally:
    #     # Close cursor and connection
    #     if 'cur' in locals():
    #         cur.close()
    #     if 'conn' in locals():
    #         conn.close()
    
    
def main():
    sfconnection = SnowflakeHelper()

    today = datetime.now().strftime("%Y-%m-%d")
    data_dir = Path(os.getenv("DOORDASH_DATA_DIR", "./data"))

    # Define per-type configs
    file_configs = {
        "DOORDASH_AVOIDABLE_WAIT_ORDERS": {
            "filename_prefix": "DOORDASH_AVOIDABLE_WAIT_ORDERS",
            "table": "DOORDASH_AVOIDABLE_WAIT_ORDERS",
            "columns": [
                'ORDER_DELIVERED_DATE', 'ORDER_DELIVERED_TIME', 'DD_ORDER_ID', 'CLIENT_ORDER_ID',
                'CUSTOMER_NAME', 'STORE_ID', 'MERCHANT_SUPPLIED_ID', 'STORE_NAME', 'WAS_ASAP',
                'CONFIRMED_FOOD_READY_TIME', 'DASHER_ARRIVAL_TIME', 'ORDER_PICK_UP_TIME',
                'AVOIDABLE_WAIT_TIME', 'DELIVERED_AT_TIMESTAMP', 'TOTAL_DELIVERY_TIME',
                'ORDER_SUBTOTAL', 'NET_PAYOUT', 'CURRENCY', 'RECORD_INSERTED_AT'
            ]
        },
        "DOORDASH_MISSING_INCORRECT_ORDERS": {
            "filename_prefix": "DOORDASH_MISSING_INCORRECT_ORDERS",
            "table": "DOORDASH_MISSING_INCORRECT_ORDERS",
            "columns": [
                'ORDER_DELIVERED_DATE', 'ORDER_DELIVERED_TIME', 'DD_ORDER_ID', 'CLIENT_ORDER_ID',
                'STORE_ID', 'MERCHANT_SUPPLIED_ID', 'STORE_NAME', 'ERROR_CATEGORY', 'MENU_CATEGORY',
                'ITEM_NAME', 'QUANTITY', 'ERROR_CHARGE', 'CUSTOMER_COMMENT', 'MODIFIER_DETAIL',
                'ORDER_LINK', 'CUSTOMER_NAME', 'DASHER_NAME', 'CURRENCY', 'RECORD_INSERTED_AT'
            ]
        },
        "DOORDASH_CANCELLED_ORDERS": {
            "filename_prefix": "DOORDASH_CANCELLED_ORDERS",
            "table": "DOORDASH_CANCELLED_ORDERS",
            "columns": [
                'ORDER_PLACED_DATE', 'ORDER_PLACED_TIME', 'DD_ORDER_ID', 'CLIENT_ORDER_ID',
                'CUSTOMER_NAME', 'STORE_ID', 'MERCHANT_SUPPLIED_ID', 'STORE_NAME', 'WAS_ASAP',
                'CANCELLED_AT_TIMESTAMP', 'CANCELLATION_CATEGORY_SHORT',
                'CANCELLATION_CATEGORY_DESCRIPTION', 'PAID', 'NON_PAYMENT_REASON',
                'ORDER_CONFIRMATION_TIMESTAMP', 'MINUTES_TO_CONFIRMATION', 'MINUTES_TO_CANCEL',
                'ORDER_SUBTOTAL', 'NET_PAYOUT', 'CURRENCY', 'RECORD_INSERTED_AT'
            ]
        }
    }

    for keyword, config in file_configs.items():
        expected_filename = f"{config['filename_prefix']}-{today}.csv"
        filepath = data_dir / expected_filename
        if filepath.exists():
            print(f"🚀 Loading {filepath.name} into {config['table']}")
            load_doordash_csv_file_to_snowflake(
                sfconnection=sfconnection,
                filepath=str(filepath),
                table_name=config["table"],
                columns_list=config["columns"],
                dry_run=False
            )
        else:
            print(f"⚠️  Skipping {keyword} — file not found for today: {expected_filename}")


if __name__ == "__main__":
    main()
